#鲜达服务（清美鲜达APP）
qingyun-xda
    qingyun-xda-cms         (资源，广告，设置，配置等后台管理功能)
    qingyun-xda-product     （鲜达商品管理等功能）
####服务简介
鲜达服务

#### 主要研发人员
ALL

####git 地址
1. http://*************/pinshang/qingyun-xda.git
2. git@*************:pinshang/qingyun-xda.git

####jenkins 部署 

一。服务部署
1. qingyun-xda-cms-service build 配置
   > Root POM = qingyun-xda-cms/qingyun-xda-cms-service/pom.xml
   > Goals and options = clean package  -Dmaven.test.skip=true
2. qingyun-xda-product-service build 配置
      > Root POM = qingyun-xda-product/qingyun-xda-product-service/pom.xml
      > Goals and options = clean package  -Dmaven.test.skip=true
 
二。Client部署
1. qingyun-xda-cms-client build 配置
   > Root POM = qingyun-xd-cms/pom.xml
   > Goals and options = clean install deploy -Dmaven.test.skip=true -P dev install -pl qingyun-xda-cms-client -am -amd
2. qingyun-xda-product-client build 配置
      > Root POM = qingyun-xd-product/pom.xml
      > Goals and options = clean install deploy -Dmaven.test.skip=true -P dev install -pl qingyun-xda-product-client -am -amd 
         
####服务架构
1. springboot + springcloud + redis + kafka + db + es + eureka + apollo 

#### 安装要求
1. pingshang库
2. jar包运行
3. 2核，4G，20G最低要求
4. 注册中心eureka
5. 配置中心apollo/config
6. 鲜到redis
7. kafka集群 