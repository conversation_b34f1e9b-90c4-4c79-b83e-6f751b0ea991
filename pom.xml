<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.pinshang.qingyun</groupId>
    <artifactId>qingyun-xda</artifactId>
    <version>1.2.1-UP-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>qingyun-xda-parent</module>
        <module>qingyun-xda-cms</module>
        <module>qingyun-xda-product</module>
        <module>qingyun-xda-search</module>
    </modules>
<!--    <distributionManagement>-->
<!--        <repository>-->
<!--            <id>nexus-releases</id>-->
<!--            <name>Nexus Release Repository</name>-->
<!--            <url>http://192.168.0.31:9001/nexus/content/repositories/releases/</url>-->
<!--        </repository>-->
<!--        <snapshotRepository>-->
<!--            <id>nexus-snapshots</id>-->
<!--            <name>Nexus Snapshot Repository</name>-->
<!--            <url>http://192.168.0.31:9001/nexus/content/repositories/snapshots/</url>-->
<!--        </snapshotRepository>-->
<!--    </distributionManagement>-->

<!--    <build>-->
<!--        <plugins>-->
<!--            <plugin>-->
<!--                <groupId>org.apache.maven.plugins</groupId>-->
<!--                <artifactId>maven-compiler-plugin</artifactId>-->
<!--                <configuration>-->
<!--                    <source>1.8</source>-->
<!--                    <target>1.8</target>-->
<!--                </configuration>-->
<!--            </plugin>-->
<!--            <plugin>-->
<!--                <groupId>org.jetbrains.kotlin</groupId>-->
<!--                <artifactId>kotlin-maven-plugin</artifactId>-->
<!--                <version>${kotlin.version}</version>-->
<!--                <configuration>-->
<!--                    <args>-->
<!--                        <arg>-Xjsr305=strict</arg>-->
<!--                    </args>-->
<!--                    <compilerPlugins>-->
<!--                        <plugin>spring</plugin>-->
<!--                    </compilerPlugins>-->
<!--                </configuration>-->
<!--                <dependencies>-->
<!--                    <dependency>-->
<!--                        <groupId>org.jetbrains.kotlin</groupId>-->
<!--                        <artifactId>kotlin-maven-allopen</artifactId>-->
<!--                        <version>${kotlin.version}</version>-->
<!--                    </dependency>-->
<!--                </dependencies>-->
<!--            </plugin>-->
<!--        </plugins>-->
<!--    </build>-->
</project>
