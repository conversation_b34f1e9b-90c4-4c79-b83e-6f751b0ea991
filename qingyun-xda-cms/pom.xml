<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.pinshang.qingyun</groupId>
        <artifactId>qingyun-xda-parent</artifactId>
        <version>1.2.1-UP-SNAPSHOT</version>
        <relativePath>../qingyun-xda-parent/pom.xml</relativePath>
    </parent>
    <artifactId>qingyun-xda-cms</artifactId>
    <packaging>pom</packaging>
    <modules>
        <module>qingyun-xda-cms-client</module>
        <module>qingyun-xda-cms-service</module>
    </modules>
    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <poi.version>4.1.1</poi.version>
        <poi-ooxml.version>4.1.1</poi-ooxml.version>
        <kafka.version>3.0.1</kafka.version>
        <spring.kafka.version>2.8.4</spring.kafka.version>
    </properties>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-store-client</artifactId>
                <version>${qingyun.store.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-marketing-client</artifactId>
                <version>${qingyun.marketing.version}</version>
            </dependency>
			<!--kafka -->
	        <dependency>
	            <groupId>org.springframework.kafka</groupId>
	            <artifactId>spring-kafka</artifactId>
	            <version>${spring.kafka.version}</version>
	        </dependency>
	        <dependency>
	            <groupId>org.apache.kafka</groupId>
	            <artifactId>kafka-clients</artifactId>
	            <version>${kafka.version}</version>
	        </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi-ooxml.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-xda-product-client</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-order-client</artifactId>
                <version>${qingyun.order.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-switch</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-health-check</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-health-check</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-apmCat-starter</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-file-export-cache</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-loadBalancer</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
               <groupId>qingyun-infrastructure</groupId>
               <artifactId>qingyun-infrastructure-components-inventory</artifactId>
               <version>${qingyun.infrastructure.version}</version>
           </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-springcloud-common</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
                <groupId>qingyun-infrastructure</groupId>
                <artifactId>qingyun-infrastructure-common</artifactId>
                <version>${qingyun.infrastructure.version}</version>
            </dependency>
            <dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-metrics-client</artifactId>
				<version>${qingyun.infrastructure.version}</version>
			</dependency>
			<dependency>
				<groupId>qingyun-infrastructure</groupId>
				<artifactId>qingyun-infrastructure-mq</artifactId>
				<version>${qingyun.infrastructure.version}</version>
			</dependency>
			<dependency>
	            <groupId>qingyun-infrastructure</groupId>
	            <artifactId>qingyun-infrastructure-cache</artifactId>
	            <version>${qingyun.infrastructure.version}</version>
	        </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-tms-client</artifactId>
                <version>${qingyun.tms.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <name>Nexus Release Repository</name>
            <url>http://192.168.0.31:9001/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://192.168.0.31:9001/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
