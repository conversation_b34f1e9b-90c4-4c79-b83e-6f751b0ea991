package com.pinshang.qingyun.xda.cms.dto.positionInfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 资源位-商品信息
 */
@Data
public class PositionCommodityInfoODTO {
	@ApiModelProperty(position = 11, required = true, value = "商品ID")
	private String commodityId;
	@ApiModelProperty(position = 12, required = true, value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(position = 13, value = "商品名称")
	private String commodityName;
	@ApiModelProperty(position = 14, value = "商品规格")
	private String commoditySpec;
	@ApiModelProperty(position = 15, required = true, value = "排序（正整数）")
	private Integer sortNum;
}
