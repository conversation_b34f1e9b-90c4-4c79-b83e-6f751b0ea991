package com.pinshang.qingyun.xda.cms.hystrix;

import com.pinshang.qingyun.xda.cms.service.XdaFavorPositionClient;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


@Component
public class XdaFavorPositionClientHystrix implements FallbackFactory<XdaFavorPositionClient> {
    @Override
    public XdaFavorPositionClient create(Throwable throwable) {
        return new XdaFavorPositionClient() {


            @Override
            public Boolean checkFavorXPositionCountForShopEnable() {
                return null;
            }

        };
    }
}
