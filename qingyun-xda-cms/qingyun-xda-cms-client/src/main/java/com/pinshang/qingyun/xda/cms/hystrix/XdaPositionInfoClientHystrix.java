package com.pinshang.qingyun.xda.cms.hystrix;

import java.util.List;

import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import com.pinshang.qingyun.xda.cms.dto.positionInfo.PositionCommodityInfoIDTO;
import com.pinshang.qingyun.xda.cms.dto.positionInfo.PositionCommodityInfoODTO;
import com.pinshang.qingyun.xda.cms.service.XdaPositionInfoClient;

/**
 * 鲜达-资源位
 */
@Component
public class XdaPositionInfoClientHystrix implements FallbackFactory<XdaPositionInfoClient> {
    @Override
    public XdaPositionInfoClient create(Throwable throwable) {
        return new XdaPositionInfoClient() {
        	
            @Override
			public List<PositionCommodityInfoODTO> importSelectPositionCommodityInfoList(List<PositionCommodityInfoIDTO> commodityList) {
				return null;
			}

            @Override
            public void batchExpireJob() {

            }

        };
    }
    
}
