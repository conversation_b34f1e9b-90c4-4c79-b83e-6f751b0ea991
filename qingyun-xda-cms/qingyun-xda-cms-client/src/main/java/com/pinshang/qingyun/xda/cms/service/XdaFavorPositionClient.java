package com.pinshang.qingyun.xda.cms.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.xda.cms.hystrix.XdaFavorPositionClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;


@FeignClient(value = ApplicationNameConstant.QINGYUN_XDA_CMS_SERVICE, fallbackFactory = XdaFavorPositionClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XdaFavorPositionClient {

    @RequestMapping(value = "/xdaFavorXPosition/checkFavorXCountForShopEnable", method = RequestMethod.GET)
    Boolean checkFavorXPositionCountForShopEnable();


}
