package com.pinshang.qingyun.xda.cms.service;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.xda.cms.dto.positionInfo.PositionCommodityInfoIDTO;
import com.pinshang.qingyun.xda.cms.dto.positionInfo.PositionCommodityInfoODTO;
import com.pinshang.qingyun.xda.cms.hystrix.XdaPositionInfoClientHystrix;

/**
 * 鲜达-资源位
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_XDA_CMS_SERVICE, fallbackFactory = XdaPositionInfoClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XdaPositionInfoClient {
	
	@RequestMapping(value = "/xdaPositionInfo/importSelectPositionCommodityInfoList", method = RequestMethod.POST)
    public List<PositionCommodityInfoODTO> importSelectPositionCommodityInfoList(@RequestBody List<PositionCommodityInfoIDTO> commodityList);
	
	@RequestMapping(value = "/xdaPositionInfo/batchExpireJob", method = RequestMethod.POST)
   	public void batchExpireJob();

}
