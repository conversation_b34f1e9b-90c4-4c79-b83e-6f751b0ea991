package com.pinshang.qingyun;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.pinshang.qingyun.base.spring.MainArgsPreHandler;
import com.pinshang.qingyun.infrastructure.apm.cat.springboot.EnableCatMetrics;
import com.pinshang.qingyun.infrastructure.cache.starter.EnableCacheComponent;
import com.pinshang.qingyun.infrastructure.loadBalancer.starter.EnableQyLoadBalancer;
import com.pinshang.qingyun.infrastructure.mq.starter.EnableMqComponent;
import com.pinshang.qingyun.infrastructure.switcher.starter.EnableOnlineSwitchComponent;
import com.pinshang.qinyun.cache.service.RedisServiceDefinition;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import tk.mybatis.spring.annotation.MapperScan;

@Controller
//@EnableWebMvc
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@ComponentScan(basePackages = { "com.pinshang.qingyun"})
@MapperScan(basePackages = { "com.pinshang.qingyun.xda.*.mapper" })
@Import(value = {RedisServiceDefinition.class})
@EnableMqComponent
@EnableApolloConfig
@EnableOnlineSwitchComponent
@EnableCatMetrics
@EnableQyLoadBalancer
@EnableCacheComponent
public class ApplicationXdaCmsService extends WebMvcConfigurerAdapter {

    public static void main(String[] args) {
        SpringApplication.run(ApplicationXdaCmsService.class,  MainArgsPreHandler.argsHandle(args));
    }

    @GetMapping(value={"","/"})
    public String index(){
        return "redirect:/chk.html";
    }

}
