package com.pinshang.qingyun.xda.cms.conf;

import com.pinshang.qingyun.base.interceptor.QYServiceInterceptor;
import com.pinshang.qingyun.base.interceptor.XdaServiceInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

/**
 * <AUTHOR> weican on 2018-11-14.
 */
@Configuration
public class XdaCmsConfiguration {
    @Bean("xdaServiceInterceptor")
    public HandlerInterceptorAdapter xdaServiceInterceptor(){
        return new XdaServiceInterceptor();
    }

    @Bean("qyServiceInterceptor")
    public HandlerInterceptorAdapter qyServiceInterceptor(){
        return new QYServiceInterceptor();
    }

    //菜谱分组商品数量
    public static Integer COOKBOOK_GROUP_COMMODITY_NUM = 20;


}
