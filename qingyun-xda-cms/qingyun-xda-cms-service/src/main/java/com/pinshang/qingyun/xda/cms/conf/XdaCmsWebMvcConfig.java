

package com.pinshang.qingyun.xda.cms.conf;

import com.pinshang.qingyun.xda.cms.aspect.RequestBodyAndHeaderResolver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import java.util.List;

@Configuration
public class XdaCmsWebMvcConfig implements WebMvcConfigurer {
    @Autowired
    private HandlerInterceptorAdapter qyServiceInterceptor;
    @Autowired
    private HandlerInterceptorAdapter xdaServiceInterceptor;
    @Autowired
    private RequestBodyAndHeaderResolver requestBodyAndHeaderResolver;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        registry.addResourceHandler("/static/**").addResourceLocations("classpath:/static/");
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(qyServiceInterceptor);
        registry.addInterceptor(xdaServiceInterceptor);
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(requestBodyAndHeaderResolver);
    }
}
