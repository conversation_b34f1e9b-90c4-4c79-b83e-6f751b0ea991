package com.pinshang.qingyun.xda.cms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.cms.dto.qa.*;
import com.pinshang.qingyun.xda.cms.model.qa.XdaAppQa;
import com.pinshang.qingyun.xda.cms.service.qa.XdaAppQaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description:
 * @author: hhf
 * @time: 2020/12/16 10:14
 */
@RestController
@RequestMapping("/xdaAppQa")
@Api(value = "鲜达App常见问题", tags = "XdaAppQa", description = "鲜达App常见问题" )
public class XdaAppQaController {

    @Autowired
    private XdaAppQaService xdaAppQaService;

    @ApiOperation(value = "鲜达常见问题设置-列表", notes = "鲜达常见问题设置-列表")
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaAppQaIDTO")
    @PostMapping("/findXdaAppQaList")
    public PageInfo<XdaAppQaODTO> findXdaAppQaList(@RequestBody XdaAppQaIDTO idto){
        return xdaAppQaService.findXdaAppQaList(idto);
    }

    @ApiOperation(value = "鲜达常见问题-详细", notes = "鲜达常见问题-详细")
    @ApiImplicitParam(name = "id", value = "问题id", required = true, paramType = "query")
    @PostMapping("/findXdaAppQaDetailsById")
    public XdaAppQa findXdaAppQaDetailsById(@RequestParam(value = "id",required = false) Long id){
        return xdaAppQaService.findXdaAppQaDetailsById(id);
    }

    @ApiOperation(value = "鲜达常见问题-保存", notes = "鲜达常见问题-保存")
    @ApiImplicitParam(name = "saveIDTO", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaAppQaSaveIDTO")
    @PostMapping("/saveXdaAppQa")
    public Long saveXdaAppQa(@RequestBody XdaAppQaSaveIDTO saveIDTO){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        saveIDTO.setUserId(tokenInfo.getUserId());
        return xdaAppQaService.saveXdaAppQa(saveIDTO);
    }

    @ApiOperation(value = "鲜达常见问题-修改", notes = "鲜达常见问题-修改")
    @ApiImplicitParam(name = "editIDTO", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaAppQaEditIDTO")
    @PostMapping("/updateXdaAppQa")
    public Long updateXdaAppQa(@RequestBody XdaAppQaEditIDTO editIDTO){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        editIDTO.setUserId(tokenInfo.getUserId());
        return xdaAppQaService.updateXdaAppQa(editIDTO);
    }

    @ApiOperation(value = "鲜达常见问题-修改顺序", notes = "鲜达常见问题-修改顺序")
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaAppQaEditSortIDTO")
    @PostMapping("/updateXdaAppQaSortNum")
    public Long updateXdaAppQaSortNum(@RequestBody XdaAppQaEditSortIDTO idto){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        idto.setUserId(tokenInfo.getUserId());
        return xdaAppQaService.updateXdaAppQaSortNum(idto);
    }

    @ApiOperation(value = "鲜达常见问题-修改状态", notes = "鲜达常见问题-修改状态")
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaAppQaEditStatusIDTO")
    @PostMapping("/updateXdaAppQaStatus")
    public Long updateXdaAppQaStatus(@RequestBody XdaAppQaEditStatusIDTO idto){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        idto.setUserId(tokenInfo.getUserId());
        return xdaAppQaService.updateXdaAppQaStatus(idto);
    }
}
