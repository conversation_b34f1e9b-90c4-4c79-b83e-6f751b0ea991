package com.pinshang.qingyun.xda.cms.controller;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.common.dto.DictionaryODTO;
import com.pinshang.qingyun.common.service.DictionaryClient;
import com.pinshang.qingyun.xda.cms.dto.qa.XdaAppQaContentFrontODTO;
import com.pinshang.qingyun.xda.cms.dto.qa.XdaAppQaFrontODTO;
import com.pinshang.qingyun.xda.cms.dto.qa.XdaServiceCenterODTO;
import com.pinshang.qingyun.xda.cms.mapper.qa.XdaAppQaMapper;
import com.pinshang.qingyun.xda.cms.model.qa.XdaAppQa;
import com.pinshang.qingyun.xda.cms.service.XdaBackSettingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @ClassName XdaAppQaFrontController
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/12/16 17:10
 **/
@RestController
@Api(value = "鲜达app常见问题查询和清美客服", tags = "XdaAppQaFrontController", description = "鲜达app常见问题查询和清美客服")
@RequestMapping("/xdAppQaFront")
public class XdaAppQaFrontController {
    @Autowired
    private XdaAppQaMapper xdaAppQaMapper;

    @Autowired
    private DictionaryClient dictionaryClient;

    @ApiOperation(value = "鲜达app常见问题查询", notes = "常见问题查询")
    @GetMapping("/list")
    public List<XdaAppQaFrontODTO> findListForFront() {
//        XdaBackSetting backSetting =  xdaBackSettingService.findXdBackSettingDetails();
        List<XdaAppQaFrontODTO> qaList = xdaAppQaMapper.findListForFront();
//        return new XdaServiceCenterODTO(backSetting.getPhone(), backSetting.getPhoneTime(), qaList);
        return qaList;
    }

    @ApiOperation(value = "鲜达常见问题详情查询", notes = "常见问题详情查询")
    @GetMapping("/detail")
    public XdaAppQaContentFrontODTO detail(@RequestParam(value = "id",required = false) Long id) {
        QYAssert.notNull(id, "参数错误");
        XdaAppQa xdaAppQa = xdaAppQaMapper.selectByPrimaryKey(id);
        if (xdaAppQa != null) {
            XdaAppQaContentFrontODTO oVo = new XdaAppQaContentFrontODTO(xdaAppQa.getId(), xdaAppQa.getTitle(), xdaAppQa.getContent());
            return oVo;
        }
        return null;
    }

    @ApiOperation(value = "鲜达客服", notes = "鲜达-清美客服")
    @GetMapping("/customerService")
    public XdaServiceCenterODTO customerService() {
        DictionaryODTO serviceTelTime = dictionaryClient.getDictionaryByCode("service-tel-time");
//        DictionaryODTO serviceTel = dictionaryClient.getDictionaryByCode("service-tel");
//        DictionaryODTO serviceTime= dictionaryClient.getDictionaryByCode("service-time");

        return new XdaServiceCenterODTO(serviceTelTime.getOptionValue(), serviceTelTime.getOptionDefaultCode());
    }


}
