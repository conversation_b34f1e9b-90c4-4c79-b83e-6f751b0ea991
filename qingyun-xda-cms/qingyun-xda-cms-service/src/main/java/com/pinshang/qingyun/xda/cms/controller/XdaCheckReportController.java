package com.pinshang.qingyun.xda.cms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.xda.cms.dto.checkReport.ReportListDTO;
import com.pinshang.qingyun.xda.cms.service.checkReport.CheckReportService;
import com.pinshang.qingyun.xda.cms.vo.AddReportVO;
import com.pinshang.qingyun.xda.cms.vo.ReportListVO;
import com.pinshang.qingyun.xda.cms.vo.XdaMyCheckReportVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/xdaCheckReport")
@Api(value = "鲜达检验报告管理", tags = "xdaCheckReport", description = "鲜达检验报告管理" )
public class XdaCheckReportController {
    @Resource
    private CheckReportService checkReportService;

    @PostMapping("/queryMyCheckReport")
    @ApiOperation(value = "我的--检验报告列表")
    public ApiResponse<ReportListDTO> queryMyCheckReport(@RequestBody XdaMyCheckReportVO param, HttpServletResponse response) {
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG, QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        return ApiResponse.convert(checkReportService.queryMyCheckReport(param));
    }

    @PostMapping("/list")
    @ApiOperation(value = "查询列表")
    public PageInfo<ReportListDTO> list(@RequestBody ReportListVO param){
        return checkReportService.list(param);
    }

    @PostMapping("/add")
    @ApiOperation(value = "上传检验报告")
    public void add(@RequestBody AddReportVO param){
        checkReportService.add(param);
    }

    @GetMapping("/delete")
    @ApiOperation(value = "删除检验报告")
    public void delete(@RequestParam(value = "id",required = false) Long id){
        checkReportService.delete(id);
    }

}
