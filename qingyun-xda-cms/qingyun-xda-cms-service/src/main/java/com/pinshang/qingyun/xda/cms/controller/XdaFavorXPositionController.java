package com.pinshang.qingyun.xda.cms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.cms.dto.ForceSubmitResultODTO;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.*;
import com.pinshang.qingyun.xda.cms.service.favorPosition.XdaFavorXPositionLogService;
import com.pinshang.qingyun.xda.cms.service.favorPosition.XdaFavorXPositionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 鲜达横向位
 */
@RestController
@Api(value = "鲜达横向位", tags = "favorXPosition", description = "鲜达横向位")
@RequestMapping("/xdaFavorXPosition")
public class XdaFavorXPositionController {

    @Autowired
    private XdaFavorXPositionService xPositionService;
    @Autowired
    private XdaFavorXPositionLogService logService;

    /**
     * 横向资源位 分页查询
     * @param vo
     * @return
     */
    @ApiOperation(value = "分页查询横向位")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataType = "XdaFavorXPositionPageIDTO")
    @RequestMapping(value = "/queryXPositionPage", method = RequestMethod.POST)
    public PageInfo<XdaFavorXPositionInfoODTO> queryXPositionPage(@RequestBody XdaFavorXPositionPageIDTO vo) {
        return xPositionService.queryXPositionPage(vo);
    }

    @ApiOperation(value = "新增横向位")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataType = "XdaFavorXPositionSaveIDTO")
    @RequestMapping(value = "/addXPosition", method = RequestMethod.POST)
    public ForceSubmitResultODTO addXPosition(@RequestBody XdaFavorXPositionSaveIDTO vo) {
        vo.setUserId(FastThreadLocalUtil.getQY().getUserId());
        return xPositionService.addXPosition(vo);
    }

    @ApiOperation(value = "修改横向位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "favorPositionId", value = "横向资源位表ID", required = true, paramType = "path"),
            @ApiImplicitParam(name="vo", value="", required = true, paramType = "body", dataType ="XdaFavorXPositionSaveIDTO")
    })
    @RequestMapping(value = "/updateXPosition/{favorPositionId}", method = RequestMethod.POST)
    public ForceSubmitResultODTO updateXPosition(@PathVariable("favorPositionId")Long favorPositionId, @RequestBody XdaFavorXPositionSaveIDTO vo) {
        vo.setUserId(FastThreadLocalUtil.getQY().getUserId());
        return xPositionService.updateXPosition(favorPositionId,vo);
    }

    @ApiOperation(value = "横向位启用/停用")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataType = "XdaFavorPositionStatusIDTO")
    @RequestMapping(value = "/updateXPositionStatus", method = RequestMethod.POST)
    public ForceSubmitResultODTO updateXPositionStatus(@RequestBody XdaFavorPositionStatusIDTO vo) {
        vo.setUserId(FastThreadLocalUtil.getQY().getUserId());
        return xPositionService.updateXPositionStatus(vo);
    }

    @ApiOperation(value = "查询横向位详情")
    @ApiImplicitParam(name = "favorPositionId", value = "横向资源位表ID", required = true, paramType = "path")
    @RequestMapping(value = "/queryXPositionDetail/{favorPositionId}", method = RequestMethod.GET)
    public XdaFavorXPositionInfoODTO queryXPositionDetail(@PathVariable("favorPositionId")Long favorPositionId){
        return xPositionService.queryXPositionDetail(favorPositionId);
    }

    @ApiOperation(value = "查询横向位操作日志")
    @ApiImplicitParam(name = "favorPositionId", value = "横向资源位表ID", required = true, paramType = "path")
    @RequestMapping(value = "/queryXPositionLogList/{favorPositionId}", method = RequestMethod.GET)
    public List<XdaFavorPositionLogODTO> queryXPositionLogList(@PathVariable("favorPositionId")Long favorPositionId){
        return logService.queryXPositionLogList(favorPositionId);
    }

}
