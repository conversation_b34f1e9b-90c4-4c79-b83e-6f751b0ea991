package com.pinshang.qingyun.xda.cms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.ImageUtils;
import com.pinshang.qingyun.xda.cms.dto.ForceSubmitResultODTO;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.*;
import com.pinshang.qingyun.xda.cms.service.favorPosition.XdaFavorYPositionLogService;
import com.pinshang.qingyun.xda.cms.service.favorPosition.XdaFavorYPositionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 鲜达纵向位
 */
@RestController
@Api(value = "鲜达纵向位", tags = "favorYPosition", description = "鲜达纵向位")
@RequestMapping("/xdaFavorYPosition")
public class XdaFavorYPositionController {

    @Autowired
    private XdaFavorYPositionService positionService;
    @Autowired
    private XdaFavorYPositionLogService logService;
    @Value("${pinshang.img-xd-server-url}")
    private String imgXdServerUrl;


    /**
     * 纵向资源位 分页查询
     * @param vo
     * @return
     */
    @ApiOperation(value = "分页查询纵向位")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataType = "XdaFavorYPositionPageIDTO")
    @RequestMapping(value = "/queryYPositionPage", method = RequestMethod.POST)
    public PageInfo<XdaFavorYPositionInfoODTO> queryYPositionPage(@RequestBody XdaFavorYPositionPageIDTO vo) {
        return positionService.queryYPositionPage(vo);
    }

    @ApiOperation(value = "新增纵向位")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataType = "XdaFavorYPositionSaveIDTO")
    @RequestMapping(value = "/addYPosition", method = RequestMethod.POST)
    public ForceSubmitResultODTO addYPosition(@RequestBody XdaFavorYPositionSaveIDTO vo) {
        vo.setUserId(FastThreadLocalUtil.getQY().getUserId());
        return positionService.addYPosition(vo);
    }

    @ApiOperation(value = "修改纵向位")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "favorPositionId", value = "纵向位表ID", required = true, paramType = "path"),
            @ApiImplicitParam(name="vo", value="", required = true, paramType = "body", dataType ="XdaFavorYPositionSaveIDTO")
    })
    @RequestMapping(value = "/updateYPosition/{favorPositionId}", method = RequestMethod.POST)
    public ForceSubmitResultODTO updateXPosition(@PathVariable("favorPositionId")Long favorPositionId, @RequestBody XdaFavorYPositionSaveIDTO vo) {
        vo.setUserId(FastThreadLocalUtil.getQY().getUserId());
        return positionService.updateYPosition(favorPositionId,vo);
    }

    @ApiOperation(value = "纵向位启用/停用")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataType = "XdaFavorPositionStatusIDTO")
    @RequestMapping(value = "/updateYPositionStatus", method = RequestMethod.POST)
    public ForceSubmitResultODTO updateYPositionStatus(@RequestBody XdaFavorPositionStatusIDTO vo) {
        vo.setUserId(FastThreadLocalUtil.getQY().getUserId());
        return positionService.updateYPositionStatus(vo);
    }

    @ApiOperation(value = "查询纵向位详情")
    @ApiImplicitParam(name = "favorPositionId", value = "纵向位表ID", required = true, paramType = "path")
    @RequestMapping(value = "/queryYPositionDetail/{favorPositionId}", method = RequestMethod.GET)
    public XdaFavorYPositionInfoODTO queryYPositionDetail(@PathVariable("favorPositionId")Long favorPositionId){
        XdaFavorYPositionInfoODTO detailInfo = positionService.queryYPositionDetail(favorPositionId);
        if(detailInfo!=null && StringUtils.isNotEmpty(detailInfo.getPicUrl())){
            detailInfo.setVisitPicUrl(imgXdServerUrl + ImageUtils.getXdImgUrlV2(detailInfo.getPicUrl(), null));
        }
        return detailInfo;
    }

    @ApiOperation(value = "查询纵向位操作日志")
    @ApiImplicitParam(name = "favorPositionId", value = "资源位表ID", required = true, paramType = "path")
    @RequestMapping(value = "/queryYPositionLogList/{favorPositionId}", method = RequestMethod.GET)
    public List<XdaFavorPositionLogODTO> queryYPositionLogList(@PathVariable("favorPositionId")Long favorPositionId){
        return logService.queryYPositionLogList(favorPositionId);
    }


}
