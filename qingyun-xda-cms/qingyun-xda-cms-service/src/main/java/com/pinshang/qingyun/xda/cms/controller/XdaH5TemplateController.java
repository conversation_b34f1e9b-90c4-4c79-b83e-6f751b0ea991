package com.pinshang.qingyun.xda.cms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.cms.dto.h5.*;
import com.pinshang.qingyun.xda.cms.service.h5.XdaH5TemplateService;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5AddSelectCommoditySearchIDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @description: 鲜达H5模板
 * @author: hhf
 * @time: 2020/12/10 15:26
 */
@RestController
@RequestMapping("/xdaH5Template")
@Api(value = "鲜达H5模板", tags = "XdaH5Template", description = "鲜达H5模板" )
public class XdaH5TemplateController {

    @Autowired
    private XdaH5TemplateService xdaH5TemplateService;

    @ApiOperation(value = "鲜达-H5模板列表", notes = "鲜达-H5模板列表")
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaH5TemplateIDTO")
    @PostMapping(value = "/findXdaH5TemplateListByParams")
    public PageInfo<XdaH5TemplateODTO> findXdaH5TemplateListByParams(@RequestBody XdaH5TemplateIDTO idto){
        return xdaH5TemplateService.findXdaH5TemplateListByParams(idto);
    }

    @ApiOperation(value = "鲜达-H5模板日志列表", notes = "鲜达-H5模板日志列表")
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaH5TemplateLogIDTO")
    @PostMapping(value = "/findXdaH5TemplateLogListByParams")
    public PageInfo<XdaH5TemplateLogODTO> findXdaH5TemplateLogListByParams(@RequestBody XdaH5TemplateLogIDTO idto){
        return xdaH5TemplateService.findXdaH5TemplateLogListByParams(idto);
    }

    @ApiOperation(value = "鲜达-H5模板列表 导出", notes = "鲜达-H5模板列表 导出")
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaH5TemplateIDTO")
    @GetMapping(value = "/exportXdaH5TemplateListByParams")
    public void exportXdaH5TemplateListByParams(XdaH5TemplateIDTO idto, HttpServletResponse response, HttpServletRequest request){
        xdaH5TemplateService.exportXdaH5TemplateListByParams(idto, response, request);
    }

    @ApiOperation(value = "鲜达-H5模板日志列表导出", notes = "鲜达-H5模板日志列表--导出")
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaH5TemplateLogIDTO")
    @GetMapping(value = "/exportXdaH5TemplateLogListByParams")
    public void exportXdaH5TemplateLogListByParams(XdaH5TemplateLogIDTO idto, HttpServletResponse response, HttpServletRequest request) {
        xdaH5TemplateService.exportXdaH5TemplateLogListByParams(idto, response, request);
    }

    @ApiOperation(value = "鲜达-选择H5模板-查询所有模板列表", notes = "鲜达-选择H5模板-查询所有模板列表")
    @PostMapping(value = "/findXdaH5TemplateStyleCodeList")
    public List<XdaH5TemplateStyleCodeODTO> findXdaH5TemplateStyleCodeList(){
        return xdaH5TemplateService.findXdaH5TemplateStyleCodeList();
    }

    @ApiOperation(value = "鲜达-h5模板状态修改-0停用/1启用", notes = "鲜达-h5模板状态修改-0停用/1启用")
    @ApiImplicitParam(name = "editIDTO", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaH5TemplateStatusEditIDTO")
    @PostMapping(value = "/updateXdaH5TemplateStatus")
    public Long updateXdaH5TemplateStatus(@RequestBody XdaH5TemplateStatusEditIDTO editIDTO){
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        editIDTO.setUserId(userId);
        return xdaH5TemplateService.updateXdaH5TemplateStatus(editIDTO);
    }

    @ApiOperation(value = "鲜达-保存H5大模板", notes = "鲜达-保存H5大模板")
    @ApiImplicitParam(name = "saveIDTO", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaH5TemplateSaveIDTO")
    @PostMapping(value = "/saveXdaH5Template")
    public Long saveXdaH5Template(@RequestBody XdaH5TemplateSaveIDTO saveIDTO){
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        saveIDTO.setUserId(userId);
        return xdaH5TemplateService.saveXdaH5Template(saveIDTO);
    }

    @ApiOperation(value = "鲜达-保存自定义H5", notes = "鲜达-保存自定义H5")
    @ApiImplicitParam(name = "saveIDTO", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaH5CustomTemplateSaveIDTO")
    @PostMapping(value = "/saveXdaH5CustomTemplate")
    public Long saveXdaH5CustomTemplate(@RequestBody XdaH5CustomTemplateSaveIDTO saveIDTO){
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        saveIDTO.setUserId(userId);
        return xdaH5TemplateService.saveXdaH5CustomTemplate(saveIDTO);
    }

    @ApiOperation(value = "鲜达-修改自定义H5", notes = "鲜达-修改自定义H5")
    @ApiImplicitParam(name = "editIDTO", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaH5CustomTemplateEditIDTO")
    @PostMapping(value = "/editXdaH5CustomTemplate")
    public Long editXdaH5CustomTemplate(@RequestBody XdaH5CustomTemplateEditIDTO editIDTO){
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        editIDTO.setUserId(userId);
        return xdaH5TemplateService.editXdaH5CustomTemplate(editIDTO);
    }

    @ApiOperation(value = "鲜达H5-详情", notes = "鲜达H5-详情")
    @RequestMapping(value = "/viewXdaH5TemplateDetails/{id}",method = RequestMethod.GET)
    public XdaH5TemplateDetailsODTO viewXdaH5TemplateDetails(@PathVariable("id") Long id){
        return xdaH5TemplateService.viewXdaH5TemplateDetails(id);
    }

    @ApiOperation(value = "鲜达H5-查看详情页面-根据tabId查询tab下商品的明细列表", notes = "鲜达H5-查看详情页面-根据tabId查询tab下商品的明细列表")
    @GetMapping(value = "/findXdaH5TemplateTabCommodityListByTabId/{tabId}")
    public List<XdaH5TemplateTabCommodityODTO> findXdaH5TemplateTabCommodityListByTabId(@PathVariable("tabId") Long tabId){
        return xdaH5TemplateService.findXdaH5TemplateTabCommodityListByTabId(tabId);
    }

    @ApiOperation(value = "鲜达H5-选择商品,条件查询商品列表 ", notes = "鲜达H5-选择商品,条件查询商品列表")
    @ApiImplicitParam(name = "searchIDTO", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaH5AddSelectCommoditySearchIDTO")
    @PostMapping(value = "/addXdaH5SelectCommodityByParams")
    public PageInfo<XdaH5AddSelectCommodityODTO> addXdaH5SelectCommodityByParams(@RequestBody XdaH5AddSelectCommoditySearchIDTO searchIDTO){
        return xdaH5TemplateService.addXdaH5SelectCommodityByParams(searchIDTO);
    }

    @ApiOperation(value = "鲜达H5-添加H5-根据商品编码集合查询商品信息 ", notes = "鲜达H5-添加H5-根据商品编码集合查询商品信息")
    @ApiImplicitParam(name = "commodityCodeList", value = "商品编码集合", required = true, paramType = "query")
    @PostMapping("/addXdaH5QueryCommodityDetailsByCommodityCodeList")
    public List<XdaH5AddSelectCommodityODTO> addXdaH5QueryCommodityDetailsByCommodityCodeList(@RequestParam(value = "commodityCodeList",required = false)List<String> commodityCodeList){
        return xdaH5TemplateService.addXdaH5QueryCommodityDetailsByCommodityCodeList(commodityCodeList);
    }

    /**
     * 由于批发H5和鲜达公用，所以获取商品接口在当前项目中研发
     * @param commodityCodeList
     * @return
     */
    @ApiOperation(value = "批发H5-添加H5-根据商品编码集合查询商品信息 ", notes = "批发H5-添加H5-根据商品编码集合查询商品信息")
    @ApiImplicitParam(name = "commodityCodeList", value = "商品编码集合", required = true, paramType = "query")
    @PostMapping("/addPfH5QueryCommodityDetailsByCommodityCodeList")
    public List<XdaH5AddSelectCommodityODTO> addPfH5QueryCommodityDetailsByCommodityCodeList(@RequestParam(value = "commodityCodeList",required = false)List<String> commodityCodeList){
        return xdaH5TemplateService.addPfH5QueryCommodityDetailsByCommodityCodeList(commodityCodeList);
    }
    /**
     * 批量添加商品确定时判断:根据商品编码判断商品是否存
     * @param commodityCodeList
     * @return
     *
     * 目前取商品取总的商品池 不区分是否前置仓
     */
    @ApiOperation(value = "鲜达H5-添加H5-批量添加商品确定时判断:根据商品编码判断商品是否存 ", notes = "鲜达H5-添加H5-批量添加商品确定时判断:根据商品编码判断商品是否存")
    @ApiImplicitParam(name = "commodityCodeList", value = "商品编码集合", required = true, paramType = "query")
    @PostMapping(value = "/addXdaH5JudgeCommodityExistsByCommodityCodeList")
    public boolean addXdaH5JudgeCommodityExistsByCommodityCodeList(@RequestParam(value = "commodityCodeList",required = false)List<String> commodityCodeList){
        xdaH5TemplateService.addXdaH5JudgeCommodityExistsByCommodityCodeList(commodityCodeList);
        return true;
    }
}
