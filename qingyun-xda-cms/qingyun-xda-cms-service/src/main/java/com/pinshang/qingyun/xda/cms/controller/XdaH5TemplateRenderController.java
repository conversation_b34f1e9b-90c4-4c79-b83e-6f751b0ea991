package com.pinshang.qingyun.xda.cms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.xd.H5TemplateCodeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.xda.cms.dto.h5.*;
import io.swagger.annotations.Api;
import com.pinshang.qingyun.xda.cms.entry.h5.*;
import com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5TemplateStyleCodeMapper;
import com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5TemplateTabCommodityMapper;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5Pic;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5TemplateStyleCode;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5TemplateTabCommodity;
import com.pinshang.qingyun.xda.cms.service.h5.XdaH5TemplateRenderService;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletResponse;
import java.io.StringWriter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/10 14:25:26
 */
@RestController
@RequestMapping("/xdaH5Render")
@Api(value = "鲜打H5模板页面渲染接口列表", tags = "XdaH5TemplateRenderController", description = "鲜打H5模板页面渲染接口列表" )
public class XdaH5TemplateRenderController {
    private Logger logger = LoggerFactory.getLogger(getClass());
    @Autowired
    private XdaH5TemplateRenderService xdH5TemplateService;
    @Autowired
    private XdaH5TemplateStyleCodeMapper xdH5TemplateStyleCodeMapper;
    @Autowired
    private XdaH5TemplateTabCommodityMapper xdH5TemplateTabCommodityMapper;
    @Autowired
    private XdaH5TemplateRenderService xdaH5TemplateRenderService;




    /**
     *模板后台页面渲染展示接口
     * @param templateId 模板id
     * @param storeId 客户id
     * @param map
     * @return
     */
    @RequestMapping(value = "/renderXdH5", method = RequestMethod.GET)
    @ApiOperation(value = "模板后台页面渲染展示接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "templateId", value = "模板id", required = true, paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "storeId", value = "客户id", required = true, paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "isPreview", value ="小模板后台系统预览时，不管模板是否停用都要展示页面,传值非空就行", paramType = "query", dataType = "String")
    })
    public ModelAndView renderXdH5(@RequestParam(value = "templateId",required = false)Long templateId, @RequestParam(value = "storeId",required = false)Long storeId,@RequestParam(value = "isPreview",required = false) String isPreview,
                                   @RequestParam(value = "orderTime",required = false) String orderTime,ModelMap map){

        String h5Html = xdH5TemplateService.generateXdH5(templateId,storeId,isPreview,orderTime);
        map.put("h5Html",h5Html);
        return new ModelAndView("h5",map);
    }

    /***
     * 小程序页面渲染展示接口
     * @param templateId 模板id
     * @param storeId 客户id
     * @param map
     * @return
     */
    @ApiOperation(value = "小程序页面渲染展示接口")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "templateId", value = "模板id", required = true, paramType = "query", dataType = "Long"),
            @ApiImplicitParam(name = "storeId", value = "客户id", required = true, paramType = "query", dataType = "Long")
    })
    @RequestMapping(value = "/loadXdH5Data", method = RequestMethod.GET)
    public XdaH5TemplateDataEntry loadXdH5Data(@RequestParam(value = "templateId",required = false)Long templateId, @RequestParam(value = "storeId",required = false)Long storeId,
                                               @RequestParam(value = "orderTime",required = false) String orderTime, ModelMap map){
        XdaH5TemplateDataEntry dataEntry = xdH5TemplateService.loadXdH5Data(templateId,storeId,orderTime);
        return dataEntry;
    }

    /**
     * 新建大模板时预览接口
     * @param
     * @return
     */
    @ApiOperation(value = "新建大模板时预览接口")
    @ApiImplicitParam(name = "json", value = "新建大模板的json串", required = true, paramType = "query", dataType = "String")
    @RequestMapping(value = "/previewH5", method = RequestMethod.GET)
    public ModelAndView previewH5(@RequestParam(value = "json",required = false)String json, HttpServletResponse response){
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG, QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        XdaH5TemplateSaveIDTO vo = JsonUtil.json2java(json, XdaH5TemplateSaveIDTO.class);
        String content = "";
        String pageHtml= "";
        xdaH5TemplateRenderService.checkVo(vo,true);

        logger.error("----previewH5---------H5TemplateSaveVo="+vo);
        if(null != vo){
            if(null != vo.getTemplateCodeId()){
                //2.通过模板id 获取 模板样式
                XdaH5TemplateStyleCode h5TemplateStyleCode = xdH5TemplateStyleCodeMapper.selectByPrimaryKey(vo.getTemplateCodeId());
                content = h5TemplateStyleCode.getTemplateContent();

                //以下渲染模板
                VelocityEngine velocityEngine = new VelocityEngine();
                // 取得velocity的上下文context
                VelocityContext context = new VelocityContext();

                //根据不同的模板样式，获取不同的模板内容项数据
                if( h5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE1.getCode())){
                    //解析H5模板1
                    initH5Mode_1(vo, context);
                }else if( h5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE2.getCode())){
                    //解析H5模板1
                    initH5Mode_2(vo, context);
                }else if( h5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE3.getCode())){
                    //解析H5模板1
                    initH5Mode_3(vo,context);
                }else if( h5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE4.getCode())){
                    //解析H5模板1
                    initH5Mode_4(vo, context);
                }

                StringWriter writer = new StringWriter();
                velocityEngine.evaluate(context, writer, "", content);
                pageHtml = writer.toString();
            }
        }

        ModelMap map = new ModelMap();
        map.put("h5Html",pageHtml);
        return new ModelAndView("h5",map);
    }

    //预览加载  初始化H5模板1
    private void initH5Mode_1(XdaH5TemplateSaveIDTO vo, VelocityContext context) {
        //1.取H5模板1 头图
        List<XdaH5TemplateListODTO> h5PicList = getHeadH5PicList(vo);
        h5PicList.forEach(h5Pic ->{
            if(StringUtils.isNotBlank(h5Pic.getPicUrl()))
                h5Pic.setPicUrl(xdaH5TemplateRenderService.getNonCommodityimgServerUrl() + h5Pic.getPicUrl());//组装头图显示地址
        });
        context.put("h5PicList", h5PicList);
        //==========================================================================================================================================
        //2.取H5模板1 图片+商品
        List<XdaH5TemplateListODTO> h5PicCommodityList  = getH5PicList(vo);
        h5PicCommodityList.forEach(h5PicCommodity ->{
            if(StringUtils.isNotBlank(h5PicCommodity.getPicUrl()))
                h5PicCommodity.setPicUrl(xdaH5TemplateRenderService.getNonCommodityimgServerUrl() + h5PicCommodity.getPicUrl());//组装图版显示地址
            h5PicCommodity.setCommodityName(h5PicCommodity.getCommodityName());
        });
        context.put("h5PicCommodityList", h5PicCommodityList);
        //==========================================================================================================================================
        //3.取H5模板1  tab列表+tab明细
        List<XdaH5TemplateTabODTO> h5TemplateTabList  = getH5TemploateTabList(vo);
        if(null != h5TemplateTabList && h5TemplateTabList.size() > 0 ){
            h5TemplateTabList.forEach(tab->{
                List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList = getH5TemplateTabCommodityList(tab.getCommodityCodeList(),vo.getShopId(),tab.getAppShowNum());
                //处理商品公斤转市斤
                //this.gongjinTOshijin(h5TemplateTabCommodityList);
                tab.setH5TemplateTabCommodityEntryList(h5TemplateTabCommodityList);
                tab.setCommodityCount(h5TemplateTabCommodityList.size());
            });
        }
        context.put("h5TemplateTabList", h5TemplateTabList);
    }

    //预览加载  初始化H5模板2
    private void initH5Mode_2(XdaH5TemplateSaveIDTO vo, VelocityContext context) {
        //1.取H5模板2 头图
        List<XdaH5TemplateListODTO> h5PicList = getHeadH5PicList(vo);
        h5PicList.forEach(h5Pic ->{
            if(StringUtils.isNotBlank(h5Pic.getPicUrl()))
                h5Pic.setPicUrl(xdaH5TemplateRenderService.getNonCommodityimgServerUrl()+h5Pic.getPicUrl());//组装头图显示地址
        });
        context.put("h5PicList", h5PicList);
        //==========================================================================================================================================
        //2.取H5模板2 通栏区数据
        //2-1图片
        XdaH5PicODTO h5Pic = new XdaH5PicODTO();
        h5Pic.setPicUrl(xdaH5TemplateRenderService.getNonCommodityimgServerUrl()+vo.getH5PicBanner().getPicUrl());
        context.put("h5Pic", h5Pic);
        //==========================================================================================================================================
        //2-2图片连接H5
        XdaH5PicH5ODTO h5PicH5 = new XdaH5PicH5ODTO();
        h5PicH5.setPicUrl(xdaH5TemplateRenderService.getNonCommodityimgServerUrl() + vo.getH5PicH5().getPicUrl());
        context.put("h5PicH5", h5PicH5);
        //==========================================================================================================================================
        List<Long> templateCommodityIdList = new ArrayList<Long>();
        vo.getH5TemplateTabCommodityList().forEach(h5TemplateTabCommodity -> {
            templateCommodityIdList.add(h5TemplateTabCommodity.getCommodityId());
        });

        //区配前置仓门店下存在的商品信息
        List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityItem = getH5TemplateTabCommodityListByIds(templateCommodityIdList,vo.getShopId());

        //处理商品公斤转市斤
        //this.gongjinTOshijin(h5TemplateTabCommodityItem);
        context.put("h5TemplateTabCommodityItem", h5TemplateTabCommodityItem);

        //==========================================================================================================================================
        //3.取H5模板1  tab列表+tab明细
        List<XdaH5TemplateTabODTO> h5TemplateTabList = getH5TemploateTabList(vo);
        if(null != h5TemplateTabList && h5TemplateTabList.size() > 0 ){
            h5TemplateTabList.forEach(tab->{
                List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList = getH5TemplateTabCommodityList(tab.getCommodityCodeList(),vo.getShopId(),tab.getAppShowNum());
                //处理商品公斤转市斤
                //this.gongjinTOshijin(h5TemplateTabCommodityList);
                tab.setH5TemplateTabCommodityEntryList(h5TemplateTabCommodityList);
                tab.setCommodityCount(h5TemplateTabCommodityList.size());
            });
        }
        context.put("h5TemplateTabList", h5TemplateTabList);

    }

    //预览加载  初始化H5模板3
    private void initH5Mode_3(XdaH5TemplateSaveIDTO vo, VelocityContext context) {
        //1.取H5模板3 头图
        List<XdaH5TemplateListODTO> h5PicList = getHeadH5PicList(vo);
        h5PicList.forEach(h5Pic ->{
            if(StringUtils.isNotBlank(h5Pic.getPicUrl()))
                h5Pic.setPicUrl(xdaH5TemplateRenderService.getNonCommodityimgServerUrl()+h5Pic.getPicUrl());//组装头图显示地址
        });
        context.put("h5PicList", h5PicList);
        //==========================================================================================================================================
        //2.取H5模板3 图片+商品
        List<XdaH5TemplateListODTO> h5PicCommodityList = getH5PicCommodityList(vo);
        context.put("h5PicCommodityList", h5PicCommodityList);
        //==========================================================================================================================================
        //3.取H5模板3  tab列表+tab明细
        List<XdaH5TemplateTabODTO> h5TemplateTabList  = getH5TemploateTabList(vo);
        if(null != h5TemplateTabList && h5TemplateTabList.size() > 0 ){
            h5TemplateTabList.forEach(tab->{
                List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList = getH5TemplateTabCommodityList(tab.getCommodityCodeList(),vo.getShopId(),tab.getAppShowNum());
                //处理商品公斤转市斤
                //this.gongjinTOshijin(h5TemplateTabCommodityList);
                tab.setH5TemplateTabCommodityEntryList(h5TemplateTabCommodityList);
                tab.setCommodityCount(h5TemplateTabCommodityList.size());
            });
        }
        context.put("h5TemplateTabList", h5TemplateTabList);

    }

    //预览加载  初始化H5模板4
    private void initH5Mode_4(XdaH5TemplateSaveIDTO vo, VelocityContext context) {
        //1.取H5模板4 头图
        List<XdaH5TemplateListODTO> h5PicList = getHeadH5PicList(vo);
        h5PicList.forEach(h5Pic ->{
            if(StringUtils.isNotBlank(h5Pic.getPicUrl()))
                h5Pic.setPicUrl(xdaH5TemplateRenderService.getNonCommodityimgServerUrl()+h5Pic.getPicUrl());//组装头图显示地址
        });
        context.put("h5PicList", h5PicList);
        //==========================================================================================================================================
        //2.取H5模板4  tab列表+tab明细
        List<XdaH5TemplateTabODTO> h5TemplateTabList  = new ArrayList<XdaH5TemplateTabODTO>();
        XdaH5TemplateTabODTO xdH5TemplateTabEntry = new XdaH5TemplateTabODTO();//构造一空Tab
        xdH5TemplateTabEntry.setTabName("");
        h5TemplateTabList.add(xdH5TemplateTabEntry);
        if(null != h5TemplateTabList && h5TemplateTabList.size() > 0 ){
                List<XdaH5TemplateTabCommodity> h5TemplateTabCommodityList0 = vo.getH5TemplateTabCommodityList();
                List<Long> commodityIdList=h5TemplateTabCommodityList0.stream().map(XdaH5TemplateTabCommodity::getCommodityId).collect(Collectors.toList());
                List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList = xdH5TemplateTabCommodityMapper.findCommodityIdByCommodityIdList(commodityIdList);
                h5TemplateTabCommodityList =xdH5TemplateService.setProcessStock(h5TemplateTabCommodityList,vo.getShopId(),"commodityId","defaultPicUrl","commodityPrice",null);
                //处理商品公斤转市斤
               // this.gongjinTOshijin(h5TemplateTabCommodityList);
                xdH5TemplateTabEntry.setH5TemplateTabCommodityEntryList(h5TemplateTabCommodityList);
                xdH5TemplateTabEntry.setCommodityCount(h5TemplateTabCommodityList.size());
        }
        context.put("h5TemplateTabList", h5TemplateTabList);
    }

    private List<XdaH5TemplateListODTO> getH5PicCommodityList(XdaH5TemplateSaveIDTO vo){
        List<XdaH5TemplateListODTO> h5PicCommodityList = new ArrayList<XdaH5TemplateListODTO>();
        vo.getH5PicCommodityList().forEach(h5PicCommodity -> {
            XdaH5TemplateListODTO h5TemplateListEntry= new XdaH5TemplateListODTO();
            h5TemplateListEntry.setPicUrl(xdaH5TemplateRenderService.getNonCommodityimgServerUrl() + h5PicCommodity.getPicUrl());
            h5PicCommodityList.add(h5TemplateListEntry);
        });
        return  h5PicCommodityList;
    }

    //预览-初始加载TabCommodityList
    private List<XdaH5TemplateTabCommodityODTO> getH5TemplateTabCommodityList(List<String> commodityCodeList, Long storeId, Integer appShowNum){
        /***
         * 根据商品code
         * 查询商品的文描及商品信息
         */
        List<XdaH5TemplateTabCommodityODTO>  xdH5TemplateTabCommodityList = xdH5TemplateTabCommodityMapper.findCommodityListByCommodityCodeAndShopId(commodityCodeList,null);
        xdH5TemplateTabCommodityList=xdH5TemplateService.setProcessStock(xdH5TemplateTabCommodityList,storeId,"commodityIdLong","defaultPicUrl","commodityPrice",null);

        List<XdaH5TemplateTabCommodityODTO>  tabCommodityEntrys = new ArrayList<XdaH5TemplateTabCommodityODTO>();
        if(null != xdH5TemplateTabCommodityList && xdH5TemplateTabCommodityList.size() > 0 ){
            //实现Comparator进行库存排序
           /* Collections.sort(xdH5TemplateTabCommodityList,new Comparator<Object>() {
                @Override
                public int compare(Object o1, Object o2) {
                    return ((XdaH5TemplateTabCommodityODTO) o2).getIsStock() - ((XdaH5TemplateTabCommodityODTO) o1).getIsStock();
                }
            });*/


            int size=xdH5TemplateTabCommodityList.size();
            if(size<appShowNum){
                appShowNum=size;
            }
            for(int i=0; i < appShowNum; i++ ){
                tabCommodityEntrys.add(xdH5TemplateTabCommodityList.get(i));
            }
        }

        return tabCommodityEntrys;
    }

    //取前置仓门店下 商品数量
    private List<XdaH5TemplateTabCommodityODTO> getH5TemplateTabCommodityListByIds(List<Long> commodityIdList, Long storeId){
        List<XdaH5TemplateTabCommodityODTO>  xdH5TemplateTabCommodityList = xdH5TemplateTabCommodityMapper.findCommodityIdByCommodityIdList(commodityIdList);
        xdH5TemplateTabCommodityList =xdH5TemplateService.setProcessStock(xdH5TemplateTabCommodityList,storeId,"commodityId","defaultPicUrl","commodityPrice",null);
        return xdH5TemplateTabCommodityList;
    }

    //预览-初始加载Tab
    private List<XdaH5TemplateTabODTO> getH5TemploateTabList(XdaH5TemplateSaveIDTO vo){
        List<XdaH5TemplateTabODTO> h5TemplateTabList = new ArrayList<XdaH5TemplateTabODTO>();
        vo.getH5TemplateTabList().forEach(h5TemplateTabVo -> {
            XdaH5TemplateTabODTO h5TemplateTabEntry = new XdaH5TemplateTabODTO();
            h5TemplateTabEntry.setTabName(h5TemplateTabVo.getTabName());
            h5TemplateTabEntry.setPicUrl(h5TemplateTabVo.getPicUrl());
            h5TemplateTabEntry.setAppShowNum(h5TemplateTabVo.getAppShowNum());
            h5TemplateTabEntry.setCommodityCodeList(h5TemplateTabVo.getCommodityCodeList());
            h5TemplateTabList.add(h5TemplateTabEntry);
        });
        return h5TemplateTabList;
    }

    //预览-初始加载图片
    private List<XdaH5TemplateListODTO> getHeadH5PicList(XdaH5TemplateSaveIDTO vo){
        List<XdaH5TemplateListODTO> h5PicList = new ArrayList<XdaH5TemplateListODTO>();
        XdaH5Pic xdH5Pic = vo.getH5PicHead();
        XdaH5TemplateListODTO h5TemplateListEntry = new XdaH5TemplateListODTO();
        h5TemplateListEntry.setPicUrl(xdH5Pic.getPicUrl());
        h5PicList.add(h5TemplateListEntry);
        return h5PicList;
    }

    //预览-初始加载图片
    private List<XdaH5TemplateListODTO> getH5PicList(XdaH5TemplateSaveIDTO vo){
        List<XdaH5TemplateListODTO> h5PicList = new ArrayList<XdaH5TemplateListODTO>();
        vo.getH5PicCommodityList().forEach(h5PicCommodity -> {
            XdaH5TemplateListODTO h5TemplateListEntry = new XdaH5TemplateListODTO();
            h5TemplateListEntry.setCommodityId(h5PicCommodity.getCommodityId());
            h5TemplateListEntry.setPicUrl(h5PicCommodity.getPicUrl());
            h5PicList.add(h5TemplateListEntry);
        });
        return h5PicList;
    }

    /**
     * 商品公斤 转 市斤  显示
     * @param h5TemplateTabCommodityList
     * @return
     */
    /*public  List<XdaH5TemplateTabCommodityODTO> gongjinTOshijin(List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList){
        if(null != h5TemplateTabCommodityList && h5TemplateTabCommodityList.size() > 0 ) {
            h5TemplateTabCommodityList.forEach(commodity -> {
                //处理称重商品
                if(null != commodity.getIsWeight() &&  commodity.getIsWeight() == 1) {
                    commodity.setUnitName("份");
                    BigDecimal commodityPrice = commodity.getCommodityPrice();//.multiply(new BigDecimal(commodity.getCommodityPackageSpec())).setScale(2,BigDecimal.ROUND_HALF_UP);
                    commodity.setCommodityPrice(commodityPrice);
                }
            });
        }
        return h5TemplateTabCommodityList;
    }*/

    /***
     * 检验模板信息
     * @param vo
     * @return
     */
   @PostMapping(value = "/checkXdH5Template")
    public int checkXdH5Template(@RequestBody XdaH5TemplateSaveIDTO vo){
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        vo.setUserId(userId);
       xdaH5TemplateRenderService.checkVo(vo,false);
       return 1;
    }

    /***
     * 模板后台预览时选择客户列表接口
     * 输入编码和客户名称
     * @return
     */
    @ApiOperation(value = "模板后台预览时选择客户列表接口:输入编码和客户名称")
    @ApiImplicitParam(name = "idto", value = "idto", required = true, paramType = "body", dataType = "XdaStoreDataIDTO")
    @PostMapping(value="/storeByCodeOrName")
    public PageInfo<XdaStoreDataODTO> selectStoreCodeAndName(@RequestBody XdaStoreDataIDTO idto){
        return  xdaH5TemplateRenderService.selectStoreDataByCodeOrName(idto);
    }

}
