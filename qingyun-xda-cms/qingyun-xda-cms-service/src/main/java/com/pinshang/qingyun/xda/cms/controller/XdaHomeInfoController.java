
package com.pinshang.qingyun.xda.cms.controller;


import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.marketing.dto.mtCoupon.MtMyCouponUserIDTO;
import com.pinshang.qingyun.marketing.dto.mtCoupon.MtMyCouponUserODTO;
import com.pinshang.qingyun.marketing.service.mtCoupon.MtCouponTobClient;
import com.pinshang.qingyun.order.service.XDAShoppingCartClient;
import com.pinshang.qingyun.xda.cms.dto.MarkCouponNotifyReadIDTO;
import com.pinshang.qingyun.xda.cms.dto.home.*;
import com.pinshang.qingyun.xda.cms.service.XdaHomeInfoService;
import com.pinshang.qingyun.xda.cms.service.popup.XdaPopupMsgFrontService;
import com.pinshang.qingyun.xda.cms.vo.XdaUserCouponFrontVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.List;

import static com.pinshang.qingyun.base.api.ApiResponse.convert;

/**
 *  鲜达首页
 */
@RestController
@RequestMapping("/homeInfo")
@Api(value = "鲜达首页", tags = "XdHomeInfo", description = "鲜达到首页" )
@Slf4j
public class XdaHomeInfoController {
    @Autowired
    private XdaHomeInfoService xdaHomeInfoService;
    @Autowired
    private XdaPopupMsgFrontService popupMsgFrontService;
    @Autowired
    private XDAShoppingCartClient xdaShoppingCartClient;
    @Autowired
    private MtCouponTobClient mtCouponTobClient;

    @Deprecated
    @ApiOperation(value = "鲜到首页", notes = "鲜到首页", response = XdaHomePageODTO.class)
    @ApiImplicitParam(name = "idto", value = "", required = true, paramType = "body", dataTypeClass = XdaHomePageIDTO.class)
    @RequestMapping(value = "/queryHomeInfo", method = RequestMethod.POST)
    public XdaHomePageODTO queryHomeInfo(@RequestBody XdaHomePageIDTO idto) {
        QYAssert.isTrue(idto!=null&& idto.getOrderTime()!=null,"送货日期不能为空！");
        XdaTokenInfo ti = FastThreadLocalUtil.getXDA();
        HomeBaseIDTO baseIDTO = new HomeBaseIDTO(ti,idto.getOrderTime());
        XdaHomePageODTO dto = xdaHomeInfoService.queryAppHomePositionInfo(baseIDTO);
        if (dto.getItems() == null) {
            log.error("查询客户首页时返回内容为空,客户ID:{}", baseIDTO.getStoreId());
        }
        Integer count = xdaShoppingCartClient.getCategoryNumV4(new SimpleDateFormat("yyyy-MM-dd").format(baseIDTO.getOrderTime()),baseIDTO.getStoreId());
        dto.setShoppingCartCount(count);
        //首页弹框通知
        XdaPopupMsgAppODTO popupMsgAppODTO = popupMsgFrontService.queryXdPopupMsgForApp(baseIDTO.getStoreId(), idto.getOrderTime());
        if (popupMsgAppODTO != null){
            dto.setPopupMsgAppODTO(popupMsgAppODTO);
        }
        return dto;
    }
    @ApiOperation(value = "鲜到首页", notes = "鲜到首页", response = XdaHomePageODTO.class)
    @ApiImplicitParam(name = "idto", value = "", required = true, paramType = "body", dataTypeClass = XdaHomePageIDTO.class)
    @RequestMapping(value = "/queryHomeInfoV4", method = RequestMethod.POST)
    public XdaHomePageODTO queryHomeInfoV4(@RequestBody XdaHomePageIDTO idto) {
        QYAssert.isTrue(idto!=null&& idto.getOrderTime()!=null,"送货日期不能为空！");
        XdaTokenInfo ti = FastThreadLocalUtil.getXDA();
        HomeBaseIDTO baseIDTO = new HomeBaseIDTO(ti,idto.getOrderTime());
        XdaHomePageODTO dto = xdaHomeInfoService.queryAppHomePositionInfoV4(baseIDTO);
        if (dto.getItems() == null) {
            log.error("查询客户首页时返回内容为空,客户ID:{}", baseIDTO.getStoreId());
        }
        Integer count = xdaShoppingCartClient.getCategoryNumV4(new SimpleDateFormat("yyyy-MM-dd").format(baseIDTO.getOrderTime()),baseIDTO.getStoreId());
        dto.setShoppingCartCount(count);
        //首页弹框通知
        XdaPopupMsgAppODTO popupMsgAppODTO = popupMsgFrontService.queryXdPopupMsgForApp(baseIDTO.getStoreId(), idto.getOrderTime());
        if (popupMsgAppODTO != null){
            dto.setPopupMsgAppODTO(popupMsgAppODTO);
        }
        return dto;
    }

    @ApiOperation(value = "鲜到首页(优惠券标记已读)", notes = "鲜到首页(优惠券标记已读)")
    @RequestMapping(value = "/markCouponNotifyRead", method = RequestMethod.POST)
    public Boolean markCouponNotifyRead(@RequestBody List<String> couponUserIdList) {
        mtCouponTobClient.markCouponNotifyRead(couponUserIdList);
        return Boolean.TRUE;
    }

    /**
     * IOS不能直接调用，需要转换下
     * @param idto
     * @return
     */
    @ApiOperation(value = "鲜到首页(优惠券标记已读)", notes = "鲜到首页(优惠券标记已读)")
    @RequestMapping(value = "/markCouponNotifyReadIOS", method = RequestMethod.POST)
    public Boolean markCouponNotifyReadIOS(@RequestBody MarkCouponNotifyReadIDTO idto) {
        mtCouponTobClient.markCouponNotifyRead(idto.getCouponUserIdList());
        return Boolean.TRUE;
    }

    @ApiOperation(value = "获取客户送货时间", notes = "获取客户送货时间")
    @RequestMapping(value = "/selectOrderTimeList", method = RequestMethod.GET)
    public List<OrderTimeODTO> selectOrderTimeList() {
        XdaTokenInfo ti = FastThreadLocalUtil.getXDA();
        return xdaHomeInfoService.selectOrderTimeList(ti.getStoreId());
    }

    @Deprecated
    @ApiOperation(value = "横向位商品数据", notes = "横栏商品数据")
    @ApiImplicitParam(name = "idto", value = "", required = true, paramType = "body", dataTypeClass = XdaPositionInfoCommodityIDTO.class)
    @RequestMapping(value = "/queryFavorXPositionCommodity", method = RequestMethod.POST)
    public ApiResponse<XdaPositionInfoCommodityODTO> queryFavorXPositionCommodity(@RequestBody XdaPositionInfoCommodityIDTO idto, HttpServletResponse response) {
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG, QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        HomeBaseIDTO baseIDTO = new HomeBaseIDTO(FastThreadLocalUtil.getXDA(),idto.getOrderTime());
        idto.setPageSize(40);
        return convert(xdaHomeInfoService.queryFavorXPositionCommodity(idto,baseIDTO));
    }
    @ApiOperation(value = "横向位商品数据")
    @ApiImplicitParam(name = "idto", value = "", required = true, paramType = "body", dataTypeClass = XdaPositionInfoCommodityIDTO.class)
    @RequestMapping(value = "/queryFavorXPositionCommodityV4", method = RequestMethod.POST)
    public ApiResponse<XdaPositionInfoCommodityODTO> queryFavorXPositionCommodityV4(@RequestBody XdaPositionInfoCommodityIDTO idto, HttpServletResponse response) {
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG, QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        HomeBaseIDTO baseIDTO = new HomeBaseIDTO(FastThreadLocalUtil.getXDA(),idto.getOrderTime());
        idto.setPageSize(40);
        return convert(xdaHomeInfoService.queryFavorXPositionCommodityV4(idto,baseIDTO));
    }

    @Deprecated
    @ApiOperation(value = "纵向位商品数据", notes = "纵栏商品数据")
    @ApiImplicitParam(name = "idto", value = "", required = true, paramType = "body", dataTypeClass = XdaPositionInfoCommodityIDTO.class)
    @RequestMapping(value = "/favorYPosition", method = RequestMethod.POST)
    public ApiResponse<XdaHomeFavorYPositionInfoODTO> favorYPosition(@RequestBody XdaPositionInfoCommodityIDTO idto, HttpServletResponse response) {
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG, QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        HomeBaseIDTO baseIDTO = new HomeBaseIDTO(FastThreadLocalUtil.getXDA(),idto.getOrderTime());
        PageInfo<XdaHomeFavorYPositionInfoODTO> pageInfo = xdaHomeInfoService.favorYPosition(idto,baseIDTO);
        return convert(pageInfo);
    }
    @ApiOperation(value = "纵向位商品数据")
    @ApiImplicitParam(name = "idto", value = "", required = true, paramType = "body", dataTypeClass = XdaPositionInfoCommodityIDTO.class)
    @RequestMapping(value = "/favorYPositionV4", method = RequestMethod.POST)
    public ApiResponse<XdaHomeFavorYPositionInfoODTO> favorYPositionV4(@RequestBody XdaPositionInfoCommodityIDTO idto, HttpServletResponse response) {
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG, QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        HomeBaseIDTO baseIDTO = new HomeBaseIDTO(FastThreadLocalUtil.getXDA(),idto.getOrderTime());
        PageInfo<XdaHomeFavorYPositionInfoODTO> pageInfo = xdaHomeInfoService.favorYPositionV4(idto,baseIDTO);
        return convert(pageInfo);
    }


    @ApiOperation(value = "我的优惠券列表")
    @ApiImplicitParam(name = "idto", value = "", required = true, paramType = "body", dataTypeClass = XdaUserCouponFrontVo.class)
    @RequestMapping(value = "/queryMtUserCouponList", method = RequestMethod.POST)
    public ApiResponse<List<MtMyCouponUserODTO>> queryMtUserCouponList(@RequestBody XdaUserCouponFrontVo idto, HttpServletResponse response) {
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG, QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        XdaTokenInfo token = FastThreadLocalUtil.getXDA();
        QYAssert.isTrue(token != null,"登录已过期");

        MtMyCouponUserIDTO mtMyCouponUserIDTO = new MtMyCouponUserIDTO();
        mtMyCouponUserIDTO.setStoreId(token.getStoreId());
        mtMyCouponUserIDTO.setStatus(idto.getStatus());
        mtMyCouponUserIDTO.setType(idto.getType());
        mtMyCouponUserIDTO.setPageNo(idto.getPageNo());
        mtMyCouponUserIDTO.setPageSize(idto.getPageSize());
        PageInfo<MtMyCouponUserODTO> mtMyCouponUserODTOPageInfo = mtCouponTobClient.queryMtUserCouponList(mtMyCouponUserIDTO);
        return convert(mtMyCouponUserODTOPageInfo);
    }
}
