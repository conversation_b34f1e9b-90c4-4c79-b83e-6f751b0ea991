package com.pinshang.qingyun.xda.cms.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.cms.dto.popup.*;
import com.pinshang.qingyun.xda.cms.service.popup.XdaPopupMsgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @description: 鲜达弹框通知
 * @author: hhf
 * @time: 2020/12/9 13:32
 */
@RestController
@RequestMapping("/xdPopupMsg")
@Api(value = "鲜达-弹框通知", tags = "XdaPopupMsg", description = "鲜达：弹框通知" )
public class XdaPopupMsgController {

    @Autowired
    private XdaPopupMsgService xdaPopupMsgService;


    @ApiOperation(value = "鲜达-弹框通知列表", notes = "鲜达-弹框通知列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "xdaPopupMsgIDTO", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaPopupMsgIDTO")
    @PostMapping("/findXdaPopupMsgListByParams")
    public PageInfo<XdaPopupMsgODTO> findXdPopupMsgListByParams(@RequestBody XdaPopupMsgIDTO xdaPopupMsgIDTO){
        return xdaPopupMsgService.findXdaPopupMsgListByParams(xdaPopupMsgIDTO);
    }


    @ApiOperation(value = "鲜达-弹框通知日志列表", notes = "鲜达-弹框通知日志列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name="popupMsgId", value="弹框通知Id", required = true, paramType = "query", dataType ="Long")
    @PostMapping("/findXdaPopupMsgLogListByPopupMsgId")
    public List<XdaPopupMsgLogODTO> findXdaPopupMsgLogListByPopupMsgId(@RequestParam(value = "popupMsgId",required = false)Long popupMsgId){
        return xdaPopupMsgService.findXdaPopupMsgLogListByPopupMsgId(popupMsgId);
    }

    @ApiOperation(value = "鲜达-弹框通知-修改状态:1-启用,0-停用", notes = "鲜达-弹框通知-修改状态:1-启用,0-停用", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "editIDTO", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaPopupMsgStatusEditIDTO")
    @PostMapping("/editXdaPopupMsgStatus")
    public Long editXdaPopupMsgStatus(@RequestBody XdaPopupMsgStatusEditIDTO editIDTO){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        editIDTO.setUserId(tokenInfo.getUserId());
        return xdaPopupMsgService.editXdaPopupMsgStatus(editIDTO);
    }


    @ApiOperation(value = "鲜达-弹框通知-保存", notes = "鲜达-弹框通知-保存", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "saveIDTO", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaPopupMsgSaveIDTO")
    @PostMapping("/saveXdaPopupMsg")
    public Long saveXdaPopupMsg(@RequestBody XdaPopupMsgSaveIDTO saveIDTO){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        saveIDTO.setUserId(tokenInfo.getUserId());
        return xdaPopupMsgService.saveXdaPopupMsg(saveIDTO);
    }


    @ApiOperation(value = "鲜达-弹框通知-修改", notes = "鲜达-弹框通知-修改", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "editIDTO", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaPopupMsgEditIDTO")
    @PostMapping("/editXdaPopupMsg")
    public Long editXdaPopupMsg(@RequestBody XdaPopupMsgEditIDTO editIDTO){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        editIDTO.setUserId(tokenInfo.getUserId());
        return xdaPopupMsgService.editXdaPopupMsg(editIDTO);
    }

    @ApiOperation(value = "鲜达-弹框通知-查看详情", notes = "鲜达-弹框通知-查看详情", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name="popupMsgId", value="弹框通知Id", required = true, paramType = "query", dataType ="Long")
    @PostMapping("/findXdaPopupMsgDetailsById")
    public XdaPopupMsgDetailsODTO findXdaPopupMsgDetailsById(@RequestParam(value = "popupMsgId",required = false)Long popupMsgId){
        return xdaPopupMsgService.findXdaPopupMsgDetailsById(popupMsgId);
    }

    @ApiOperation(value = "鲜达-弹框通知--导出", notes = "鲜达-弹框通知--导出", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @GetMapping(value = "/exportXdaPopupMsgByParams")
    public void exportXdaPopupMsgByParams(XdaPopupMsgIDTO vo, HttpServletResponse response, HttpServletRequest request) {
        xdaPopupMsgService.exportXdaPopupMsgByParams(vo, response, request);
    }
}
