
package com.pinshang.qingyun.xda.cms.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.enums.XSAppPositionIdEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.shop.dto.position.XSAppPositionODTO;
import com.pinshang.qingyun.xda.cms.dto.ForceSubmitResultODTO;
import com.pinshang.qingyun.xda.cms.dto.position.InsertPositionInfoIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionCommodityInfoIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionCommodityInfoODTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionDetailODTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionInfoBlockODTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionInfoLogInfoODTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionInfoODTO;
import com.pinshang.qingyun.xda.cms.dto.position.SelectPositionInfoPageIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.UpdatePositionInfoIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.UpdatePositionInfoStatusIDTO;
import com.pinshang.qingyun.xda.cms.service.position.XdaPositionInfoService;

/**
 * 鲜达资源位
 */
@RestController
@RequestMapping("/xdaPositionInfo")
@Api(value = "鲜达-资源位", tags = "XdaPositionInfoController", description = "鲜达-资源位" )
public class XdaPositionInfoController {
	
    @Autowired
    private XdaPositionInfoService xdPositionInfoService;
    
    @Value("${pinshang.img-server-url}")
    private String imgServerUrl;
    
    @ApiOperation(value = "分页查询  资源位信息  列表", notes = "分页查询  资源位信息  列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "SelectPositionInfoPageIDTO")
    @PostMapping("/selectPositionInfoPage")
	public PageInfo<PositionInfoODTO> selectPositionInfoPage(@RequestBody SelectPositionInfoPageIDTO idto) {
		return xdPositionInfoService.selectPositionInfoPage(idto);
	}
    
    @ApiOperation(value = "查询  资源位日志信息  列表", notes = "查询  资源位日志信息  列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name="positionInfoId", value="资源位信息Id", required = true, paramType = "query", dataType ="Long")
	@PostMapping("/selectPositionInfoLogInfoList")
	public List<PositionInfoLogInfoODTO> selectPositionInfoLogInfoList(@RequestParam(value = "positionInfoId",required = false)Long positionInfoId) {
		return xdPositionInfoService.selectPositionInfoLogInfoList(positionInfoId);
	}
	
    @ApiOperation(value = "查询  资源位详情", notes = "查询  资源位详情", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name="positionInfoId", value="资源位信息Id", required = true, paramType = "query", dataType ="Long")
	@PostMapping("/selectPositionDetail")
	public PositionDetailODTO selectPositionDetail(@RequestParam(value = "positionInfoId",required = false)Long positionInfoId) {
    	PositionDetailODTO detail = xdPositionInfoService.selectPositionDetail(positionInfoId);
    	if (null != detail) {
    		detail.setVisitPicUrl(this.getVisitPicUrl(detail.getPicUrl()));
    		List<PositionInfoBlockODTO> blockList = detail.getBlockList();
    		if (SpringUtil.isNotEmpty(blockList)) {
    			blockList.forEach(block -> {
    				block.setVisitPicUrl(this.getVisitPicUrl(block.getPicUrl()));
    			});
    		}
    	}
    	return detail;
	}
    private String getVisitPicUrl (String picUrl) {
    	String visitPicUrl = null;
    	if (!StringUtil.isNullOrEmpty(picUrl)) {
    		visitPicUrl = imgServerUrl + "/" + picUrl.trim();
		}
    	return visitPicUrl;
    }
	
    @ApiOperation(value = "插入  资源位信息", notes = "插入  资源位信息", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "InsertPositionInfoIDTO")
	@PostMapping("/insertPositionInfo")
	public ForceSubmitResultODTO insertPositionInfo(@RequestBody InsertPositionInfoIDTO idto) {
		TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
		idto.setUserId(tokenInfo.getUserId());
		return xdPositionInfoService.insertPositionInfo(idto);
	}
	
    @ApiOperation(value = "更新  资源位信息", notes = "更新  资源位信息", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "UpdatePositionInfoIDTO")
	@PostMapping("/updatePositionInfo")
	public ForceSubmitResultODTO updatePositionInfo(@RequestBody UpdatePositionInfoIDTO idto) {
    	TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
    	idto.setUserId(tokenInfo.getUserId());
		return xdPositionInfoService.updatePositionInfo(idto);
	}
	
    @ApiOperation(value = "更新  资源位信息状态", notes = "更新  资源位状态", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "UpdatePositionInfoStatusIDTO")
	@PostMapping("/updatePositionInfoStatus")
	public ForceSubmitResultODTO updatePositionInfoStatus(@RequestBody UpdatePositionInfoStatusIDTO idto) {
    	TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
    	idto.setUserId(tokenInfo.getUserId());
		return xdPositionInfoService.updatePositionInfoStatus(idto);
	}
	
//    @ApiOperation(value = "分页查询  资源位门店信息  列表", notes = "分页查询  资源位门店信息  列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    @ApiImplicitParam(name = "vo", value = "请求IDTO", required = true, paramType = "body", dataType = "SimpleShopListByPageVO")
//	@PostMapping("/selectPositionShopInfoPage")
//    public PageInfo<SimpleShopEntry> selectPositionShopInfoPage(@RequestBody SimpleShopListByPageVO vo){
//		return xdPositionInfoService.selectPositionShopInfoPage(vo);
//	}
	
    @ApiOperation(value = "导入查询  资源位商品信息  列表", notes = "导入查询  资源位商品信息  列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "commodityList", value = "请求IDTO", required = true, paramType = "body", dataType = "List<PositionCommodityInfoIDTO>")
	@PostMapping("/importSelectPositionCommodityInfoList")
	public List<PositionCommodityInfoODTO> importSelectPositionCommodityInfoList(@RequestBody List<PositionCommodityInfoIDTO> commodityList) {
		return xdPositionInfoService.importSelectPositionCommodityInfoList(commodityList);
	}
	
    @ApiOperation(value = "查询  资源位  列表", notes = "查询  资源位  列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "positionType", value = "资源位类型", required = true, paramType = "query", dataType ="Integer")
    @PostMapping("/selectPositionList")
    public List<XSAppPositionODTO> selectPositionList(@RequestParam(value = "positionType",required = false)Integer positionType) {
		EnumSet<XSAppPositionIdEnums> positionIdList = null;
		if (XSAppPositionTypeEnums.BANNER.getCode().equals(positionType)) {
			positionIdList = XSAppPositionIdEnums.bannerList();
		} else if (XSAppPositionTypeEnums.ICON.getCode().equals(positionType)) {
			positionIdList = XSAppPositionIdEnums.iconList();
		} else if (XSAppPositionTypeEnums.积木组.getCode().equals(positionType)) {
			positionIdList = XSAppPositionIdEnums.xdJmzList();
		} else if (XSAppPositionTypeEnums.推荐组.getCode().equals(positionType)) {
			positionIdList = XSAppPositionIdEnums.tjzList();
		} else if (XSAppPositionTypeEnums.通栏.getCode().equals(positionType)) {
			positionIdList = XSAppPositionIdEnums.tlList();
		} else if (XSAppPositionTypeEnums.横栏.getCode().equals(positionType)) {
			positionIdList = XSAppPositionIdEnums.xdHlList();
		} else if (XSAppPositionTypeEnums.猜你喜欢横向位.getCode().equals(positionType)) {
			positionIdList = XSAppPositionIdEnums.xdFavorXList();
		} else if (XSAppPositionTypeEnums.猜你喜欢纵向位.getCode().equals(positionType)) {
			positionIdList = XSAppPositionIdEnums.xdFavorYList();
		} else if (XSAppPositionTypeEnums.头图.getCode().equals(positionType)) {
			positionIdList = XSAppPositionIdEnums.xdSyttList();
		} else if (XSAppPositionTypeEnums.服务说明栏.getCode().equals(positionType)) {
			positionIdList = XSAppPositionIdEnums.xdFwsmlList();
		} else if (XSAppPositionTypeEnums.弹框广告.getCode().equals(positionType)) {
			positionIdList = XSAppPositionIdEnums.xdTkggList();
		}
		
		List<XSAppPositionODTO> reuslt = new ArrayList<>();
		if (null != positionIdList) {
			positionIdList.forEach(o -> {
				reuslt.add(new XSAppPositionODTO(o.getCode() + "", o.getName()));
			});
		}
    	return reuslt;
    }
	
    @ApiOperation(value = "批量过期Job", notes = "批量过期Job", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@PostMapping("/batchExpireJob")
	public void batchExpireJob() {
		xdPositionInfoService.batchExpireJob();
	}


//	@ApiOperation(value = "查询  资源位门店ID  列表", notes = "查询  资源位门店ID  列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//	@ApiImplicitParam(name = "vo", value = "请求IDTO", required = true, paramType = "body", dataType = "SimpleShopListByPageVO")
//	@PostMapping("/selectShopIdListByPositionInfoId")
//	public List<Long> selectShopIdListByPositionInfoId(@RequestBody SimpleShopListByPageVO vo){
//		return xdPositionInfoService.selectShopIdListByPositionInfoId(vo);
//	}
}
