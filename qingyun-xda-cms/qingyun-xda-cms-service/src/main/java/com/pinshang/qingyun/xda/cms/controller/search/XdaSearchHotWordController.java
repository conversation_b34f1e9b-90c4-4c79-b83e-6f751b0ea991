package com.pinshang.qingyun.xda.cms.controller.search;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordLogIDTO;
import com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordLogODTO;
import com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordODTO;
import com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordSaveIDTO;
import com.pinshang.qingyun.xda.cms.service.search.XdaSearchHotWordLogService;
import com.pinshang.qingyun.xda.cms.service.search.XdaSearchHotWordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * 热搜词
 */
@RestController
@Api(value = "热搜词", tags = "hotWord", description = "鲜达热搜词")
@RequestMapping("/hotWord")
public class XdaSearchHotWordController {

    @Autowired
    private XdaSearchHotWordService hotWordService;
    @Autowired
    private XdaSearchHotWordLogService logService;


    @ApiOperation(value = "查询热搜词列表(不分页)")
    @RequestMapping(value = "/queryHotWordList", method = RequestMethod.POST)
    public List<XdaSearchHotWordODTO> queryHotWordList() {
        return hotWordService.queryHotWordList();
    }

    @ApiOperation(value = "新增热搜词")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataType = "XdaSearchHotWordSaveIDTO")
    @RequestMapping(value = "/addHotWord", method = RequestMethod.POST)
    public Integer addHotWord(@RequestBody XdaSearchHotWordSaveIDTO vo) {
        return hotWordService.addHotWord(vo);
    }

    @ApiOperation(value = "修改热搜词")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataType = "XdaSearchHotWordSaveIDTO")
    @RequestMapping(value = "/updateHotWord", method = RequestMethod.POST)
    public Integer updateHotWord(@RequestBody XdaSearchHotWordSaveIDTO vo) {
        return hotWordService.updateHotWord(vo);
    }

    @ApiOperation(value = "删除热搜词")
    @ApiImplicitParam(name = "hotWordId", value = "热搜词ID", required = true, paramType = "path")
    @RequestMapping(value = "/deleteHotWord/{hotWordId}", method = RequestMethod.POST)
    public Integer deleteHotWord(@PathVariable("hotWordId") Long hotWordId) {
        return hotWordService.deleteHotWord(hotWordId);
    }

    @ApiOperation(value = "查询热搜词默认最大序号")
    @RequestMapping(value = "/queryHotWordMaxNum", method = RequestMethod.GET)
    public Integer queryXdSearchHotWordMaxNum() {
        return hotWordService.queryHotWordMaxNum();
    }

    @ApiOperation(value = "分页查询热搜词日志")
    @ApiImplicitParam(name = "vo", value = "", required = true, paramType = "body", dataType = "XdaSearchHotWordLogIDTO")
    @RequestMapping(value = "/queryHotWordLogPage", method = RequestMethod.POST)
    public PageInfo<XdaSearchHotWordLogODTO> queryHotWordLogPage(@RequestBody XdaSearchHotWordLogIDTO vo) {
        return logService.queryHotWordLogPage(vo);
    }

}
