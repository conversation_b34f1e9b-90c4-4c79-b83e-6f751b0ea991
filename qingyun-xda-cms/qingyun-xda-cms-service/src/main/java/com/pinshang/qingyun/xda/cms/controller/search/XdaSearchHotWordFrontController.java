package com.pinshang.qingyun.xda.cms.controller.search;

import com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordAppODTO;
import com.pinshang.qingyun.xda.cms.service.search.XdaSearchHotWordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * 热搜词
 */
@RestController
@Api(value = "热搜词APP", tags = "hotWordFront", description = "鲜达APP热搜词")
@RequestMapping("/hotWordFront")
public class XdaSearchHotWordFrontController {

    @Autowired
    private XdaSearchHotWordService hotWordService;

    @ApiOperation(value = "APP查询热搜词列表(不分页)")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public List<XdaSearchHotWordAppODTO> queryHotWordList() {
        return hotWordService.queryHotWordListFront();
    }


}
