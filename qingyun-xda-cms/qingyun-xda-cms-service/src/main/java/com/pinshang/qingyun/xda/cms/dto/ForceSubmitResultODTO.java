package com.pinshang.qingyun.xda.cms.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 特别结果处理，用于通知前端是否需要强制提交
 *
 * <AUTHOR>
 *
 * @date 2017年12月21日
 */
@Data
@AllArgsConstructor
public class ForceSubmitResultODTO {
	@ApiModelProperty(value = "不强制提交标志位", hidden = true)
	public static final Integer FORCE_STATUS_NO = 0;
	@ApiModelProperty(value = "强制提交标志位", hidden = true)
	public static final Integer FORCE_STATUS_YES = 1;
	
	@ApiModelProperty(position = 11, required = true, value = "反馈代码-是否需要强制提交代码：1-是")
	private String code;
	@ApiModelProperty(position = 12, required = true, value = "反馈信息")
	private String message;
	
	@ApiModelProperty(value = "中间结果-长整型", hidden = true)
	private Long longValue;
	@ApiModelProperty(value = "中间结果-整型", hidden = true)
	private Integer intValue;
	@ApiModelProperty(value = "中间结果-对象型", hidden = true)
	private Object objectValue;
}
