package com.pinshang.qingyun.xda.cms.dto.checkReport;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "检验报告列表")
public class ReportListDTO implements Serializable {
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern="yyyy-MM-dd",timezone="GMT+8")
    @ApiModelProperty(value = "报告日期")
    private Date reportTime;
    @ApiModelProperty(value = "报告类型")
    private String dictName;
    @ApiModelProperty(value = "检验报告名称")
    private String reportName;
    @ApiModelProperty(value = "检验人")
    private String checkUser;
    @ApiModelProperty(value = "文件大小")
    private BigDecimal fileSize;
    @ApiModelProperty(value = "复核人")
    private String reviewUser;
    @ApiModelProperty(value = "文件地址")
    private String fileUrl;
    @ApiModelProperty(value = "操作人")
    private String createUser;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "操作时间")
    private Date createTime;

    public ReportListDTO() {
    }

    public ReportListDTO(Long id, Date reportTime, String dictName, String reportName, String checkUser, String reviewUser, String fileUrl, String createUser, Date createTime) {
        this.id = id;
        this.reportTime = reportTime;
        this.dictName = dictName;
        this.reportName = reportName;
        this.checkUser = checkUser;
        this.reviewUser = reviewUser;
        this.fileUrl = fileUrl;
        this.createUser = createUser;
        this.createTime = createTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getReportTime() {
        return reportTime;
    }

    public void setReportTime(Date reportTime) {
        this.reportTime = reportTime;
    }

    public String getDictName() {
        return dictName;
    }

    public void setDictName(String dictName) {
        this.dictName = dictName;
    }

    public String getReportName() {
        return reportName;
    }

    public void setReportName(String reportName) {
        this.reportName = reportName;
    }

    public String getCheckUser() {
        return checkUser;
    }

    public void setCheckUser(String checkUser) {
        this.checkUser = checkUser;
    }

    public String getReviewUser() {
        return reviewUser;
    }

    public void setReviewUser(String reviewUser) {
        this.reviewUser = reviewUser;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public BigDecimal getFileSize() {
        return fileSize;
    }

    public void setFileSize(BigDecimal fileSize) {
        this.fileSize = fileSize;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"id\":")
                .append(id);
        sb.append(",\"reportTime\":\"")
                .append(reportTime).append('\"');
        sb.append(",\"dictName\":\"")
                .append(dictName).append('\"');
        sb.append(",\"reportName\":\"")
                .append(reportName).append('\"');
        sb.append(",\"checkUser\":\"")
                .append(checkUser).append('\"');
        sb.append(",\"reviewUser\":\"")
                .append(reviewUser).append('\"');
        sb.append(",\"fileUrl\":\"")
                .append(fileUrl).append('\"');
        sb.append(",\"createUser\":\"")
                .append(createUser).append('\"');
        sb.append(",\"createTime\":\"")
                .append(createTime).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
