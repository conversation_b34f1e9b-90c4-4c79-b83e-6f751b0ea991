package com.pinshang.qingyun.xda.cms.dto.favorPosition;

import com.pinshang.qingyun.base.enums.IsAllShopTypeEnums;
import com.pinshang.qingyun.base.enums.TermTypeEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionIdEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionInfoStatusEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.xda.cms.dto.storeScope.StoreScopeODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 猜你喜欢资源位绑定信息--基类
 */
@Data
public class XdaFavorPositionBaseInfoODTO {
	@ApiModelProperty("资源位绑定表ID")
	private Long favorPositionId;

	@ApiModelProperty("资源位ID")
	private Integer positionId;

	@ApiModelProperty("资源位名称")
	private String positionName;

	@ApiModelProperty("绑定期限：1-长期，2-短期")
	private Integer termType;

	@ApiModelProperty("绑定期限名称")
	private String termTypeName;

	@ApiModelProperty("生效时间")
	private String termTime;

	@ApiModelProperty("APP渠道：1-是，0-否")
	private Integer appChannel;

	@ApiModelProperty("小程序渠道：1-是，0-否")
	private Integer miniChannel;

	@ApiModelProperty("所有门店：0-全部客户、1-部分客户")
	private Integer isAllStore;

	@ApiModelProperty("门店范围：全部客户/部分客户")
	private String storeScopeDesc;

	@ApiModelProperty("绑定期限开始时间")
	private Date beginTime;

	@ApiModelProperty("绑定期限结束时间")
	private Date endTime;

	@ApiModelProperty("更新时间")
	private Date updateTime;

	@ApiModelProperty("状态：1-启用，2-停用")
	private Integer status;

	@ApiModelProperty("状态名称")
	private String statusName;

	@ApiModelProperty(position = 108, value = "产品价格方案")
	private List<StoreScopeODTO> productPriceModelList;
	@ApiModelProperty(position = 108, value = "结账客户")
	private List<StoreScopeODTO> settlementList;
	@ApiModelProperty(position = 108, value = "客户类型")
	private List<StoreScopeODTO> storeTypeList;
	@ApiModelProperty(position = 108, value = "客户渠道")
	private List<StoreScopeODTO> storeChannelList;
	@ApiModelProperty(position = 108, value = "客户")
	private List<StoreScopeODTO> storeList;

	public String getPositionName() {
		return XSAppPositionIdEnums.getName(this.positionId);
	}
	public String getTermTypeName() {
		return TermTypeEnums.getName(this.termType);
	}
	public String getTermTime() {
		String termTime = "";
		if (TermTypeEnums.长期.getCode() == this.termType.intValue()) {
			termTime = "永久";
		} else if (TermTypeEnums.短期.getCode() == this.termType.intValue() && this.beginTime!=null && this.endTime!=null) {
			termTime = DateUtil.get4yMdHms(this.beginTime) + " ~ " + DateUtil.get4yMdHms(this.endTime);
		}
		return termTime;
	}

	public String getStoreScopeDesc() {
		if (null != this.isAllStore && IsAllShopTypeEnums.ALL_SHOP.getCode().intValue() == this.isAllStore.intValue()) {
			return "全部客户";
		}
		return "部分客户";
	}

	public String getStatusName() {
		String statusName = XSAppPositionInfoStatusEnums.getName(this.status);
		if (TermTypeEnums.短期.getCode() == this.termType.intValue() && XSAppPositionInfoStatusEnums.启用.getCode() == this.status) {
			Date now = new Date(System.currentTimeMillis());
			if (this.endTime!=null && now.after(this.endTime)) {
				statusName = XSAppPositionInfoStatusEnums.已过期.getName();
			}
		}
		return statusName;
	}
	// 这里再次修正数据
	public Integer getStatus() {
		if (TermTypeEnums.短期.getCode() == this.termType.intValue() && XSAppPositionInfoStatusEnums.启用.getCode() == this.status && this.getStatusName().equals(XSAppPositionInfoStatusEnums.已过期.getName())) {
			return XSAppPositionInfoStatusEnums.已过期.getCode();
		}
		return this.status;
	}

}
