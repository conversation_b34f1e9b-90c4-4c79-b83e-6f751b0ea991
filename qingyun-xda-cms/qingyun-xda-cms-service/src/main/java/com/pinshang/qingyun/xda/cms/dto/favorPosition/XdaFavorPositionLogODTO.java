package com.pinshang.qingyun.xda.cms.dto.favorPosition;

import com.pinshang.qingyun.base.enums.XdOperateTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 猜你喜欢横向位日志
 */
@Data
public class XdaFavorPositionLogODTO {

	@ApiModelProperty("操作类型：1-新增、2-修改、3-停用、4-启用、5-改前台序号")
	private Integer operateType;

	@ApiModelProperty("操作类型，页面展示取值")
	private String operateName;

	@ApiModelProperty("操作人")
	private String createName;

	@ApiModelProperty("操作时间")
	private Date createTime;

	public String getOperateName() {
		return XdOperateTypeEnums.getName(this.operateType);
	}

}
