package com.pinshang.qingyun.xda.cms.dto.favorPosition;

import com.pinshang.qingyun.xda.cms.dto.position.StoreScopeIDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 横纵向位保存入参
 */
@Data
public class XdaFavorPositionSaveIDTO {

	@ApiModelProperty(value = "资源位ID",position = 1)
	private Integer positionId;

	@ApiModelProperty(value = "期限类型：1-长期、2-短期",position = 2)
	private Integer termType;

	@ApiModelProperty(value = "短期-生效开始时间",position = 3)
	private String beginTime;

	@ApiModelProperty(value = "短期-生效结束时间",position = 3)
	private String endTime;

	@ApiModelProperty(value = "渠道App：1＝是，0＝否",position = 4,hidden = true)
	private Integer appChannel;

	@ApiModelProperty(value = "渠道小程序：1＝是，0＝否",position = 4,hidden = true)
	private Integer miniChannel;

	@ApiModelProperty(value = "是否所有客户：0=全部客户，1=部分客户",position = 5)
	private Integer isAllStore;

	@ApiModelProperty(value = "客户范围集合，isAllStore=1时，不能为空",position = 5)
	private List<StoreScopeIDTO> storeScopeList;

	@ApiModelProperty(value = "强制提交状态",position = 6)
	private Integer forceStatus;

	@ApiModelProperty(value = "操作人ID",hidden = true)
	private Long userId;

	public Integer getAppChannel() {
		return appChannel==null?1:appChannel;
	}

	public Integer getMiniChannel() {
		return miniChannel==null?0:miniChannel;
	}
}
