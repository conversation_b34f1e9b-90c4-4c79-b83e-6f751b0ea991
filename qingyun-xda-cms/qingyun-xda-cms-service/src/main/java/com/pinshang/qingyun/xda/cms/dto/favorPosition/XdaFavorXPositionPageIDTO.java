package com.pinshang.qingyun.xda.cms.dto.favorPosition;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 首页猜你喜欢资源位绑定分页查询请求参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XdaFavorXPositionPageIDTO extends Pagination {
	@ApiModelProperty("资源位")
	private Integer positionId;

	@ApiModelProperty("绑定期限：1-长期、2-短期")
	private Integer termType;

	@ApiModelProperty("客户ID")
	private Long storeId;

	@ApiModelProperty("绑定标题")
	private String title;

	@ApiModelProperty("状态：1-启用、2-停用、3-过期")
	private Integer status;

	@ApiModelProperty("渠道：1-APP、2-小程序")
	private Integer channel;


	@ApiModelProperty("生效开始时间")
	private String beginTime;

	@ApiModelProperty("生效结束时间")
	private String endTime;

	@ApiModelProperty("创建开始时间")
	private String createBeginTime;

	@ApiModelProperty("创建结束时间")
	private String createEndTime;

	public String getBeginTime() {
		return StringUtil.isNullOrEmpty(beginTime)? null: beginTime + " 00:00:00";
	}

	public String getEndTime() {
		return StringUtil.isNullOrEmpty(endTime)? null: endTime + " 23:59:59";
	}

	public String getCreateBeginTime() {
		return StringUtil.isNullOrEmpty(createBeginTime)? null: createBeginTime + " 00:00:00";
	}

	public String getCreateEndTime() {
		return StringUtil.isNullOrEmpty(createEndTime)? null: createEndTime + " 23:59:59";
	}


}
