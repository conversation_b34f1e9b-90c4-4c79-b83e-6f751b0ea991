package com.pinshang.qingyun.xda.cms.dto.favorPosition;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 猜你喜欢纵向资源位绑定信息
 */
@Data
public class XdaFavorYPositionInfoODTO extends XdaFavorPositionBaseInfoODTO {

	@ApiModelProperty(value = "标的类型：1-前台类目、2-H5页面、4-商品详情",position = 21)
	private Integer targetType;

	@ApiModelProperty(value = "标的ID",position = 22)
	private String targetTypeId;

	@ApiModelProperty(value = "图片地址",position = 23)
	private String picUrl;

    @ApiModelProperty(position = 113, value = "图片地址-用于显示")
    private String visitPicUrl;

    @ApiModelProperty(value = "一级类目",position = 24)
    private Integer firstLevel;

    @ApiModelProperty(value = "二级类目",position = 25)
    private Integer secondLevel;

    @ApiModelProperty(value = "标的地址",position = 26)
    private String targetTypeUrl;

    @ApiModelProperty(value = "绑定的h5名称",position = 27)
    private String h5TemplateName;

    @ApiModelProperty(value = "绑定前台类目名称",position = 28)
    private String targetCateName;

    @ApiModelProperty(value = "绑定商品名称",position = 29)
    private String targetCommodityName;

    @JsonIgnore
    @ApiModelProperty("绑定单品时门店商品的上架状态：0=上架")
    private Integer appStatus;




}
