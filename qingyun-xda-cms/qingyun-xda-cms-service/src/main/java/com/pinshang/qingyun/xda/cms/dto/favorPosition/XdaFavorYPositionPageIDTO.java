package com.pinshang.qingyun.xda.cms.dto.favorPosition;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.xda.cms.dto.home.HomeBaseIDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 首页猜你喜欢资源位绑定分页查询请求参数
 */
@Data
@NoArgsConstructor
public class XdaFavorYPositionPageIDTO extends Pagination {
	@ApiModelProperty("资源位")
	private Integer positionId;

	@ApiModelProperty("绑定期限：1-长期、2-短期")
	private Integer termType;

	@ApiModelProperty("客户ID")
	private Long storeId;

	@ApiModelProperty("标的类型:1-前台分类、2-H5、4-商品详情")
	private Integer targetType;

	@ApiModelProperty("状态：1-启用、2-停用、3-过期")
	private Integer status;

	@ApiModelProperty("渠道：1-APP、2-小程序")
	private Integer channel;


	@ApiModelProperty("生效开始时间")
	private String beginTime;

	@ApiModelProperty("生效结束时间")
	private String endTime;

	@ApiModelProperty("创建开始时间")
	private String createBeginTime;

	@ApiModelProperty("创建结束时间")
	private String createEndTime;

    public XdaFavorYPositionPageIDTO(HomeBaseIDTO baseIDTO, Integer status) {
        this.status = status;
        this.storeId = baseIDTO.getStoreId();
    }
}
