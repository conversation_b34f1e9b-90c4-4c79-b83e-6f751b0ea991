package com.pinshang.qingyun.xda.cms.dto.h5;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by hhf on 2019/11/20.
 */
@Data
public class XdaH5AddSelectCommodityODTO {

    @ApiModelProperty("商品id")
    private String commodityId;
    @ApiModelProperty("编码")
    private String commodityCode;
    @ApiModelProperty("名称")
    private String commodityName;
    @ApiModelProperty("规格")
    private String commoditySpec;

    /**条形码**/
    @ApiModelProperty("条形码")
    private String barCode;

    /**pos自编码**/
    @ApiModelProperty("pos自编码")
    private String commodityCodeSiss;

    /**状态名称**/
    @ApiModelProperty("状态名称")
    private String statusName;

}
