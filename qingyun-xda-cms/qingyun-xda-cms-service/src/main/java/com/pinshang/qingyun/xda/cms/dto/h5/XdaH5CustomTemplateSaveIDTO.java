package com.pinshang.qingyun.xda.cms.dto.h5;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: 鲜达H5-自定义模板保存对象
 * @author: hhf
 * @time: 2020/12/14 10:46
 */
@Data
public class XdaH5CustomTemplateSaveIDTO {

    /**H5模板名称 **/
    @ApiModelProperty("模板名称")
    private String templateName;

    /**小模板 JSON 串**/
    @ApiModelProperty("JSON字符串")
    private String templateContent;

    @ApiModelProperty("商品组集合:商品id集合和 显示数量")
    private List<XdaH5TemplateCommoditySaveIDTO> commodityList;

    @ApiModelProperty("优惠券ID集合")
    private List<Long> couponPublishRuleIdList;

    @ApiModelProperty("操作类型: 1-新增,2-复制新增")
    private Integer operateType;

    @ApiModelProperty("操作人")
    private Long userId;
}
