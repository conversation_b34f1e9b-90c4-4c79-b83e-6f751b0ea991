package com.pinshang.qingyun.xda.cms.dto.h5;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: hhf
 * @time: 2020/12/14 14:14
 */
@Data
public class XdaH5TemplateDetailsODTO {

    /**H5活动页相关**/
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("编码")
    private String templateCode;
    @ApiModelProperty("名称")
    private String templateName;
    @ApiModelProperty("小模板JSON字符串")
    private String templateContent;

    /**状态:1-启用,0-禁用 **/
    private Integer status;

    /**模板样式相关**/
    @ApiModelProperty("模板样式id")
    private Long templateCodeId;
    @ApiModelProperty("模板样式名称")
    private String templateStyleName;
    @ApiModelProperty("模板样式URL")
    private String url;


    /**H5模板明细-H5图片-头图**/
    @ApiModelProperty("H5模板明细-H5图片-头图")
    private XdaH5PicODTO h5PicHead;

    /**模板2-通栏区-图片对象**/
    @ApiModelProperty("小模板JSON字符串")
    private XdaH5PicODTO h5PicBanner;

    /**H5模板明细-图片商品**/
    @ApiModelProperty("H5模板明细-图片商品")
    private List<XdaH5PicCommodityODTO> h5PicCommodityList;

    /**H5模板明细-图片连接H5**/
    @ApiModelProperty("H5模板明细-图片连接H5")
    private XdaH5PicH5ODTO h5PicH5;

    /** 商品区,4个商品位**/
    @ApiModelProperty("商品区,4个商品位")
    private List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList;

    /**H5模板明细-tab**/
    @ApiModelProperty("H5模板明细-tab")
    private List<XdaH5TemplateTabODTO> h5TemplateTabList;

    /**H5模板明细-模块商品内容项**/
    @ApiModelProperty("H5模板明细-模块商品内容项")
    private List<XdaH5TemplateListODTO> h5TemplateListEntryList;

    /**H5模板明细-url地址**/
    @ApiModelProperty("H5模板明细-url地址")
    private XdaH5TemplateUrlODTO h5TemplateUrl;

    @ApiModelProperty("商品组集合:商品id集合和 显示数量")
    private List<XdaH5TemplateCommodityODTO> commodityList;

    @ApiModelProperty("优惠券ID集合")
    private List<Long> couponPublishRuleIdList;

}
