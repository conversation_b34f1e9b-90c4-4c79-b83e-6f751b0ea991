package com.pinshang.qingyun.xda.cms.dto.h5;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 鲜达 -H5模板列表搜索对象
 * @author: hhf
 * @time: 2020/12/10 15:38
 */
@Data
public class XdaH5TemplateIDTO extends Pagination{

    /**H5模板名称 **/
    @ApiModelProperty(value = "H5模板名称")
    private String templateName;
    @ApiModelProperty(value = "H5模板编码")
    private String templateCode;

    /**H5模板类型 1-大模板.2-小模板 **/
    @ApiModelProperty(value = "H5模板类型 1-大模板.2-小模板")
    private Integer templateType;
    /**状态:1-启用,0-禁用 **/
    @ApiModelProperty(value = "状态:1-启用,0-禁用 ")
    private Integer status;
}
