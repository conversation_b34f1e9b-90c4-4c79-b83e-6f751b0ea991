package com.pinshang.qingyun.xda.cms.dto.h5;

import lombok.Data;


@Data
public class XdaH5TemplateListODTO {

	/**H5模板id **/
	private Long templateId;
	/**位置-区域:1-头图区,2-模块区 **/
	private Integer positionLevel;
	/**位置-具体位置如模板1-元素1-1,2-元素2-1,3-元素2-2,4-Tab区(不同模板不同的序号数量)**/
	private Integer positionLevelSort;
	/**资源数据类型:10-H5图片,11-H5图片带h5连接,12-H5图片连接商品,20.h5-Tab,21.h5-通样,22.h5-商品区 **/
	private Integer resourceType;
	/**资源数据ID **/
	private Long resourceId;
	/**H5模板-图片URL **/
	private String picUrl;
	/**H5模板-图片名称 **/
	private String picName;
	/**图片连接H5模板id **/
	private Long h5TemplateId;
	/**H5模板-图片-关联商品 **/
	private Long commodityId;
	private String commodityIdStr;
	/**商品名称**/
	private String commodityName;
	/**商品前台品名**/
	private String commodityAppName;
	/**商品副标题**/
	private String commoditySubName;

	/*** 是否速冻 0：是 、1：否 */
	private String isQuickFreeze;
	/***名称 速冻*/
	private String quickFreezeName;
	/***箱规*/
	private String salesBoxCapacity;
	/**标签背景色**/
	private String tagBgColor;
	/**标签名称**/
	private String tagName;


}
