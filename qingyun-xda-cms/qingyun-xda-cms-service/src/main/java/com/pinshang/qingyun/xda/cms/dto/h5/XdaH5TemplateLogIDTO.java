package com.pinshang.qingyun.xda.cms.dto.h5;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:  鲜达 -H5模板日志列表搜索对象
 * @author: hhf
 * @time: 2020/12/10 15:55
 */
@Data
public class XdaH5TemplateLogIDTO extends Pagination{

    /**H5模板名称 **/
    @ApiModelProperty("H5模板名称")
    private String templateName;
    @ApiModelProperty("H5模板编码")
    private String templateCode;

    /**操作类型**/
    @ApiModelProperty("操作类型:1-新增,2-复制新增,3-修改,4-启用,5-停用")
    private Integer operateType;

    @ApiModelProperty("创建人")
    private Long userId;

    @ApiModelProperty("操作开始时间")
    private String beginTime;

    @ApiModelProperty("操作结束时间")
    private String endTime;
}
