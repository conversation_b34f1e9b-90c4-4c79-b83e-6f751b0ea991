package com.pinshang.qingyun.xda.cms.dto.h5;

import com.pinshang.qingyun.base.enums.xd.XdH5OperateTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @description:  鲜达 -H5模板日志列表对象
 * @author: hhf
 * @time: 2020/12/10 15:55
 */
@Data
public class XdaH5TemplateLogODTO {

    /**模板样式id**/
    private Long templateCodeId;
    /**H5模板code **/
    @ApiModelProperty("H5模板code")
    private String templateCode;
    /**H5模板名称 **/
    @ApiModelProperty("H5模板名称")
    private String templateName;

    @ApiModelProperty("H5模板类型")
    private String templateTypeName;

    /** 操作类型 **/
    @ApiModelProperty("操作类型")
    private String operateTypeName;
    private Integer operateType;

    @ApiModelProperty("创建人")
    private String createName;
    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("创建时间-格式化(yyyy-MM-dd HH:mm:ss)")
    private String createTimeStr;

    public String getTemplateTypeName() {
        if(null != templateCodeId ){
            if(templateCodeId.intValue() == 7){
                return "小模板H5";
            }else{
                return "大模板H5";
            }
        }
        return templateTypeName;
    }


    public String getOperateTypeName() {
        if(null != operateType){
            return XdH5OperateTypeEnums.getName(operateType);
        }
        return operateTypeName;
    }

    public String getCreateTimeStr() {
        if(null != createTime){
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return formatter.format(createTime);
        }
        return createTimeStr;
    }
}
