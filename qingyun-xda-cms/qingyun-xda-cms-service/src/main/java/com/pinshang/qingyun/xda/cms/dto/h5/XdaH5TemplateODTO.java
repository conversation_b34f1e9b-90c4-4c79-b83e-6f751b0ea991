package com.pinshang.qingyun.xda.cms.dto.h5;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description: 鲜达-H5模板列表展示对象
 * @author: hhf
 * @time: 2020/12/10 15:30
 */
@Data
public class XdaH5TemplateODTO {

    @ApiModelProperty(value = "id")
    private Long id;

    /**模板样式id**/
    @ApiModelProperty(value = "模板样式id")
    private Long templateCodeId;

    /**H5模板code **/
    @ApiModelProperty(value = "H5模板code")
    private String templateCode;

    /**H5模板名称 **/
    @ApiModelProperty(value = "H5模板名称")
    private String templateName;

    @ApiModelProperty(value = "H5模板类型")
    private String templateTypeName;

    /**状态:1-启用,0-禁用 **/
    @ApiModelProperty(value = "状态名称")
    private String statusName;
    @ApiModelProperty(value = "状态:1-启用,0-禁用")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    public String getTemplateTypeName() {
        if(null != templateCodeId ){
            if(templateCodeId.intValue() == 7){
                return "小模板H5";
            }else{
                return "大模板H5";
            }
        }
        return templateTypeName;
    }
}
