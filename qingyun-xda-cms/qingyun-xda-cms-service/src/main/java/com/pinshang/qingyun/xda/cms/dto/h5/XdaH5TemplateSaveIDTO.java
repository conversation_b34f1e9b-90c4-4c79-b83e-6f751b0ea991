package com.pinshang.qingyun.xda.cms.dto.h5;

import com.pinshang.qingyun.xda.cms.model.h5.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: 保存大模板
 * @author: hhf
 * @time: 2020/12/11 15:11
 */
@Data
public class XdaH5TemplateSaveIDTO {

    /**H5模板名称 **/
    @ApiModelProperty(value = "H5模板名称")
    private String templateName;

    /**活动模板id **/
    @ApiModelProperty(value = "活动模板id")
    private Long templateCodeId;

    private Long userId;

    @ApiModelProperty(value = "H5图片对象")
    private XdaH5Pic h5PicHead;

    @ApiModelProperty(value = "模板2通栏区图片对象")
    private XdaH5Pic h5PicBanner;

    @ApiModelProperty(value = "H5图片商品对象")
    private List<XdaH5PicCommodity> h5PicCommodityList;

    @ApiModelProperty(value = "单独商品集合:模板2:元素2-3 4个商品位,模板4:商品区")
    private List<XdaH5TemplateTabCommodity> h5TemplateTabCommodityList;

    @ApiModelProperty(value = "H5图片H5对象")
    private XdaH5PicH5 h5PicH5;

    @ApiModelProperty(value = "H5tab对象")
    private List<XdaH5TemplateTabIDTO> h5TemplateTabList;

    @ApiModelProperty(value = "h5-URL对象")
    private XdaH5TemplateUrl h5TemplateUrl;

    /**操作类型: 1-新增,2-复制新增**/
    @ApiModelProperty(value = "操作类型: 1-新增,2-复制新增")
    private Integer operateType;

    @ApiModelProperty(value = "门店id")
    private Long shopId;
}
