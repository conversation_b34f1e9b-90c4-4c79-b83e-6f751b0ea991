package com.pinshang.qingyun.xda.cms.dto.h5;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 鲜达-H5模板选择
 * @author: hhf
 * @time: 2020/12/11 14:49
 */
@Data
public class XdaH5TemplateStyleCodeODTO {

    @ApiModelProperty(value = "id")
    private Long id;
    /**H5模板code **/
    @ApiModelProperty(value = "H5模板code")
    private String templateCode;
    /**H5模板名称 **/
    @ApiModelProperty(value = "H5模板名称")
    private String templateName;
    /**H5模板内容 **/
    @ApiModelProperty(value = "H5模板内容")
    private String templateContent;
    /**状态:1-启用,0-禁用 **/
    @ApiModelProperty(value = "状态:1-启用,0-禁用")
    private Integer status;
    /**活动说明 **/
    @ApiModelProperty(value = "H5模板描述")
    private String templateDesc;
    /**URL地址**/
    @ApiModelProperty(value = "URL地址")
    private String url;

    @ApiModelProperty(value = "下拉框显示-label")
    private String label;
    @ApiModelProperty(value = "下拉框显示-value")
    private Long value;
}
