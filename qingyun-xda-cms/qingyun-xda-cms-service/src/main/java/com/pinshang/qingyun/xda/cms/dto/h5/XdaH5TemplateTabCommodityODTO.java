package com.pinshang.qingyun.xda.cms.dto.h5;

import java.math.BigDecimal;

public class XdaH5TemplateTabCommodityODTO {

	private Long id ;
	/**Tab区 Tab标签 **/
	private Long tabId;
	/**商品id **/
	private String commodityId;
	private Long commodityIdLong;
	/**商品id串**/
	private String commodityIdStr;
	/**商品编码**/
	private String commodityCode;
	/**商品名**/
	private String commodityName;
	/**前台品名**/
	private String commodityAppName;
	/**副标题**/
	private String commoditySubName;
	/**规格**/
	private String commoditySpec;
	/**条码**/
	private String barCode;
	/**零售价**/
	private BigDecimal commodityPrice;
	private String commodityPriceStr;
	/**库存**/
    /***
     * 库存给默认值为了解决模板显示已抢光问题
     * xda B端默认库存1,模板商品展示不涉及到商品是否已抢光,库存验证:加入购物车时实时验证
     */
	private Integer stockNumber=1;
	/**是否有库存**/
	private Integer isStock=1;
	/**商品默认图片地址**/
	private String defaultPicUrl;
	private String unitId;
	/**单位**/
	private String unitName;
	//特价限购描述
	private String limitInfo="";
	/**商品三级分类ID*/
	private Long categoryId;
	/**是否称重 0非称重 1称重*/
	private Integer isWeight;
	/**新品id**/
	private Long newCommodityId;
	/**标签id**/
	private Long tagId;
	/**标签背景色**/
	private String tagBgColor;
	/**标签名称**/
	private String tagName;
	/**商品包装规格**/
	private String commodityPackageSpec;
	//促销
	private  String promotionInfo="";
	/**特价标志：1-特价**/
	private  Integer promation = 0;
	/**特价价格**/
	private BigDecimal specialPrice;
	private String specialPriceStr;
	/**商品处理方式 可以有多个**/
	//private List<CommodityProcessInfoODTO> processList;
	/**此对象转化成json**/
	private String jsonStr;

	private Long templateCommodityId;

	/*** 是否速冻 0：是 、1：否 */
	private String isQuickFreeze;
	/***名称 速冻*/
	private String quickFreezeName;
	/***箱规*/
	private String salesBoxCapacity;

	/***商品不可订货标志 1:正常 0:购物车标志置灰 */
	private Integer icons=1;

	private Long secondCategoryId;

	public Long getSecondCategoryId() {
		return secondCategoryId;
	}

	public void setSecondCategoryId(Long secondCategoryId) {
		this.secondCategoryId = secondCategoryId;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getTabId() {
		return tabId;
	}

	public void setTabId(Long tabId) {
		this.tabId = tabId;
	}

	public String getCommodityId() {
		return commodityId;
	}

	public void setCommodityId(String commodityId) {
		this.commodityId = commodityId;
	}

	public Long getCommodityIdLong() {
		return commodityIdLong;
	}

	public void setCommodityIdLong(Long commodityIdLong) {
		this.commodityIdLong = commodityIdLong;
	}

	public String getCommodityIdStr() {
		return commodityIdStr;
	}

	public void setCommodityIdStr(String commodityIdStr) {
		this.commodityIdStr = commodityIdStr;
	}

	public String getCommodityCode() {
		return commodityCode;
	}

	public void setCommodityCode(String commodityCode) {
		this.commodityCode = commodityCode;
	}

	public String getCommodityName() {
		return commodityName;
	}

	public void setCommodityName(String commodityName) {
		this.commodityName = commodityName;
	}

	public String getCommodityAppName() {
		return commodityAppName;
	}

	public void setCommodityAppName(String commodityAppName) {
		this.commodityAppName = commodityAppName;
	}

	public String getCommoditySubName() {
		return commoditySubName;
	}

	public void setCommoditySubName(String commoditySubName) {
		this.commoditySubName = commoditySubName;
	}

	public String getCommoditySpec() {
		return commoditySpec;
	}

	public void setCommoditySpec(String commoditySpec) {
		this.commoditySpec = commoditySpec;
	}

	public String getBarCode() {
		return barCode;
	}

	public void setBarCode(String barCode) {
		this.barCode = barCode;
	}

	public BigDecimal getCommodityPrice() {
		if(commodityPrice!=null){
			commodityPrice=commodityPrice.setScale(2,BigDecimal.ROUND_DOWN);
		}
		return commodityPrice;
	}

	public void setCommodityPrice(BigDecimal commodityPrice) {
		this.commodityPrice = commodityPrice;
	}

	public Integer getStockNumber() {
		return stockNumber;
	}

	public void setStockNumber(Integer stockNumber) {
		this.stockNumber = stockNumber;
	}

	public String getDefaultPicUrl() {
		return defaultPicUrl;
	}

	public void setDefaultPicUrl(String defaultPicUrl) {
		this.defaultPicUrl = defaultPicUrl;
	}

	public String getUnitId() {
		return unitId;
	}

	public void setUnitId(String unitId) {
		this.unitId = unitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public String getLimitInfo() {
		return limitInfo;
	}

	public void setLimitInfo(String limitInfo) {
		this.limitInfo = limitInfo;
	}

	public Long getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Long categoryId) {
		this.categoryId = categoryId;
	}

	public Integer getIsWeight() {
		return isWeight;
	}

	public void setIsWeight(Integer isWeight) {
		this.isWeight = isWeight;
	}

	public Integer getIsStock() {
		return isStock;
	}

	public void setIsStock(Integer isStock) {
		this.isStock = isStock;
	}

	public Long getNewCommodityId() {
		return newCommodityId;
	}

	public void setNewCommodityId(Long newCommodityId) {
		this.newCommodityId = newCommodityId;
	}

	public Long getTagId() {
		return tagId;
	}

	public void setTagId(Long tagId) {
		this.tagId = tagId;
	}

	public String getTagBgColor() {
		return tagBgColor;
	}

	public void setTagBgColor(String tagBgColor) {
		this.tagBgColor = tagBgColor;
	}

	public String getTagName() {
		return tagName;
	}

	public void setTagName(String tagName) {
		this.tagName = tagName;
	}

	public String getCommodityPackageSpec() {
		return commodityPackageSpec;
	}

	public void setCommodityPackageSpec(String commodityPackageSpec) {
		this.commodityPackageSpec = commodityPackageSpec;
	}

	public String getPromotionInfo() {
		return promotionInfo;
	}

	public void setPromotionInfo(String promotionInfo) {
		this.promotionInfo = promotionInfo;
	}

	public Integer getPromation() {
		return promation;
	}

	public void setPromation(Integer promation) {
		this.promation = promation;
	}

	public BigDecimal getSpecialPrice() {
		if(specialPrice!=null){
			specialPrice=specialPrice.setScale(2,BigDecimal.ROUND_DOWN);
		}
		return specialPrice;
	}

	public void setSpecialPrice(BigDecimal specialPrice) {
		this.specialPrice = specialPrice;
	}

	/*public List<CommodityProcessInfoODTO> getProcessList() {
		return processList;
	}

	public void setProcessList(List<CommodityProcessInfoODTO> processList) {
		this.processList = processList;
	}*/

	public String getJsonStr() {
		return jsonStr;
	}

	public void setJsonStr(String jsonStr) {
		this.jsonStr = jsonStr;
	}

	public Long getTemplateCommodityId() {
		return templateCommodityId;
	}

	public void setTemplateCommodityId(Long templateCommodityId) {
		this.templateCommodityId = templateCommodityId;
	}

	public String getCommodityPriceStr() {
		if(getCommodityPrice()!=null){
			commodityPriceStr=getCommodityPrice().toString();
		}
		return commodityPriceStr;
	}

	public void setCommodityPriceStr(String commodityPriceStr) {
		this.commodityPriceStr = commodityPriceStr;
	}

	public String getSpecialPriceStr() {
		if(getSpecialPrice()!=null){
			specialPriceStr=getSpecialPrice().toString();
		}
		return specialPriceStr;
	}

	public void setSpecialPriceStr(String specialPriceStr) {
		this.specialPriceStr = specialPriceStr;
	}


	public String getIsQuickFreeze() {
		return isQuickFreeze;
	}

	public void setIsQuickFreeze(String isQuickFreeze) {
		this.isQuickFreeze = isQuickFreeze;
	}

	public String getQuickFreezeName() {
		return quickFreezeName;
	}

	public void setQuickFreezeName(String quickFreezeName) {
		this.quickFreezeName = quickFreezeName;
	}

	public String getSalesBoxCapacity() {
		return salesBoxCapacity;
	}

	public void setSalesBoxCapacity(String salesBoxCapacity) {
		this.salesBoxCapacity = salesBoxCapacity;
	}

	public Integer getIcons() {
		return icons;
	}

	public void setIcons(Integer icons) {
		this.icons = icons;
	}
}
