package com.pinshang.qingyun.xda.cms.dto.h5;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: 鲜达-H5tab对象
 * @author: hhf
 * @time: 2020/12/11 15:49
 */
@Data
public class XdaH5TemplateTabIDTO {

    /**Tab区 Tab标签 **/
    @ApiModelProperty(value = "Tab区 Tab标签")
    private String tabName;

	/**app前台显示几个商品位 **/
    @ApiModelProperty(value = "app前台显示几个商品位")
    private Integer appShowNum;

	/**1-tab,2-通栏,3-商品区**/
    @ApiModelProperty(value = "1-tab,2-通栏,3-商品区")
    private Integer tabType;

	/**状通栏图片URL **/
    @ApiModelProperty(value = "状通栏图片URL")
    private String picUrl;

    @ApiModelProperty(value = "tab商品集合对象")
    private List<String> commodityCodeList;

}