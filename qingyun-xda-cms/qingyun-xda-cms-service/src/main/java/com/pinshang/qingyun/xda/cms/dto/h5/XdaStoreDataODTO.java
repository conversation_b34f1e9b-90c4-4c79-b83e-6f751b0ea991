package com.pinshang.qingyun.xda.cms.dto.h5;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/12/15 11:15
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdaStoreDataODTO {

    @ApiModelProperty(value = "客户id")
    private String storeId;
    @ApiModelProperty(value = "客户code")
    private String storeCode;
    @ApiModelProperty(value = "客户名称")
    private String storeName;
    @ApiModelProperty(value = "客户停启状态")
    private String storeStatus;
    @ApiModelProperty(value = "客户停启状态名称")
    private String storeStatusName;
    @ApiModelProperty(value = "客户code+客户名称")
    private String concatCodeAndName;
    @ApiModelProperty(value = "门店code")
    private String shopCode;
    @ApiModelProperty(value = "门店名称")
    private String shopName;
    @ApiModelProperty(value = "客户所属公司id")
    private String companyId;


}
