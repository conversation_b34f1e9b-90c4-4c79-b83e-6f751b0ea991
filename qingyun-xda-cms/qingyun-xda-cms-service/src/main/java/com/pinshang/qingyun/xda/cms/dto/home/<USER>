package com.pinshang.qingyun.xda.cms.dto.home;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * 首页弹框通知返回对象
 */
@Data
public class XdaPopupMsgAppODTO {
	@ApiModelProperty("弹框通知ID")
	private Long msgId;
	@ApiModelProperty("弹框频率，代表几小时，0为不限制")
	private Integer msgPopupRate;
	@ApiModelProperty("通知方式：1=文字，2=图片")
	private Integer msgWay;
	@ApiModelProperty("文字通知标题：当msgWay=1时有值")
	private String msgTitle;
	@ApiModelProperty("文字通知详情：当msgWay=1时有值")
	private String msgDetails;
	@ApiModelProperty("图片通知pic地址：当msgWay=2时有值")
	private String msgPicUrl;
	@ApiModelProperty("图片通知是否跳转： 1=无需跳转,2=跳转到H5页面")
	private Integer jumpStatus;
	@ApiModelProperty("图片跳转关联的H5 ID ")
	private Long msgTargetTypeId;
	@ApiModelProperty("图片跳转关联的H5 url ")
	private String msgTargetTypeUrl;

	public String getMsgDetails() {
		return StringUtils.isNotBlank(msgDetails)? StringEscapeUtils.unescapeJava(msgDetails):null;
	}
}
