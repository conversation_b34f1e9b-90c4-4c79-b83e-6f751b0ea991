package com.pinshang.qingyun.xda.cms.dto.home;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class LogisticsBatchODTO {
    @ApiModelProperty("客户id")
    @JsonSerialize(
        using = ToStringSerializer.class
    )
    private Long storeId;
    @ApiModelProperty("业务类型：10-通达销售\t—— 参见枚举： TmsBusinessTypeEnums")
    private Integer businessType;
    @ApiModelProperty("业务类型名称")
    private String businessTypeName;
    @ApiModelProperty("物流中心ID")
    @JsonSerialize(
        using = ToStringSerializer.class
    )
    private Long logisticsCenterId;
    @ApiModelProperty("物流中心名称")
    private String logisticsCenterName;
    @ApiModelProperty("配送批次：1-一配、2-二配\t\t—— 参见枚举： TmsDeliveryBatchEnums")
    private Integer deliveryBatch;
    @ApiModelProperty("配送批次名称")
    private String deliveryBatchName;
    @ApiModelProperty("客户可下单时间-开始：HH:mm")
    private String storeBeginTime;
    @ApiModelProperty("客户可下单时间-截止：HH:mm")
    private String storeEndTime;
    @ApiModelProperty("客服截单时间：HH:mm")
    private String kfEndTime;
    @ApiModelProperty(
        value = "ID",
        hidden = true
    )
    @JsonSerialize(
        using = ToStringSerializer.class
    )
    private Long logisticsCenterBatchId;
    @ApiModelProperty("客户可选送货时间段：HH:mm~HH:mm和是否可选标识： 1-可选， 0-不可选")
    private Map<String, Integer> deliveryTimeMap;

    @ApiModelProperty("客户可选送货时间段：HH:mm~HH:mm")
    private List<String> deliveryTimeList;
}