package com.pinshang.qingyun.xda.cms.dto.home;

import com.pinshang.qingyun.box.utils.DateTimeUtil;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * APP资源位商品信息
 */
@Data
@NoArgsConstructor
public class OrderTimeODTO {
    /** 时间 **/
    private String orderTime;

    /** 别称 **/
    private String anotherName;

    private List<TdaDeliveryTimeRangeODTO> deliveryTimeRangeList;

    private final static String[] weeks = {"周日","周一","周二","周三","周四","周五","周六"};

    public OrderTimeODTO(Date orderTime,Integer n){
        String date = DateTimeUtil.formatDate(orderTime,"yyyy-MM-dd");
        if(n == 0){
            this.anotherName = date + " 今日";
        }else if(n == 1){
            this.anotherName = date + " 明日";
        }else if(n == 2){
            this.anotherName = date + " 后天";
        }else{
            this.anotherName = date + " " + getWeek(orderTime);
        }
        this.orderTime = date;
    }

    public static String getWeek(Date date){
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int week_index = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if(week_index<0){
            week_index = 0;
        }
        return weeks[week_index];
    }
}
