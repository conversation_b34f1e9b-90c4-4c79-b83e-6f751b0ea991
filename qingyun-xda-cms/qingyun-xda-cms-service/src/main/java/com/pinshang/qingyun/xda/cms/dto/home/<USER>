package com.pinshang.qingyun.xda.cms.dto.home;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 积木
 */
@Data
public class XdaPositionInfoODTO {
	@ApiModelProperty("101-banner，201-icon，301-积木1，302-积木2，303-积木3，501-通栏，602-横栏2，701-限时抢购，横向位-1001，服务栏与横栏01的结合-1011")
	private Integer type;
	@ApiModelProperty("具体积木数据")
	private List<XdaPositionInfoItemODTO> items;
	@ApiModelProperty("图片")
	private String picUrl;
	@ApiModelProperty("显示名称（ICON/推荐组）")
	private String label;
	@ApiModelProperty("商品信息")
	private List<XdaPositionInfoCommodityODTO> list;
	@ApiModelProperty("横向位")
    private List<XdaFavorXPositionODTO> favorXPositionList;
}
