package com.pinshang.qingyun.xda.cms.dto.home;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 首页弹框广告返回对象
 */
@Data
public class XdaPopupAdAppODTO {
	@ApiModelProperty("弹框广告ID")
	private Long adId;
	@ApiModelProperty("弹框频率，最小时间间隔，0为不限制【取资源位表min_interval】")
	private Integer adPopupRate;
	@ApiModelProperty("弹框广告图片地址")
	private String adPicUrl;
	@ApiModelProperty("标的类型：1-前台分类、2-H5页面、4-单品详情页")
	private Integer adTargetType;
	@ApiModelProperty("标的ID：当标的类型为单品详情页(adTargetType=4)时，标识绑定的商品ID")
	private String adTargetTypeId;
	@ApiModelProperty("标的url")
	private String adTargetTypeUrl;
	@ApiModelProperty("一级类目")
	private Integer adFirstLevel;
	@ApiModelProperty("二级类目")
	private Integer adSecondLevel;

}
