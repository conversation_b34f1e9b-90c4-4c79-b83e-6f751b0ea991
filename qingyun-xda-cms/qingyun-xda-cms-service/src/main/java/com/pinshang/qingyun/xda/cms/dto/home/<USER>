package com.pinshang.qingyun.xda.cms.dto.home;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 猜你喜欢纵向资源位绑定信息
 */
@Data
public class XdaHomeFavorYPositionInfoODTO extends XdaPositionInfoCommodityBase {

    @ApiModelProperty("猜你喜欢纵向位资源位ID")
    private Integer positionId;

	@ApiModelProperty("标的类型：1-前台类目、2-H5页面")
	private Integer targetType;

	@ApiModelProperty("标的ID")
	private String targetTypeId;

    @ApiModelProperty("标的地址")
    private String targetTypeUrl;

	@ApiModelProperty("广告图片地址")
	private String picUrl;

	@ApiModelProperty("是否是广告资源位,0-不是，1-是")
    private Integer isAd;

    @ApiModelProperty("APP上架状态：0-上架，1-下架")
    private Integer appStatus;

    @ApiModelProperty("一级类目")
    private Integer firstLevel;

    @ApiModelProperty("二级类目")
    private Integer secondLevel;

    @ApiModelProperty("图片实际宽度")
    private Integer realWidth;

    @ApiModelProperty("图片实际高度")
    private Integer realHeight;
    
    @ApiModelProperty(position = 11, required = true, value = "销售状态：1-正常可订货的商品、2-已抢光的商品、3-当前送货日期不支持订货的商品	 —— 参见枚举 SalesStatusEnums")
	private Integer salesStatus;

}
