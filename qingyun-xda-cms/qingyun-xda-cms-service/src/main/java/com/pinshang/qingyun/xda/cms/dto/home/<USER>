package com.pinshang.qingyun.xda.cms.dto.home;

import com.pinshang.qingyun.marketing.dto.mtCoupon.MtCouponUserNotifyODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * APP首页dto
 */
@Data
public class XdaHomePageODTO {

    @ApiModelProperty(value = "ModleItem")
    private List<XdaPositionInfoODTO> items;
    @ApiModelProperty("头图")
    private String homeBgHeadPic;
    @ApiModelProperty("首页弹框通知")
    private XdaPopupMsgAppODTO popupMsgAppODTO;
    @ApiModelProperty("首页弹框广告")
    private XdaPopupAdAppODTO popupAdAppODTO;
    @ApiModelProperty(value = "购物车数量")
    private Integer shoppingCartCount;

    @ApiModelProperty(value = "优惠券弹窗")
    private MtCouponUserNotifyODTO notify;
}
