package com.pinshang.qingyun.xda.cms.dto.home;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.pinshang.qingyun.base.po.BaseIDPO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 猜你喜欢横向资源位表
 */
@Data
public class XdaFavorXPositionODTO extends BaseIDPO {

    @ApiModelProperty("横向资源位ID,String类型")
    private String idStr;
    @ApiModelProperty("XSAppPositionIdEnums.X_FAVOR_02，X_FAVOR_03等")
    private Integer positionId;
    @ApiModelProperty("期限类型：1-长期、2-短期	【TermTypeEnums】")
    private Integer termType;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("副标题")
    private String subTitle;
    @ApiModelProperty("开始时间")
    private Date beginTime;
    @ApiModelProperty("结束时间")
    private Date endTime;
    @ApiModelProperty("门店Id")
    private Long shopId;
    @JsonIgnore
    private Long commodityId;

    public String getIdStr() {
        return id!=null?String.valueOf(id):null;
    }
}
