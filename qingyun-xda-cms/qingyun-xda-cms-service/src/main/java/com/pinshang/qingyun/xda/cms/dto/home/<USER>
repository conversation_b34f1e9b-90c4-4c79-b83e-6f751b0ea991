package com.pinshang.qingyun.xda.cms.dto.home;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description App商品基类
 * @date 2019/12/6
 */
@Data
public class XdaPositionInfoCommodityBase {
    @ApiModelProperty("商品id")
    private Long commodityId;
    @ApiModelProperty("商品id,String类型")
    private String commodityIdStr;
    @ApiModelProperty("商品编码")
    private String commodityCode;
    @ApiModelProperty("商品前台名称")
    private String commodityName;
    @ApiModelProperty("副标题")
    private String commoditySubName;
    @ApiModelProperty("商品规格")
    private String commoditySpec;
    @ApiModelProperty("是否是称重商品，0-不是，1-是")
    private Integer isWeight;
    @ApiModelProperty("箱规")
    private BigDecimal salesBoxCapacity;
    @ApiModelProperty("商品计量单位")
    private String commodityUnitName;
    @ApiModelProperty("是否速冻：0-否、1-是")
    private Integer isQuickFreeze;
    @ApiModelProperty("商品单价，取客户价格方案")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;
    @ApiModelProperty("默认图片URL,拼接后的url")
    private String imageUrl;
    @ApiModelProperty("自定义标签列表")
    private List<CommodityTextTagInfoODTO> tagList;
    @ApiModelProperty("是否有特价：0=无特价，1=普通特价")
    private Integer isSpecialPrice;
    @ApiModelProperty("原始特价，产品特价方案价格")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal specialPrice;
    @ApiModelProperty("是否有促销/赠品：0=无，1=有")
    private Integer isPromotion;
    @ApiModelProperty("是否可订货")
    private Boolean isCanOrder;
    @ApiModelProperty("是否有限量：0=无，1=有")
    private Integer isLimit;
    @ApiModelProperty("商品限量值")
    private BigDecimal limitNumber;
    // 标签
    @ApiModelProperty(value ="标签合集-列表页：1-自定义标签、2-特价、3-促销（、4-凑整、5-速冻）、6-限量",position = 13)
    private List<CommodityTextTagInfoODTO> listTagList;
    @ApiModelProperty(value ="标签集合-详情页：2-特价、4-凑整、5-速冻、6-限量",position = 13)
    private List<CommodityTextTagInfoODTO> tagV2List;
}
