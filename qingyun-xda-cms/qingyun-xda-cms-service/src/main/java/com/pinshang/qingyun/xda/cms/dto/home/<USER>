package com.pinshang.qingyun.xda.cms.dto.home;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 详细积木信息
 */
@Data
public class XdaBlockODTO {
	/**资源位主键*/
	private Long positionInfoId;
    /**资源位ID-积木组02：130201-左侧、130202-右上、130203-右下，积木组03：...	*/
    private Integer positionId;
    /**资源位类型：1301-积木组01、1302-积木组02、1303-积木组03*/
	private Integer positionType;
    /**图片地址*/
    private String picUrl;
    /**标的类型：1-前台类目、2-H5页面、4=单品详情*/
	private Integer targetType;
	/**标的ID*/
	private String targetTypeId;

    @ApiModelProperty("一级类目")
    private Integer firstLevel;
    @ApiModelProperty("二级类目")
    private Integer secondLevel;
    @JsonIgnore
    @ApiModelProperty("绑定单品时门店商品的上架状态：0=上架")
    private Integer appStatus;

}
