package com.pinshang.qingyun.xda.cms.dto.home;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 首页资源位信息
 */
@Data
public class XdaPositionInfoItemODTO {
	@ApiModelProperty("资源位主键")
	private Long positionInfoId;
    @ApiModelProperty("资源位ID【XSAppPositionIdEnums】")
    private Integer positionId;
	@ApiModelProperty("资源位类型")
	private Integer positionType;
	@ApiModelProperty("标的类型：1-前台类目、2-H5页面、3-组合、4-商品")
	private Integer targetType;
	@ApiModelProperty("标的ID；targetType=2:H5页面ID，targetType=4:商品ID(如果为空不跳转商品详情页)")
	private String targetTypeId;
    @ApiModelProperty("显示名称（ICON、推荐组、限时抢购）")
    private String label;
    @ApiModelProperty("图片")
    private String picUrl;
    @ApiModelProperty("标的url（标的为H5时）")
    private String targetUrl;
    @ApiModelProperty("一级类目")
    private Integer firstLevel;
    @ApiModelProperty("二级类目")
    private Integer secondLevel;
    @JsonIgnore
    @ApiModelProperty("最小间隔(小时)")
    private Integer minInterval;
    @JsonIgnore
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @JsonIgnore
    @ApiModelProperty("绑定单品时门店商品的上架状态：0=上架")
    private Integer appStatus;
}
