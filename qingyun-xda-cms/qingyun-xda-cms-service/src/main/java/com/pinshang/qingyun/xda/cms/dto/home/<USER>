package com.pinshang.qingyun.xda.cms.dto.home;

import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppV4ODTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * APP资源位商品信息
 */
@Data
public class XdaPositionInfoCommodityODTO extends XdaPositionInfoCommodityBase {
    @ApiModelProperty("资源位id")
    private Long positionInfoId;
    @ApiModelProperty("资源位id,String类型")
    private String positionInfoIdStr;
    @ApiModelProperty("排序")
    private Integer sortNum;
    
    @ApiModelProperty(position = 11, required = true, value = "销售状态：1-正常可订货的商品、2-已抢光的商品、3-当前送货日期不支持订货的商品	 —— 参见枚举 SalesStatusEnums")
	private Integer salesStatus;

    public String getPositionInfoIdStr() {
        return this.positionInfoId != null ? String.valueOf(this.positionInfoId) : null;
    }

    public void convertData(Map<Long,XdaCommodityAppODTO> map, XdaCommodityAppODTO odto) {
        this.setCommodityId(odto.getCommodityId());
        this.setCommodityIdStr(odto.getCommodityIdStr());
        this.setCommodityCode(odto.getCommodityCode());
        this.setCommodityName(odto.getCommodityName());
        this.setCommoditySubName(odto.getCommoditySubName());
        this.setCommoditySpec(odto.getCommoditySpec());
        this.setSalesBoxCapacity(odto.getSalesBoxCapacity());
        this.setCommodityUnitName(odto.getCommodityUnitName());
        this.setIsQuickFreeze(odto.getIsQuickFreeze());
        this.setCommodityPrice(odto.getCommodityPrice());
        this.setImageUrl(odto.getImageUrl());
        this.setTagList(odto.getTagList());
        this.setIsSpecialPrice(odto.getIsSpecialPrice());
        this.setSpecialPrice(odto.getSpecialPrice());
        this.setIsPromotion(odto.getIsPromotion());
        this.setIsCanOrder(odto.getIsCanOrder());
        this.setIsLimit(odto.getIsLimit());
        this.setLimitNumber(odto.getLimitNumber());
    }
    
    public void convertDataV4(XdaCommodityAppV4ODTO odto) {
        this.setCommodityId(odto.getCommodityId());
        this.setCommodityIdStr(odto.getCommodityIdStr());
        this.setCommodityCode(odto.getCommodityCode());
        this.setCommodityName(odto.getCommodityName());
        this.setCommoditySubName(odto.getCommoditySubName());
        this.setCommoditySpec(odto.getCommoditySpec());
        this.setSalesBoxCapacity(odto.getSalesBoxCapacity());
        this.setCommodityUnitName(odto.getCommodityUnitName());
        this.setIsQuickFreeze(odto.getIsQuickFreeze());
        this.setCommodityPrice(odto.getCommodityPrice());
        this.setImageUrl(odto.getImageUrl());
        this.setTagList(odto.getTagList());
        this.setListTagList(odto.getListTagList());
        this.setTagV2List(odto.getTagV2List());
        this.setIsSpecialPrice(odto.getIsSpecialPrice());
        this.setSpecialPrice(odto.getSpecialPrice());
        this.setIsPromotion(odto.getIsPromotion());
        this.setIsCanOrder(odto.getIsCanOrder());
        this.setIsLimit(odto.getIsLimit());
        this.setLimitNumber(odto.getLimitNumber());
        this.setSalesStatus(odto.getSalesStatus());
    }
    
}
