package com.pinshang.qingyun.xda.cms.dto.popup;

import com.pinshang.qingyun.base.enums.IsAllShopTypeEnums;
import com.pinshang.qingyun.base.enums.xd.XdPopupMagWayEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.xda.cms.dto.storeScope.DictionaryODTO;
import com.pinshang.qingyun.xda.cms.dto.storeScope.ProductPriceModelODTO;
import com.pinshang.qingyun.xda.cms.dto.storeScope.SettlementODTO;
import com.pinshang.qingyun.xda.cms.dto.storeScope.StoreODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description: 鲜达-弹框通知-查看详情
 * @author: hhf
 * @time: 2020/12/9 17:51
 */
@Data
public class XdaPopupMsgDetailsODTO {

    private Long id;

    /**通知概要**/
    @ApiModelProperty(value = "通知概要")
    private String msgSummary;

    //生效时间
    private Date beginTime;
    private Date endTime;
    @ApiModelProperty(value = "生效时间")
    private String validTime;

    /**'弹框频率'**/
    @ApiModelProperty(value = "弹框频率")
    private Integer frequency;

    @ApiModelProperty(value = "是否全部客户")
    private Integer isAllStore;
    @ApiModelProperty(value = "客户数量")
    private String storeQuantity;

    /**结账客户**/
    @ApiModelProperty(value = "结账客户")
    private List<SettlementODTO> settlementList;
    /**产品价格方案**/
    @ApiModelProperty(value = "产品价格方案")
    private List<ProductPriceModelODTO> productPriceModelList;
    /**客户**/
    @ApiModelProperty(value = "客户")
    private List<StoreODTO> storeList;
    /**客户类型**/
    @ApiModelProperty(value = "客户类型")
    private List<DictionaryODTO> storeTypeList;
    /**客户渠道**/
    @ApiModelProperty(value = "客户渠道")
    private List<DictionaryODTO> storeChannelList;

    @ApiModelProperty(value = "部门")
    private List<DictionaryODTO> departmentList;
    /**通知方式: 1-文字,2-图片**/
    @ApiModelProperty(value = "通知方式: 1-文字,2-图片")
    private Integer msgWay;
    @ApiModelProperty(value = "通知方式名称")
    private String msgWayName;


    /**前台通知标题**/
    @ApiModelProperty(value = "前台通知标题")
    private String appMsgTitle;

    /**前台通知详情**/
    @ApiModelProperty(value = "前台通知详情")
    private String appMsgDetails;

    /**弹框图片地址**/
    @ApiModelProperty(value = "弹框图片地址")
    private String picUrl;
    private String visitPicUrl;

    /**是否跳转: 1-无需跳转,2-H5页面**/
    @ApiModelProperty(value = "是否跳转: 1-无需跳转,2-H5页面")
    private Integer isJump;

    /**关联H5 Id**/
    @ApiModelProperty(value = "关联H5 Id")
    private Long h5Id;
    @ApiModelProperty(value = "h5 名称")
    private String h5Name;

    public String getMsgWayName() {
        if(null != msgWay){
            return XdPopupMagWayEnums.getName(this.msgWay);
        }
        return msgWayName;
    }

    public String getValidTime() {
        if(null != beginTime && null != endTime){
            return DateUtil.get4yMdHms(this.beginTime) + " ~ " + DateUtil.get4yMdHms(this.endTime);
        }
        return validTime;
    }

    public String getStoreQuantity() {
        if (null != this.isAllStore && IsAllShopTypeEnums.ALL_SHOP.getCode().intValue() == this.isAllStore.intValue()) {
            return "全部";
        }
        return this.storeQuantity;
    }
}
