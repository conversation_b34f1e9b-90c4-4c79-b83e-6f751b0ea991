package com.pinshang.qingyun.xda.cms.dto.popup;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: 鲜达-弹框通知列表搜索条件对象
 * @author: hhf
 * @time: 2020/12/9 13:44
 */
@Data
public class XdaPopupMsgIDTO extends Pagination{

    /**通知编号**/
    @ApiModelProperty(value = "通知编号")
    private String msgNo;

    /**通知概要**/
    @ApiModelProperty(value = "通知概要")
    private String msgSummary;

    /**生效开始日期**/
    @ApiModelProperty(value = "生效开始日期")
    private String beginTime;

    /**生效结束日期**/
    @ApiModelProperty(value = "生效结束日期")
    private String endTime;

    /**适用客户**/
    @ApiModelProperty(value = "适用客户")
    private Long storeId;

    /**通知方式: 1-文字,2-图片**/
    @ApiModelProperty(value = "通知方式: 1-文字,2-图片")
    private Integer msgWay;

    /**状态: 1-启用,0-停用**/
    @ApiModelProperty(value = "状态: 1-启用,0-停用")
    private Integer status;

    private List<Long> settlementIdList;

    private List<Long> productPriceModelIdList;

    private Long storeTypeId;

    private Long storeChannelId;
}
