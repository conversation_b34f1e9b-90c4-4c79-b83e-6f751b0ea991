package com.pinshang.qingyun.xda.cms.dto.popup;

import com.pinshang.qingyun.base.enums.OperateTypeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @description: 鲜达-弹框通知日志列表展示对象
 * @author: hhf
 * @time: 2020/12/9 13:44
 */
@Data
public class XdaPopupMsgLogODTO {

    //弹框消息id
    @ApiModelProperty(value = "弹框消息id")
    private Long popupMsgId;

    //操作类型: 1-新增、3-修改、5-启用、6-停用	【OperateTypeEnums】
    @ApiModelProperty(value = "操作类型: 1-新增、3-修改、5-启用、6-停用")
    private Integer operateType;
    @ApiModelProperty(value = "操作类型名称")
    private String operateTypeName;

    @ApiModelProperty(value = "创建人")
    private String createName;

    private Date createTimeDate;

    @ApiModelProperty(value = "创建时间")
    private String createTime;

    public String getOperateTypeName() {
        if(null != operateType){
            return OperateTypeEnums.getName(operateType);
        }
        return operateTypeName;
    }

    public String getCreateTime() {
        if(null != createTimeDate){
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return simpleDateFormat.format(createTimeDate);
        }
        return createTime;
    }
}
