package com.pinshang.qingyun.xda.cms.dto.popup;

import com.pinshang.qingyun.base.enums.IsAllShopTypeEnums;
import com.pinshang.qingyun.base.enums.xd.XdPopupMagWayEnums;
import com.pinshang.qingyun.base.enums.xd.XdPopupMsgStatusEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description: 鲜达-弹框通知列表显示对象
 * @author: hhf
 * @time: 2020/12/9 13:41
 */
@Data
public class XdaPopupMsgODTO {

    @ApiModelProperty(value = "弹框消息id")
    private Long id;

    /**通知编号**/
    @ApiModelProperty(value = "通知编号")
    private String msgNo;

    /**通知概要**/
    @ApiModelProperty(value = "通知概要")
    private String msgSummary;

    /**通知方式: 1-文字,2-图片**/
    private Integer msgWay;
    @ApiModelProperty(value = "通知方式")
    private String msgWayName;

    /**生效时间**/
    private Date beginTime;
    private Date endTime;
    @ApiModelProperty(value = "生效时间")
    private String validTime;

    @ApiModelProperty(value = "是否全部客户")
    private Integer isAllStore;
    @ApiModelProperty(value = "客户数量")
    private String storeQuantity;

    /**状态: 1-启用,0-停用**/
    private Integer status;
    @ApiModelProperty(value = "状态名称")
    private String statusName;

    public String getMsgWayName() {
        if(null != msgWay){
            return XdPopupMagWayEnums.getName(this.msgWay);
        }
        return msgWayName;
    }

    public String getStatusName() {
        if(null != status){
            return XdPopupMsgStatusEnums.getName(this.status);
        }
        return statusName;
    }

    public String getValidTime() {
        if(null != beginTime && null != endTime){
            return DateUtil.get4yMdHms(this.beginTime) + " ~ " + DateUtil.get4yMdHms(this.endTime);
        }
        return validTime;
    }

    public String getStoreQuantity() {
        if (null != this.isAllStore && IsAllShopTypeEnums.ALL_SHOP.getCode().intValue() == this.isAllStore.intValue()) {
            return "全部客户";
        }
        return "部分客户";
    }
}
