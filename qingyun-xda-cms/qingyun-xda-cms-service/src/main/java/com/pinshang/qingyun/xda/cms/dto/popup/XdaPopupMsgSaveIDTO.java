package com.pinshang.qingyun.xda.cms.dto.popup;

import com.pinshang.qingyun.xda.cms.model.popup.XdaPopupMsgStoreScope;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description: 鲜达-弹框通知-保存对象
 * @author: hhf
 * @time: 2020/12/9 15:00
 */
@Data
public class XdaPopupMsgSaveIDTO {

    /**通知编号**/
    @ApiModelProperty(value = "通知编号")
    private String msgNo;

    /**'通知概要'**/
    @ApiModelProperty(value = "通知概要")
    private String msgSummary;

    /**'生效开始时间'**/
    @ApiModelProperty(value = "生效开始时间")
    private String beginTime;

    /**'生效结束时间'**/
    @ApiModelProperty(value = "生效结束时间")
    private String endTime;

    /**'弹框频率'**/
    @ApiModelProperty(value = "弹框频率")
    private Integer frequency;

    /**是否所有客户：0-所有客户、1-指定客户	【IsAllStoreTypeEnums】**/
    @ApiModelProperty(value = "是否所有客户：0-所有客户、1-指定客户")
    private Integer isAllStore;

    @ApiModelProperty(value = "弹框通知关联客户范围")
    private List<XdaPopupMsgStoreScope> scopeList;


    /**通知方式: 1-文字,2-图片**/
    @ApiModelProperty(value = "通知方式: 1-文字,2-图片")
    private Integer msgWay;

    /**前台通知标题**/
    @ApiModelProperty(value = "前台通知标题")
    private String appMsgTitle;

    /**前台通知详情**/
    @ApiModelProperty(value = "前台通知详情")
    private String appMsgDetails;

    /**弹框图片地址**/
    @ApiModelProperty(value = "弹框图片地址")
    private String picUrl;

    /**是否跳转: 1-无需跳转,2-H5页面**/
    @ApiModelProperty(value = "是否跳转: 1-无需跳转,2-H5页面")
    private Integer isJump;

    /**关联H5 Id**/
    @ApiModelProperty(value = "关联H5 Id")
    private Long h5Id;

    private Long userId;

    private Date bTime;
    private Date eTime;
}
