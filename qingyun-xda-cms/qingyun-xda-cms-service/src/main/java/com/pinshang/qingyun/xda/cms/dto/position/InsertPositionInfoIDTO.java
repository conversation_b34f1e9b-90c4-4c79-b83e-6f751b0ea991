package com.pinshang.qingyun.xda.cms.dto.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 插入  资源位信息
 */
@Data
@ApiModel
@NoArgsConstructor
public class InsertPositionInfoIDTO {
	@ApiModelProperty(position = 11, required = true, value = "资源位类型：11-BANNER、12-ICON、13-积木组、14-推荐组、15-通栏、16-横栏、19-头图、20-服务说明栏、21-弹框广告	—— 参见【XSAppPositionTypeEnums】")
	private Integer positionType;
	@ApiModelProperty(position = 12, required = true, value = "资源位ID	—— 参见【XSAppPositionIdEnums】")
	private Integer positionId;
	@ApiModelProperty(position = 13, required = true, value = "期限类型：1-长期、2-短期	—— 参见【TermTypeEnums】")
	private Integer termType;
	@ApiModelProperty(position = 14, value = "开始时间")
	private String beginTime;
	@ApiModelProperty(position = 14, value = "结束时间")
	private String endTime;
	@ApiModelProperty(position = 15, value = "最小间隔(小时)[0,24]")
	private Integer minInterval;
	@ApiModelProperty(position = 16, required = true, value = "是否所有客户：0-所有客户、1-指定客户	—— 参见【IsAllStoreTypeEnums】")
	private Integer isAllStore;
	@ApiModelProperty(position = 17, value = "客户范围集合，isAllStore=1时，不能为空")
	private List<StoreScopeIDTO> storeScopeList;
	
	@ApiModelProperty(position = 21, value = "标的类型：1-前台类目、2-H5页面、3-组合、4-单品详情页		—— 参见【XSAppPositionInfoTargetTypeEnums】")
	private Integer targetType;
	@ApiModelProperty(position = 22, value = "标的ID")
	private Long targetTypeId;
	@ApiModelProperty(position = 23, value = "标的名称")
	private String label;
	
	@ApiModelProperty(position = 31, value = "图片地址")
	private String picUrl;
	@ApiModelProperty(position = 32, value = "通栏图片-标的类型：1-前台类目、2-H5页面、4-单品详情页	—— 参见【XSAppPositionInfoTargetTypeEnums】")
	private Integer picTargetType;
	@ApiModelProperty(position = 32, value = "通栏图片-标的ID")
	private Long picTargetTypeId;
	
	@ApiModelProperty(position = 41, value = "商品集合（推荐组/通栏）")
	private List<PositionCommodityInfoIDTO> commodityList;
	@ApiModelProperty(position = 42, value = "积木组信息集合")
	private List<PositionInfoBlockIDTO> blockList;
	
	@ApiModelProperty(position = 51, value = "是否强制提交：0-否、1-是")
	private Integer forceStatus;
	
	@ApiModelProperty(value = "由后端设置，前端无需设置", hidden = true)
	private Long userId;
	@ApiModelProperty(value = "由后端设置，前端无需设置", hidden = true)
	private Date bTime;
	@ApiModelProperty(value = "由后端设置，前端无需设置", hidden = true)
	private Date eTime;
}
