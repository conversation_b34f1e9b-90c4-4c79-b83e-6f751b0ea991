package com.pinshang.qingyun.xda.cms.dto.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 资源位-商品信息
 */
@Data
@ApiModel
@NoArgsConstructor
public class PositionCommodityInfoIDTO {
	@ApiModelProperty(position = 11, required = true, value = "商品ID")
	private Long commodityId;
	@ApiModelProperty(position = 12, required = true, value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(position = 13, required = true, value = "排序（正整数）")
	private Integer sortNum;
}
