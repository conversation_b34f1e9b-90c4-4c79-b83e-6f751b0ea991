package com.pinshang.qingyun.xda.cms.dto.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 鲜达资源位-商品信息
 */
@Data
@ApiModel
@NoArgsConstructor
public class PositionCommodityInfoODTO {
	@ApiModelProperty(position = 11, required = true, value = "商品ID")
	private String commodityId;
	@ApiModelProperty(position = 12, required = true, value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(position = 13, value = "商品名称")
	private String commodityName;
	@ApiModelProperty(position = 14, value = "商品规格")
	private String commoditySpec;
	@ApiModelProperty(position = 15, required = true, value = "排序（正整数）")
	private Integer sortNum;
}
