package com.pinshang.qingyun.xda.cms.dto.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.xda.cms.dto.storeScope.StoreScopeODTO;

/**
 * 资源位详情
 */
@Data
@ApiModel
@NoArgsConstructor
public class PositionDetailODTO {
	@ApiModelProperty(position = 100, required = true, value = "ID")
	private Long id;
	@ApiModelProperty(position = 101, required = true, value = "资源位编号")
	private String positionCode;
	@ApiModelProperty(position = 102, required = true, value = "资源位类型：11-BANNER、12-ICON、13-积木组、14-推荐组、15-通栏、16-横栏、19-头图、20-服务说明栏、21-弹框广告	—— 参见【XSAppPositionTypeEnums】")
	private Integer positionType;
	@ApiModelProperty(position = 103, required = true, value = "资源位ID		—— 参见【XSAppPositionIdEnums】")
	private Integer positionId;
	@ApiModelProperty(position = 104, required = true, value = "期限类型：1-长期、2-短期		—— 参见【TermTypeEnums】")
	private Integer termType;
	@ApiModelProperty(position = 105, required = true, value = "开始时间")
	private Date beginTime;
	@ApiModelProperty(position = 105, required = true, value = "结束时间")
	private Date endTime;
	@ApiModelProperty(position = 106, required = true, value = "最小间隔(小时)[0,24]")
	private Integer minInterval;
	@ApiModelProperty(position = 107, required = true, value = "是否所有客户：0-所有客户、1-指定客户	—— 参见【IsAllStoreTypeEnums】")
	private Integer isAllStore;
	
	@ApiModelProperty(position = 108, value = "产品价格方案")
    private List<StoreScopeODTO> productPriceModelList;
    @ApiModelProperty(position = 108, value = "结账客户")
    private List<StoreScopeODTO> settlementList;
    @ApiModelProperty(position = 108, value = "客户类型")
    private List<StoreScopeODTO> storeTypeList;
    @ApiModelProperty(position = 108, value = "客户渠道")
    private List<StoreScopeODTO> storeChannelList;
    @ApiModelProperty(position = 108, value = "客户")
    private List<StoreScopeODTO> storeList;

	@ApiModelProperty(position = 109, required = true, value = "标的类型：1-前台类目、2-H5页面、3-组合、4-单品详情页	—— 参见【XSAppPositionInfoTargetTypeEnums】")
	private Integer targetType;
	@ApiModelProperty(position = 110, required = true, value = "标的ID")
	private String targetTypeId;
	@ApiModelProperty(position = 110, required = true, value = "标的原标题")
	private String targetTypeTitle;
	@ApiModelProperty(position = 111, required = true, value = "显示名称（ICON/推荐组）")
	private String label;
	
	@ApiModelProperty(position = 113, required = true, value = "图片地址-用于提交")
	private String picUrl;
	@ApiModelProperty(position = 113, value = "图片地址-用于显示")
    private String visitPicUrl;
	@ApiModelProperty(position = 114, required = true, value = "图片标的类型：1-前台类目、2-H5页面、4-单品详情页		—— 参见【XSAppPositionInfoTargetTypeEnums】")
	private Integer picTargetType;
	@ApiModelProperty(position = 115, required = true, value = "通栏图片-标的ID")
	private String picTargetTypeId;
	@ApiModelProperty(position = 115, required = true, value = "通栏图片-标的原标题")
	private String picTargetTypeTitle;
	
//	@ApiModelProperty(position = 116, required = true, value = "状态：1-启用、2-停用、3-过期	—— 参见【XSAppPositionInfoStatusEnums】")
//	private Integer status;
	
	@ApiModelProperty(position = 302, required = true, value = "商品列表")
	private List<PositionCommodityInfoODTO> commodityList;
	@ApiModelProperty(position = 303, required = true, value = "积木列表")
	private List<PositionInfoBlockODTO> blockList;
}
