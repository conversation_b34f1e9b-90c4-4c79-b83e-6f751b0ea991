package com.pinshang.qingyun.xda.cms.dto.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 鲜达资源位-积木
 */
@Data
@ApiModel
@NoArgsConstructor
public class PositionInfoBlockIDTO {
	@ApiModelProperty(position = 11, required = true, value = "资源位ID-积木组02：130201-左侧、130202-右上、130203-右下||资源位ID-积木组03：130301-左上、130302-左下、130303-右上、130304-右下_左、130304-右下_右	—— 参见 XSAppPositionBlockIdEnums")
	private Integer positionId;
	@ApiModelProperty(position = 11, required = true, value = "标的类型：1-前台类目、2-H5页面		—— 参见 XSAppPositionInfoTargetTypeEnums")
	private Integer targetType;
	@ApiModelProperty(position = 11, required = true, value = "标的ID")
	private Long targetTypeId;
	@ApiModelProperty(position = 11, required = true, value = "图片地址")
	private String picUrl;
}
