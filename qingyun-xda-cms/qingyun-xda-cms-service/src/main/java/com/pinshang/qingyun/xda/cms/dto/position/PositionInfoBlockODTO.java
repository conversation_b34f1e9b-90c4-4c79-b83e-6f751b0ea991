package com.pinshang.qingyun.xda.cms.dto.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.enums.XSAppPositionBlockIdEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionInfoTargetTypeEnums;

/**
 * 鲜达资源位-积木组
 */
@Data
@ApiModel
@NoArgsConstructor
public class PositionInfoBlockODTO {
	@ApiModelProperty(position = 11, required = true, value = "资源位类型（积木类型）：1301-积木组01、1302-积木组02、1303-积木组03	【XSAppPositionBlockTypeEnums】")
	private Integer positionType;
	@ApiModelProperty(position = 12, required = true, value = "资源位ID：积木组02-(130201-左侧、130202-右上、130203-右下)、积木组03-(130301-左上、130302-左下、130303-右上、130304-右下_左、130305-右下_右)	【XSAppPositionBlockIdEnums】")
	private Integer positionId;
	@ApiModelProperty(position = 12, required = true, value = "资源位名称")
	private String positionName;
	@ApiModelProperty(position = 13, required = true, value = "标的类型：1-前台类目、2-H5页面	【XSAppPositionInfoTargetTypeEnums】")
	private Integer targetType;
	@ApiModelProperty(position = 13, required = true, value = "标的类型名称")
	private String targetTypeName;
	@ApiModelProperty(position = 14, required = true, value = "标的ID")
	private String targetTypeId;
	@ApiModelProperty(position = 14, required = true, value = "标的原标题")
	private String targetTypeTitle;
	@ApiModelProperty(position = 15, required = true, value = "图片地址-用于提交")
	private String picUrl;
	@ApiModelProperty(position = 15, value = "图片地址-用于显示")
    private String visitPicUrl;
	
	public String getPositionName() {
		return XSAppPositionBlockIdEnums.getName(this.positionId);
	}
	public String getTargetTypeName() {
		return XSAppPositionInfoTargetTypeEnums.getName(this.targetType);
	}
	
}
