package com.pinshang.qingyun.xda.cms.dto.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.enums.OperateTypeEnums;

/**
 * 资源位信息-日志信息
 */
@Data
@ApiModel
@NoArgsConstructor
public class PositionInfoLogInfoODTO {
	@ApiModelProperty(position = 11, required = true, value = "操作类型: 1-新增、3-修改、5-启用、6-停用	【OperateTypeEnums】", hidden = true)
	private Integer operateType;
	@ApiModelProperty(position = 11, required = true, value = "操作类型名称")
	private String operateTypeName;
	@ApiModelProperty(position = 12, required = true, value = "操作人")
	private String createName;
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@ApiModelProperty(position = 13, required = true, value = "操作时间：yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
	public String getOperateTypeName() {
		return OperateTypeEnums.getName(this.operateType);
	}
	
}
