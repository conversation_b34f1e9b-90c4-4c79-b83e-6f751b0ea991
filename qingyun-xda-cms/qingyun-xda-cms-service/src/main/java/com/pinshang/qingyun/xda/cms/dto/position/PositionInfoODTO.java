package com.pinshang.qingyun.xda.cms.dto.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.enums.IsAllShopTypeEnums;
import com.pinshang.qingyun.base.enums.TermTypeEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionIdEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionInfoStatusEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionInfoTargetTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;

/**
 * 资源位信息
 */
@Data
@ApiModel
@NoArgsConstructor
public class PositionInfoODTO {
	@ApiModelProperty(position = 0, required = true, value = "ID")
	private String id;
	
	@ApiModelProperty(position = 11, required = true, value = "资源位编号")
	private String positionCode;
	@ApiModelProperty(position = 11, required = true, value = "资源位类型：11-BANNER、12-ICON、13-积木组、14-推荐组、15-通栏、16-横栏、19-头图、20-服务说明栏、21-弹框广告	—— 参见【XSAppPositionTypeEnums】")
	private Integer positionType;
	
	@ApiModelProperty(position = 12, required = true, value = "资源位ID		—— 参见【XSAppPositionIdEnums】")
	private Integer positionId;
	@ApiModelProperty(position = 12, required = true, value = "资源位名称")
	private String positionName;
	
	@ApiModelProperty(position = 13, required = true, value = "期限类型：1-长期、2-短期		—— 参见【TermTypeEnums】")
	private Integer termType;
	@ApiModelProperty(position = 13, required = true, value = "期限类型名称")
	private String termTypeName;
	
	@ApiModelProperty(position = 14, required = true, value = "标的类型：1-前台类目、2-H5页面、3-组合、4-单品详情页	—— 参见【XSAppPositionInfoTargetTypeEnums】")
	private Integer targetType;
	@ApiModelProperty(position = 14, required = true, value = "标的类型名称")
	private String targetTypeName;
	
	@ApiModelProperty(position = 15, required = true, value = "标的名称")
	private String label;
	
	@ApiModelProperty(position = 16, required = true, value = "开始时间", hidden =true)
	private Date beginTime;
	@ApiModelProperty(position = 16, required = true, value = "结束时间", hidden =true)
	private Date endTime;
	@ApiModelProperty(position = 16, required = true, value = "生效时间")
	private String termTime;
	
	@ApiModelProperty(position = 17, required = true, value = "是否所有客户：0-所有客户、1-指定客户	—— 参见【IsAllStoreTypeEnums】")
	private Integer isAllStore;
	@ApiModelProperty(position = 17, required = true, value = "客户范围")
	private String storeScope;
	
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@ApiModelProperty(position = 18, required = true, value = "更新时间：yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	
	@ApiModelProperty(position = 19, required = true, value = "状态：1-启用、2-停用、3-过期	—— 参见【XSAppPositionInfoStatusEnums】")
	private Integer status;
	@ApiModelProperty(position = 19, required = true, value = "状态名称")
	private String statusName;
	
	public String getPositionName() {
		return XSAppPositionIdEnums.getName(this.positionId);
	}
	public String getTermTypeName() {
		return TermTypeEnums.getName(this.termType);
	}
	public String getTargetTypeName() {
		return XSAppPositionInfoTargetTypeEnums.getName(this.targetType);
	}
	public String getTermTime() {
		String termTime = "";
		if (TermTypeEnums.长期.getCode().equals(this.termType)) {
			termTime = "永久";
		} else if (TermTypeEnums.短期.getCode().equals(this.termType)) {
			termTime = DateUtil.get4yMdHms(this.beginTime) + " ~ " + DateUtil.get4yMdHms(this.endTime);
		}
		return termTime;
	}
	public String getStoreScope() {
		return IsAllShopTypeEnums.ALL_SHOP.getCode().equals(this.isAllStore)? "全部客户": "部分客户";
	}
	
	// 这里再次修正数据
	Date now = new Date(System.currentTimeMillis());
	public Integer getStatus() {
		if (TermTypeEnums.短期.getCode().equals(this.termType) && XSAppPositionInfoStatusEnums.启用.getCode().equals(this.status)) {
			if (now.after(this.endTime)) {
				return XSAppPositionInfoStatusEnums.已过期.getCode();
			}
		}
		return this.status;
	}
	
	public String getStatusName() {
		return XSAppPositionInfoStatusEnums.getName(this.getStatus());
	}
	
}
