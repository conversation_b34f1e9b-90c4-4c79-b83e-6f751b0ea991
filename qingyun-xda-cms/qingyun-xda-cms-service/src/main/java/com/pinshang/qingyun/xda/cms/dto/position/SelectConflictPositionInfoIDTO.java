package com.pinshang.qingyun.xda.cms.dto.position;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.xda.cms.model.position.XdaPositionInfo;

/**
 * 查询  冲突的资源位  信息
 */
@Data
@NoArgsConstructor
public class SelectConflictPositionInfoIDTO extends InsertPositionInfoIDTO {
	@ApiModelProperty(position = 51, value = "资源位信息ID")
	private Long id;

	private SelectConflictPositionInfoIDTO(Integer positionType, Integer positionId, String beginTime, String endTime, Integer isAllStore, List<StoreScopeIDTO> storeScopeList) {
		this.setPositionType(positionType);
		this.setPositionId(positionId);
		this.setBeginTime(beginTime);
		this.setEndTime(endTime);
		this.setIsAllStore(isAllStore);
//		this.setStoreScopeList(storeScopeList); // 不校验客户范围
	}
	
	public SelectConflictPositionInfoIDTO(Long id, InsertPositionInfoIDTO vo) {
		this(vo.getPositionType(), vo.getPositionId(), vo.getBeginTime(), vo.getEndTime(), vo.getIsAllStore(), vo.getStoreScopeList());
		this.setTermType(vo.getTermType());
		this.id = id;
	}
	
	public SelectConflictPositionInfoIDTO(XdaPositionInfo positionInfo, List<StoreScopeIDTO> storeScopeList) {
		this(positionInfo.getPositionType(), positionInfo.getPositionId(), DateUtil.get4yMdHms(positionInfo.getBeginTime()), 
				DateUtil.get4yMdHms(positionInfo.getEndTime()), positionInfo.getIsAllStore(), storeScopeList);
		this.setTermType(positionInfo.getTermType());
		this.id = positionInfo.getId();
	}
	
}
