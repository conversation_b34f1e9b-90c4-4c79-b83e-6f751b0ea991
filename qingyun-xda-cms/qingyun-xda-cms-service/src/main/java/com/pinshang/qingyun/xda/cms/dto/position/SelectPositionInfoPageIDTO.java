package com.pinshang.qingyun.xda.cms.dto.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.box.utils.StringUtil;

/**
 * 分页查询  资源位信息  列表
 */
@Data
@ApiModel
@NoArgsConstructor
public class SelectPositionInfoPageIDTO extends Pagination {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(position = 11, value = "资源位编号")
	private String positionCode;
	@ApiModelProperty(position = 12, value = "资源位类型：11-BANNER、12-ICON、13-积木组、14-推荐组、15-通栏、16-横栏、19-头图、20-服务说明栏、21-弹框广告	—— 参见 XSAppPositionTypeEnums")
	private Integer positionType;
	@ApiModelProperty(position = 13, value = "期限类型：1-长期、2-短期		—— 参见 TermTypeEnums")
	private Integer termType;
	
	@ApiModelProperty(position = 14, value = "客户ID")
	private Long storeId;
	@ApiModelProperty(position = 15, value = "标的类型：1-前台类目、2-H5页面、3-组合、4-单品详情页	—— 参见 XSAppPositionInfoTargetTypeEnums")
	private Integer targetType;
	@ApiModelProperty(position = 16, value = "标的名称")
	private String label;
	
	@ApiModelProperty(position = 17, value = "生效时间-起：yyyy-MM-dd")
	private String beginTime;
	@ApiModelProperty(position = 17, value = "生效时间-讫：yyyy-MM-dd")
	private String endTime;
	
	@ApiModelProperty(position = 18, value = "创建时间-起：yyyy-MM-dd")
	private String createBeginTime;
	@ApiModelProperty(position = 18, value = "创建时间-讫：yyyy-MM-dd")
	private String createEndTime;
	
	@ApiModelProperty(position = 19, value = "状态：1-启用、2-停用、3-过期		—— 参见 XSAppPositionInfoStatusEnums")
	private Integer status;

	public String getBeginTime() {
		return StringUtil.isNullOrEmpty(beginTime)? null: beginTime + " 00:00:00";
	}

	public String getEndTime() {
		return StringUtil.isNullOrEmpty(endTime)? null: endTime + " 23:59:59";
	}

	public String getCreateBeginTime() {
		return StringUtil.isNullOrEmpty(createBeginTime)? null: createBeginTime + " 00:00:00";
	}

	public String getCreateEndTime() {
		return StringUtil.isNullOrEmpty(createEndTime)? null: createEndTime + " 23:59:59";
	}
	
}
