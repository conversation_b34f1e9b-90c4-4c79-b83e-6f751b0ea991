package com.pinshang.qingyun.xda.cms.dto.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 鲜达资源位-客户范围
 */
@Data
@ApiModel
@NoArgsConstructor
public class StoreScopeIDTO {
	@ApiModelProperty(position = 11, required = true, value = "参考对象类型：1-结账客户、2-产品价格方案、3-客户类型、4-渠道、8-客户		——参见 MessageStoreScopeTypeEnums")
    private Integer refObjType;
	@ApiModelProperty(position = 12, required = true, value = "参考对象ID")
    private Long refObjId;
}
