package com.pinshang.qingyun.xda.cms.dto.position;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新  资源位信息状态
 */
@Data
@ApiModel
@NoArgsConstructor
public class UpdatePositionInfoStatusIDTO {
	@ApiModelProperty(position = 11, required = true, value = "ID")
	private Long id;
	@ApiModelProperty(position = 12, required = true, value = "状态: 1-启用、2-停用")
	private Integer status;
	
	@ApiModelProperty(position = 21, value = "是否强制提交：0-否、1-是")
	private Integer forceStatus;
	
	@ApiModelProperty(hidden = true)
	private Long userId;
}
