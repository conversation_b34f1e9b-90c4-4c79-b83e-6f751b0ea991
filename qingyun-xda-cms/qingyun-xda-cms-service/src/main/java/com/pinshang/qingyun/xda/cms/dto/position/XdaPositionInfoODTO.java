package com.pinshang.qingyun.xda.cms.dto.position;

import com.pinshang.qingyun.base.enums.*;
import com.pinshang.qingyun.box.utils.DateUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 资源位信息
 */
@Data
public class XdaPositionInfoODTO {
	// private String id;
	// private Integer positionType;
	private Integer positionId;
	private Integer termType;
	private Integer targetType;
	// private String label;
	private Date beginTime;
	private Date endTime;
	// private Integer isAllShop;
	// private String shopQuantity;
	private Date updateTime;
	// private Integer status;
	
	@ApiModelProperty(position = 0, required = true, value = "资源位编号")
	private String positionCode;
	@ApiModelProperty(position = 10, required = true, value = "资源位类型：11-BANNER、12-ICON、13-积木组、14-推荐组、15-通栏、16-横栏、19-头图、20-服务说明栏、21-弹框广告")
	private Integer positionType;
	@ApiModelProperty(position = 11, required = true, value = "资源位")
	private String positionName;
	@ApiModelProperty(position = 12, required = true, value = "期限类型：1-长期、2-短期")
	private String termTypeName;
	@ApiModelProperty(position = 13, required = true, value = "标的类型：1-前台类目、2-H5页面、3-组合、4-单品详情页")
	private String targetTypeName;
	@ApiModelProperty(position = 14, required = true, value = "标的名称")
	private String label;
	@ApiModelProperty(position = 15, required = true, value = "生效时间")
	private String termTime;
	@ApiModelProperty(position = 16, required = true, value = "适用门店")
	private String shopQuantity;
	@ApiModelProperty(position = 17, required = true, value = "更新时间")
	private String updateTimeStr;
	@ApiModelProperty(position = 18, required = true, value = "状态")
	private String statusName;
	
	@ApiModelProperty(position = 51, required = true, value = "ID")
	private String id;
	@ApiModelProperty(position = 52, required = true, value = "是否所有门店：0-所有门店、1-指定门店")
	private Integer isAllShop;
	@ApiModelProperty(position = 53, required = true, value = "状态：1-启用、2-停用、3-过期")
	private Integer status;
	
	public String getPositionName() {
		return XSAppPositionIdEnums.getName(this.positionId);
	}
	public String getTermTypeName() {
		return TermTypeEnums.getName(this.termType);
	}
	public String getTargetTypeName() {
		return XSAppPositionInfoTargetTypeEnums.getName(this.targetType);
	}
	public String getTermTime() {
		String termTime = "";
		if (TermTypeEnums.长期.getCode() == this.termType.intValue()) {
			termTime = "永久";
		} else if (TermTypeEnums.短期.getCode() == this.termType.intValue()) {
			termTime = DateUtil.get4yMdHms(this.beginTime) + " ~ " + DateUtil.get4yMdHms(this.endTime);
		}
		return termTime;
	}
	public String getShopQuantity() {
		if (null != this.isAllShop && IsAllShopTypeEnums.ALL_SHOP.getCode().intValue() == this.isAllShop.intValue()) {
			return "全部";
		}
		return this.shopQuantity;
	}
	public String getUpdateTimeStr() {
		return DateUtil.get4yMdHms(this.updateTime);
	}
	public String getStatusName() {
		String statusName = XSAppPositionInfoStatusEnums.getName(this.status);
		if (TermTypeEnums.短期.getCode() == this.termType.intValue() && XSAppPositionInfoStatusEnums.启用.getCode() == this.status) {
			Date now = new Date(System.currentTimeMillis());
			if (now.after(this.endTime)) {
				statusName = XSAppPositionInfoStatusEnums.已过期.getName();
			}
		}
		return statusName;
	}
	// 这里再次修正数据
	public Integer getStatus() {
		if (TermTypeEnums.短期.getCode() == this.termType.intValue() && XSAppPositionInfoStatusEnums.启用.getCode() == this.status && this.getStatusName().equals(XSAppPositionInfoStatusEnums.已过期.getName())) {
			return XSAppPositionInfoStatusEnums.已过期.getCode();
		}
		return this.status;
	}
}
