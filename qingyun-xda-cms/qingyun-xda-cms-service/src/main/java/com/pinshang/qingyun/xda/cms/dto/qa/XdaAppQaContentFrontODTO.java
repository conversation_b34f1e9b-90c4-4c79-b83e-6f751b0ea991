package com.pinshang.qingyun.xda.cms.dto.qa;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/12/29 10:40
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdaAppQaContentFrontODTO extends XdaAppQaFrontODTO {

    @ApiModelProperty(value = "常见问题内容")
    private String content;

    public XdaAppQaContentFrontODTO(Long id, String title, String content) {
        this.id = id;
        this.title = title;
        this.content = content;
    }
}