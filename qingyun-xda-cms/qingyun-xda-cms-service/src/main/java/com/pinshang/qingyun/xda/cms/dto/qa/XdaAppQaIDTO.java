package com.pinshang.qingyun.xda.cms.dto.qa;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 鲜达app常见问题列表搜索对象
 * @author: hhf
 * @time: 2020/12/16 11:15
 */
@Data
public class XdaAppQaIDTO extends Pagination{

    @ApiModelProperty(value = "问题标题")
    private String title;

    @ApiModelProperty(value = "状态：1-启用、2-停用")
    private Integer status;
}
