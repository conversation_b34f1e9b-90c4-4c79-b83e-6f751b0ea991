package com.pinshang.qingyun.xda.cms.dto.qa;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 鲜达app常见问题
 * @author: hhf
 * @time: 2020/12/16 11:11
 */
@Data
public class XdaAppQaODTO {

    @ApiModelProperty(value = "id")
    private Long id;
    /**问题标题**/
    @ApiModelProperty(value = "问题标题")
    private String title;
    /**状态：1-启用、2-停用**/
    @ApiModelProperty(value = "状态：1-启用、2-停用")
    private Integer status;
    @ApiModelProperty(value = "状态名称")
    private String statusName;
    /**排序号**/
    @ApiModelProperty(value = "排序号")
    private Integer sortNum;


    /**问题内容**/
    @ApiModelProperty(value = "问题内容")
    private String content;
}
