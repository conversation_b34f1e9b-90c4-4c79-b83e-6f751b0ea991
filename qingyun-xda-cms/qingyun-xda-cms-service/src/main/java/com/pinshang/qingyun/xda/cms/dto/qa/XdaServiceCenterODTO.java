package com.pinshang.qingyun.xda.cms.dto.qa;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/12/29 10:25
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class XdaServiceCenterODTO {

    @ApiModelProperty(value = "400电话号码")
    private String phone;

    @ApiModelProperty(value = "400电话工作时间")
    private String phoneTime;

//    @ApiModelProperty(value = "常见问题集合")
//    private List<XdaAppQaFrontODTO> qaList ;
}
