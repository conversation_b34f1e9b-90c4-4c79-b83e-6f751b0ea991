package com.pinshang.qingyun.xda.cms.dto.search;

import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * APP热搜词列表返回
 */
@Data
public class XdaSearchHotWordAppODTO {
	@ApiModelProperty("热搜词ID")
	private Long id;

	@ApiModelProperty("热搜词")
	private String hotWord;

	@ApiModelProperty("特效状态:0=无，1=有")
	private Integer effectStatus;

	@ApiModelProperty("前台序号")
	private Integer sort;


	public static XdaSearchHotWordAppODTO convert(XdaSearchHotWordODTO entry) {
		XdaSearchHotWordAppODTO frontODTO = BeanCloneUtils.copyTo(entry, XdaSearchHotWordAppODTO.class);
		frontODTO.setHotWord(entry.getHotWord());
		frontODTO.setSort(entry.getSortNum());
		return frontODTO;
	}

}
