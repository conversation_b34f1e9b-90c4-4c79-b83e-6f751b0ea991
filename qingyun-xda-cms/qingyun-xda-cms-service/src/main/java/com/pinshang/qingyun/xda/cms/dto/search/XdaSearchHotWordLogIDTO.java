package com.pinshang.qingyun.xda.cms.dto.search;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 热搜词日志查询参数
 */
@Data
public class XdaSearchHotWordLogIDTO extends Pagination {

    @ApiModelProperty("操作类型: 1-新增、2-修改、3-删除")
    private Integer   operType;

    @ApiModelProperty("操作人ID")
    private Long  createId;

    @ApiModelProperty("操作开始日期")
    private String startTime;

    @ApiModelProperty("操作结束日期")
    private String endTime;

}