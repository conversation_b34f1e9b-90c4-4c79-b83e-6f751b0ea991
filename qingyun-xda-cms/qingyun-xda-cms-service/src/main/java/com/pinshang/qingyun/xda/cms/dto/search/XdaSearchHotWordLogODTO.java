package com.pinshang.qingyun.xda.cms.dto.search;

import com.pinshang.qingyun.xda.cms.model.search.XdaSearchHotWordLog;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 热搜词日志
 */
@Data
public class XdaSearchHotWordLogODTO {

	@ApiModelProperty("操作类型: 1-新增、2-修改、3-删除")
	private Integer operType;

	@ApiModelProperty("操作类型描述，页面展示取值")
	private String operTypeDesc;

	@ApiModelProperty("修改前")
	private String oldValue;

	@ApiModelProperty("修改后")
	private String newValue;

	@ApiModelProperty("操作人")
	private String createName;

	@ApiModelProperty("操作时间")
	private Date createTime;

	public String getOperTypeDesc() {
		return XdaSearchHotWordLog.XdaSearchHotWordLogTypeEnums.getName(this.operType);
	}
}
