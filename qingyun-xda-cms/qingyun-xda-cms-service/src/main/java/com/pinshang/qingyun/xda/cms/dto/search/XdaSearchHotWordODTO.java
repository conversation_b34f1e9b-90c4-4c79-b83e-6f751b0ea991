package com.pinshang.qingyun.xda.cms.dto.search;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * WEB热搜词列表返回
 */
@Data
public class XdaSearchHotWordODTO {
	@ApiModelProperty("热搜词ID")
	private Long id;

	@ApiModelProperty("热搜词")
	private String hotWord;

	@ApiModelProperty("特效状态:0=无，1=有")
	private Integer effectStatus;

	@ApiModelProperty("特效状态文本描述：无|有")
	private String effectStatusDesc;

	@ApiModelProperty("前台序号")
	private Integer sortNum;

	public String getEffectStatusDesc() {
		return effectStatus==null?"":(effectStatus==0?"无":"有");
	}

}
