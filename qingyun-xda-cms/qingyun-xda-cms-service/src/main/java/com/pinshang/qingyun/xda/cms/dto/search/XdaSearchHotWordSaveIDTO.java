package com.pinshang.qingyun.xda.cms.dto.search;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 热搜词新增/修改保存参数
 */
@Data
public class XdaSearchHotWordSaveIDTO {

    @ApiModelProperty("热搜词")
    private String hotWord;

    @ApiModelProperty("特效状态:0=无，1=有")
    private Integer   effectStatus;

    @ApiModelProperty("输入的前台排序号，范围：[1,9999]")
    private Integer   sortNum;

    @ApiModelProperty("热搜词ID，修改时必传")
    private Long   id;

    public String getHotWord() {
        return StringUtils.isNotEmpty(this.hotWord) && this.hotWord.length() > 12 ? this.hotWord.substring(0,12) : this.hotWord;
    }

}