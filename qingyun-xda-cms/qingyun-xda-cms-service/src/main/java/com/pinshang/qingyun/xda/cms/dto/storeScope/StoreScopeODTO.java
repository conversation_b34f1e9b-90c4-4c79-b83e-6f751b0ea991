package com.pinshang.qingyun.xda.cms.dto.storeScope;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 鲜达-资源位等-客户范围对象
 */
@Data
@NoArgsConstructor
public class StoreScopeODTO {
	@ApiModelProperty(position = 11, required = true, value = "结账客户、产品价格方案、客户类型、渠道、客户  等ID")
    private String id;
	@ApiModelProperty(position = 12, required = true, value = "结账客户、产品价格方案、客户类型、渠道、客户  等编码")
    private String optionCode;
	@ApiModelProperty(position = 13, required = true, value = "结账客户、产品价格方案、客户类型、渠道、客户  等名称")
    private String optionName;
}
