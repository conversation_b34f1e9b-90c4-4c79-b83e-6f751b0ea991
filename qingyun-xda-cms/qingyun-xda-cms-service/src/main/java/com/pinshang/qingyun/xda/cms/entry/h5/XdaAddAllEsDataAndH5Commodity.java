package com.pinshang.qingyun.xda.cms.entry.h5;

import com.pinshang.qingyun.marketing.dto.app.CommodityODTO;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaStoreSettODTO;

import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @date 2020/12/15 11:36
 */
public class XdaAddAllEsDataAndH5Commodity<T> {

    /***
     *  根据B端客户Id 获取特价商品商品信息
     */
    //private Map<Long, StorePromotionCommodityPriceODTO> promotionToMap;


    /***
     *  根据B端客户Id 获取赠品商品商品信息
     */
   // private Map<Integer,Map<String, XdaGiftProductODTO>> giftToMap;

    /**
     * B端促销、特价商品信息
     */
    private Map<Long, CommodityODTO> commodityPromotionMap;

    /***
     * b端客户信息
     */

    private XdaStoreSettODTO storeSettODTO;

    /***
     * 过滤后的商品相关信息
     */
    private  List<T> commodityItem;

    /***
     * 过滤后的商品id
     */
    private List<Long> commodityIds;

    /***
     * 库存数据
     */
    //private  Map<Long, StockItemODTO> stockMap;


    public List<T> getCommodityItem() {
        return commodityItem;
    }

    public void setCommodityItem(List<T> commodityItem) {
        this.commodityItem = commodityItem;
    }

    public List<Long> getCommodityIds() {
        return commodityIds;
    }

    public void setCommodityIds(List<Long> commodityIds) {
        this.commodityIds = commodityIds;
    }

    /*public Map<Long, StockItemODTO> getStockMap() {
        return stockMap;
    }

    public void setStockMap(Map<Long, StockItemODTO> stockMap) {
        this.stockMap = stockMap;
    }*/

    public XdaStoreSettODTO getStoreSettODTO() {
        return storeSettODTO;
    }

    public void setStoreSettODTO(XdaStoreSettODTO storeSettODTO) {
        this.storeSettODTO = storeSettODTO;
    }

    public Map<Long, CommodityODTO> getCommodityPromotionMap() {
        return commodityPromotionMap;
    }

    public void setCommodityPromotionMap(Map<Long, CommodityODTO> commodityPromotionMap) {
        this.commodityPromotionMap = commodityPromotionMap;
    }
}
