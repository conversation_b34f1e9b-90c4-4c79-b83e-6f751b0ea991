package com.pinshang.qingyun.xda.cms.entry.h5;

import com.pinshang.qingyun.xda.cms.dto.h5.*;
import lombok.Data;

import java.util.List;


@Data
public class XdaH5TemplateDataEntry {

	//模板Id
	private Long templateId;

	//模板Id
	private String templateName;

	//状态:1-启用,0-禁用
	private Integer status;

	//取H5 头图
	private List<XdaH5TemplateListODTO> h5PicList;

	//通栏区数据
	private XdaH5PicODTO h5Pic;

	//图片连接H5
	private XdaH5PicH5ODTO h5PicH5;

	//图片+商品
	private List<XdaH5TemplateListODTO> h5PicCommodityList;

	//tab列表+tab明细
	private List<XdaH5TemplateTabODTO> h5TemplateTabList ;

	//tab列表+tab明细
	private List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityItem;


	//auto 新模板模板对应所需数据信息
	private XdaH5AutoTemplateAllDataEntry xdH5AutoTemplateAllDataEntry;


}
