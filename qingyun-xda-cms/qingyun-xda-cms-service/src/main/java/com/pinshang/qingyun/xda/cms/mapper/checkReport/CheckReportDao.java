package com.pinshang.qingyun.xda.cms.mapper.checkReport;

import com.pinshang.qingyun.xda.cms.dto.checkReport.ReportListDTO;
import com.pinshang.qingyun.xda.cms.model.checkReport.CheckReport;
import com.pinshang.qingyun.xda.cms.vo.ReportListVO;
import com.pinshang.qingyun.xda.cms.vo.XdaMyCheckReportVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CheckReportDao{

	/**
	 * 我的检验报告
	 */
    List<ReportListDTO> queryMyCheckReport(XdaMyCheckReportVO param);


	/**
	 * 查询数据集合
	 */
    List<ReportListDTO> list(ReportListVO param);

	/**
	 * 根据名称查询数据
	 */
    String selectByName(@Param("name") String checkName);

	/**
	 * 新增数据
	 */
    int insert(CheckReport value);

	/**
	 * 逻辑删除数据
	 */
    void updateDelete(CheckReport value);

	/**
	 * 根据字典编码查询字典名称
	 */
	String selectDictName(@Param("dictCode") String dictCode);

}