package com.pinshang.qingyun.xda.cms.mapper.favorPosition;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorXPositionCommodityODTO;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorXPositionCommodity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 横向位-商品
 */
@Mapper
@Repository
public interface XdaFavorXPositionCommodityMapper extends MyMapper<XdaFavorXPositionCommodity> {

    List<XdaFavorXPositionCommodityODTO> selectXPositionCommodityList(@Param("xPositionId") Long xPositionId);

}
