package com.pinshang.qingyun.xda.cms.mapper.favorPosition;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorPositionLogODTO;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorXPositionLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 横向位-日志
 */
@Mapper
@Repository
public interface XdaFavorXPositionLogMapper extends MyMapper<XdaFavorXPositionLog> {
    List<XdaFavorPositionLogODTO> queryXPositionLogList(@Param("xPositionId")Long xPositionId);

}
