package com.pinshang.qingyun.xda.cms.mapper.favorPosition;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorXPositionPageIDTO;
import com.pinshang.qingyun.xda.cms.dto.home.XdaFavorXPositionODTO;
import com.pinshang.qingyun.xda.cms.dto.home.XdaPositionInfoCommodityODTO;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorPositionBaseInfoODTO;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorXPositionInfoODTO;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorXPosition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 横向位
 */
@Mapper
@Repository
public interface XdaFavorXPositionMapper extends MyMapper<XdaFavorXPosition> {
    /**
     * app首页使用
     * @param storeId
     * @return
     */
    List<XdaFavorXPositionODTO> queryFavorXPosition(@Param("storeId") Long storeId);

    List<XdaPositionInfoCommodityODTO> queryFavorXPositionCommodity(@Param("orderTime")Date orderTime,@Param("favorXPositionId") Long favorXPositionId, @Param("storeId") Long storeId);

    /**
     * web分页查询横向位
     * @param vo
     * @return
     */
    List<XdaFavorXPositionInfoODTO> queryFavorXPositionPage(XdaFavorXPositionPageIDTO vo);

    /**
     * 查询特定资源位生效时间有交叉的短期记录ID列表
     * @param positionId
     * @param beginTime
     * @param endTime
     * @return
     */
    List<Long> selectXPositionWithTimeCrossList(@Param("positionId") Integer positionId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    /**
     * web查询横向位详情
     * @param favorPositionId
     * @return
     */
    XdaFavorPositionBaseInfoODTO queryFavorXPositionById(@Param("favorPositionId") Long favorPositionId);




}
