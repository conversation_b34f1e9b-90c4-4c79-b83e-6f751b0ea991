package com.pinshang.qingyun.xda.cms.mapper.favorPosition;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.home.XdaPositionInfoCommodityODTO;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorXPosition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 横向位
 */
@Mapper
@Repository
public interface XdaFavorXPositionMapperV4 extends MyMapper<XdaFavorXPosition> {
    List<XdaPositionInfoCommodityODTO> queryFavorXPositionCommodityV4(@Param("orderTime")Date orderTime,@Param("favorXPositionId") Long favorXPositionId,
                                                                      @Param("storeId") Long storeId, @Param("businessType") Integer businessType,
                                                                      @Param("isPfsStore") Boolean isPfsStore);
}
