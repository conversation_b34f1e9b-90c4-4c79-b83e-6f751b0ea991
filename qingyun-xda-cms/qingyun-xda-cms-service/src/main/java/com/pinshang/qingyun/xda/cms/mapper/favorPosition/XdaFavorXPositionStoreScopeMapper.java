package com.pinshang.qingyun.xda.cms.mapper.favorPosition;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.storeScope.StoreScopeODTO;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorXPositionStoreScope;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 横向位-客户范围
 */
@Mapper
@Repository
public interface XdaFavorXPositionStoreScopeMapper extends MyMapper<XdaFavorXPositionStoreScope> {

    List<StoreScopeODTO> selectSettlementList(@Param("xPositionId")Long xPositionId, @Param("refObjType")Integer refObjType);

    List<StoreScopeODTO> selectProductPriceModelList(@Param("xPositionId")Long xPositionId, @Param("refObjType")Integer refObjType);

    List<StoreScopeODTO> selectStoreList(@Param("xPositionId")Long xPositionId, @Param("refObjType")Integer refObjType);

    List<StoreScopeODTO> selectDictionaryList(@Param("xPositionId")Long xPositionId, @Param("refObjType")Integer refObjType);


}
