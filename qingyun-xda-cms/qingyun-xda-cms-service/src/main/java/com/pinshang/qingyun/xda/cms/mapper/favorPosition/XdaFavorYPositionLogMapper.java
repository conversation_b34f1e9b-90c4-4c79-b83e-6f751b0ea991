package com.pinshang.qingyun.xda.cms.mapper.favorPosition;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorPositionLogODTO;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorYPositionLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 纵向位-日志
 */
@Mapper
@Repository
public interface XdaFavorYPositionLogMapper extends MyMapper<XdaFavorYPositionLog> {
    List<XdaFavorPositionLogODTO> queryYPositionLogList(@Param("yPositionId")Long yPositionId);

}
