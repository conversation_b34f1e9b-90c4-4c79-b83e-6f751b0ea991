package com.pinshang.qingyun.xda.cms.mapper.favorPosition;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorPositionBaseInfoODTO;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorYPositionInfoODTO;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorYPositionPageIDTO;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorYPosition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 纵向位
 */
@Mapper
@Repository
public interface XdaFavorYPositionMapper extends MyMapper<XdaFavorYPosition> {

    List<XdaFavorYPositionInfoODTO> queryFavorYPositionPage(XdaFavorYPositionPageIDTO vo);

    List<Long> selectYPositionWithTimeCrossList(@Param("positionId") Integer positionId, @Param("beginTime") Date beginTime, @Param("endTime") Date endTime);

    XdaFavorPositionBaseInfoODTO queryFavorYPositionById(@Param("favorPositionId") Long favorPositionId);

    XdaFavorYPositionInfoODTO queryFavorYPositionDetail(@Param("yPositionId") Long yPositionId);

    List<XdaFavorYPositionInfoODTO> queryHomeFavorYPositionPage(@Param("storeId") Long storeId);

    List<Long> selectYPositionCommodityList(@Param("orderTime")Date orderTime,@Param("storeId") Long storeId);

}
