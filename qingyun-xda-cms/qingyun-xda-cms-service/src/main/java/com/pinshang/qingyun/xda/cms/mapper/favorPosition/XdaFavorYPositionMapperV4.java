package com.pinshang.qingyun.xda.cms.mapper.favorPosition;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorYPosition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 纵向位
 */
@Mapper
@Repository
public interface XdaFavorYPositionMapperV4 extends MyMapper<XdaFavorYPosition> {

    List<Long> selectYPositionCommodityListV4(@Param("orderTime") Date orderTime,
                                              @Param("storeId") Long storeId,
                                              @Param("businessType") Integer businessType
    );

}
