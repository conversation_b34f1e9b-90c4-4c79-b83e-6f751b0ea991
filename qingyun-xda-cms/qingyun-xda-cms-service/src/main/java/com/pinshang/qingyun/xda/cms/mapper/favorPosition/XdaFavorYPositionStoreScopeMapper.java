package com.pinshang.qingyun.xda.cms.mapper.favorPosition;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.storeScope.StoreScopeODTO;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorYPositionStoreScope;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 纵向位-客户范围
 */
@Mapper
@Repository
public interface XdaFavorYPositionStoreScopeMapper extends MyMapper<XdaFavorYPositionStoreScope> {
    List<StoreScopeODTO> selectSettlementList(@Param("yPositionId")Long yPositionId, @Param("refObjType")Integer refObjType);

    List<StoreScopeODTO> selectProductPriceModelList(@Param("yPositionId")Long yPositionId, @Param("refObjType")Integer refObjType);

    List<StoreScopeODTO> selectStoreList(@Param("yPositionId")Long yPositionId, @Param("refObjType")Integer refObjType);

    List<StoreScopeODTO> selectDictionaryList(@Param("yPositionId")Long yPositionId, @Param("refObjType")Integer refObjType);


}
