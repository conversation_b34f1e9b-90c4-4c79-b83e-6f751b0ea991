package com.pinshang.qingyun.xda.cms.mapper.h5;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5PicCommodityODTO;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5PicCommodity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description:
 * @author: hhf
 * @time: 2020/12/11 15:37
 */
@Repository
@Mapper
public interface XdaH5PicCommodityMapper extends MyMapper<XdaH5PicCommodity> {

    /**
     * 查询H5图片+商品集合
     * @param h5TemplateId
     * @return
     */
    List<XdaH5PicCommodityODTO> findXdaH5PicCommodityListByH5TemplateId(@Param("h5TemplateId")Long h5TemplateId);
}
