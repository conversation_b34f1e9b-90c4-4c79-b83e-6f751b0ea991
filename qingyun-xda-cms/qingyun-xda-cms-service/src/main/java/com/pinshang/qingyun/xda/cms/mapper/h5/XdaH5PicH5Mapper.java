package com.pinshang.qingyun.xda.cms.mapper.h5;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5PicH5ODTO;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5PicH5;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * Created by hhf on 2019/11/18.
 */
@Repository
@Mapper
public interface XdaH5PicH5Mapper extends MyMapper<XdaH5PicH5> {
    /**
     * 查询H5图片+h5
     * @param h5TemplateId
     * @return
     */
    XdaH5PicH5ODTO findXdaH5PicH5ByH5TemplateId(@Param("h5TemplateId") Long h5TemplateId);
}
