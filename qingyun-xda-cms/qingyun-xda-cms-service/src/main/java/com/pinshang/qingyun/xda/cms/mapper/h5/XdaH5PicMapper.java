package com.pinshang.qingyun.xda.cms.mapper.h5;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5PicODTO;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5Pic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * Created by hhf on 2019/11/18.
 * H5模板图片
 */
@Repository
@Mapper
public interface XdaH5PicMapper extends MyMapper<XdaH5Pic> {

    /**
     * 查询头图详细信息
     * @param h5TemplateId
     * @return
     */
    XdaH5PicODTO findXdaH5PicEntryByH5TemplateId(@Param("h5TemplateId") Long h5TemplateId, @Param("positionLevel") Integer positionLevel);

}
