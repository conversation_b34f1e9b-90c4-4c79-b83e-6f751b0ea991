package com.pinshang.qingyun.xda.cms.mapper.h5;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateCommodityListODTO;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5TemplateCommodityList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description:
 * @author: hhf
 * @time: 2020/12/14 10:54
 */
@Repository
@Mapper
public interface XdaH5TemplateCommodityListMapper extends MyMapper<XdaH5TemplateCommodityList> {

    /**
     * 鲜达H5模板商品集合
     * @param templateCommodityId
     * @return
     */
    List<XdaH5TemplateCommodityListODTO> findXdaH5TemplateCommodityListByTemplateCommodityId(@Param("templateCommodityId") Long templateCommodityId);

}
