package com.pinshang.qingyun.xda.cms.mapper.h5;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.h5.*;
import com.pinshang.qingyun.xda.cms.entry.h5.XdaH5TemplateTemplateCommoditysEntry;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5TemplateCommodity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by hhf on 2020/4/26.
 */
@Repository
@Mapper
public interface XdaH5TemplateCommodityMapper extends MyMapper<XdaH5TemplateCommodity> {

    /***
     * 获取商品信息
     * @param templateCommodityIds
     * @param isPfsStore
     * @return
     */
    List<XdaH5TemplateTabCommodityODTO> getCommodityData(@Param("templateCommodityIds") List<Long> templateCommodityIds, @Param("isPfsStore") Boolean isPfsStore);

    /***
     * 获取主模板信息
     * @param templateId
     * @return
     */
    List<XdaH5TemplateTemplateCommoditysEntry> getTemplateCommodityByTemplateId(@Param("templateId") Long templateId);

    /***
     * 根据B端客户id和所属的公司Id
     * 查询产品价格方案，结结账客户
     */
    List<XdaStoreSettODTO> selectStoreSettByStoreId(@Param("storeId")Long storeId);

    /***
     * 根据B端客户id和所属的公司Id
     * 查询产品价格方案Id，获取产品价格方案下的商品
     */
    List<XdaStoreProductPriceODTO> selectStoreProductPriceCommodityById(@Param("id")Long id);


    /***
     * 根据所属公司获取特价商品
     */
    List<XdaPromotionProductPriceODTO> selectPromotionProductPriceCommodity(XdaPromotionProductPriceIDTO idto);

    /***
     * 根据所属公司获取赠品商品
     */
    List<XdaGiftProductODTO> selectGiftProductCommodity(XdaPromotionProductPriceIDTO idto);

    /***
     * 获取后台预览时查询客户
     * @return
     */
    List<XdaStoreDataODTO> selectStoreDataByCodeOrName(XdaStoreDataIDTO idto);

}
