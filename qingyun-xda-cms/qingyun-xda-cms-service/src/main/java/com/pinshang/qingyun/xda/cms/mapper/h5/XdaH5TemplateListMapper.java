package com.pinshang.qingyun.xda.cms.mapper.h5;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateListODTO;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabODTO;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5TemplateList;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by hhf on 2019/11/18.
 */
@Repository
@Mapper
public interface XdaH5TemplateListMapper extends MyMapper<XdaH5TemplateList> {

    /**
     * 查询H5模板内容项-H5图片
     * @param templateId
     * @return
     */
    List<XdaH5TemplateListODTO> findXdH5PicListByTemplateId(@Param("templateId") Long templateId, @Param("resourceType") Integer resourceType);

    List<XdaH5TemplateListODTO> findXdH5PicListByH5TemplateId(@Param("templateId") Long templateId, @Param("resourceType") Integer resourceType, @Param("positionLevelSort") Integer positionLevelSort);
    /**
     * 查询H5模板内容项-H5图片连接商品
     * @param templateId
     * @return
     */
    List<XdaH5TemplateListODTO> findXdH5PicCommodityListByTemplateId(@Param("templateId") Long templateId, @Param("resourceType") Integer resourceType);
    /**
     * 查询H5模板内容项-H5图片连接H5
     * @param templateId
     * @return
     */
    List<XdaH5TemplateListODTO> findXdH5PicH5ListByTemplateId(@Param("templateId") Long templateId, @Param("resourceType") Integer resourceType);
    /**
     * 查询H5模板内容项-tab列表
     * @param templateId
     * @return
     */
    List<XdaH5TemplateTabODTO> findXdH5TemplateTabListByTemplateId(@Param("templateId") Long templateId, @Param("resourceType") Integer resourceType);


}
