package com.pinshang.qingyun.xda.cms.mapper.h5;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateLogIDTO;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateLogODTO;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5TemplateLog;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description:
 * @author: hhf
 * @time: 2020/12/10 16:05
 */
@Repository
@Mapper
public interface XdaH5TemplateLogMapper extends MyMapper<XdaH5TemplateLog> {

    /**
     * 鲜达-H5模板日志列表
     * @param idto
     * @return
     */
    List<XdaH5TemplateLogODTO> findXdaH5TemplateLogListByParams(XdaH5TemplateLogIDTO idto);
}
