package com.pinshang.qingyun.xda.cms.mapper.h5;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateDetailsODTO;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateIDTO;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateODTO;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5AddSelectCommodityODTO;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5Template;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5AddSelectCommoditySearchIDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by hhf on 2019/11/18.
 * h5 模板
 */
@Repository
@Mapper
public interface XdaH5TemplateMapper extends MyMapper<XdaH5Template> {

    /**
     * 条件查询H5模板列表
     * @param idto
     * @retur
     **/
    List<XdaH5TemplateODTO> findXdaH5TemplateListByParams(XdaH5TemplateIDTO idto);

    /**
     * 根据id 查询H5 详情
     * @param id
     * @return
     */
    XdaH5TemplateDetailsODTO findXdaH5TemplateDetailsById(@Param("id") Long id);


    /**
     * 鲜达H5-选择商品,条件查询商品列表
     * @param vo
     * @return
     */
    List<XdaH5AddSelectCommodityODTO> addXdaH5SelectCommodityByParams(XdaH5AddSelectCommoditySearchIDTO vo);

    /**
     * 添加H5 根据商品编码集合查询商品信息
     * @param commodityCodeList
     * @return
     */
    List<XdaH5AddSelectCommodityODTO> addXdaH5QueryCommodityDetailsByCommodityCodeList(@Param("commodityCodeList") List<String> commodityCodeList);

    List<XdaH5AddSelectCommodityODTO> addPfH5QueryCommodityDetailsByCommodityCodeList(@Param("commodityCodeList") List<String> commodityCodeList);

    /**
     * 添加H5 根据商品编码集合查询商品信息
     * @param commodityCode
     * @return
     */
    List<XdaH5AddSelectCommodityODTO> addXdaH5QueryCommodityByCommodityCode(@Param("commodityCode") String commodityCode);
}
