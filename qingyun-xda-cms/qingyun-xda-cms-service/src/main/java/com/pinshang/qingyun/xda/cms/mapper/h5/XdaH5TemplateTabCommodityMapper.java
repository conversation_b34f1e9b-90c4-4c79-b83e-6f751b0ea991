package com.pinshang.qingyun.xda.cms.mapper.h5;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabCommodityODTO;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5TemplateTabCommodity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/10 14:25:26
 */
@Repository
@Mapper
public interface XdaH5TemplateTabCommodityMapper extends MyMapper<XdaH5TemplateTabCommodity> {

    /**
     * 商品编码集合查询商品id
     *
     * @param commodityCodeList
     * @return
     */
    List<XdaH5TemplateTabCommodity> findCommodityIdByCommodityCodeList(@Param("commodityCodeList") List<String> commodityCodeList);

    List<XdaH5TemplateTabCommodityODTO> findCommodityListByCommodityCodeAndShopId(@Param("commodityCodeList") List<String> commodityCodeList, @Param("showNum") Integer showNum);

    List<XdaH5TemplateTabCommodityODTO> findCommodityIdByCommodityIdList(@Param("commodityIdList") List<Long> commodityIdList);


    /**
     * 根据模板id查询商品明细
     *
     * @param h5TemplateId
     * @return
     */
    List<XdaH5TemplateTabCommodityODTO> findXdaH5TemplateTabCommodityListByH5TemplateId(@Param("h5TemplateId") Long h5TemplateId);

    List<XdaH5TemplateTabCommodityODTO> findXdH5TemplateCommodityListByH5TemplateId(@Param("templateId") Long templateId, @Param("resourceType") Integer resourceType, @Param("positionLevelSort") Integer positionLevelSort);


    /**
     * tabId 查询 tab区 商品详细
     *
     * @param tabId
     * @return
     */
    List<XdaH5TemplateTabCommodityODTO> findXdaH5TemplateTabCommodityListByTabId(@Param("tabId") Long tabId);

    List<XdaH5TemplateTabCommodityODTO> findH5TemplateTabCommodityListByTabId(@Param("templateId") Long templateId, @Param("tabId") Long tabId, @Param("resourceType") Integer resourceType);

    List<XdaH5TemplateTabCommodityODTO> findXdH5TemplateTabCommodityListByTabIdAndShowNum(@Param("templateId") Long templateId, @Param("tabId") Long tabId, @Param("resourceType") Integer resourceType, @Param("showNum") Integer showNum);


    List<XdaH5TemplateTabCommodityODTO> findCommodityListByCommodityIds(@Param("commodityIdList") List<Long> commodityIdList);


}
