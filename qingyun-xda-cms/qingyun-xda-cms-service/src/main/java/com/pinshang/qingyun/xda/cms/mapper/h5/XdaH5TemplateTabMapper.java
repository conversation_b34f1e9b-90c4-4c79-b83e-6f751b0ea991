package com.pinshang.qingyun.xda.cms.mapper.h5;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabODTO;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5TemplateTab;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description:
 * @author: hhf
 * @time: 2020/12/11 15:39
 */
@Repository
@Mapper
public interface XdaH5TemplateTabMapper extends MyMapper<XdaH5TemplateTab> {

    /**
     * 查询Tab列表
     * @param h5TemplateId
     * @return
     */
    List<XdaH5TemplateTabODTO> findXdaH5TemplateTabListByByH5TemplateId(@Param("h5TemplateId")Long h5TemplateId);
}
