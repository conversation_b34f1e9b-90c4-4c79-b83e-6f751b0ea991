package com.pinshang.qingyun.xda.cms.mapper.h5;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateUrlODTO;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5TemplateUrl;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * Created by hhf on 2019/11/18.
 * h5 模板
 */
@Repository
@Mapper
public interface XdaH5TemplateUrlMapper extends MyMapper<XdaH5TemplateUrl> {

    /**
     * 通过模板ID查询H5对应的页面URL信息
     * @param templateId
     * @return
     */
    XdaH5TemplateUrlODTO findXdaH5TemplateUrlByTemplateId(@Param("templateId") Long templateId);
}
