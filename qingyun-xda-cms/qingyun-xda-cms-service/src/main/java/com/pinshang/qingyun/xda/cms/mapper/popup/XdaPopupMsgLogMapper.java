package com.pinshang.qingyun.xda.cms.mapper.popup;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.popup.XdaPopupMsgLogODTO;
import com.pinshang.qingyun.xda.cms.model.popup.XdaPopupMsgLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description:
 * @author: hhf
 * @time: 2020/12/9 14:03
 */
@Repository
@Mapper
public interface XdaPopupMsgLogMapper extends MyMapper<XdaPopupMsgLog> {

    /**
     * 弹框通知id 查询日志
     * @param popupMsgId
     * @return
     */
    List<XdaPopupMsgLogODTO> findXdaPopupMsgLogListByPopupMsgId(@Param("popupMsgId") Long popupMsgId);
}
