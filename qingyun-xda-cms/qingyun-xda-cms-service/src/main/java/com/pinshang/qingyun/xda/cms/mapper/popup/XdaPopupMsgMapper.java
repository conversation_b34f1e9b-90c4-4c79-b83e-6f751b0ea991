package com.pinshang.qingyun.xda.cms.mapper.popup;

import java.util.Date;
import java.util.List;

import com.pinshang.qingyun.xda.cms.dto.home.XdaPopupMsgAppODTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.popup.XdaPopupMsgIDTO;
import com.pinshang.qingyun.xda.cms.dto.popup.XdaPopupMsgODTO;
import com.pinshang.qingyun.xda.cms.dto.popup.XdaPopupMsgStoreScopeIDTO;
import com.pinshang.qingyun.xda.cms.model.popup.XdaPopupMsg;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description: 鲜达-弹框通知Mapper
 * @author: hhf
 * @time: 2020/12/9 13:36
 */
@Repository
@Mapper
public interface XdaPopupMsgMapper extends MyMapper<XdaPopupMsg> {

    /**
     * 弹框通知列表
     * @param xdPopupMsgIDTO
     * @return
     */
    List<XdaPopupMsgODTO> findXdPopupMsgListByParams(XdaPopupMsgIDTO xdPopupMsgIDTO);

    /**
     * 根据客户id查询客户类型id和渠道id
     * @param storeId
     * @return
     */
    XdaPopupMsgStoreScopeIDTO selectStoreTypeIdAndStoreChannelIdInfoById(@Param("storeId") Long storeId);

    /**
     * 根据客户id查询结账客户id和产品价格方案id
     * @param storeId
     * @return
     */
    List<XdaPopupMsgStoreScopeIDTO> selectSettlementIdAndProductPriceModelIdById(@Param("storeId") Long storeId);

    XdaPopupMsgAppODTO queryXdPopupMsgForApp(@Param("storeId") Long storeId, @Param("nowTime") Date nowTime);

}
