package com.pinshang.qingyun.xda.cms.mapper.popup;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.storeScope.DictionaryODTO;
import com.pinshang.qingyun.xda.cms.dto.storeScope.ProductPriceModelODTO;
import com.pinshang.qingyun.xda.cms.dto.storeScope.SettlementODTO;
import com.pinshang.qingyun.xda.cms.dto.storeScope.StoreODTO;
import com.pinshang.qingyun.xda.cms.model.popup.XdaPopupMsgStoreScope;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description:
 * @author: hhf
 * @time: 2020/12/9 15:37
 */
@Repository
@Mapper
public interface XdaPopupMsgStoreScopeMapper extends MyMapper<XdaPopupMsgStoreScope> {

    /**
     * 弹框通知id 查询结账客户
     * @param popupMsgId
     * @return
     */
    List<SettlementODTO> findSettlementByPopupMsgIdAndRefObjType(@Param("popupMsgId") Long popupMsgId,@Param("refObjType")Integer refObjType);

    /**
     * 弹框通知id 查询结账客户
     * @param popupMsgId
     * @return
     */
    List<ProductPriceModelODTO> findProductPriceModelByPopupMsgIdAndRefObjType(@Param("popupMsgId") Long popupMsgId,@Param("refObjType")Integer refObjType);

    /**
     * 弹框通知id 查询产品价格方案
     * @param popupMsgId
     * @return
     */
    List<StoreODTO> findStoreByPopupMsgIdAndRefObjType(@Param("popupMsgId") Long popupMsgId,@Param("refObjType")Integer refObjType);

    StoreODTO findStoreById(@Param("storeId") Long storeId);

    /**
     * 弹框通知id 查询渠道和客户类型
     * @param popupMsgId
     * @return
     */
    List<DictionaryODTO> findDictionaryByPopupMsgIdAndRefObjType(@Param("popupMsgId") Long popupMsgId,@Param("refObjType")Integer refObjType);

}
