package com.pinshang.qingyun.xda.cms.mapper.position;

import com.pinshang.qingyun.xda.cms.dto.home.XdaBlockODTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.model.position.XdaPositionInfoBlock;

import java.util.List;

/**
 * 鲜达资源位-积木2
 */
@Mapper
@Repository
public interface XdaPositionInfoBlockMapper extends MyMapper<XdaPositionInfoBlock> {

    List<XdaBlockODTO> selectSubBlockList(@Param("positionInfoIds") List<Long> positionInfoIds);
}
