package com.pinshang.qingyun.xda.cms.mapper.position;

import java.util.List;
import java.util.Map;

import com.pinshang.qingyun.xda.cms.dto.home.XdaPositionInfoCommodityODTO;
import com.pinshang.qingyun.xda.cms.vo.XdaFlashSaleCommodityStockVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.position.PositionCommodityInfoODTO;
import com.pinshang.qingyun.xda.cms.model.position.XdaPositionInfoCommodity;

/**
 * 鲜达资源位-商品
 */
@Mapper
@Repository
public interface XdaPositionInfoCommodityMapper extends MyMapper<XdaPositionInfoCommodity> {
	
	/**
     * 查询  商品信息  列表
     * 
     * @param map
     * @return
     */
    public PositionCommodityInfoODTO selectCommodityInfoList(Map<String, Object> map);

    /**
     * 查询  资源位商品信息  列表
     * 
     * @param positionInfoId
     * @return
     */
	public List<PositionCommodityInfoODTO> selectPositionCommodityInfoList(@Param("positionInfoId") Long positionInfoId);

    /**
     * 查询资源位的商品明细
     * @param positionInfoIds 资源位ids
     * @param limit 条数
     * @return
     */
    List<XdaPositionInfoCommodityODTO> selectPositionCommodityList(@Param("positionInfoIds") List<Long> positionInfoIds, @Param("limit") Integer limit);

    List<XdaFlashSaleCommodityStockVO> queryCommodityStock(@Param("shopId") Long shopId);
}
