package com.pinshang.qingyun.xda.cms.mapper.position;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.position.PositionInfoLogInfoODTO;
import com.pinshang.qingyun.xda.cms.model.position.XdaPositionInfoLog;

/**
 * 鲜达资源位信息-日志
 */
@Mapper
@Repository
public interface XdaPositionInfoLogMapper extends MyMapper<XdaPositionInfoLog> {

	/**
     * 查询  资源位信息-日志信息  列表
     * 
     * @param vo
     * @return
     */
    public List<PositionInfoLogInfoODTO> selectPositionInfoLogInfoList(@Param("positionInfoId")Long positionInfoId);

}
