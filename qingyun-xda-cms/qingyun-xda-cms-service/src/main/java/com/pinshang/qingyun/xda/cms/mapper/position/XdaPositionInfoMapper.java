package com.pinshang.qingyun.xda.cms.mapper.position;

import java.util.List;

import com.pinshang.qingyun.xda.cms.dto.home.XdaPositionInfoItemODTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.position.PositionInfoODTO;
import com.pinshang.qingyun.xda.cms.dto.position.SelectPositionInfoPageIDTO;
import com.pinshang.qingyun.xda.cms.model.position.XdaPositionInfo;

/**
 * 鲜达资源位
 */
@Mapper
@Repository
public interface XdaPositionInfoMapper extends MyMapper<XdaPositionInfo> {

    /**
     * 查询门店有效资源位列表
     * @return
     */
    List<XdaPositionInfoItemODTO> selectShopPositionList(@Param("storeId") Long storeId);
	
	/**
     * 查询  资源位信息  列表
     * 
     * @param idto
     * @return
     */
    public List<PositionInfoODTO> selectPositionInfoList(SelectPositionInfoPageIDTO idto);

}
