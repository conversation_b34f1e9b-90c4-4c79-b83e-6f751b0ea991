package com.pinshang.qingyun.xda.cms.mapper.position;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.storeScope.StoreScopeODTO;
import com.pinshang.qingyun.xda.cms.model.position.XdaPositionInfoStoreScope;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 鲜达资源位-客户范围
 */
@Mapper
@Repository
public interface XdaPositionInfoStoreScopeMapper extends MyMapper<XdaPositionInfoStoreScope> {
	
	/**
     * 查询  资源位-结账客户  列表
     * 
     * @param popupMsgId
     * @param refObjType
     * @return
     */
    public List<StoreScopeODTO> selectSettlementList(@Param("positionInfoId")Long positionInfoId, @Param("refObjType")Integer refObjType);

    /**
     * 查询  资源位-产品价格方案  列表
     * 
     * @param popupMsgId
     * @param refObjType
     * @return
     */
    public List<StoreScopeODTO> selectProductPriceModelList(@Param("positionInfoId")Long positionInfoId, @Param("refObjType")Integer refObjType);

    /**
     * 查询  资源位-客户  列表
     * 
     * @param popupMsgId
     * @param refObjType
     * @return
     */
    public List<StoreScopeODTO> selectStoreList(@Param("positionInfoId")Long positionInfoId, @Param("refObjType")Integer refObjType);

    /**
     * 查询  资源位-客户类型/渠道等  列表
     * 
     * @param popupMsgId
     * @param refObjType
     * @return
     */
    public List<StoreScopeODTO> selectDictionaryList(@Param("positionInfoId")Long positionInfoId, @Param("refObjType")Integer refObjType);

    public List<String> selectOrderTimeList(@Param("storeId") Long storeId, @Param("isPfsStore") Boolean isPfsStore);

    public List<Long> selectCommodityPriceModelByStore(@Param("storeId") Long storeId, @Param("isPfsStore") Boolean isPfsStore);

}
