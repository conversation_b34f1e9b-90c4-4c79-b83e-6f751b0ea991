package com.pinshang.qingyun.xda.cms.mapper.qa;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.qa.XdaAppQaFrontODTO;
import com.pinshang.qingyun.xda.cms.dto.qa.XdaAppQaIDTO;
import com.pinshang.qingyun.xda.cms.dto.qa.XdaAppQaODTO;
import com.pinshang.qingyun.xda.cms.model.qa.XdaAppQa;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description:
 * @author: hhf
 * @time: 2020/12/16 10:59
 */
@Repository
@Mapper
public interface XdaAppQaMapper extends MyMapper<XdaAppQa> {

    /**
     * 鲜达App常见问题列表
     * @param xdaAppQaIDTO
     * @return
     */
    List<XdaAppQaODTO> findXdaAppQaList(XdaAppQaIDTO xdaAppQaIDTO);

    /**
     * 鲜达App常见问题列表 简要信息
     * status=1
     * @return
     */
    List<XdaAppQaFrontODTO> findListForFront();
}
