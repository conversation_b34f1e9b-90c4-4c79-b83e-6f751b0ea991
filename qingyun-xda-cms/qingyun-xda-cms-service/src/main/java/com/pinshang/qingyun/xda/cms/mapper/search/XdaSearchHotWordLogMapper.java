package com.pinshang.qingyun.xda.cms.mapper.search;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordLogIDTO;
import com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordLogODTO;
import com.pinshang.qingyun.xda.cms.model.search.XdaSearchHotWordLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XdaSearchHotWordLogMapper extends MyMapper<XdaSearchHotWordLog> {
    List<XdaSearchHotWordLogODTO> queryHotWordLogPage(@Param("vo") XdaSearchHotWordLogIDTO vo);
}
