package com.pinshang.qingyun.xda.cms.mapper.search;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordODTO;
import com.pinshang.qingyun.xda.cms.model.search.XdaSearchHotWord;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface XdaSearchHotWordMapper extends MyMapper<XdaSearchHotWord> {
    //查询热搜词数量
    int queryCount(@Param("hotWord")String hotWord);

    //获取热搜词最大序号
    Integer getMaxSortNum();

    //查询热搜词列表
    List<XdaSearchHotWordODTO> queryHotWordList();

}
