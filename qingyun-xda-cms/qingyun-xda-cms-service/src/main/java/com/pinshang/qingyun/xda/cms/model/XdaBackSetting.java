package com.pinshang.qingyun.xda.cms.model;

import com.pinshang.qingyun.base.po.BasePO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * Created by hhf on 2019/12/2.
 * 鲜到后台设置
 */
@Data
@NoArgsConstructor
@Table(name = "t_xda_back_setting")
public class XdaBackSetting extends BasePO {

    /**运费设置**/
    private BigDecimal freight;//运费
    private BigDecimal orderAmount;//订单金额达到x元,免运费

    /**400电话设置**/
    private String phone; //400电话号码
    private String phoneTime;//400电话有效时间

    /**超时未付款自动取消订单设置**/
    private Integer timeoutMinutes;//下单后超过X分钟，订单自动取消

    /**送货时间设置**/
    private Integer deliveryTimeDay;  //用户可选择X天时间段
    private Integer deliveryTimeDelivered; //付款后 X时间送达

    /**可配送距离设置**/
    private BigDecimal deliveryCoverage; //前置仓周围 X 千米内

    /**自动退货时间设置**/
    private Integer autoReturnTime; //自动退货 X 分钟

    /**配送超时赔付**/
    private Integer compensationTime; //完成 超 X 分钟
    private Integer compensationScore; //赔付 X 积分

    /**完成配送地址围栏**/
    private Integer addressRange; //完成配送必须在收货地址 X 米范围内

    /**配送员订单上线**/
    private Integer orderUpperLimit; //配送员订单上限 X 单

    //'距时间段结束最少时间:例如50分钟, 8:11下单 则首选时间段: 9:00 ~10:00, 8:03下单 则首选时间段:8:00~9:00',
    private Integer orderDeliveryMin;
    //'开始配送时间(9:00开始配送)',
    private String beginDeliveryTime;
    // '结束配送时间(21:00开始配送)',
    private String endDeliveryTime;
    //'配送时间区间:可选时间段起始到结束的时长,例如60分钟  8:00~9:00, 9:00~10:00 例如30分钟 8:00~8:30, 8:30~9:00',
    private Integer deliveryTimeInterval;
    //'可是开始的时间段自然时间点间隔,例如15分钟 则:8:00~9:00, 8:15~9:15, 8:30~9:30, 8:45~9:45. 例如 20分钟 则8:00~9:00 , 8:20~9:20, 8:40~9:40.',
    private Integer deliveryTimeStartInterval;

    // 特价互斥开关: 1-互斥,2-不互斥
    private Integer promotionMutexSwitch;

    //拣货单预警开始时间
    private Integer distributionBeginWarning;

    //拣货单预警结束时间
    private Integer distributionEndWarning;

    //配送单预警时间
    private Integer deliveryEndWarning;

}
