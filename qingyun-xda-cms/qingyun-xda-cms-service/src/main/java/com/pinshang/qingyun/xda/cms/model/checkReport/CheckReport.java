package com.pinshang.qingyun.xda.cms.model.checkReport;

import java.math.BigDecimal;
import java.util.Date;

public class CheckReport {
    private Long id;
    private Long updateId;//修改人ID
    private String fileUrl;//文件URL
    private java.util.Date createTime;//创建时间
    private String dictCode;//类型的key
    private java.util.Date updateTime;//修改时间
    private String dictName;//报告类型的名称
    private Long createId;//创建人ID
    private java.util.Date reportTime;//报告日期
    private String checkUser;//检验人
    private String checkName;//检验报告名称
    private String createUser;//创建人
    private String reviewUser;//复核人
    private Integer delete;//是否删除
    private String updateUser;//修改人
    private BigDecimal fileSize;//文件大小

    public CheckReport() {
        super();
    }

    public CheckReport(Long id, Long updateId, String fileUrl, Date createTime, String dictCode, Date updateTime, String dictName, Long createId, Date reportTime, String checkUser, String checkName, String createUser, String reviewUser, Integer delete, String updateUser, BigDecimal fileSize) {
        this.id = id;
        this.updateId = updateId;
        this.fileUrl = fileUrl;
        this.createTime = createTime;
        this.dictCode = dictCode;
        this.updateTime = updateTime;
        this.dictName = dictName;
        this.createId = createId;
        this.reportTime = reportTime;
        this.checkUser = checkUser;
        this.checkName = checkName;
        this.createUser = createUser;
        this.reviewUser = reviewUser;
        this.delete = delete;
        this.updateUser = updateUser;
        this.fileSize = fileSize;
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUpdateId() {
        return this.updateId;
    }

    public void setUpdateId(Long updateId) {
        this.updateId = updateId;
    }

    public String getFileUrl() {
        return this.fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
    }

    public String getDictCode() {
        return this.dictCode;
    }

    public void setDictCode(String dictCode) {
        this.dictCode = dictCode;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getDictName() {
        return this.dictName;
    }

    public void setDictName(String dictName) {
        this.dictName = dictName;
    }

    public Long getCreateId() {
        return this.createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public java.util.Date getReportTime() {
        return this.reportTime;
    }

    public void setReportTime(java.util.Date reportTime) {
        this.reportTime = reportTime;
    }

    public String getCheckUser() {
        return this.checkUser;
    }

    public void setCheckUser(String checkUser) {
        this.checkUser = checkUser;
    }

    public String getCheckName() {
        return this.checkName;
    }

    public void setCheckName(String checkName) {
        this.checkName = checkName;
    }

    public String getCreateUser() {
        return this.createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public String getReviewUser() {
        return this.reviewUser;
    }

    public void setReviewUser(String reviewUser) {
        this.reviewUser = reviewUser;
    }

    public Integer getDelete() {
        return delete;
    }

    public void setDelete(Integer delete) {
        this.delete = delete;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public BigDecimal getFileSize() {
        return fileSize;
    }

    public void setFileSize(BigDecimal fileSize) {
        this.fileSize = fileSize;
    }
}
