package com.pinshang.qingyun.xda.cms.model.favorPosition;

import com.pinshang.qingyun.base.enums.XSAppPositionInfoStatusEnums;
import com.pinshang.qingyun.base.po.BasePO;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorXPositionSaveIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.InsertPositionInfoIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.UpdatePositionInfoIDTO;
import com.pinshang.qingyun.xda.cms.model.position.XdaPositionInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.Date;

/**
 * 鲜达--横向位--主表
 */
@Data
@Table(name = "t_xda_favor_x_position")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class XdaFavorXPosition extends BasePO {

    // 资源位(XSAppPositionIdEnums.X_FAVOR_02，X_FAVOR_03等)
    private Integer positionId;

    // 期限类型：1-长期、2-短期	【TermTypeEnums】
    private Integer termType;

    // 是否适用 App 渠道  1＝是，0＝否
    private Integer appChannel;

    // 是否适用 小程序 渠道 1＝是，0＝否
    private Integer miniChannel;

    // 是否所有客户：0-全部客户、1-部分客户
    private Integer isAllStore;

    // 标题
    private String title;

    //副标题
    private String subTitle;

    // 状态：1-启用、2-停用、3-过期
    private Integer status;

    // 开始时间、结束时间
    private Date beginTime;
    private Date endTime;

    public static XdaFavorXPosition forInsert(XdaFavorXPositionSaveIDTO vo, Date beginTime, Date endTime) {
        XdaFavorXPosition xPosition = new XdaFavorXPosition();
        SpringUtil.copyProperties(vo,xPosition);
        xPosition.setCreateId(vo.getUserId());
        xPosition.setCreateTime(new Date());
        xPosition.setUpdateId(vo.getUserId());
        xPosition.setUpdateTime(new Date());
        xPosition.beginTime = beginTime;
        xPosition.endTime = endTime;
        xPosition.status = XSAppPositionInfoStatusEnums.启用.getCode();
        return xPosition;
    }

    public static void forUpdate(XdaFavorXPosition xPosition, XdaFavorXPositionSaveIDTO vo, Date beginTime, Date endTime) {
        SpringUtil.copyProperties(vo,xPosition);
        xPosition.setUpdateId(vo.getUserId());
        xPosition.setUpdateTime(new Date());
        xPosition.beginTime = beginTime;
        xPosition.endTime = endTime;
    }

    public static XdaFavorXPosition forUpdateStatus(Long positionInfoId, Integer newStatus, Long updateId) {
        XdaFavorXPosition xPosition = new XdaFavorXPosition();
        xPosition.id = positionInfoId;
        xPosition.status = newStatus;
        xPosition.setUpdateId(updateId);
        xPosition.setUpdateTime(new Date());
        return xPosition;
    }



}
