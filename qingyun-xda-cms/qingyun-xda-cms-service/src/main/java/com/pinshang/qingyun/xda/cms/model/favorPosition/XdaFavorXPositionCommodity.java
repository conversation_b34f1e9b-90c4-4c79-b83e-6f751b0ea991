package com.pinshang.qingyun.xda.cms.model.favorPosition;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Table;

/**
 * 鲜达-横向位-商品
 */
@Data
@Table(name = "t_xda_favor_x_position_commodity")
public class XdaFavorXPositionCommodity extends BaseIDPO {

    private Long favorXPositionId;
    private Long commodityId;
    private Integer sortNum;

    public XdaFavorXPositionCommodity(Long favorXPositionId, Long commodityId, Integer sortNum) {
        this.favorXPositionId = favorXPositionId;
        this.commodityId = commodityId;
        this.sortNum = sortNum;
    }
}
