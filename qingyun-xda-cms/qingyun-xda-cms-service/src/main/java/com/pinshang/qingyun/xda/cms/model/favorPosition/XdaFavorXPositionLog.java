package com.pinshang.qingyun.xda.cms.model.favorPosition;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import lombok.Data;

import javax.persistence.Table;

/**
 * 鲜达-横向位-日志
 */
@Data
@Table(name = "t_xda_favor_x_position_log")
public class XdaFavorXPositionLog extends BaseSimplePO {

    // 横向资源位ID
    private Long favorXPositionId;

    // 操作类型: 1-新增、3-修改、5-启用、6-停用 (枚举：XdOperateTypeEnums)
    private Integer operateType;

    public XdaFavorXPositionLog(Long favorXPositionId, Integer operateType) {
        this.favorXPositionId = favorXPositionId;
        this.operateType = operateType;
        this.setCreateId(FastThreadLocalUtil.getQY().getUserId());
    }
}
