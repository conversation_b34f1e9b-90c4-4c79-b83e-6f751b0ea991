package com.pinshang.qingyun.xda.cms.model.favorPosition;

import com.pinshang.qingyun.base.enums.XSAppPositionInfoStatusEnums;
import com.pinshang.qingyun.base.po.BasePO;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorYPositionSaveIDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.Date;

/**
 * 鲜达-纵向位-主表
 */
@Data
@Table(name = "t_xda_favor_y_position")
@AllArgsConstructor
@NoArgsConstructor
public class XdaFavorYPosition extends BasePO {

    // 资源位(XSAppPositionIdEnums.Y_FAVOR_01，Y_FAVOR_02等)
    private Integer positionId;

    // 期限类型：1-长期、2-短期	【TermTypeEnums】
    private Integer termType;

    // 是否适用 App 渠道  1＝是，0＝否
    private Integer appChannel;

    // 是否适用 小程序 渠道 1＝是，0＝否
    private Integer miniChannel;

    // 是否所有客户：0-所有客户、1-指定客户
    private Integer isAllStore;

    // 状态：1-启用、2-停用、3-过期	【XSAppPositionInfoStatusEnums】
    private Integer status;

    // 开始时间、结束时间
    private Date beginTime;
    private Date endTime;

    // 标的类型：1-前台类目、2-H5页面 XSAppPositionInfoTargetTypeEnums
    private Integer targetType;

    // 标的ID
    private Long targetTypeId;

    // 图片地址
    private String picUrl;

    public static XdaFavorYPosition forInsert(XdaFavorYPositionSaveIDTO vo, Date beginTime, Date endTime) {
        XdaFavorYPosition yPosition = new XdaFavorYPosition();
        SpringUtil.copyProperties(vo,yPosition);
        yPosition.setCreateId(vo.getUserId());
        yPosition.setCreateTime(new Date());
        yPosition.setUpdateId(vo.getUserId());
        yPosition.setUpdateTime(new Date());
        yPosition.beginTime = beginTime;
        yPosition.endTime = endTime;
        yPosition.status = XSAppPositionInfoStatusEnums.启用.getCode();
        return yPosition;
    }

    public static void forUpdate(XdaFavorYPosition yPosition, XdaFavorYPositionSaveIDTO vo, Date beginTime, Date endTime) {
        SpringUtil.copyProperties(vo,yPosition);
        yPosition.setUpdateId(vo.getUserId());
        yPosition.setUpdateTime(new Date());
        yPosition.beginTime = beginTime;
        yPosition.endTime = endTime;
    }

    public static XdaFavorYPosition forUpdateStatus(Long positionInfoId, Integer newStatus, Long updateId) {
        XdaFavorYPosition yPosition = new XdaFavorYPosition();
        yPosition.id = positionInfoId;
        yPosition.status = newStatus;
        yPosition.setUpdateId(updateId);
        yPosition.setUpdateTime(new Date());
        return yPosition;
    }

}
