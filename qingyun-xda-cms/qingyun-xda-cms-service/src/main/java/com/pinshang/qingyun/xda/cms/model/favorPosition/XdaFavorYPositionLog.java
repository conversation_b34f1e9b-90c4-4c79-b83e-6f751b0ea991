package com.pinshang.qingyun.xda.cms.model.favorPosition;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import lombok.Data;

import javax.persistence.Table;

/**
 * 鲜达-纵向位-日志
 */
@Data
@Table(name = "t_xda_favor_y_position_log")
public class XdaFavorYPositionLog extends BaseSimplePO {

    // 纵向资源位ID
    private Long favorYPositionId;

    // 操作类型: 1-新增、3-修改、5-启用、6-停用 (枚举：XdOperateTypeEnums)
    private Integer operateType;

    public XdaFavorYPositionLog(Long favorYPositionId, Integer operateType) {
        this.favorYPositionId = favorYPositionId;
        this.operateType = operateType;
        this.setCreateId(FastThreadLocalUtil.getQY().getUserId());
    }
}
