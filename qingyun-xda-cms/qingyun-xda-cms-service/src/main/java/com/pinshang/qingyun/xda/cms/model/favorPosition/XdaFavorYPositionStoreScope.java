package com.pinshang.qingyun.xda.cms.model.favorPosition;

import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.base.po.BaseSimplePO;
import com.pinshang.qingyun.xda.cms.dto.position.StoreScopeIDTO;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

/**
 * 鲜达-纵向位-客户范围
 */
@Data
@Table(name = "t_xda_favor_y_position_store_scope")
public class XdaFavorYPositionStoreScope extends BaseIDPO {

    private Long favorYPositionId;
    // 参考对象类型：1-结账客户、2-产品价格方案、3-客户类型、4-渠道、8-客户		——参见 MessageStoreScopeTypeEnums
    private Integer refObjType;
    // 参考对象ID
    private Long refObjId;

    public XdaFavorYPositionStoreScope(Long favorYPositionId, StoreScopeIDTO idto) {
        this.favorYPositionId = favorYPositionId;
        this.refObjType = idto.getRefObjType();
        this.refObjId = idto.getRefObjId();
    }
}
