package com.pinshang.qingyun.xda.cms.model.h5;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by hhf on 2019/11/18.
 * H5模板图片
 */
@Entity
@Data
@Table(name = "t_xda_h5_pic")
@NoArgsConstructor
public class XdaH5Pic extends BaseIDPO {

    /**H5模板-图片URL **/
    private String picUrl;
    /**H5模板-图片名称 **/
    private String picName;

    public XdaH5Pic(String picUrl, String picName) {
        this.picUrl = picUrl;
        this.picName = picName;
    }
}
