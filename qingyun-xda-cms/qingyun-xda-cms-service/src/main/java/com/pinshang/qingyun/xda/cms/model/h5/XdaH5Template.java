package com.pinshang.qingyun.xda.cms.model.h5;

import com.pinshang.qingyun.base.po.BasePO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * Created by hhf on 2019/11/18.
 * h5
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "t_xda_h5_template")
public class XdaH5Template extends BasePO {

    /**H5模板code **/
    private String templateCode;
    /**H5模板名称 **/
    private String templateName;
    /**活动模板id **/
    private Long templateCodeId;
    /**状态:1-启用,0-禁用 **/
    private Integer status;

    /**小模板 JSON 串**/
    private String templateContent;

    public XdaH5Template(String templateCode, String templateName, Long templateCodeId, Integer status, String templateContent, Long createId, Date createTime, Long updateId, Date updateTime) {
        this.setCreateId(createId);
        this.setCreateTime(createTime);
        this.setUpdateId(updateId);
        this.setUpdateTime(updateTime);

        this.templateCode = templateCode;
        this.templateName = templateName;
        this.templateCodeId = templateCodeId;
        this.status = status;
        this.templateContent = templateContent;
    }

    /**
     * 更新状态
     * @param id
     * @param status
     * @param userId
     * @return
     */
    public static XdaH5Template forUpdateStatus(Long id, Integer status, Long userId, Date updateTime) {
        XdaH5Template xdH5Template = new XdaH5Template();
        xdH5Template.setUpdateId(userId);
        xdH5Template.setUpdateTime(updateTime);
        xdH5Template.setStatus(status);
        xdH5Template.setId(id);
        return xdH5Template;
    }

    public static XdaH5Template forInsertXdH5Template(String templateCode, String templateName, Long templateCodeId, String templateContent, Long userId, Date createTime){
        return new XdaH5Template(templateCode,templateName,templateCodeId,0,templateContent,userId,createTime,userId,createTime);
    }

    public static XdaH5Template forUpdate(Long id, String templateName, String templateContent, Long userId, Date createTime){
        XdaH5Template xdH5Template = new XdaH5Template();
        xdH5Template.setId(id);
        xdH5Template.setTemplateName(templateName);
        xdH5Template.setTemplateContent(templateContent);
        xdH5Template.setUpdateId(userId);
        xdH5Template.setUpdateTime(createTime);
        return xdH5Template;
    }
}
