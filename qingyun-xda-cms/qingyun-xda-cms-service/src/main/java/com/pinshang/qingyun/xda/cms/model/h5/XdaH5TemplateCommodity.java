package com.pinshang.qingyun.xda.cms.model.h5;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by hhf on 2020/4/24.
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "t_xda_h5_template_commodity")
public class XdaH5TemplateCommodity extends BaseIDPO {
    /**H5模板id**/
    private Long templateId;
    /**商品显示数量**/
    private Integer showCount;

    public XdaH5TemplateCommodity(Long templateId, Integer showCount) {
        this.templateId = templateId;
        this.showCount = showCount;
    }
}
