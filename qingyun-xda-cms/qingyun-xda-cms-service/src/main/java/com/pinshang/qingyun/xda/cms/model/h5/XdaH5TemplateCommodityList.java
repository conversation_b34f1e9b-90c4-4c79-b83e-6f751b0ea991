package com.pinshang.qingyun.xda.cms.model.h5;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 *
 * <AUTHOR>
 * @date 2020/4/24
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "t_xda_h5_template_commodity_list")
public class XdaH5TemplateCommodityList extends BaseIDPO {
    /**H5模板商品id**/
    private Long templateCommodityId;
    /**商品id**/
    private Long commodityId;

    public XdaH5TemplateCommodityList(Long templateCommodityId, Long commodityId) {
        this.templateCommodityId = templateCommodityId;
        this.commodityId = commodityId;
    }
}
