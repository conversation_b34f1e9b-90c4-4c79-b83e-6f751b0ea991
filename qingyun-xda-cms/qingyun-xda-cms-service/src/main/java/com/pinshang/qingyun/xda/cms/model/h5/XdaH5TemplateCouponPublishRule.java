package com.pinshang.qingyun.xda.cms.model.h5;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 *
 * <AUTHOR>
 * @date 2020/4/24
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "t_xda_h5_template_coupon_publish_rule")
public class XdaH5TemplateCouponPublishRule extends BaseIDPO {
    /**H5模板id**/
    private Long templateId;
    /**优惠券发放规则ID**/
    private Long couponPublishRuleId;

    public XdaH5TemplateCouponPublishRule(Long templateId, Long couponPublishRuleId) {
        this.templateId = templateId;
        this.couponPublishRuleId = couponPublishRuleId;
    }
}
