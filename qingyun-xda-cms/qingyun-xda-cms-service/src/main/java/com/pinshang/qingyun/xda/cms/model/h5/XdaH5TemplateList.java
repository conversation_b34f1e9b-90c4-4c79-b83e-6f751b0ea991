package com.pinshang.qingyun.xda.cms.model.h5;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by hhf on 2019/11/18.
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "t_xda_h5_template_list")
public class XdaH5TemplateList extends BaseIDPO {

    /**H5模板id **/
    private Long templateId;
    /**位置-区域:1-头图区,2-模块区 **/
    private Integer positionLevel;
    /**位置-具体位置如模板1-元素1-1,2-元素2-1,3-元素2-2,4-Tab区**/
    private Integer positionLevelSort;
    /**资源数据类型:10-H5图片,11-H5图片带h5连接,12-H5图片连接商品,20.h5-Tab,21.h5-通样,22.h5-商品区 **/
    private Integer resourceType;
    /**资源数据ID **/
    private Long resourceId;

    /**
     * 带参数的构造方法
     * @param templateId 主表模板id
     * @param positionLevel 位置-区域:1-头图区,2-模块区
     * @param positionLevelSort 位置-具体位置如模板1-元素1-1,2-元素2-1,3-元素2-2,4-Tab区
     * @param resourceType 资源数据类型:10-H5图片,11-H5图片带h5连接,12-H5图片连接商品,20.h5-Tab,21.h5-通样,22.h5-商品区
     * @param resourceId 资源数据ID
     */
    public XdaH5TemplateList(Long templateId, Integer positionLevel, Integer positionLevelSort, Integer resourceType, Long resourceId) {
        this.templateId = templateId;
        this.positionLevel = positionLevel;
        this.positionLevelSort = positionLevelSort;
        this.resourceType = resourceType;
        this.resourceId = resourceId;
    }
}

