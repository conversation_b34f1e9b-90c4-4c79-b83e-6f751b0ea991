package com.pinshang.qingyun.xda.cms.model.h5;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * Created by hhf on 2020/4/24.
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "t_xda_h5_template_log")
public class XdaH5TemplateLog extends BaseSimplePO {

    /**H5模板id**/
    private Long templateId;

    /**操作类型: 1-新增、2-复制新增,3-修改、4-启用、5-停用**/
    private Integer operateType;

    public XdaH5TemplateLog(Long templateId, Integer operateType, Date createTime, Long createId) {
        this.templateId = templateId;
        this.operateType = operateType;
        this.setCreateId(createId);
        this.setCreateTime(createTime);
    }
}
