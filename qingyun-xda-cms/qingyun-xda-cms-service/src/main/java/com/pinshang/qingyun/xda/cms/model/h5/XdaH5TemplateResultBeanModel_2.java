package com.pinshang.qingyun.xda.cms.model.h5;

import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5PicH5ODTO;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5PicODTO;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabCommodityODTO;
import com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabODTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/23 21:39
 */
public class XdaH5TemplateResultBeanModel_2 {
    private XdaH5PicODTO h5Pic;
    private XdaH5PicH5ODTO h5PicH5;
    private List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityItem;
    private List<XdaH5TemplateTabODTO> h5TemplateTabList;

    public XdaH5PicODTO getH5Pic() {
        return h5Pic;
    }

    public void setH5Pic(XdaH5PicODTO h5Pic) {
        this.h5Pic = h5Pic;
    }

    public XdaH5PicH5ODTO getH5PicH5() {
        return h5PicH5;
    }

    public void setH5PicH5(XdaH5PicH5ODTO h5PicH5) {
        this.h5PicH5 = h5PicH5;
    }

    public List<XdaH5TemplateTabCommodityODTO> getH5TemplateTabCommodityItem() {
        return h5TemplateTabCommodityItem;
    }

    public void setH5TemplateTabCommodityItem(List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityItem) {
        this.h5TemplateTabCommodityItem = h5TemplateTabCommodityItem;
    }

    public List<XdaH5TemplateTabODTO> getH5TemplateTabList() {
        return h5TemplateTabList;
    }

    public void setH5TemplateTabList(List<XdaH5TemplateTabODTO> h5TemplateTabList) {
        this.h5TemplateTabList = h5TemplateTabList;
    }
}
