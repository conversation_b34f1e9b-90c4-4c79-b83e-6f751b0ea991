package com.pinshang.qingyun.xda.cms.model.h5;

import com.pinshang.qingyun.base.po.BasePO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by hhf on 2019/11/18.
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "t_xda_h5_template_style_code")
public class XdaH5TemplateStyleCode extends BasePO {

    /**H5模板code **/
    private String templateCode;
    /**H5模板名称 **/
    private String templateName;
    /**H5模板内容 **/
    private String templateContent;
    /**状态:1-启用,0-禁用 **/
    private Integer status;
    /**活动说明 **/
    private String templateDesc;
    /**URL地址**/
    private String url;
}
