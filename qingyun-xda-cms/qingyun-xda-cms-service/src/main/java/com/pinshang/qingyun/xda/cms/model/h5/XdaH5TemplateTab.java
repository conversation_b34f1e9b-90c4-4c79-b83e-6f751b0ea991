package com.pinshang.qingyun.xda.cms.model.h5;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * Created by hhf on 2019/11/18.
 */
@Data
@Entity
@Table(name = "t_xda_h5_template_tab")
public class XdaH5TemplateTab extends BaseIDPO {

    /**Tab区 Tab标签 **/
    private String tabName;
    /**app前台显示几个商品位 **/
    private Integer appShowNum;
    /**1-tab,2-通栏,3-商品区**/
    private Integer tabType;
    /**状通栏图片URL **/
    private String picUrl;
    /**已添加商品数量**/
    private Integer commodityNum;

    public XdaH5TemplateTab(String tabName, Integer appShowNum, Integer tabType, String picUrl, Integer commodityNum) {
        this.tabName = tabName;
        this.appShowNum = appShowNum;
        this.tabType = tabType;
        this.picUrl = picUrl;
        this.commodityNum = commodityNum;
    }
}
