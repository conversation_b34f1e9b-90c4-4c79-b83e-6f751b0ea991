package com.pinshang.qingyun.xda.cms.model.popup;

import com.pinshang.qingyun.base.enums.xd.XdPopupMsgStatusEnums;
import com.pinshang.qingyun.base.po.BasePO;
import com.pinshang.qingyun.xda.cms.dto.popup.XdaPopupMsgSaveIDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @description: 鲜达-弹框通知
 * @author: hhf
 * @time: 2020/12/9 13:34
 */
@Entity
@Data
@Table(name = "t_xda_popup_msg")
@NoArgsConstructor
@AllArgsConstructor
public class XdaPopupMsg extends BasePO {

    /**通知编号**/
    private String msgNo;

    /**'通知概要'**/
    private String msgSummary;

    /**'生效开始时间'**/
    private Date beginTime;

    /**'生效结束时间'**/
    private Date endTime;

    /**'弹框频率'**/
    private Integer frequency;

    /**是否所有门店：0-所有门店、1-指定门店	【IsAllStoreTypeEnums】**/
    private Integer isAllStore;

    /**状态: 1-启用,0-停用**/
    private Integer status;

    /**通知方式: 1-文字,2-图片**/
    private Integer msgWay;

    /**前台通知标题**/
    private String appMsgTitle;

    /**前台通知详情**/
    private String appMsgDetails;

    /**弹框图片地址**/
    private String picUrl;

    /**是否跳转: 1-无需跳转,2-H5页面**/
    private Integer isJump;

    /**关联H5 Id**/
    private Long h5Id;

    public static  XdaPopupMsg forUpdateStatus(Long id,Integer status,Long userId){
        XdaPopupMsg xdPopupMsg = new XdaPopupMsg();
        xdPopupMsg.setId(id);
        xdPopupMsg.setStatus(status);
        xdPopupMsg.setUpdateId(userId);
        xdPopupMsg.setUpdateTime(new Date());
        return xdPopupMsg;
    }

    /**
     * 新增
     * @param saveIDTO
     * @param date
     * @return
     */
    public static XdaPopupMsg forInsert(XdaPopupMsgSaveIDTO saveIDTO, Date date){
        Long createId = saveIDTO.getUserId();
        XdaPopupMsg xdPopupMsg = new XdaPopupMsg(saveIDTO.getMsgNo(), saveIDTO.getMsgSummary(), saveIDTO.getBTime(), saveIDTO.getETime(),
                saveIDTO.getFrequency(), saveIDTO.getIsAllStore(), XdPopupMsgStatusEnums.ENABLE.getCode(), saveIDTO.getMsgWay(), saveIDTO.getAppMsgTitle(),
                saveIDTO.getAppMsgDetails(), saveIDTO.getPicUrl(), saveIDTO.getIsJump(), saveIDTO.getH5Id());
        xdPopupMsg.setUpdateId(createId);
        xdPopupMsg.setCreateId(createId);
        xdPopupMsg.setCreateTime(date);
        xdPopupMsg.setUpdateTime(date);
        return xdPopupMsg;
    }

    /**
     * 修改
     * @param saveIDTO
     * @param date
     * @param xdPopupMsgId
     * @return
     */
    public static XdaPopupMsg forUpdate(XdaPopupMsgSaveIDTO saveIDTO,Date date,Long xdPopupMsgId){
        XdaPopupMsg xdPopupMsg = new XdaPopupMsg(null, saveIDTO.getMsgSummary(), saveIDTO.getBTime(), saveIDTO.getETime(),
                saveIDTO.getFrequency(), saveIDTO.getIsAllStore(), null, saveIDTO.getMsgWay(), saveIDTO.getAppMsgTitle(), saveIDTO.getAppMsgDetails(),
                saveIDTO.getPicUrl(), saveIDTO.getIsJump(), saveIDTO.getH5Id());
        xdPopupMsg.setId(xdPopupMsgId);
        xdPopupMsg.setUpdateId(saveIDTO.getUserId());
        xdPopupMsg.setUpdateTime(date);
        return xdPopupMsg;
    }

    public XdaPopupMsg(Long xdPopupMsgId) {
        this.setId(xdPopupMsgId);
    }
}
