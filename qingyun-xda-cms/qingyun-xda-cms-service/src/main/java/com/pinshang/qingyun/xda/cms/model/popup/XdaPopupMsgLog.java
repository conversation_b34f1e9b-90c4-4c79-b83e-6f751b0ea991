package com.pinshang.qingyun.xda.cms.model.popup;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @description:
 * @author: hhf
 * @time: 2020/12/9 14:02
 */
@Entity
@Data
@Table(name = "t_xda_popup_msg_log")
@NoArgsConstructor
public class XdaPopupMsgLog extends BaseSimplePO {

    /**弹框消息id**/
    private Long popupMsgId;

    /**操作类型: 1-新增、3-修改、5-启用、6-停用	【OperateTypeEnums】**/
    private Integer operateType;

    public XdaPopupMsgLog(Long popupMsgId, Integer operateType, Date createTime, Long createId) {
        this.popupMsgId = popupMsgId;
        this.operateType = operateType;
        this.setCreateId(createId);
        this.setCreateTime(createTime);
    }

    public static XdaPopupMsgLog forInsert(Long popupMsgId, Integer operateType, Date createTime, Long createId){
      return new XdaPopupMsgLog(popupMsgId,operateType,createTime,createId);
    }
}
