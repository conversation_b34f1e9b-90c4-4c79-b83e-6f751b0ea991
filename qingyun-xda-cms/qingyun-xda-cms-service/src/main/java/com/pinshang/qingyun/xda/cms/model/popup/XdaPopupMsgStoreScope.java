package com.pinshang.qingyun.xda.cms.model.popup;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @description: 鲜达-弹框通知关联客户范围
 * @author: hhf
 * @time: 2020/12/9 15:34
 */
@Entity
@Data
@Table(name = "t_xda_popup_msg_store_scope")
@NoArgsConstructor
public class XdaPopupMsgStoreScope extends BaseSimplePO {

    /** 弹框消息id **/
    private Long  popupMsgId;

    /** 参考对象类型：1-结账客户、2-产品价格方案、3-客户类型、4-渠道、8-客户		——参见 MessageStoreScopeTypeEnums **/
    @ApiModelProperty(value = "参考对象类型：1-结账客户、2-产品价格方案、3-客户类型、4-渠道、8-客户")
    private Integer refObjType;
    /** 参考对象ID **/
    @ApiModelProperty(value = "参考对象ID: 结账客户id/产品价格方案id/客户类型id/渠道id/客户id")
    private Long refObjId;

    public XdaPopupMsgStoreScope(Long popupMsgId, Integer refObjType, Long refObjId, Date createTime,Long createId) {
        this.popupMsgId = popupMsgId;
        this.refObjType = refObjType;
        this.refObjId = refObjId;
        this.setCreateId(createId);
        this.setCreateTime(createTime);
    }

    public XdaPopupMsgStoreScope(Long popupMsgId) {
        this.popupMsgId = popupMsgId;
    }
}
