package com.pinshang.qingyun.xda.cms.model.position;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.enums.XSAppPositionInfoStatusEnums;
import com.pinshang.qingyun.base.po.BasePO;
import com.pinshang.qingyun.xda.cms.dto.position.InsertPositionInfoIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.UpdatePositionInfoIDTO;

/**
 * 鲜达资源位
 */
@Data
@Entity
@NoArgsConstructor
@Table(name="t_xda_position_info")
public class XdaPositionInfo extends BasePO {
	// 资源位编号
	private String positionCode;
	// 资源位类型：11-BANNER、12-ICON、13-积木组、14-推荐组、15-通栏、16-横栏、19-头图、20-服务说明栏、21-弹框广告	—— 参见【XSAppPositionTypeEnums】
	private Integer positionType;
	// 资源位ID		—— 参见【XSAppPositionIdEnums】
	private Integer positionId;
	// 期限类型：1-长期、2-短期		—— 参见【TermTypeEnums】
	private Integer termType;
	// 开始时间、结束时间
	private Date beginTime;
	private Date endTime;
	// 最小间隔(小时)[0,24]
	private Integer minInterval;
	// 是否所有客户：0-所有客户、1-指定客户	—— 参见【IsAllStoreTypeEnums】
	private Integer isAllStore;
	// 标的类型：1-前台类目、2-H5页面、3-组合、4-单品详情页	—— 参见【XSAppPositionInfoTargetTypeEnums】
	private Integer targetType;
	// 标的ID
	private Long targetTypeId;
	// 标的名称
	private String label;
	// 图片地址
	private String picUrl;
	// 图片标的类型：1-前台类目、2-H5页面、4-单品详情页		—— 参见【XSAppPositionInfoTargetTypeEnums】
	private Integer picTargetType;
	// 图片标的ID
	private Long picTargetTypeId;
	// 状态：1-启用、2-停用、3-过期	—— 参见【XSAppPositionInfoStatusEnums】
	private Integer status;
	
	public XdaPositionInfo(Long positionInfoId) {
		this.setId(positionInfoId);
	}
	
	public static XdaPositionInfo forInsert(String positionCode, InsertPositionInfoIDTO idto, Date createTime) {
		Long createId = idto.getUserId();
		XdaPositionInfo model = new XdaPositionInfo();
		// model.setId(null);
		model.setCreateId(createId);
		model.setCreateTime(createTime);
		model.setUpdateId(createId);
		model.setUpdateTime(createTime);
		
		model.positionCode = positionCode;
		model.positionType = idto.getPositionType();
		model.positionId = idto.getPositionId();
		model.termType = idto.getTermType();
		model.beginTime = idto.getBTime();
		model.endTime = idto.getETime();
		model.minInterval = idto.getMinInterval();
		model.isAllStore = idto.getIsAllStore();
		model.targetType = idto.getTargetType();
		model.targetTypeId = idto.getTargetTypeId();
		model.label = idto.getLabel();
		model.picUrl = idto.getPicUrl();
		model.picTargetType = idto.getPicTargetType();
		model.picTargetTypeId = idto.getPicTargetTypeId();
		model.status = XSAppPositionInfoStatusEnums.启用.getCode();
		return model;
	}
	
	public static void forUpdate(XdaPositionInfo model, UpdatePositionInfoIDTO idto, Date updateTime) {
		// model.setId(id);
		// model.setCreateId(createId);
		// model.setCreateTime(createTime);
		model.setUpdateId(idto.getUserId());
		model.setUpdateTime(updateTime);
		
		// model.setPositionCode(positionCode);
		// model.setPositionType(positionType);
		model.setPositionId(idto.getPositionId());
		model.setTermType(idto.getTermType());
		model.setBeginTime(idto.getBTime());
		model.setEndTime(idto.getETime());
		model.setMinInterval(idto.getMinInterval());
		model.setIsAllStore(idto.getIsAllStore());
		model.setTargetType(idto.getTargetType());
		model.setTargetTypeId(idto.getTargetTypeId());
		model.setLabel(idto.getLabel());
		model.setPicUrl(idto.getPicUrl());
		model.setPicTargetType(idto.getPicTargetType());
		model.setPicTargetTypeId(idto.getPicTargetTypeId());
		// model.setStatus(status);
	}
	
	public static XdaPositionInfo forUpdateStatus(Long positionInfoId, Integer newStatus, Long updateId, Date updateTime) {
		XdaPositionInfo model = new XdaPositionInfo();
		model.id = positionInfoId;
		model.status = newStatus;
		model.setUpdateId(updateId);
		model.setUpdateTime(updateTime);
		return model;
	}
	
}
