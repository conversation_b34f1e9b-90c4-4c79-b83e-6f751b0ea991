package com.pinshang.qingyun.xda.cms.model.position;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 鲜达资源位-积木2
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "t_xda_position_info_block")
public class XdaPositionInfoBlock extends BaseIDPO {
	// 鲜食APP资源位绑定信息ID
	private Long positionInfoId;
	// 资源位类型（积木类型）：1301-积木组01、1302-积木组02、1303-积木组03							—— 参见【XSAppPositionBlockTypeEnums】
	private Integer positionType;
	// 资源位ID-积木组02：130201-左侧、130202-右上、130203-右下									—— 参见【XSAppPositionBlockIdEnums】
	// 资源位ID-积木组03：130301-左上、130302-左下、130303-右上、130304-右下_左、130304-右下_右	—— 参见【XSAppPositionBlockIdEnums】
	private Integer positionId;
	// 标的类型：1-前台类目、2-H5页面																—— 参见【XSAppPositionInfoTargetTypeEnums】
	private Integer targetType;
	// 标的ID
	private Long targetTypeId;
	// 图片地址
	private String picUrl;
	
	public XdaPositionInfoBlock(Long positionInfoId) {
		this.positionInfoId = positionInfoId;
	}
	
	public XdaPositionInfoBlock(Long positionInfoId, Integer positionType, Integer positionId, Integer targetType, Long targetTypeId, String picUrl) {
		this.positionInfoId = positionInfoId;
		this.positionType = positionType;
		this.positionId = positionId;
		this.targetType = targetType;
		this.targetTypeId = targetTypeId;
		this.picUrl = picUrl;
	}
	
}
