package com.pinshang.qingyun.xda.cms.model.position;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * 鲜达资源位-商品
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "t_xda_position_info_commodity")
public class XdaPositionInfoCommodity extends BaseIDPO {
	private Long positionInfoId;		// 鲜食APP资源位绑定信息ID
	private Long commodityId;			// 商品ID
	private Integer sortNum;			// 排序号
	
	public XdaPositionInfoCommodity(Long positionInfoId) {
		this.positionInfoId = positionInfoId;
	}
	
	public XdaPositionInfoCommodity(Long positionInfoId, Long commodityId, Integer sortNum) {
		this.positionInfoId = positionInfoId;
		this.commodityId = commodityId;
		this.sortNum = sortNum;
	}
	
}
