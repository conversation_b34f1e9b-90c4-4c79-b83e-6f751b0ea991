package com.pinshang.qingyun.xda.cms.model.position;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BaseSimplePO;

/**
 * 鲜达资源位-日志
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "t_xda_position_info_log")
public class XdaPositionInfoLog extends BaseSimplePO {
	private Long positionInfoId;		// 资源位信息ID
	private Integer operateType;		// 操作类型: 1-新增、3-修改、5-启用、6-停用	【OperateTypeEnums】
	
	public XdaPositionInfoLog(Long positionInfoId, Integer operateType, Long createId, Date createTime) {
		this.setCreateId(createId);
		this.setCreateTime(createTime);
		this.positionInfoId = positionInfoId;
		this.operateType = operateType;
	}
	
}
