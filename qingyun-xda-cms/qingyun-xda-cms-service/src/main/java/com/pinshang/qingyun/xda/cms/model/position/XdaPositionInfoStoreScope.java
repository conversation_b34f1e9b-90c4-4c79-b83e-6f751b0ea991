package com.pinshang.qingyun.xda.cms.model.position;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import com.pinshang.qingyun.xda.cms.dto.position.StoreScopeIDTO;

/**
 * 鲜达资源位-客户范围
 */
@Data
@Entity
@NoArgsConstructor
@Table(name = "t_xda_position_info_store_scope")
public class XdaPositionInfoStoreScope extends BaseSimplePO {
	// 资源位信息ID
	private Long positionInfoId;
    // 参考对象类型：1-结账客户、2-产品价格方案、3-客户类型、4-渠道、8-客户		——参见 MessageStoreScopeTypeEnums
    private Integer refObjType;
    // 参考对象ID
    private Long refObjId;

    public XdaPositionInfoStoreScope(Long positionInfoId) {
        this.positionInfoId = positionInfoId;
    }
    
    public XdaPositionInfoStoreScope(Long positionInfoId, StoreScopeIDTO idto, Long createId, Date createTime) {
    	this.setCreateId(createId);
        this.setCreateTime(createTime);
        
        this.positionInfoId = positionInfoId;
        this.refObjType = idto.getRefObjType();
        this.refObjId = idto.getRefObjId();
    }
    
}
