package com.pinshang.qingyun.xda.cms.model.qa;

import com.pinshang.qingyun.base.enums.settlement.StatusEnum;
import com.pinshang.qingyun.base.po.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @description:
 * @author: hhf
 * @time: 2020/12/16 10:44
 */
@Entity
@Data
@Table(name = "t_xda_app_qa")
@NoArgsConstructor
@AllArgsConstructor
public class XdaAppQa extends BasePO {

    /**问题标题**/
    private String title;
    /**问题内容**/
    private String content;
    /**状态：1-启用、2-停用**/
    private Integer status;
    /**排序号**/
    private Integer sortNum;

    public static XdaAppQa forInsert(String title, String content, Integer sortNum, Long userId, Date date) {
        XdaAppQa xdaAppQa = new XdaAppQa(title,content,StatusEnum.ENABLE.getCode(),sortNum);
        xdaAppQa.setCreateId(userId);
        xdaAppQa.setUpdateId(userId);
        xdaAppQa.setCreateTime(date);
        xdaAppQa.setUpdateTime(date);
        return xdaAppQa;
    }

    public static XdaAppQa forUpdate(Long id ,String title, String content, Long userId, Date date) {
        XdaAppQa xdaAppQa = new XdaAppQa();
        xdaAppQa.setTitle(title);
        xdaAppQa.setContent(content);
        xdaAppQa.setId(id);
        xdaAppQa.setUpdateId(userId);
        xdaAppQa.setUpdateTime(date);
        return xdaAppQa;
    }

    public static XdaAppQa forUpdateSortNum(Long id ,Integer sortNum, Long userId, Date date){
        XdaAppQa xdaAppQa = new XdaAppQa();
        xdaAppQa.setId(id);
        xdaAppQa.setSortNum(sortNum);
        xdaAppQa.setUpdateId(userId);
        xdaAppQa.setUpdateTime(date);
        return xdaAppQa;
    }

    public static XdaAppQa forUpdateStatus(Long id ,Integer status, Long userId, Date date){
        XdaAppQa xdaAppQa = new XdaAppQa();
        xdaAppQa.setId(id);
        xdaAppQa.setStatus(status);
        xdaAppQa.setUpdateId(userId);
        xdaAppQa.setUpdateTime(date);
        return xdaAppQa;
    }
}
