package com.pinshang.qingyun.xda.cms.model.qa;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @description:
 * @author: hhf
 * @time: 2020/12/16 10:44
 */
@Entity
@Data
@Table(name = "t_xda_app_qa_log")
@NoArgsConstructor
@AllArgsConstructor
public class XdaAppQaLog extends BaseSimplePO {

    /** 前台品类广告id**/
    private Long qaId;
    /** 操作类型:1-新增，3-修改，5-启用，6-停用，7-排序 参考 XdOperateTypeEnums **/
    private Integer operateType;

    public XdaAppQaLog(Long qaId, Integer operateType,Date createTime,Long createId) {
        this.qaId = qaId;
        this.operateType = operateType;
        this.setCreateId(createId);
        this.setCreateTime(createTime);
    }

    public static XdaAppQaLog forInsert(Long qaId, Integer operateType,Long createId, Date date) {
        XdaAppQaLog xdaAppQaLog = new XdaAppQaLog(qaId,operateType,date,createId);
        return xdaAppQaLog;
    }
}
