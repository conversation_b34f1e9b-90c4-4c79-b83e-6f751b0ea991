package com.pinshang.qingyun.xda.cms.model.search;

import com.pinshang.qingyun.base.po.BasePO;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * 搜索热搜词表
 */
@Data
@Table(name = "t_xda_search_hot_word")
@NoArgsConstructor
public class XdaSearchHotWord extends BasePO {
    //热搜词
    @ApiModelProperty("热搜词")
    private String hotWord;
    //特效状态:0=无，1=有
    @ApiModelProperty("特效状态:0=无，1=有")
    private Integer effectStatus;
    //排序
    @ApiModelProperty("前台序号")
    private Integer sortNum;

    @Transient
    @ApiModelProperty("特效状态文本描述:0=无，1=有")
    private String effectStatusDesc;

    public XdaSearchHotWord(String hotWord, Integer effectStatus, Integer sortNum) {
        this.hotWord = hotWord;
        this.effectStatus = effectStatus;
        this.sortNum = sortNum;
        this.setUpdateId(FastThreadLocalUtil.getQY().getUserId());
    }

    public String getEffectStatusDesc() {
        return effectStatus==null?"":(effectStatus==0?"无":"有");
    }
}
