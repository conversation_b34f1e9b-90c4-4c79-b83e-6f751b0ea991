package com.pinshang.qingyun.xda.cms.model.search;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.EnumUtils;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;

/**
 * 搜索热搜词变更日志表
 */
@Data
@NoArgsConstructor
@Table(name = "t_xda_search_hot_word_log")
public class XdaSearchHotWordLog extends BaseSimplePO {
    //操作类型：1=新增，2=修改，3＝删除
    private Integer operType;
    private String oldValue;
    private String newValue;

    public XdaSearchHotWordLog(Integer operType, String oldValue, String newValue) {
        this.operType = operType;
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.setCreateId(FastThreadLocalUtil.getQY().getUserId());
    }

    public enum XdaSearchHotWordLogTypeEnums{
        ADD(1,"新增"),
        EDIT(2,"修改"),
        DELETE(3,"删除"),
        ;
        private Integer code;
        private String name;
        public String getName() {
            return name;
        }
        public int getCode() {
            return code;
        }

        XdaSearchHotWordLogTypeEnums(Integer code, String name) {
            this.code = code;
            this.name = name;
        }

        public static XdaSearchHotWordLogTypeEnums fromName(Integer code) {
            return EnumUtils.fromEnumProperty(XdaSearchHotWordLogTypeEnums.class, "code", code);
        }

        public static String getName(Integer code) {
            XdaSearchHotWordLogTypeEnums o = fromName(code);
            return null == o? "": o.name;
        }

    }
}
