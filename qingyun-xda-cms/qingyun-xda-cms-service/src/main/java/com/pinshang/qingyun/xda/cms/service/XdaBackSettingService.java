package com.pinshang.qingyun.xda.cms.service;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.cms.mapper.XdaBackSettingMapper;
import com.pinshang.qingyun.xda.cms.model.XdaBackSetting;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;


@Service
public class XdaBackSettingService {

    @Autowired
    private XdaBackSettingMapper backSettingMapper;

    /**
     * 后台设置详细信息
     * @return
     */
    public XdaBackSetting findXdBackSettingDetails(){
        XdaBackSetting backSetting = new XdaBackSetting();
        List<XdaBackSetting> xdBackSettings = backSettingMapper.selectAll();
        if(SpringUtil.isNotEmpty(xdBackSettings)){
            backSetting = xdBackSettings.get(0);
        }
        return backSetting;
    }
}
