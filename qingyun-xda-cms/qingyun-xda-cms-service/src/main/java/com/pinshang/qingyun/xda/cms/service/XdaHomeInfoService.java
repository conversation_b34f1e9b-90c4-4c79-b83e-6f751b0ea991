package com.pinshang.qingyun.xda.cms.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.BlockTypeConstant;
import com.pinshang.qingyun.base.constant.RedisKeyPrefixConst;
import com.pinshang.qingyun.base.enums.*;
import com.pinshang.qingyun.base.enums.pic.PicSizeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.ImageLibraryODTO;
import com.pinshang.qingyun.common.service.ImageLibraryClient;
import com.pinshang.qingyun.marketing.dto.mtCoupon.MtCouponUserNotifyODTO;
import com.pinshang.qingyun.marketing.service.mtCoupon.MtCouponTobClient;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryDetailIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.storage.service.tob.ToBClient;
import com.pinshang.qingyun.store.dto.customer.StoreSelectODTO;
import com.pinshang.qingyun.store.service.StoreManageClient;
import com.pinshang.qingyun.tms.dto.logisticscenterbatch.LogisticsBatchStoreInfoODTO;
import com.pinshang.qingyun.tms.dto.logisticscenterbatch.LogisticsBatchStoreSearchIDTO;
import com.pinshang.qingyun.tms.service.LogisticsCenterBatchClient;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorYPositionInfoODTO;
import com.pinshang.qingyun.xda.cms.dto.home.*;
import com.pinshang.qingyun.xda.cms.dto.storeScope.StoreODTO;
import com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorXPositionMapper;
import com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorXPositionMapperV4;
import com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorYPositionMapper;
import com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorYPositionMapperV4;
import com.pinshang.qingyun.xda.cms.mapper.popup.XdaPopupMsgStoreScopeMapper;
import com.pinshang.qingyun.xda.cms.mapper.position.XdaPositionInfoBlockMapper;
import com.pinshang.qingyun.xda.cms.mapper.position.XdaPositionInfoCommodityMapper;
import com.pinshang.qingyun.xda.cms.mapper.position.XdaPositionInfoMapper;
import com.pinshang.qingyun.xda.cms.mapper.position.XdaPositionInfoStoreScopeMapper;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppV4IDTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppV4ODTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityFrontClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 鲜到资源位
 */
@Service
@Slf4j
public class XdaHomeInfoService {
	
    @Autowired
    private XdaPositionInfoMapper xdaPositionInfoMapper;
    @Autowired
    private XdaPositionInfoBlockMapper xdaPositionInfoBlockMapper;
    @Autowired
    private XdaPositionInfoCommodityMapper xdaPositionInfoCommodityMapper;
    @Autowired
    private XdaFavorXPositionMapper xdaFavorXPositionMapper;
    @Autowired
    private XdaFavorYPositionMapper xdaFavorYPositionMapper;
    @Autowired
    private XdaFavorXPositionMapperV4 xdaFavorXPositionMapperV4;
    @Autowired
    private XdaFavorYPositionMapperV4 xdaFavorYPositionMapperV4;
    @Autowired
    private ImageLibraryClient imageLibraryClient;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private XdaCommodityFrontClient xdaCommodityFrontClient;
    @Autowired
    private XdaPositionInfoStoreScopeMapper xdaPositionInfoStoreScopeMapper;
    @Autowired
    private LogisticsCenterBatchClient logisticsCenterBatchClient;
    @Autowired
    private XdaPopupMsgStoreScopeMapper xdaPopupMsgStoreScopeMapper;
    @Value("${pinshang.img-server-url}")
    private String imgServerUrl;
    @Value("${pinshang.domain-name}")
    private String domainName;
    @Value("${pinshang.service-agreement-url}")
    private String serviceAgreementUrl;
    private String targetUrl = "/gateXdaApi/xdaCms/xdaH5Render/renderXdH5?templateId=";
    @Autowired
    private ToBClient toBClient;
    @Autowired
    private StoreManageClient storeManageClient;
    @Autowired
    private MtCouponTobClient mtCouponTobClient;

    public Map<Long, CommodityInventoryODTO> getbStockMap(Date orderTime, List<CommodityInventoryDetailIDTO> orderCommodityList) {
        CommodityInventoryIDTO commodityInventoryIDTO = new CommodityInventoryIDTO();
        commodityInventoryIDTO.setOrderTime(orderTime != null ? orderTime : DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"));
        commodityInventoryIDTO.setOrderCommodityList(orderCommodityList);
        // 后续若使用该方法需传入物流中心ID
        List<CommodityInventoryODTO> toBStockList = toBClient.queryCommodityWithBomInventory(commodityInventoryIDTO);
        Map<Long, CommodityInventoryODTO> toBStockMap;
        if(CollectionUtils.isNotEmpty(toBStockList)){
            toBStockMap = toBStockList.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId, Function.identity()));
        } else {
            toBStockMap = new HashMap<>();
        }
        return toBStockMap;
    }

    /**
     * 获取首页资源信息
     * @param baseIDTO 首页入参
     * @return
     */
    @Deprecated
    public XdaHomePageODTO queryAppHomePositionInfo(HomeBaseIDTO baseIDTO) {
        XdaHomePageODTO dto = new XdaHomePageODTO();
        List<XdaPositionInfoItemODTO> positionInfoItemList = xdaPositionInfoMapper.selectShopPositionList(baseIDTO.getStoreId());
        if(SpringUtil.isEmpty(positionInfoItemList)){
            return dto;
        }

        //按资源位类型分组
        Map<Integer, List<XdaPositionInfoItemODTO>> positionGroup = new HashMap<>();
        positionInfoItemList.stream().collect(Collectors.groupingBy(XdaPositionInfoItemODTO::getPositionType)).forEach((positionType, typeItemList)->{
            List<XdaPositionInfoItemODTO> itemODTOS = new ArrayList<>();
            List<XdaPositionInfoItemODTO> sortTypeItemList = typeItemList.stream().sorted(Comparator.comparing(XdaPositionInfoItemODTO::getPositionId)).collect(Collectors.toList());
            sortTypeItemList.stream().collect(Collectors.groupingBy(XdaPositionInfoItemODTO::getPositionId,LinkedHashMap::new,Collectors.toList())).forEach((positionId, positionItemList)->{
                XdaPositionInfoItemODTO positionInfoItem = null;
                if(positionItemList.size()==1){
                    positionInfoItem = positionItemList.get(0);
                }else{
                    //同一个资源位下如果多条记录，取修改时间最新的一条
                    positionInfoItem = positionItemList.stream().sorted(Comparator.comparing(XdaPositionInfoItemODTO::getUpdateTime,Comparator.reverseOrder())).collect(Collectors.toList()).get(0);
                }
                itemODTOS.add(positionInfoItem);
            });
            positionGroup.put(positionType,itemODTOS);
        });
        //获取客户可展示的商品
        List<Long> showCommodityList = xdaPositionInfoStoreScopeMapper.selectCommodityPriceModelByStore(baseIDTO.getStoreId(),storeManageClient.isPfsStore(baseIDTO.getStoreId()));

        //获取可点击的单品详情
        List<Long> commodityIdList = positionInfoItemList.stream().filter(item -> XSAppPositionInfoTargetTypeEnums.单品详情页.getCode().equals(item.getTargetType())).distinct().map(item->Long.valueOf(item.getTargetTypeId())).collect(Collectors.toList());
        commodityIdList = this.queryCommodityAppStatus(commodityIdList,showCommodityList);

        List<XdaPositionInfoODTO> resultList = new ArrayList<>();
        //banner信息
        List<XdaPositionInfoItemODTO> bannerData = positionGroup.get(XSAppPositionTypeEnums.BANNER.getCode());
        if(SpringUtil.isNotEmpty(bannerData)){
            this.bannerData(bannerData,resultList,baseIDTO);
        }

        //横栏
        List<XdaPositionInfoItemODTO> HLData = this.setAppStatus(positionGroup.get(XSAppPositionTypeEnums.横栏.getCode()),commodityIdList);
        Map<Integer, XdaPositionInfoItemODTO> HLMap = new HashMap<>();
        if(SpringUtil.isNotEmpty(HLData)){
            HLMap = HLData.stream().collect(Collectors.toMap(XdaPositionInfoItemODTO::getPositionId, Function.identity()));
        }
        //横栏01
        XdaPositionInfoItemODTO HLDataO1 = HLMap.get(XSAppPositionIdEnums.横栏01.getCode());
        //设置服务栏和头图信息（应APP端要求，横栏01和服务栏放在同一个list返回，type=1011）
        List<XdaPositionInfoItemODTO> bgHeadData = positionGroup.get(XSAppPositionTypeEnums.头图.getCode());
        List<XdaPositionInfoItemODTO> serviceSectionData = positionGroup.get(XSAppPositionTypeEnums.服务说明栏.getCode());
        String HomeBgHeadPic = this.serviceSectionAndHeadPicData(HLDataO1,bgHeadData,serviceSectionData,resultList,baseIDTO);

        //横栏02
        XdaPositionInfoItemODTO HLDataO2 = HLMap.get(XSAppPositionIdEnums.横栏02.getCode());
        this.HLData(HLDataO2,resultList,baseIDTO);

        //ICON
        List<XdaPositionInfoItemODTO> iconData = positionGroup.get(XSAppPositionTypeEnums.ICON.getCode());
        if(SpringUtil.isNotEmpty(iconData)){
            this.iconData(iconData,resultList,baseIDTO);
        }

        // 积木组信息
        List<XdaPositionInfoItemODTO> blocksData = this.setAppStatus(positionGroup.get(XSAppPositionTypeEnums.积木组.getCode()),commodityIdList);
        if(SpringUtil.isNotEmpty(blocksData)){
            this.blocksData(blocksData,resultList,baseIDTO,showCommodityList);
        }

        // 推荐组信息
        List<XdaPositionInfoItemODTO> recommendData = positionGroup.get(XSAppPositionTypeEnums.推荐组.getCode());
        if(SpringUtil.isNotEmpty(recommendData)){
            this.recommendData(recommendData,resultList,baseIDTO,showCommodityList);
        }

        // 通栏信息
        List<XdaPositionInfoItemODTO> TLData = this.setAppStatus(positionGroup.get(XSAppPositionTypeEnums.通栏.getCode()),commodityIdList);
        if(SpringUtil.isNotEmpty(TLData)){
            this.TLData(TLData,resultList,baseIDTO,showCommodityList);
        }

        //横向资源
        this.favorXPosition(resultList,baseIDTO,showCommodityList);

        //首页弹框广告
        List<XdaPositionInfoItemODTO> popupAdList = this.setAppStatus(positionGroup.get(XSAppPositionTypeEnums.弹框广告.getCode()),commodityIdList);
        if(SpringUtil.isNotEmpty(popupAdList)){
            this.setHomePopupAd(dto,popupAdList,baseIDTO);
        }

        dto.setHomeBgHeadPic(this.picUrl(HomeBgHeadPic, PicSizeEnums.PIC_750x590.getSize(),true));
        dto.setItems(resultList);
        return dto;
    }
    public XdaHomePageODTO queryAppHomePositionInfoV4(HomeBaseIDTO baseIDTO) {
        XdaHomePageODTO dto = new XdaHomePageODTO();
        List<XdaPositionInfoItemODTO> positionInfoItemList = xdaPositionInfoMapper.selectShopPositionList(baseIDTO.getStoreId());
        if(SpringUtil.isEmpty(positionInfoItemList)){
            return dto;
        }

        //按资源位类型分组
        Map<Integer, List<XdaPositionInfoItemODTO>> positionGroup = new HashMap<>();
        positionInfoItemList.stream().collect(Collectors.groupingBy(XdaPositionInfoItemODTO::getPositionType)).forEach((positionType, typeItemList)->{
            List<XdaPositionInfoItemODTO> itemODTOS = new ArrayList<>();
            List<XdaPositionInfoItemODTO> sortTypeItemList = typeItemList.stream().sorted(Comparator.comparing(XdaPositionInfoItemODTO::getPositionId)).collect(Collectors.toList());
            sortTypeItemList.stream().collect(Collectors.groupingBy(XdaPositionInfoItemODTO::getPositionId,LinkedHashMap::new,Collectors.toList())).forEach((positionId, positionItemList)->{
                XdaPositionInfoItemODTO positionInfoItem = null;
                if(positionItemList.size()==1){
                    positionInfoItem = positionItemList.get(0);
                }else{
                    //同一个资源位下如果多条记录，取修改时间最新的一条
                    positionInfoItem = positionItemList.stream().sorted(Comparator.comparing(XdaPositionInfoItemODTO::getUpdateTime,Comparator.reverseOrder())).collect(Collectors.toList()).get(0);
                }
                itemODTOS.add(positionInfoItem);
            });
            positionGroup.put(positionType,itemODTOS);
        });
        //获取客户可展示的商品
        List<Long> showCommodityList = xdaPositionInfoStoreScopeMapper.selectCommodityPriceModelByStore(baseIDTO.getStoreId(),storeManageClient.isPfsStore(baseIDTO.getStoreId()));

        //获取可点击的单品详情
        List<Long> commodityIdList = positionInfoItemList.stream().filter(item -> XSAppPositionInfoTargetTypeEnums.单品详情页.getCode().equals(item.getTargetType())).distinct().map(item->Long.valueOf(item.getTargetTypeId())).collect(Collectors.toList());
        commodityIdList = this.queryCommodityAppStatus(commodityIdList,showCommodityList);

        List<XdaPositionInfoODTO> resultList = new ArrayList<>();
        //banner信息
        List<XdaPositionInfoItemODTO> bannerData = positionGroup.get(XSAppPositionTypeEnums.BANNER.getCode());
        if(SpringUtil.isNotEmpty(bannerData)){
            this.bannerData(bannerData,resultList,baseIDTO);
        }

        //横栏
        List<XdaPositionInfoItemODTO> HLData = this.setAppStatus(positionGroup.get(XSAppPositionTypeEnums.横栏.getCode()),commodityIdList);
        Map<Integer, XdaPositionInfoItemODTO> HLMap = new HashMap<>();
        if(SpringUtil.isNotEmpty(HLData)){
            HLMap = HLData.stream().collect(Collectors.toMap(XdaPositionInfoItemODTO::getPositionId, Function.identity()));
        }
        //横栏01
        XdaPositionInfoItemODTO HLDataO1 = HLMap.get(XSAppPositionIdEnums.横栏01.getCode());
        //设置服务栏和头图信息（应APP端要求，横栏01和服务栏放在同一个list返回，type=1011）
        List<XdaPositionInfoItemODTO> bgHeadData = positionGroup.get(XSAppPositionTypeEnums.头图.getCode());
        List<XdaPositionInfoItemODTO> serviceSectionData = positionGroup.get(XSAppPositionTypeEnums.服务说明栏.getCode());
        String HomeBgHeadPic = this.serviceSectionAndHeadPicData(HLDataO1,bgHeadData,serviceSectionData,resultList,baseIDTO);

        //横栏02
        XdaPositionInfoItemODTO HLDataO2 = HLMap.get(XSAppPositionIdEnums.横栏02.getCode());
        this.HLData(HLDataO2,resultList,baseIDTO);

        //ICON
        List<XdaPositionInfoItemODTO> iconData = positionGroup.get(XSAppPositionTypeEnums.ICON.getCode());
        if(SpringUtil.isNotEmpty(iconData)){
            this.iconData(iconData,resultList,baseIDTO);
        }

        // 积木组信息
        List<XdaPositionInfoItemODTO> blocksData = this.setAppStatus(positionGroup.get(XSAppPositionTypeEnums.积木组.getCode()),commodityIdList);
        if(SpringUtil.isNotEmpty(blocksData)){
            this.blocksData(blocksData,resultList,baseIDTO,showCommodityList);
        }

        // 推荐组信息
        List<XdaPositionInfoItemODTO> recommendData = positionGroup.get(XSAppPositionTypeEnums.推荐组.getCode());
        if(SpringUtil.isNotEmpty(recommendData)){
            this.recommendDataV4(recommendData,resultList,baseIDTO,showCommodityList);
        }

        // 通栏信息
        List<XdaPositionInfoItemODTO> TLData = this.setAppStatus(positionGroup.get(XSAppPositionTypeEnums.通栏.getCode()),commodityIdList);
        if(SpringUtil.isNotEmpty(TLData)){
            this.TLDataV4(TLData,resultList,baseIDTO,showCommodityList);
        }

        //横向资源
        this.favorXPosition(resultList,baseIDTO,showCommodityList);

        //首页弹框广告
        List<XdaPositionInfoItemODTO> popupAdList = this.setAppStatus(positionGroup.get(XSAppPositionTypeEnums.弹框广告.getCode()),commodityIdList);
        if(SpringUtil.isNotEmpty(popupAdList)){
            this.setHomePopupAd(dto,popupAdList,baseIDTO);
        }

        dto.setHomeBgHeadPic(this.picUrl(HomeBgHeadPic, PicSizeEnums.PIC_750x590.getSize(),true));
        dto.setItems(resultList);

        // 优惠券弹窗
        MtCouponUserNotifyODTO notifyODTO = mtCouponTobClient.queryCouponNotifyByStoreId(baseIDTO.getStoreId());
        dto.setNotify(notifyODTO);
        return dto;
    }

    public List<XdaPositionInfoItemODTO> setAppStatus(List<XdaPositionInfoItemODTO> positionInfoItemList, List<Long> commodityIdList){
        if(SpringUtil.isNotEmpty(positionInfoItemList)){
            for (XdaPositionInfoItemODTO positionInfoItem : positionInfoItemList) {
                if(positionInfoItem.getTargetType() != null && positionInfoItem.getTargetType().intValue()== XSAppPositionInfoTargetTypeEnums.单品详情页.getCode()){
                    String targetTypeId = positionInfoItem.getTargetTypeId();
                    if(StringUtils.isNotBlank(targetTypeId)){
                        if(commodityIdList.contains(Long.valueOf(targetTypeId))){
                            positionInfoItem.setAppStatus(0);
                        }else{
                            positionInfoItem.setAppStatus(1);
                        }
                    }
                }
            }
        }
        return positionInfoItemList;
    }

     public List<Long> queryCommodityAppStatus(List<Long> commodityIdList,List<Long> showCommodityList){
        List<Long> result = new ArrayList<>();
         if(SpringUtil.isNotEmpty(commodityIdList) && SpringUtil.isNotEmpty(showCommodityList)){
             for (Long commodityIds : commodityIdList) {
                 if(showCommodityList.contains(commodityIds)){
                     result.add(commodityIds);
                 }
             }
         }
         return result;
     }

    /**
     * 横向位商品数据查询
     * @param idto 横向位商品信息查询
     * */
	@Deprecated
    public PageInfo<XdaPositionInfoCommodityODTO> queryFavorXPositionCommodity(XdaPositionInfoCommodityIDTO idto,HomeBaseIDTO baseIDTO){
        QYAssert.isTrue(baseIDTO != null && baseIDTO.getStoreId() != null && baseIDTO.getStoreId() > 0 ,"未定位到前置仓");
        QYAssert.isTrue(idto.getPositionInfoId() != null && idto.getPositionInfoId() > 0,"横向位参数有误");
        PageInfo<XdaPositionInfoCommodityODTO> pageData = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            xdaFavorXPositionMapper.queryFavorXPositionCommodity(baseIDTO.getOrderTime(),idto.getPositionInfoId(),baseIDTO.getStoreId());
        });
        if(SpringUtil.isNotEmpty(pageData.getList())){
            //获取资源位商品数据
            List<Long> commdityIds = pageData.getList().stream().map(XdaPositionInfoCommodityODTO::getCommodityId).collect(Collectors.toList());
            Map<Long,XdaCommodityAppODTO> map = this.queryPositionInfoCommodityDataV2(commdityIds,PicSizeEnums.PIC_346x346,baseIDTO);
            Iterator<XdaPositionInfoCommodityODTO> it = pageData.getList().iterator();
            while(it.hasNext()){
                XdaPositionInfoCommodityODTO positionCommodity = it.next();
                //商品信息
                XdaCommodityAppODTO odto = map.get(positionCommodity.getCommodityId());
                if(odto != null){
                    positionCommodity.convertData(map,odto);
                }else{
                    it.remove();
                }
            }
        }
        return pageData;
    }
    public PageInfo<XdaPositionInfoCommodityODTO> queryFavorXPositionCommodityV4(XdaPositionInfoCommodityIDTO idto,HomeBaseIDTO baseIDTO){
        QYAssert.isTrue(baseIDTO != null && baseIDTO.getStoreId() != null && baseIDTO.getStoreId() > 0 ,"未定位到前置仓");

        StoreSelectODTO storeSelectODTO = storeManageClient.getStoreData(baseIDTO.getStoreId());
        Integer tdaStoreStatus = BusinessTypeEnums.TD_SALE.getCode().equals(storeSelectODTO.getBusinessType()) ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode();
        Integer businessType = getSafeBusinessType(storeSelectODTO.getBusinessType());

        QYAssert.isTrue(idto.getPositionInfoId() != null && idto.getPositionInfoId() > 0,"横向位参数有误");
        PageInfo<XdaPositionInfoCommodityODTO> pageData = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            Boolean isPfsStore = storeManageClient.isPfsStore(baseIDTO.getStoreId());
            xdaFavorXPositionMapperV4.queryFavorXPositionCommodityV4(baseIDTO.getOrderTime(), idto.getPositionInfoId(), baseIDTO.getStoreId(), businessType, isPfsStore);
        });
        if(SpringUtil.isNotEmpty(pageData.getList())){
            //获取资源位商品数据
            List<Long> commdityIds = pageData.getList().stream().map(XdaPositionInfoCommodityODTO::getCommodityId).collect(Collectors.toList());
            Map<Long,XdaCommodityAppV4ODTO> map = this.queryPositionInfoCommodityDataV4(commdityIds,PicSizeEnums.PIC_346x346,baseIDTO);
            Iterator<XdaPositionInfoCommodityODTO> it = pageData.getList().iterator();
            while(it.hasNext()){
                XdaPositionInfoCommodityODTO positionCommodity = it.next();
                //商品信息
                XdaCommodityAppV4ODTO odto = map.get(positionCommodity.getCommodityId());
                if(odto != null){
                    positionCommodity.convertDataV4(odto);
                }else{
                    it.remove();
                }
            }
        }
        return pageData;
    }

    /**
     * 设置纵向位信息
     * */
    @Deprecated
    public PageInfo<XdaHomeFavorYPositionInfoODTO> favorYPosition(XdaPositionInfoCommodityIDTO idto, HomeBaseIDTO baseIDTO){
        Map<Integer, XdaHomeFavorYPositionInfoODTO> favorYPositionInfoMap = new HashMap<>();
        //广告总条数
        int maxPositionId = 0;
        List<String> adImgList = new ArrayList<>();
        //获取客户可展示的商品
        List<Long> showCommodityList = xdaPositionInfoStoreScopeMapper.selectCommodityPriceModelByStore(baseIDTO.getStoreId(), storeManageClient.isPfsStore(baseIDTO.getStoreId()));
        if(idto.getPageNo() == 1){
            PageInfo<XdaFavorYPositionInfoODTO> favorYPage =  PageHelper.startPage(idto.getPageNo(), 20).doSelectPageInfo(() -> {
                xdaFavorYPositionMapper.queryHomeFavorYPositionPage(baseIDTO.getStoreId());
            });
            if(favorYPage != null && SpringUtil.isNotEmpty(favorYPage.getList())){
                //获取商品上架情况
                List<Long> commodityIdList = favorYPage.getList().stream().filter(item -> XSAppPositionInfoTargetTypeEnums.单品详情页.getCode().equals(item.getTargetType())).distinct().map(item->Long.valueOf(item.getTargetTypeId())).collect(Collectors.toList());
                commodityIdList = this.queryCommodityAppStatus(commodityIdList,showCommodityList);
                for (XdaFavorYPositionInfoODTO entry : favorYPage.getList()) {
                    if(entry.getTargetType() != null && entry.getTargetType().intValue()== XSAppPositionInfoTargetTypeEnums.单品详情页.getCode()){
                        String targetTypeId = entry.getTargetTypeId();
                        if(StringUtils.isNotBlank(targetTypeId)){
                            if(commodityIdList.contains(Long.valueOf(targetTypeId))){
                                entry.setAppStatus(0);
                            }else{
                                entry.setAppStatus(1);
                            }
                        }
                    }
                    if(this.checkTargetCommodityValid(entry.getTargetType(),entry.getAppStatus())){
                        Integer positionId = entry.getPositionId() - 1800;
                        if(favorYPositionInfoMap.keySet().contains(positionId)){
                            continue;
                        }
                        XdaHomeFavorYPositionInfoODTO ad = BeanCloneUtils.copyTo(entry, XdaHomeFavorYPositionInfoODTO.class);
                        ad.setIsAd(1);
                        ad.setTargetTypeUrl(this.targetUrl(entry.getTargetType(),Long.valueOf(entry.getTargetTypeId()),baseIDTO.getStoreId(),baseIDTO.getOrderTime()));
                        favorYPositionInfoMap.put(positionId,ad);
                        if(positionId != null && positionId > maxPositionId){
                            maxPositionId = positionId;
                        }
                        adImgList.add(entry.getPicUrl());
                    }
                }
            }
        }

        List<XdaHomeFavorYPositionInfoODTO> favorYPositionInfoList = new ArrayList<>();
        PageInfo<XdaHomeFavorYPositionInfoODTO> page = null;

        try {
            //获取推荐商品
            PageInfo<Long> commodityIdPageInfo = PageHelper.startPage(idto.getPageNo(), 20).doSelectPageInfo(() -> {
                xdaFavorYPositionMapper.selectYPositionCommodityList(baseIDTO.getOrderTime(),baseIDTO.getStoreId());
            });
            List<Long> commodityIdList = commodityIdPageInfo.getList();
            if(SpringUtil.isNotEmpty(commodityIdList)){
                Map<Long,XdaCommodityAppODTO> map = this.queryPositionInfoCommodityDataV2(commodityIdList,PicSizeEnums.PIC_200x200,baseIDTO);
                //商品数量大于等于4个才展示
                if(map!=null && map.size() >= 4){
                    Map<String,ImageLibraryODTO> imgMap = new HashMap<>(20);
                    if(SpringUtil.isNotEmpty( adImgList )){
                        imgMap = this.adPic(adImgList,false);
                    }
                    //总循环次数
                    int allLoopNum = commodityIdList.size() + maxPositionId;
                    int commdityIndex = 0;
                    for (int i = 0; i < allLoopNum; i++) {
                        XdaHomeFavorYPositionInfoODTO favorYPositionInfo = favorYPositionInfoMap.get(i+1);
                        if(favorYPositionInfo !=null){
                            ImageLibraryODTO imageLibrary = imgMap.get(this.splitImg(favorYPositionInfo.getPicUrl()));
                            if(imageLibrary == null){
                                imgMap = this.adPic(adImgList,true);
                                imageLibrary = imgMap.get(this.splitImg(favorYPositionInfo.getPicUrl()));
                            }
                            if(imageLibrary != null){
                                favorYPositionInfo.setRealHeight(imageLibrary.getRealHeight());
                                favorYPositionInfo.setRealWidth(imageLibrary.getRealWidth());
                                favorYPositionInfo.setPicUrl(imageLibrary.getImgVisitUrl());
                            }
                            favorYPositionInfoList.add(favorYPositionInfo);
                        }else{
                            if(commdityIndex <= commodityIdList.size()-1){
                                XdaCommodityAppODTO commodityInfo = map.get(commodityIdList.get(commdityIndex));
                                if(commodityInfo!=null){
                                    favorYPositionInfo = BeanCloneUtils.copyTo(commodityInfo, XdaHomeFavorYPositionInfoODTO.class);
                                    favorYPositionInfo.setIsAd(0);
                                    favorYPositionInfoList.add(favorYPositionInfo);
                                }

                            }
                            commdityIndex += 1;
                        }
                    }
                    page = new PageInfo<>(favorYPositionInfoList);
                    page.setPageNum(idto.getPageNo());
                    page.setPageSize(20);
                    page.setHasNextPage(commodityIdPageInfo.isHasNextPage());
                    return page;
                }
            }
        } catch (Exception e) {
            log.error("获取首页猜你喜欢商品信息异常，异常堆栈：{}",e);
        }
        page = new PageInfo<>(favorYPositionInfoList);
        page.setPageNum(idto.getPageNo());
        page.setPageSize(20);
        page.setHasNextPage(false);
        return page;
    }
    public PageInfo<XdaHomeFavorYPositionInfoODTO> favorYPositionV4(XdaPositionInfoCommodityIDTO idto, HomeBaseIDTO baseIDTO){
        Map<Integer, XdaHomeFavorYPositionInfoODTO> favorYPositionInfoMap = new HashMap<>();
        //广告总条数
        int maxPositionId = 0;
        List<String> adImgList = new ArrayList<>();
//        //获取客户可展示的商品
//        List<Long> showCommodityList = xdaPositionInfoStoreScopeMapper.selectCommodityPriceModelByStore(baseIDTO.getStoreId());
        if(idto.getPageNo() == 1){
            PageInfo<XdaFavorYPositionInfoODTO> favorYPage =  PageHelper.startPage(idto.getPageNo(), 20).doSelectPageInfo(() -> {
                xdaFavorYPositionMapper.queryHomeFavorYPositionPage(baseIDTO.getStoreId());
            });
            if(favorYPage != null && SpringUtil.isNotEmpty(favorYPage.getList())){
                //获取客户可展示的商品
                List<Long> showCommodityList = xdaPositionInfoStoreScopeMapper.selectCommodityPriceModelByStore(baseIDTO.getStoreId(),storeManageClient.isPfsStore(baseIDTO.getStoreId()));
                //获取商品上架情况
                List<Long> commodityIdList = favorYPage.getList().stream().filter(item -> XSAppPositionInfoTargetTypeEnums.单品详情页.getCode().equals(item.getTargetType())).distinct().map(item->Long.valueOf(item.getTargetTypeId())).collect(Collectors.toList());
                commodityIdList = this.queryCommodityAppStatus(commodityIdList,showCommodityList);
                for (XdaFavorYPositionInfoODTO entry : favorYPage.getList()) {
                    if(entry.getTargetType() != null && entry.getTargetType().intValue()== XSAppPositionInfoTargetTypeEnums.单品详情页.getCode()){
                        String targetTypeId = entry.getTargetTypeId();
                        if(StringUtils.isNotBlank(targetTypeId)){
                            if(commodityIdList.contains(Long.valueOf(targetTypeId))){
                                entry.setAppStatus(0);
                            }else{
                                entry.setAppStatus(1);
                            }
                        }
                    }
                    if(this.checkTargetCommodityValid(entry.getTargetType(),entry.getAppStatus())){
                        Integer positionId = entry.getPositionId() - 1800;
                        if(favorYPositionInfoMap.keySet().contains(positionId)){
                            continue;
                        }
                        XdaHomeFavorYPositionInfoODTO ad = BeanCloneUtils.copyTo(entry, XdaHomeFavorYPositionInfoODTO.class);
                        ad.setIsAd(1);
                        ad.setTargetTypeUrl(this.targetUrl(entry.getTargetType(),Long.valueOf(entry.getTargetTypeId()),baseIDTO.getStoreId(),baseIDTO.getOrderTime()));
                        favorYPositionInfoMap.put(positionId,ad);
                        if(positionId != null && positionId > maxPositionId){
                            maxPositionId = positionId;
                        }
                        adImgList.add(entry.getPicUrl());
                    }
                }
            }
        }

        List<XdaHomeFavorYPositionInfoODTO> favorYPositionInfoList = new ArrayList<>();
        PageInfo<XdaHomeFavorYPositionInfoODTO> page = null;

        try {
            StoreSelectODTO storeSelectODTO = storeManageClient.getStoreData(baseIDTO.getStoreId());
            Integer tdaStoreStatus = BusinessTypeEnums.TD_SALE.getCode().equals(storeSelectODTO.getBusinessType()) ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode();

            Integer businessType = getSafeBusinessType(storeSelectODTO.getBusinessType());
            //获取推荐商品
            PageInfo<Long> commodityIdPageInfo = PageHelper.startPage(idto.getPageNo(), 20).doSelectPageInfo(() -> {
                xdaFavorYPositionMapperV4.selectYPositionCommodityListV4(baseIDTO.getOrderTime(),baseIDTO.getStoreId(), businessType);
            });
            List<Long> commodityIdList = commodityIdPageInfo.getList();
            if(SpringUtil.isNotEmpty(commodityIdList)){
                //PicSizeEnums.PIC_200x200->null http://192.168.0.213/zentao/story-view-11306.html###
                Map<Long,XdaCommodityAppV4ODTO> map = this.queryPositionInfoCommodityDataV4(commodityIdList,null,baseIDTO);
                //商品数量大于等于4个才展示
                if(map!=null && map.size() >= 4){
                    Map<String,ImageLibraryODTO> imgMap = new HashMap<>(20);
                    if(SpringUtil.isNotEmpty( adImgList )){
                        imgMap = this.adPic(adImgList,false);
                    }
                    //总循环次数
                    int allLoopNum = commodityIdList.size() + maxPositionId;
                    int commdityIndex = 0;
                    for (int i = 0; i < allLoopNum; i++) {
                        XdaHomeFavorYPositionInfoODTO favorYPositionInfo = favorYPositionInfoMap.get(i+1);
                        if(favorYPositionInfo !=null){
                            ImageLibraryODTO imageLibrary = imgMap.get(this.splitImg(favorYPositionInfo.getPicUrl()));
                            if(imageLibrary == null){
                                imgMap = this.adPic(adImgList,true);
                                imageLibrary = imgMap.get(this.splitImg(favorYPositionInfo.getPicUrl()));
                            }
                            if(imageLibrary != null){
                                favorYPositionInfo.setRealHeight(imageLibrary.getRealHeight());
                                favorYPositionInfo.setRealWidth(imageLibrary.getRealWidth());
                                favorYPositionInfo.setPicUrl(imageLibrary.getImgVisitUrl());
                            }
                            favorYPositionInfoList.add(favorYPositionInfo);
                        }else{
                            if(commdityIndex <= commodityIdList.size()-1){
                                XdaCommodityAppV4ODTO commodityInfo = map.get(commodityIdList.get(commdityIndex));
                                if(commodityInfo!=null){
                                    favorYPositionInfo = BeanCloneUtils.copyTo(commodityInfo, XdaHomeFavorYPositionInfoODTO.class);
                                    favorYPositionInfo.setIsAd(0);
                                    favorYPositionInfoList.add(favorYPositionInfo);
                                }

                            }
                            commdityIndex += 1;
                        }
                    }
                    page = new PageInfo<>(favorYPositionInfoList);
                    page.setPageNum(idto.getPageNo());
                    page.setPageSize(20);
                    page.setHasNextPage(commodityIdPageInfo.isHasNextPage());
                    return page;
                }
            }
        } catch (Exception e) {
            log.error("获取首页猜你喜欢商品信息异常，异常堆栈：{}",e);
        }
        page = new PageInfo<>(favorYPositionInfoList);
        page.setPageNum(idto.getPageNo());
        page.setPageSize(20);
        page.setHasNextPage(false);
        return page;
    }

    public Map<String,ImageLibraryODTO> adPic(List<String> adImgList,Boolean flag){
        //获取广告图片尺寸
        RBucket<List<ImageLibraryODTO>> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile+":"+RedisKeyPrefixConst.XD_HOME_AD_TIME);
        List<ImageLibraryODTO> ImgData = bucket.get();
        if(SpringUtil.isEmpty(ImgData) || flag){
            adImgList = this.splitImgList(adImgList) ;
            ImgData = imageLibraryClient.findOriImgList( adImgList );
            bucket.set(ImgData, 1, TimeUnit.HOURS);
            //获取商品图片
            //List<String> commdityImgList = commodityItemList.stream().map(DefaultRpcCommodityItemDTO::getThumbnail).collect(Collectors.toList());
            //ImgData.addAll(imageLibraryClient.findImgList(this.splitImgList(commdityImgList),PicSizeEnums.PIC_346x346));
        }
        if(SpringUtil.isNotEmpty(ImgData)){
            Map<String,ImageLibraryODTO> imgMap = ImgData.stream().collect(Collectors.toMap(ImageLibraryODTO::getImgName, Function.identity()));
            return imgMap;
        }
        log.error("adImgList={},未获取到图片尺寸数据",adImgList.toString());
        return Collections.EMPTY_MAP;
    }

    public List<String> splitImgList(List<String> urls){
        List<String> urlList = new ArrayList<>();
        if(urls != null && urls.size()>0){
            urls.forEach(url->{
                if(StringUtils.isNotBlank(url)){
                    urlList.add(this.splitImg(url));
                }
            });
            return urlList;
        }
        return null;
    }

    public String splitImg(String url){
        if(StringUtils.isNotBlank(url)){
            String[] infoArray = url.split("[/\\\\]+");
            return infoArray[infoArray.length-1];
        }
        return null;
    }



    /**
     * 设置banner信息
     * */
    public void bannerData(List<XdaPositionInfoItemODTO> blockAppItemList, List<XdaPositionInfoODTO> blockAppList,HomeBaseIDTO baseIDTO){
        XdaPositionInfoODTO blockApp = new XdaPositionInfoODTO();
        blockApp.setItems(blockAppItemList);
        blockApp.setType(BlockTypeConstant.BLOCK_101);
        this.updateImgSizeList(blockAppItemList,baseIDTO.getStoreId(), PicSizeEnums.PIC_710x320.getSize(),baseIDTO.getOrderTime());
        blockAppList.add(blockApp);
    }

    /**
     * 设置ICON信息
     * */
    public void iconData(List<XdaPositionInfoItemODTO> blockAppItemList, List<XdaPositionInfoODTO> blockAppList,HomeBaseIDTO baseIDTO){
        XdaPositionInfoODTO blockApp = new XdaPositionInfoODTO();
        blockApp.setItems(blockAppItemList);
        blockApp.setType(BlockTypeConstant.BLOCK_201);
        this.updateImgSizeList(blockAppItemList, baseIDTO.getStoreId(), PicSizeEnums.PIC_110x110.getSize(),baseIDTO.getOrderTime());
        blockAppList.add(blockApp);
    }

    /**
     * 设置积木信息
     * */
    public void blocksData(List<XdaPositionInfoItemODTO> blockAppItemList, List<XdaPositionInfoODTO> blockAppList,HomeBaseIDTO baseIDTO,List<Long> showCommodityList){
        //积木组03名称
        String blockName03 = "";
        //积木组2和积木组3信息map
        Map<Long, XdaPositionInfoItemODTO> blockItemMap = new HashMap<>();
        for (XdaPositionInfoItemODTO block : blockAppItemList) {
            if (block.getPositionId().equals(XSAppPositionIdEnums.积木组01.getCode())) {
                if(this.checkTargetCommodityValid(block.getTargetType(),block.getAppStatus())){
                    XdaPositionInfoODTO blockApp = new XdaPositionInfoODTO();
                    blockApp.setType(BlockTypeConstant.BLOCK_301);
                    this.updateImgSize(block,baseIDTO.getStoreId(), PicSizeEnums.PIC_750x240.getSize(),baseIDTO.getOrderTime());
                    blockApp.setItems(new ArrayList<XdaPositionInfoItemODTO>() {{add(block);}});
                    blockAppList.add(blockApp);
                }
            } else {
                if(block.getPositionId().equals(XSAppPositionIdEnums.积木组03.getCode())){
                    blockName03 = block.getLabel();
                }
                blockItemMap.put(block.getPositionInfoId(),block);
            }
        }
        if(blockItemMap!=null && !blockItemMap.isEmpty()){
            //获取积木组2和积木组3的数据
            List<XdaBlockODTO> blockList = xdaPositionInfoBlockMapper.selectSubBlockList(new ArrayList<>(blockItemMap.keySet()));
            if (blockList != null && blockList.size() > 0) {
                //获取可点击跳转的商品信息
                List<Long> commodityIdList = blockList.stream().filter(item -> item.getTargetType().equals(4)).distinct().map(item->Long.valueOf(item.getTargetTypeId())).collect(Collectors.toList());
                commodityIdList = this.queryCommodityAppStatus(commodityIdList,showCommodityList);

                Map<Integer, List<XdaBlockODTO>> BlockMap = blockList.stream().collect(Collectors.groupingBy(XdaBlockODTO::getPositionType));
                //积木组02
                List<XdaBlockODTO> block02List = BlockMap.get(XSAppPositionIdEnums.积木组02.getCode());
                if(block02List != null && block02List.size() > 0){
                    XdaPositionInfoODTO result = new XdaPositionInfoODTO();
                    result.setType(BlockTypeConstant.BLOCK_302);
                    List<XdaPositionInfoItemODTO> positionItemList = new ArrayList<>();
                    for (XdaBlockODTO block : block02List) {
                        XdaPositionInfoItemODTO positionItem = BeanCloneUtils.copyTo(block, XdaPositionInfoItemODTO.class);
                        if(positionItem.getTargetType() != null && positionItem.getTargetType().intValue()== XSAppPositionInfoTargetTypeEnums.单品详情页.getCode()){
                            String targetTypeId = positionItem.getTargetTypeId();
                            if(StringUtils.isNotBlank(targetTypeId)){
                                if(commodityIdList.contains(Long.valueOf(targetTypeId))){
                                    positionItem.setAppStatus(0);
                                }else{
                                    positionItem.setAppStatus(1);
                                }
                            }
                        }
                        if(block.getPositionId().equals(XSAppPositionBlockIdEnums.积木组02_左侧.getCode())){
                            this.updateImgSize(positionItem,baseIDTO.getStoreId(), PicSizeEnums.PIC_355x360.getSize(),baseIDTO.getOrderTime());
                        }else{
                            this.updateImgSize(positionItem,baseIDTO.getStoreId(), PicSizeEnums.PIC_355x180.getSize(),baseIDTO.getOrderTime());
                        }
                        if(!this.checkTargetCommodityValid(positionItem.getTargetType(),positionItem.getAppStatus())){
                            positionItem.setTargetTypeId(null);//绑定单品，如果商品在门店下架或不存在，将资源位链接置空，不跳转到商品详情
                        }
                        positionItemList.add(positionItem);
                    }
                    result.setItems(positionItemList);
                    blockAppList.add(result);
                }
                //积木组03
                List<XdaBlockODTO> block03List = BlockMap.get(XSAppPositionIdEnums.积木组03.getCode());
                if(block03List != null && block03List.size() > 0){
                    XdaPositionInfoODTO result = new XdaPositionInfoODTO();
                    result.setType(BlockTypeConstant.BLOCK_303);
                    result.setLabel(blockName03);
                    List<XdaPositionInfoItemODTO> positionItemList = new ArrayList<>();
                    for (XdaBlockODTO block : block03List) {
                        XdaPositionInfoItemODTO positionItem = BeanCloneUtils.copyTo(block, XdaPositionInfoItemODTO.class);
                        if(positionItem.getTargetType() != null && positionItem.getTargetType().intValue()== XSAppPositionInfoTargetTypeEnums.单品详情页.getCode()){
                            String targetTypeId = positionItem.getTargetTypeId();
                            if(StringUtils.isNotBlank(targetTypeId)){
                                if(commodityIdList.contains(Long.valueOf(targetTypeId))){
                                    positionItem.setAppStatus(0);
                                }else{
                                    positionItem.setAppStatus(1);
                                }
                            }
                        }
                        if(block.getPositionId().equals(XSAppPositionBlockIdEnums.积木组03_左上.getCode()) ||
                                block.getPositionId().equals(XSAppPositionBlockIdEnums.积木组03_左下.getCode())||
                                block.getPositionId().equals(XSAppPositionBlockIdEnums.积木组03_右上.getCode())){
                            this.updateImgSize(positionItem,baseIDTO.getStoreId(), PicSizeEnums.PIC_355x220.getSize(),baseIDTO.getOrderTime());
                        }else{
                            this.updateImgSize(positionItem,baseIDTO.getStoreId(), PicSizeEnums.PIC_178x220.getSize(),baseIDTO.getOrderTime());
                        }
                        if(!this.checkTargetCommodityValid(positionItem.getTargetType(),positionItem.getAppStatus())){
                            positionItem.setTargetTypeId(null);//绑定单品，如果商品在门店下架或不存在，将资源位链接置空，不跳转到商品详情
                        }
                        positionItemList.add(positionItem);
                    }
                    result.setItems(positionItemList);
                    blockAppList.add(result);
                }
            }
        }
    }

    /**
     * 设置推荐组信息
     * */
    @Deprecated
    public void recommendData(List<XdaPositionInfoItemODTO> blockAppItemList, List<XdaPositionInfoODTO> blockAppList,HomeBaseIDTO baseIDTO,List<Long> showCommodityList){
        XdaPositionInfoItemODTO positionInfoItem = blockAppItemList.get(0);
        // 查询资源位信息，最多只展示15个商品
        List<XdaPositionInfoCommodityODTO> positionCommodityList =
                xdaPositionInfoCommodityMapper.selectPositionCommodityList(new ArrayList<Long>(){{add(positionInfoItem.getPositionInfoId());}}, null);
        if(SpringUtil.isNotEmpty(positionCommodityList)){
            List<Long> commdityIds = positionCommodityList.stream().map(XdaPositionInfoCommodityODTO::getCommodityId).collect(Collectors.toList());
            commdityIds = this.queryCommodityAppStatus(commdityIds,showCommodityList);
            Map<Long,XdaCommodityAppODTO> map = this.queryPositionInfoCommodityDataV2(commdityIds,PicSizeEnums.PIC_200x200,baseIDTO);
            Iterator<XdaPositionInfoCommodityODTO> it = positionCommodityList.iterator();
            while(it.hasNext()){
                XdaPositionInfoCommodityODTO positionCommodity = it.next();
                //商品信息
                XdaCommodityAppODTO odto = map.get(positionCommodity.getCommodityId());
                if(odto != null){
                    positionCommodity.convertData(map,odto);
                }else{
                    it.remove();
                }
            }
        }

        // 至少展示7个商品
        if (positionCommodityList.size() >= 7) {
            Comparator<XdaPositionInfoCommodityODTO> compare = Comparator.comparing(XdaPositionInfoCommodityODTO::getIsCanOrder,Comparator.reverseOrder());
            XdaPositionInfoODTO blockApp = new XdaPositionInfoODTO();
            blockApp.setLabel(positionInfoItem.getLabel());
            blockApp.setPicUrl(positionInfoItem.getPicUrl());
            blockApp.setType(BlockTypeConstant.BLOCK_401);
            blockApp.setList(positionCommodityList.stream().sorted(compare).collect(Collectors.toList()));
            blockApp.setItems(blockAppItemList);
            blockAppList.add(blockApp);
        }
    }
    public void recommendDataV4(List<XdaPositionInfoItemODTO> blockAppItemList, List<XdaPositionInfoODTO> blockAppList,HomeBaseIDTO baseIDTO,List<Long> showCommodityList){
        XdaPositionInfoItemODTO positionInfoItem = blockAppItemList.get(0);
        // 查询资源位信息，最多只展示15个商品
        List<XdaPositionInfoCommodityODTO> positionCommodityList =
                xdaPositionInfoCommodityMapper.selectPositionCommodityList(new ArrayList<Long>(){{add(positionInfoItem.getPositionInfoId());}}, null);
        if(SpringUtil.isNotEmpty(positionCommodityList)){
            List<Long> commdityIds = positionCommodityList.stream().map(XdaPositionInfoCommodityODTO::getCommodityId).collect(Collectors.toList());
            commdityIds = this.queryCommodityAppStatus(commdityIds,showCommodityList);
            Map<Long,XdaCommodityAppV4ODTO> map = this.queryPositionInfoCommodityDataV4(commdityIds,PicSizeEnums.PIC_200x200,baseIDTO);
            Iterator<XdaPositionInfoCommodityODTO> it = positionCommodityList.iterator();
            while(it.hasNext()){
                XdaPositionInfoCommodityODTO positionCommodity = it.next();
                //商品信息
                XdaCommodityAppV4ODTO odto = map.get(positionCommodity.getCommodityId());
                if(odto != null){
                    positionCommodity.convertDataV4(odto);
                }else{
                    it.remove();
                }
            }
        }

        // 至少展示7个商品
        if (positionCommodityList.size() >= 7) {
            Comparator<XdaPositionInfoCommodityODTO> compare = Comparator.comparing(XdaPositionInfoCommodityODTO::getIsCanOrder,Comparator.reverseOrder());
            XdaPositionInfoODTO blockApp = new XdaPositionInfoODTO();
            blockApp.setLabel(positionInfoItem.getLabel());
            blockApp.setPicUrl(positionInfoItem.getPicUrl());
            blockApp.setType(BlockTypeConstant.BLOCK_401);
            blockApp.setList(positionCommodityList.stream().sorted(compare).collect(Collectors.toList()));
            blockApp.setItems(blockAppItemList);
            blockAppList.add(blockApp);
        }
    }

    /**
     * 设置通栏信息
     * */
    @Deprecated
    public void TLData(List<XdaPositionInfoItemODTO> blockAppItemList, List<XdaPositionInfoODTO> blockAppList,HomeBaseIDTO baseIDTO,List<Long> showCommodityList){
        // 资源位ids
        List<Long> positionInfoIdList = blockAppItemList.stream().mapToLong(XdaPositionInfoItemODTO::getPositionInfoId).boxed().collect(Collectors.toList());
        // 查询所有通栏的商品明细list---每组最多展示20个
        List<XdaPositionInfoCommodityODTO> tlCommodityList = xdaPositionInfoCommodityMapper.selectPositionCommodityList(positionInfoIdList, null);
        if(SpringUtil.isNotEmpty(tlCommodityList)){
            List<Long> commdityIds = tlCommodityList.stream().map(XdaPositionInfoCommodityODTO::getCommodityId).collect(Collectors.toList());
            commdityIds = this.queryCommodityAppStatus(commdityIds,showCommodityList);
            Map<Long,XdaCommodityAppODTO> map = this.queryPositionInfoCommodityDataV2(commdityIds,PicSizeEnums.PIC_200x200,baseIDTO);
            Iterator<XdaPositionInfoCommodityODTO> it = tlCommodityList.iterator();
            while(it.hasNext()){
                XdaPositionInfoCommodityODTO positionCommodity = it.next();
                //商品信息
                XdaCommodityAppODTO odto = map.get(positionCommodity.getCommodityId());
                if(odto != null){
                    positionCommodity.convertData(map, odto);
                }else{
                    it.remove();
                }
            }
        }
        // 保存每个通栏对应的商品list
        Map<Long, List<XdaPositionInfoCommodityODTO>> map = new HashMap<>();
        if(SpringUtil.isNotEmpty(tlCommodityList)){
            tlCommodityList.forEach(positionCommodity -> {
                if (map.get(positionCommodity.getPositionInfoId()) == null) {
                    List<XdaPositionInfoCommodityODTO> itemList = new ArrayList<>();
                    itemList.add(positionCommodity);
                    map.put(positionCommodity.getPositionInfoId(), itemList);
                }else{
                    if (map.containsKey(positionCommodity.getPositionInfoId()) && map.get(positionCommodity.getPositionInfoId()).size() < 20) {
                        map.get(positionCommodity.getPositionInfoId()).add(positionCommodity);
                    }
                }
            });

            // 组合通栏数据
            for (XdaPositionInfoItemODTO entry : blockAppItemList) {
                // 至少展示7个商品
                List<XdaPositionInfoCommodityODTO> positionInfoCommodityList = map.get(entry.getPositionInfoId());
                if (positionInfoCommodityList != null && positionInfoCommodityList.size() >= 7) {
                    Comparator<XdaPositionInfoCommodityODTO> compare = Comparator.comparing(XdaPositionInfoCommodityODTO::getIsCanOrder,Comparator.reverseOrder());
                    XdaPositionInfoODTO positionInfo = new XdaPositionInfoODTO();
                    positionInfo.setType(BlockTypeConstant.BLOCK_501);
                    this.updateImgSize(entry,baseIDTO.getStoreId(), PicSizeEnums.PIC_750x240.getSize(),baseIDTO.getOrderTime());
                    if(!this.checkTargetCommodityValid(entry.getTargetType(),entry.getAppStatus())){
                        entry.setTargetTypeId(null);
                    }
                    positionInfo.setItems(new ArrayList<XdaPositionInfoItemODTO>(){{add(entry);}});
                    // 设置商品信息
                    positionInfo.setList(positionInfoCommodityList.stream().sorted(compare).collect(Collectors.toList()));
                    blockAppList.add(positionInfo);
                }
            }
        }
    }
    public void TLDataV4(List<XdaPositionInfoItemODTO> blockAppItemList, List<XdaPositionInfoODTO> blockAppList,HomeBaseIDTO baseIDTO,List<Long> showCommodityList){
        // 资源位ids
        List<Long> positionInfoIdList = blockAppItemList.stream().mapToLong(XdaPositionInfoItemODTO::getPositionInfoId).boxed().collect(Collectors.toList());
        // 查询所有通栏的商品明细list---每组最多展示20个
        List<XdaPositionInfoCommodityODTO> tlCommodityList = xdaPositionInfoCommodityMapper.selectPositionCommodityList(positionInfoIdList, null);
        if(SpringUtil.isNotEmpty(tlCommodityList)){
            List<Long> commdityIds = tlCommodityList.stream().map(XdaPositionInfoCommodityODTO::getCommodityId).collect(Collectors.toList());
            commdityIds = this.queryCommodityAppStatus(commdityIds,showCommodityList);
            Map<Long,XdaCommodityAppV4ODTO> map = this.queryPositionInfoCommodityDataV4(commdityIds,PicSizeEnums.PIC_200x200,baseIDTO);
            Iterator<XdaPositionInfoCommodityODTO> it = tlCommodityList.iterator();
            while(it.hasNext()){
                XdaPositionInfoCommodityODTO positionCommodity = it.next();
                //商品信息
                XdaCommodityAppV4ODTO odto = map.get(positionCommodity.getCommodityId());
                if(odto != null){
                    positionCommodity.convertDataV4(odto);
                }else{
                    it.remove();
                }
            }
        }
        // 保存每个通栏对应的商品list
        Map<Long, List<XdaPositionInfoCommodityODTO>> map = new HashMap<>();
        if(SpringUtil.isNotEmpty(tlCommodityList)){
            tlCommodityList.forEach(positionCommodity -> {
                if (map.get(positionCommodity.getPositionInfoId()) == null) {
                    List<XdaPositionInfoCommodityODTO> itemList = new ArrayList<>();
                    itemList.add(positionCommodity);
                    map.put(positionCommodity.getPositionInfoId(), itemList);
                }else{
                    if (map.containsKey(positionCommodity.getPositionInfoId()) && map.get(positionCommodity.getPositionInfoId()).size() < 20) {
                        map.get(positionCommodity.getPositionInfoId()).add(positionCommodity);
                    }
                }
            });

            // 组合通栏数据
            for (XdaPositionInfoItemODTO entry : blockAppItemList) {
                // 至少展示7个商品
                List<XdaPositionInfoCommodityODTO> positionInfoCommodityList = map.get(entry.getPositionInfoId());
                if (positionInfoCommodityList != null && positionInfoCommodityList.size() >= 7) {
                    Comparator<XdaPositionInfoCommodityODTO> compare = Comparator.comparing(XdaPositionInfoCommodityODTO::getIsCanOrder,Comparator.reverseOrder());
                    XdaPositionInfoODTO positionInfo = new XdaPositionInfoODTO();
                    positionInfo.setType(BlockTypeConstant.BLOCK_501);
                    this.updateImgSize(entry,baseIDTO.getStoreId(), PicSizeEnums.PIC_750x240.getSize(),baseIDTO.getOrderTime());
                    if(!this.checkTargetCommodityValid(entry.getTargetType(),entry.getAppStatus())){
                        entry.setTargetTypeId(null);
                    }
                    positionInfo.setItems(new ArrayList<XdaPositionInfoItemODTO>(){{add(entry);}});
                    // 设置商品信息
                    positionInfo.setList(positionInfoCommodityList.stream().sorted(compare).collect(Collectors.toList()));
                    blockAppList.add(positionInfo);
                }
            }
        }
    }

    /**
     * 设置横栏02
     * */
    public void HLData(XdaPositionInfoItemODTO HLData, List<XdaPositionInfoODTO> blockAppList,HomeBaseIDTO baseIDTO){
        XdaPositionInfoODTO blockApp = new XdaPositionInfoODTO();
        if (HLData != null){
            if(this.checkTargetCommodityValid(HLData.getTargetType(),HLData.getAppStatus())){
                blockApp.setType(BlockTypeConstant.BLOCK_602);
                this.updateImgSize(HLData,baseIDTO.getStoreId(), PicSizeEnums.PIC_750x240.getSize(),baseIDTO.getOrderTime());
                blockApp.setItems(Collections.singletonList(HLData));
                blockAppList.add(blockApp);
            }
        }
    }

    /**
     * 设置头图和服务栏信息
     * */
    public String serviceSectionAndHeadPicData(XdaPositionInfoItemODTO HLDataO1, List<XdaPositionInfoItemODTO> bgHeadData, List<XdaPositionInfoItemODTO> serviceSectionData, List<XdaPositionInfoODTO> blockAppList,HomeBaseIDTO baseIDTO){
        //设置服务栏
        String HomeBgHeadPic = "";
        List<XdaPositionInfoItemODTO> items = new ArrayList<>();
        XdaPositionInfoODTO result = new XdaPositionInfoODTO();
        result.setType(BlockTypeConstant.BLOCK_1011);
        if(SpringUtil.isNotEmpty(bgHeadData)){
            //头图
            HomeBgHeadPic = bgHeadData.get(0).getPicUrl();
        }
        if(SpringUtil.isNotEmpty(serviceSectionData)){
            //服务栏
            XdaPositionInfoItemODTO positionInfoItem = new XdaPositionInfoItemODTO();
            positionInfoItem.setPicUrl(imgServerUrl.concat(serviceSectionData.get(0).getPicUrl()));
            positionInfoItem.setTargetType(2);
            positionInfoItem.setTargetUrl(serviceAgreementUrl);
            items.add(positionInfoItem);
        }
        if(HLDataO1 != null){
            //横栏01
            Boolean hl01Flag = this.checkTargetCommodityValid(HLDataO1.getTargetType(),HLDataO1.getAppStatus());
            if(hl01Flag){
                this.updateImgSize(HLDataO1,baseIDTO.getStoreId(), PicSizeEnums.PIC_750x240.getSize(),baseIDTO.getOrderTime());
                items.add(HLDataO1);
            }
        }
        result.setItems(items);
        blockAppList.add(result);
        return HomeBgHeadPic;
    }

    /**
     * 设置横向位信息
     * 当上架状态的商品数少于4个时，当前横向位整个隐藏
     * */
    public void favorXPosition(List<XdaPositionInfoODTO> blockAppList,HomeBaseIDTO baseIDTO,List<Long> showCommodityList){
        List<XdaFavorXPositionODTO> favorXPositionList = xdaFavorXPositionMapper.queryFavorXPosition(baseIDTO.getStoreId());
        List<XdaFavorXPositionODTO> resultList = new ArrayList<>();
        if(SpringUtil.isNotEmpty(favorXPositionList)){
            Map<Integer,Integer> tmpPositionMap = new HashMap<>();
            favorXPositionList.stream().collect(Collectors.groupingBy(XdaFavorXPositionODTO::getPositionId,LinkedHashMap::new,Collectors.toList())).forEach((positionId,positionList)->{
                //判断每个横向位短期（没有短期使用长期）、最新修改、商品数量达到4个以上的展示
                if(!tmpPositionMap.containsKey(positionId)){
                    //判断有效商品数
                    int commodityNum = 0;
                    for (XdaFavorXPositionODTO item : positionList) {
                        if(showCommodityList.contains(item.getCommodityId())){
                            commodityNum+=1;
                        }
                    }
                    if(commodityNum >= 4){
                        resultList.add(positionList.get(0));
                        tmpPositionMap.put(positionId,positionId);
                    }
                }
            });
        }
        XdaPositionInfoODTO positionInfo = new XdaPositionInfoODTO();
        List<Long> commodityIdList = xdaFavorYPositionMapper.selectYPositionCommodityList(baseIDTO.getOrderTime(),baseIDTO.getStoreId());
        if(commodityIdList != null && commodityIdList.size() >= 4){
            //添加纵向位固定tab
            XdaFavorXPositionODTO favorYPosition = new XdaFavorXPositionODTO();
            favorYPosition.setTitle("全部");
            favorYPosition.setSubTitle("精选商品");
            resultList.add(0,favorYPosition);
        }

        positionInfo.setFavorXPositionList(resultList);
        positionInfo.setType(BlockTypeConstant.BLOCK_1001);
        blockAppList.add(positionInfo);
    }

    /**
     * 获取商品信息
     * */
    @Deprecated
    public Map<Long,XdaCommodityAppODTO> queryPositionInfoCommodityDataV2(List<Long> commdityIds, PicSizeEnums picSize, HomeBaseIDTO baseIDTO){
        Map<Long,XdaCommodityAppODTO> map = new HashMap<>();
        if(SpringUtil.isNotEmpty(commdityIds)){
            XdaCommodityAppIDTO shopCommodityInfo = XdaCommodityAppIDTO.builder()
                    .orderTime(baseIDTO.getOrderTime())
                    .commodityIdList(commdityIds)
                    .storeId(baseIDTO.getStoreId())
                    .defaultImageSize(picSize).build();
            List<XdaCommodityAppODTO> commodityAppList = xdaCommodityFrontClient.queryXdaCommodityListForApp(shopCommodityInfo);
            if(SpringUtil.isNotEmpty(commodityAppList)){
                commodityAppList.forEach(commodityApp->{
                    //上架的才展示
                    if(commodityApp.getAppStatus() == 0){
                        map.put(commodityApp.getCommodityId(),commodityApp);
                    }
                });
            }
        }
        return map;
    }
    public Map<Long,XdaCommodityAppV4ODTO> queryPositionInfoCommodityDataV4(List<Long> commdityIds, PicSizeEnums picSize, HomeBaseIDTO baseIDTO){
        Map<Long,XdaCommodityAppV4ODTO> map = new HashMap<>();
        if(SpringUtil.isNotEmpty(commdityIds)){
            XdaCommodityAppV4IDTO shopCommodityInfo = XdaCommodityAppV4IDTO.builder()
                    .orderTime(baseIDTO.getOrderTime())
                    .commodityIdList(commdityIds)
                    .storeId(baseIDTO.getStoreId())
                    .defaultImageSize(picSize).build();
            List<XdaCommodityAppV4ODTO> commodityAppList = xdaCommodityFrontClient.queryXdaCommodityListForAppV4(shopCommodityInfo);
            if(SpringUtil.isNotEmpty(commodityAppList)){
                commodityAppList.forEach(commodityApp->{
                    //上架的才展示
                    if(commodityApp.getAppStatus() == 0){
                        map.put(commodityApp.getCommodityId(),commodityApp);
                    }
                });
            }
        }
        return map;
    }

    public void updateImgSizeList(List<XdaPositionInfoItemODTO> positionItemList, Long storeId, String picSize,Date orderTime){
        if(positionItemList != null && positionItemList.size() > 0){
            positionItemList.forEach(positionInfoItem -> {
                this.updateImgSize(positionInfoItem,storeId,picSize,orderTime);
            });
        }
    }

    public void updateImgSize(XdaPositionInfoItemODTO positionItem, Long storeId, String picSize,Date orderTime){
        if(positionItem != null){
            if (positionItem.getPicUrl() != null) {
                positionItem.setPicUrl(this.picUrl(positionItem.getPicUrl(),picSize,true));
            }
            if (positionItem.getTargetType() != null && positionItem.getTargetType().equals(XSAppPositionInfoTargetTypeEnums.H5页面.getCode())) {
                positionItem.setTargetUrl(this.targetUrl(positionItem.getTargetType(),Long.valueOf(positionItem.getTargetTypeId()),storeId,orderTime));
            }
        }
    }

    public String picUrl(String picUrl,String picSize,boolean hostFlag){
        if (StringUtils.isNotBlank(picUrl) && StringUtils.isNotBlank(picSize)) {
            /*
            首页暂时不要图片尺寸
            if(hostFlag){
                return imgServerUrl.concat(ImageUtils.getXdImgUrlV2(picUrl, picSize));
            }else{
                return ImageUtils.getXdImgUrlV2(picUrl, picSize);
            }*/

            if(hostFlag){
                return imgServerUrl.concat(picUrl);
            }else{
                return picUrl;
            }
        }
        return null;
    }

    public String targetUrl(Integer targetType,Long targetTypeId,Long storeId,Date orderTime){
        if (targetType !=null && targetType.equals(XSAppPositionInfoTargetTypeEnums.H5页面.getCode()) && storeId != null && targetTypeId != null ) {
            return domainName.concat(targetUrl.concat(String.valueOf(targetTypeId)).concat("&storeId=").concat(String.valueOf(storeId)).concat("&orderTime=").concat(new SimpleDateFormat("yyyy-MM-dd").format(orderTime)));
        }
        return null;
    }

    /**
     * 首页弹框广告
     * @param dto
     * @param popupAdList
     */
    private void setHomePopupAd(XdaHomePageODTO dto, List<XdaPositionInfoItemODTO> popupAdList,HomeBaseIDTO baseIDTO){
        if(SpringUtil.isEmpty(popupAdList)){
            return;
        }
        XdaPositionInfoItemODTO psODTO = popupAdList.get(0);
        if(this.checkTargetCommodityValid(psODTO.getTargetType(),psODTO.getAppStatus())){
            XdaPopupAdAppODTO adAppODTO = new XdaPopupAdAppODTO();
            adAppODTO.setAdId(psODTO.getPositionInfoId());
            adAppODTO.setAdPopupRate(psODTO.getMinInterval());
            adAppODTO.setAdPicUrl(imgServerUrl.concat(psODTO.getPicUrl()));
            adAppODTO.setAdTargetType(psODTO.getTargetType());
            adAppODTO.setAdTargetTypeId(String.valueOf(psODTO.getTargetTypeId()));
            adAppODTO.setAdTargetTypeUrl(this.targetUrl(psODTO.getTargetType(),Long.valueOf(psODTO.getTargetTypeId()),baseIDTO.getStoreId(),baseIDTO.getOrderTime()));
            adAppODTO.setAdFirstLevel(psODTO.getFirstLevel());
            adAppODTO.setAdSecondLevel(psODTO.getSecondLevel());
            dto.setPopupAdAppODTO(adAppODTO);
        }
    }

    /**
     * 验证绑定单品时，商品是否上架，如果资源位链接的单品在当前前置仓没有或者未上架：
     * 对于 横栏1、横栏2、积木组1：隐藏相应的资源位；
     * 对于 积木组2 、积木组3、通栏：点击资源位没反应，代码将targetTypeId(即相应的商品ID)置空
     * 对于 首页猜纵向位，不显示资源位的图片，用猜你喜欢的商品填坑
     * 对于 弹框广告，不显示弹框
     * @param targerType
     * @param appStatus
     * @return
     */
    private Boolean checkTargetCommodityValid(Integer targerType,Integer appStatus){
        if(targerType!=null && targerType.intValue()== XSAppPositionInfoTargetTypeEnums.单品详情页.getCode()){
            return appStatus!=null && appStatus==0;
        }
        return Boolean.TRUE;
    }

    /**
     * 获取客户送货时间
     * */
    public List<OrderTimeODTO> selectOrderTimeList(Long storeId){
        StoreODTO storeODTO = xdaPopupMsgStoreScopeMapper.findStoreById(storeId);
        RBucket<List<OrderTimeODTO>> bucket = redissonClient.getBucket(QYApplicationContext.redisKeyProfile+":xda:orderTimeList:"+storeId);
        List<OrderTimeODTO> orderTimeSortDate = bucket.get();
        if (SpringUtil.isEmpty(orderTimeSortDate)){
            Boolean isPfsStore = storeManageClient.isPfsStore(storeId);
            List<String> orderTimeList = xdaPositionInfoStoreScopeMapper.selectOrderTimeList(storeId, isPfsStore);
            if(SpringUtil.isNotEmpty(orderTimeList)){
                Map<Integer,OrderTimeODTO> orderTimeMap = new HashMap<>();
                List<OrderTimeODTO> orderTimeODTOList = new ArrayList<>();
                for (String orderTimes : orderTimeList) {
                    if (StringUtils.isBlank(orderTimes)) {
                        continue;
                    }
                    String[] orderTimeArr = orderTimes.split("-",-1);
                    for(int i = Integer.parseInt(orderTimeArr[0]); i<=Integer.parseInt(orderTimeArr[orderTimeArr.length-1]); i++){
                        try {
                            if(!orderTimeMap.containsKey(i)){
                                Date date = DateUtil.getDatePart(DateTimeUtil.buildNewDate(DateUtil.getNowDate(),i));
                                OrderTimeODTO odto = new OrderTimeODTO(date,i);
                                orderTimeMap.put(i,odto);
                                orderTimeODTOList.add(odto);
                            }
                        } catch (ParseException e) {
                            log.error("送货日期异常", e);
                        }
                    }
                }
                if(SpringUtil.isNotEmpty(orderTimeODTOList)){
                    orderTimeSortDate = orderTimeODTOList.stream().sorted(Comparator.comparing(OrderTimeODTO::getOrderTime)).collect(Collectors.toList());
                    bucket.set(orderTimeSortDate, 5L, TimeUnit.MINUTES);

                    // 判断是否通达用户
                    if (DeliveryOrderTypeEnums.TD_SALE.getCode().equals(storeODTO.getBusinessType())
                            || Objects.equals(BusinessTypeEnums.B_COUNTRY.getCode(), storeODTO.getBusinessType())) {
                        orderTimeSortDate = selectDeliveryTimeRangeList(orderTimeSortDate, storeId, storeODTO.getBusinessType());
                    }

                    return orderTimeSortDate;
                }
            }else{
                return Collections.EMPTY_LIST;
            }
        }else {
            // 判断是否通达用户
            if (DeliveryOrderTypeEnums.TD_SALE.getCode().equals(storeODTO.getBusinessType())
                    || Objects.equals(BusinessTypeEnums.B_COUNTRY.getCode(), storeODTO.getBusinessType())) {
                orderTimeSortDate = selectDeliveryTimeRangeList(orderTimeSortDate, storeId, storeODTO.getBusinessType());
            }
        }

        return orderTimeSortDate;
    }

    /**
     * 通达选送货日期和时间段
     *
     * @param storeId
     * @param businessType
     * @return
     */
    public List<OrderTimeODTO> selectDeliveryTimeRangeList(List<OrderTimeODTO> orderTimeODTOList, Long storeId, Integer businessType) {

        String t0 = DateUtil.getDateFormate(new Date(), "yyyy-MM-dd");
        String t1 = DateUtil.getDateFormate(DateUtil.addDay(new Date(), 1), "yyyy-MM-dd");
        // 查询送货时间段
        List<TdaDeliveryTimeRangeODTO> tdaDeliveryTimeRangeList = queryDeliveryTimeRangeListByStoreId(storeId, businessType);
        if(CollectionUtils.isNotEmpty(orderTimeODTOList) && CollectionUtils.isNotEmpty(tdaDeliveryTimeRangeList)){
            tdaDeliveryTimeRangeList.forEach(item -> {
                item.setDisabled(YesOrNoEnums.NO.getCode());
            });

            SimpleDateFormat dateFormat= new SimpleDateFormat("HHmm");
            Integer currentTime = Integer.valueOf(dateFormat.format(new Date()));

            // 如果送货日期是T+0,T+1 则送货时间段根据客户截单时间判断是否可选。送货日期大于 T+1则送货时间段都可以选
            for(OrderTimeODTO odto : orderTimeODTOList){
                if(odto.getOrderTime().equals(t1) || odto.getOrderTime().equals(t0)){
                    List<TdaDeliveryTimeRangeODTO> timeRangeList = BeanCloneUtils.copyTo(tdaDeliveryTimeRangeList, TdaDeliveryTimeRangeODTO.class);
                    timeRangeList.forEach(item -> {
                        Integer storeEndTime = Integer.valueOf(item.getStoreEndTime().replace(":", ""));
                        Integer storeBeginTime = Integer.valueOf(item.getStoreBeginTime().replace(":", ""));
                        if(currentTime > storeEndTime || currentTime < storeBeginTime){
                            item.setDisabled(YesOrNoEnums.YES.getCode());
                        }
                    });
                    odto.setDeliveryTimeRangeList(timeRangeList);
                }else {
                    odto.setDeliveryTimeRangeList(tdaDeliveryTimeRangeList);
                }
            }
        }
        return orderTimeODTOList;
    }

    /**
     * 调用物流系统获取送货日期时间段
     *
     * @param storeId
     * @param businessType
     * @return
     */
    public List<TdaDeliveryTimeRangeODTO> queryDeliveryTimeRangeListByStoreId(Long storeId, Integer businessType){
        List<TdaDeliveryTimeRangeODTO> tdaDeliveryTimeRangeODTOList = new ArrayList<>();
        LogisticsBatchStoreSearchIDTO searchIDTO = new LogisticsBatchStoreSearchIDTO();
        searchIDTO.setStoreId(storeId);
        searchIDTO.setBusinessType(businessType);
        if(Objects.equals(businessType,BusinessTypeEnums.B_COUNTRY.getCode())){
            searchIDTO.setDefaultFlag(false);
        }
        List<LogisticsBatchStoreInfoODTO> logisticsBatchStoreInfoODTOList = logisticsCenterBatchClient.findLogisticsBatchStoreInfoByParams(searchIDTO);
        if(CollectionUtils.isNotEmpty(logisticsBatchStoreInfoODTOList)){
            for(LogisticsBatchStoreInfoODTO logisticsBatchStoreInfoODTO : logisticsBatchStoreInfoODTOList){
                logisticsBatchStoreInfoODTO.getDeliveryTimeMap().forEach((k,v)->{
                    TdaDeliveryTimeRangeODTO timeRangeODTO = BeanCloneUtils.copyTo(logisticsBatchStoreInfoODTO, TdaDeliveryTimeRangeODTO.class);
                    timeRangeODTO.setDeliveryTimeRange(k);
                    timeRangeODTO.setDisabled(v);
                    tdaDeliveryTimeRangeODTOList.add(timeRangeODTO);
                });
            }
        }
        return tdaDeliveryTimeRangeODTOList;
    }

    public Integer getSafeBusinessType(Integer businessType) {
        // 定义允许直接返回的业务类型列表
        Set<Integer> specialBusinessTypes = new HashSet<>(Arrays.asList(
                BusinessTypeEnums.TD_SALE.getCode(),
                BusinessTypeEnums.BIGSHOP_SALE.getCode(),
                BusinessTypeEnums.PLAN_SALE.getCode(),
                BusinessTypeEnums.B_COUNTRY.getCode()
        ));

        if (businessType != null && specialBusinessTypes.contains(businessType)) {
            return businessType;
        }

        return BusinessTypeEnums.SALE.getCode();
    }
}
