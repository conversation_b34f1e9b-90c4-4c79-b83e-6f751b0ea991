package com.pinshang.qingyun.xda.cms.service.checkReport;

import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.cms.dto.checkReport.ReportListDTO;
import com.pinshang.qingyun.xda.cms.mapper.checkReport.CheckReportDao;
import com.pinshang.qingyun.xda.cms.model.checkReport.CheckReport;
import com.pinshang.qingyun.xda.cms.vo.AddReportVO;
import com.pinshang.qingyun.xda.cms.vo.ReportListVO;
import com.pinshang.qingyun.xda.cms.vo.XdaMyCheckReportVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.Date;
import java.util.stream.Collectors;


@Slf4j
@Service
@Transactional
public class CheckReportService {
    @Value("${pinshang.img-xd-server-url}")
    private String imgServerUrl;

    @Resource
    private CheckReportDao checkReportDao;

    //图片服务地址
    private static String IMG_SERVICE_URL;

    @Value("${pinshang.img-server-url}")
    public void setImgUrl(String img){
        CheckReportService.IMG_SERVICE_URL = img;
    }


    /**
     * 我的检验报告列表
     */
    public PageInfo<ReportListDTO> queryMyCheckReport(XdaMyCheckReportVO param){
        PageInfo<ReportListDTO> reportListPageInfo = PageHelper.startPage(param.getPageNo(), param.getPageSize()).doSelectPageInfo(() ->
                checkReportDao.queryMyCheckReport(param));
        reportListPageInfo.getList().forEach(report->{
            String fileUrl = report.getFileUrl();
            if (fileUrl != null && !fileUrl.contains("http")) {
                report.setFileUrl(IMG_SERVICE_URL + report.getFileUrl());
            }
        });
        return reportListPageInfo;
    }

    /**
     * 检验报告列表
     */
    public PageInfo<ReportListDTO> list(ReportListVO param){
        log.info("检验报告列表的搜索参数为：{}",param.toString());
        return PageHelper.startPage(param.getPageNo(),param.getPageSize()).doSelectPageInfo(() ->
                checkReportDao.list(param).stream().peek(
                        temp -> {
                            if (temp.getFileUrl() != null && !temp.getFileUrl().equals("")){
                                temp.setFileUrl(IMG_SERVICE_URL + temp.getFileUrl());
                            }
                        }
                ).collect(Collectors.toList()));
    }


    /**
     * 上传检验报告（新增）
     */
    public void add(AddReportVO param){
        log.info("上传检验报告的参数为：{}",param.toString());

        String reportName = param.getReportName();
        QYAssert.isTrue(StringUtils.isEmpty(checkReportDao.selectByName(reportName)),"检验报告名称已存在！");

        Date reportTime = param.getReportTime();
        QYAssert.isTrue(reportTime.before(DateUtil.endOfDay(new Date())),"报告日期不能晚于今天！");

        //拼接新增参数
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        String dictName = checkReportDao.selectDictName(param.getDictCode());

        CheckReport checkReport = new CheckReport();
        checkReport.setReportTime(reportTime);
        checkReport.setDictCode(param.getDictCode());
        checkReport.setDictName(dictName);
        checkReport.setCheckName(reportName);
        checkReport.setFileUrl(param.getFileUrl());
        checkReport.setFileSize(param.getFileSize());
        checkReport.setCheckUser(param.getCheckUser());
        checkReport.setReviewUser(param.getReviewUser());
        checkReport.setCreateId(tokenInfo.getUserId());
        checkReport.setCreateUser(tokenInfo.getRealName());
        checkReport.setCreateTime(new Date());

        checkReportDao.insert(checkReport);
    }

    /**
     * 删除检验报告
     */
    public void delete(Long id){
        log.info("删除的数据id为：{}",id);

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        CheckReport checkReport = new CheckReport();
        checkReport.setId(id);
        checkReport.setUpdateId(tokenInfo.getUserId());
        checkReport.setUpdateUser(tokenInfo.getRealName());
        checkReport.setUpdateTime(new Date());

        checkReportDao.updateDelete(checkReport);
    }

}