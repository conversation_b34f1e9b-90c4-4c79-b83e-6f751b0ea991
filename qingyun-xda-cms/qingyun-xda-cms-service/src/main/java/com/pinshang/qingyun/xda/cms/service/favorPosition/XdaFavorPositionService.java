package com.pinshang.qingyun.xda.cms.service.favorPosition;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.*;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.cms.dto.ForceSubmitResultODTO;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorPositionBaseInfoODTO;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorPositionSaveIDTO;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorPositionStatusIDTO;
import com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorXPositionMapper;
import com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorYPositionMapper;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorXPosition;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorYPosition;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.EnumSet;
import java.util.List;
import java.util.stream.Collectors;


@Service
@Transactional
public class XdaFavorPositionService {
    @Autowired
    private XdaFavorXPositionMapper xPositionMapper;
    @Autowired
    private XdaFavorYPositionMapper yPositionMapper;

    /**
     * 启用/停用
     * @param vo
     * @param baseInfoEntry
     * @return
     */
    public ForceSubmitResultODTO updateFavorPositionStatus(XdaFavorPositionStatusIDTO vo, XdaFavorPositionBaseInfoODTO baseInfoEntry, Integer favorType) {
        int newStatus = null == vo.getStatus()? 0: vo.getStatus().intValue();
        Long favorPositionId = vo.getFavorPositionId();
        QYAssert.isTrue(newStatus == XSAppPositionInfoStatusEnums.启用.getCode() || newStatus == XSAppPositionInfoStatusEnums.停用.getCode(), "状态参数有误!");
        int originalStatus = this.getCorrectOriginalStatus(baseInfoEntry.getStatus(),baseInfoEntry.getTermType(),baseInfoEntry.getEndTime());
        QYAssert.isTrue(XSAppPositionInfoStatusEnums.已过期.getCode() != originalStatus, "该记录已过期!");

        int positionId = null == baseInfoEntry.getPositionId()? 0: baseInfoEntry.getPositionId().intValue();
        int termType = null == baseInfoEntry.getTermType()? 0: baseInfoEntry.getTermType().intValue();
        if (newStatus == XSAppPositionInfoStatusEnums.启用.getCode()) {
            QYAssert.isTrue(XSAppPositionInfoStatusEnums.停用.getCode() == originalStatus, "该记录已经处于启用状态!");
        } else if (newStatus == XSAppPositionInfoStatusEnums.停用.getCode()) {
            QYAssert.isTrue(XSAppPositionInfoStatusEnums.启用.getCode() == originalStatus, "该记录已经处于停用状态!");
            if (termType == TermTypeEnums.长期.getCode()) {
                this.checkDisableStatus(positionId, favorPositionId,1);
            }
        }

        // 校验强制提交状态
        int forceStatus = null == vo.getForceStatus()? ForceSubmitResultODTO.FORCE_STATUS_NO: vo.getForceStatus().intValue();
        ForceSubmitResultODTO temp = this.checkForceStatus(favorPositionId, positionId, termType, baseInfoEntry.getBeginTime(), baseInfoEntry.getEndTime(), forceStatus,favorType);
        if (forceStatus == ForceSubmitResultODTO.FORCE_STATUS_NO && null != temp.getMessage() && newStatus == XSAppPositionInfoStatusEnums.启用.getCode()) {
            return new ForceSubmitResultODTO(ForceSubmitResultODTO.FORCE_STATUS_YES + "", temp.getMessage(), null, null, null);
        }
        int updateCount = 0;
        if(favorType.intValue() == XSAppPositionTypeEnums.猜你喜欢横向位.getCode()){
            updateCount =  this.updateXPositionStatus(favorPositionId, newStatus);
        }else{
            updateCount = this.updateYPositionStatus(favorPositionId, newStatus);
        }
        if (updateCount > 0) {
            // 长期类型：启用状态冲突的，批量停用
            if (newStatus == XSAppPositionInfoStatusEnums.启用.getCode()) {
                this.batchDisableStatus(temp,favorType);
            }
        }
        return new ForceSubmitResultODTO("", null, null, updateCount, null);
    }

    /**
     * 新增/修改前的校验
     * @param vo
     */
    public <T extends XdaFavorPositionSaveIDTO> void checkBeforeInsertOrUpdate(T vo, Integer favorType) {
        QYAssert.isTrue(null != vo, "参数有误!");
        //验证资源位
        EnumSet<XSAppPositionIdEnums> positionIdList = favorType.intValue() == XSAppPositionTypeEnums.猜你喜欢横向位.getCode()?XSAppPositionIdEnums.xdFavorXList():XSAppPositionIdEnums.xdFavorYList();
        int positionId = null == vo.getPositionId() ? 0 : vo.getPositionId().intValue();
        QYAssert.isTrue(XSAppPositionIdEnums.contains(positionIdList, positionId), "‘资源位’参数有误!");
        vo.setPositionId(positionId);

        //验证绑定期限
        EnumSet<TermTypeEnums> termTypeList = TermTypeEnums.termTypeList();
        int termType = null == vo.getTermType() ? 0 : vo.getTermType().intValue();
        QYAssert.isTrue(TermTypeEnums.contains(termTypeList, termType), "‘绑定期限类型’参数有误");
        if (termType == TermTypeEnums.短期.getCode()) {
            QYAssert.isTrue(StringUtils.isNotEmpty(vo.getBeginTime()) && StringUtils.isNotEmpty(vo.getEndTime()),"绑定期限有效时间不能为空");
            Date bTime = DateUtil.parseDate(vo.getBeginTime(), "yyyy-MM-dd HH:mm:ss");
            Date eTime = DateUtil.parseDate(vo.getEndTime(), "yyyy-MM-dd HH:mm:ss");
            if (null == bTime || null == eTime || bTime.after(eTime)) {
                QYAssert.isTrue(false, "生效时间输入不合法!");
            } else if (new Date().after(eTime)) {
                QYAssert.isTrue(false, "生效时间在当前时间点之前！");
            }
        }
        vo.setTermType(termType);

        //验证适用客户
        int isAllStore = null == vo.getIsAllStore() ? 0 : vo.getIsAllStore().intValue();
        QYAssert.isTrue(IsAllShopTypeEnums.contains(IsAllShopTypeEnums.isAllShopTypeList(), isAllStore), "‘适用客户’参数有误!");
        if (termType == TermTypeEnums.长期.getCode() && isAllStore != IsAllShopTypeEnums.ALL_SHOP.getCode()) {
            QYAssert.isTrue(false, "‘绑定期限’为长期时，‘适用客户’必须选中所有客户!");
        }
        vo.setIsAllStore(isAllStore);
    }

    /**
     * 校验强制提交状态（新增/修改/修改状态）
     * @param favorPositionId
     * @param positionId
     * @param termType
     * @param beginTime
     * @param endTime
     * @param forceStatus
     * @return
     */
    public ForceSubmitResultODTO checkForceStatus(long favorPositionId, int positionId, int termType, Date beginTime, Date endTime, int forceStatus,Integer favorType) {
        String message = null;
        List<Long> statusConflictIdList = null;
        if (termType == TermTypeEnums.长期.getCode()) {
            // 长期-状态冲突
            if(favorType.intValue() == XSAppPositionTypeEnums.猜你喜欢横向位.getCode()){
                Example example = new Example(XdaFavorXPosition.class);
                example.createCriteria().andEqualTo("positionId", positionId).andEqualTo("termType", termType).andEqualTo("status", XSAppPositionInfoStatusEnums.启用.getCode());
                List<XdaFavorXPosition> statusConflictList = xPositionMapper.selectByExample(example);
                statusConflictIdList = statusConflictList.stream().map(XdaFavorXPosition::getId).collect(Collectors.toList());
            }else{
                Example example = new Example(XdaFavorYPosition.class);
                example.createCriteria().andEqualTo("positionId", positionId).andEqualTo("termType", termType).andEqualTo("status", XSAppPositionInfoStatusEnums.启用.getCode());
                List<XdaFavorYPosition> statusConflictList = yPositionMapper.selectByExample(example);
                statusConflictIdList = statusConflictList.stream().map(XdaFavorYPosition::getId).collect(Collectors.toList());
            }

            if (SpringUtil.isNotEmpty(statusConflictIdList)) {
                statusConflictIdList.removeIf(statusConflictId -> (null == statusConflictId || statusConflictId.longValue() == favorPositionId));
                if (forceStatus == ForceSubmitResultODTO.FORCE_STATUS_NO) {
                    if (SpringUtil.isNotEmpty(statusConflictIdList)) {
                        message = "发现该位置有其他长期绑定，启用后将以当前设置为准并且老的长期绑定将自动停用，确定要继续吗？";
                    }
                }
            }
        } else if (termType == TermTypeEnums.短期.getCode()) {
            // 短期-时间交叉
            if (forceStatus == ForceSubmitResultODTO.FORCE_STATUS_NO) {
                List<Long> timeCrossIdList = null;
                if(favorType.intValue() == XSAppPositionTypeEnums.猜你喜欢横向位.getCode()){
                    timeCrossIdList = xPositionMapper.selectXPositionWithTimeCrossList(positionId, beginTime, endTime);
                }else{
                    timeCrossIdList = yPositionMapper.selectYPositionWithTimeCrossList(positionId, beginTime, endTime);
                }
                if (SpringUtil.isNotEmpty(timeCrossIdList)) {
                    timeCrossIdList.removeIf(timeCrossId -> (null == timeCrossId || timeCrossId.longValue() == favorPositionId));
                }
                if (SpringUtil.isNotEmpty(timeCrossIdList)) {
                    message = "发现该位置的其他绑定跟当前绑定有时间交集，交集时间段将以当前的设置为准，确定要继续吗？";
                }
            }
        }
        return new ForceSubmitResultODTO(null, message, null, null, statusConflictIdList);
    }

    /**
     * 时间冲突，批量停用其他资源位绑定
     * @param temp
     */
    public void batchDisableStatus(ForceSubmitResultODTO temp,Integer favorType) {
        List<Long> statusConflictIdList = (List<Long>)temp.getObjectValue();
        if (SpringUtil.isNotEmpty(statusConflictIdList)) {
            int disableStatus = XSAppPositionInfoStatusEnums.停用.getCode();
            if(favorType.intValue() == XSAppPositionTypeEnums.猜你喜欢横向位.getCode()){
                statusConflictIdList.forEach(favorPositionId -> {
                    this.updateXPositionStatus(favorPositionId, disableStatus);
                });
            }else{
                statusConflictIdList.forEach(favorPositionId -> {
                    this.updateYPositionStatus(favorPositionId, disableStatus);
                });
            }
        }
    }

    /**
     * 更新状态
     * @param favorPositionId
     * @param newStatus
     * @return
     */
    public int updateXPositionStatus(Long favorPositionId, Integer newStatus) {
        XdaFavorXPosition xPosition = new XdaFavorXPosition();
        xPosition.setId(favorPositionId);
        xPosition.setStatus(newStatus);
        xPosition.setUpdateId(FastThreadLocalUtil.getQY().getUserId());
        xPosition.setUpdateTime(new Date(System.currentTimeMillis()));
        return xPositionMapper.updateByPrimaryKeySelective(xPosition);
    }

    public int updateYPositionStatus(Long favorPositionId, Integer newStatus) {
        XdaFavorYPosition yPosition = new XdaFavorYPosition();
        yPosition.setId(favorPositionId);
        yPosition.setStatus(newStatus);
        yPosition.setUpdateId(FastThreadLocalUtil.getQY().getUserId());
        yPosition.setUpdateTime(new Date(System.currentTimeMillis()));
        return yPositionMapper.updateByPrimaryKeySelective(yPosition);
    }

    // 根据期限类型，判断是否允许停用
    public void checkDisableStatus(Integer positionId, Long thisId,Integer favorType) {
        String msg = "该资源位类型";
        Example example = favorType.intValue()==  XSAppPositionTypeEnums.猜你喜欢横向位.getCode() ? new Example(XdaFavorXPosition.class) : new Example(XdaFavorYPosition.class);
        if (null != positionId) {
            example.createCriteria()
                    .andEqualTo("termType", TermTypeEnums.长期.getCode())
                    .andEqualTo("status", XSAppPositionInfoStatusEnums.启用.getCode())
                    .andEqualTo("positionId", positionId);
            msg = "该资源位";
        } else {
            example.createCriteria()
                    .andEqualTo("termType", TermTypeEnums.长期.getCode())
                    .andEqualTo("status", XSAppPositionInfoStatusEnums.启用.getCode())
            ;
        }
        List<Long> favorPositionIdList;
        if(favorType.intValue() == XSAppPositionTypeEnums.猜你喜欢横向位.getCode()){
            List<XdaFavorXPosition> xPositionList = xPositionMapper.selectByExample(example);
            favorPositionIdList = xPositionList.stream().map(XdaFavorXPosition:: getId).collect(Collectors.toList());
        }else{
            List<XdaFavorYPosition> yPositionList = yPositionMapper.selectByExample(example);
            favorPositionIdList = yPositionList.stream().map(XdaFavorYPosition:: getId).collect(Collectors.toList());
        }
        favorPositionIdList.removeIf(piId -> (null == piId || piId.longValue() == thisId));
        QYAssert.isTrue(favorPositionIdList.size() > 0, "该记录是" + msg + "的唯一长期绑定，不能停用!");
    }

    // 修正后的原状态
    public int getCorrectOriginalStatus(Integer status,Integer termType,Date endTime) {
        status = null == status ? 0: status;
        if (XSAppPositionInfoStatusEnums.启用.getCode().intValue() == status.intValue()) {
            termType = null == termType? 0: termType;
            if (TermTypeEnums.短期.getCode() == termType.intValue()) {
                if (new Date(System.currentTimeMillis()).after(endTime)) {
                    status = XSAppPositionInfoStatusEnums.已过期.getCode();
                }
            }
        }
        return status;
    }


}
