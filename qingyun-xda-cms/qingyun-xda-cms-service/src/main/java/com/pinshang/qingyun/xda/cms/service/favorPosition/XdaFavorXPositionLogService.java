package com.pinshang.qingyun.xda.cms.service.favorPosition;

import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorPositionLogODTO;
import com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorXPositionLogMapper;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorXPositionLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 鲜达-横向位-日志service
 */
@Service
@Slf4j
public class XdaFavorXPositionLogService {

    @Autowired
    private XdaFavorXPositionLogMapper logMapper;


    @Transactional(rollbackFor = Exception.class)
    public void saveFavorXLog(Long xPositionId, Integer operateType) {
        XdaFavorXPositionLog log = new XdaFavorXPositionLog(xPositionId,operateType);
        logMapper.insertSelective(log);
    }

    /**
     * 查询横向位操作日志
     * @param xPositionId
     * @return
     */
    public List<XdaFavorPositionLogODTO> queryXPositionLogList(Long xPositionId) {
        return logMapper.queryXPositionLogList(xPositionId);
    }

}