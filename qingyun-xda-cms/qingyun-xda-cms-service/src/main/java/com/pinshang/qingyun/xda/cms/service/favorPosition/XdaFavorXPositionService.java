package com.pinshang.qingyun.xda.cms.service.favorPosition;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.*;
import com.pinshang.qingyun.base.enums.xda.XdaStoreScopeTypeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.cms.dto.ForceSubmitResultODTO;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.*;
import com.pinshang.qingyun.xda.cms.dto.position.StoreScopeIDTO;
import com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorXPositionCommodityMapper;
import com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorXPositionMapper;
import com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorXPositionStoreScopeMapper;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorXPosition;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorXPositionCommodity;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorXPositionStoreScope;
import com.pinshang.qingyun.xda.cms.model.position.XdaPositionInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 鲜达-横向位
 */
@Service
@Transactional
public class XdaFavorXPositionService {
    @Autowired
    private XdaFavorXPositionMapper xPositionMapper;
    @Autowired
    private XdaFavorXPositionStoreScopeMapper xPositionStoreScopeMapper;
    @Autowired
    private XdaFavorXPositionCommodityMapper xPositionCommodityMapper;
    @Autowired
    private XdaFavorXPositionLogService xPositionLogService;
    @Autowired
    private XdaFavorPositionService favorService;
    public final static Integer favorType = XSAppPositionTypeEnums.猜你喜欢横向位.getCode();


    /**
     * 横向位--新增
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ForceSubmitResultODTO addXPosition(XdaFavorXPositionSaveIDTO vo) {
        favorService.checkBeforeInsertOrUpdate(vo,favorType);
        this.checkXParam(vo);
        int forceStatus = null == vo.getForceStatus()? ForceSubmitResultODTO.FORCE_STATUS_NO: vo.getForceStatus().intValue();
        int positionId = vo.getPositionId().intValue();
        int termType = vo.getTermType().intValue();
        int isAllStore = vo.getIsAllStore().intValue();
        Date beginTime = DateUtil.parseDate(vo.getBeginTime(), "yyyy-MM-dd HH:mm:ss");
        Date endTime = DateUtil.parseDate(vo.getEndTime(), "yyyy-MM-dd HH:mm:ss");
        // 校验强制提交状态
        ForceSubmitResultODTO temp = favorService.checkForceStatus(0, positionId,termType, beginTime, endTime, forceStatus,favorType);
        if (forceStatus == ForceSubmitResultODTO.FORCE_STATUS_NO && null != temp.getMessage()) {
            return new ForceSubmitResultODTO(ForceSubmitResultODTO.FORCE_STATUS_YES + "", temp.getMessage(), null, null, null);
        }
        XdaFavorXPosition xPosition = XdaFavorXPosition.forInsert(vo,beginTime,endTime);
        xPositionMapper.insertSelective(xPosition);
        Long positionInfoId = xPosition.getId();
        if (positionInfoId > 0) {
            // 适用门店
            if (isAllStore == IsAllShopTypeEnums.SPECIFIC_SHOP.getCode()) {
                this.insertFavorXPositionStore(positionInfoId, vo.getStoreScopeList());
            }
            // 商品
            this.insertFavorXPositionCommodity(positionInfoId, vo.getCommodityList());
            // 长期类型：启用状态冲突的，批量停用
            favorService.batchDisableStatus(temp,favorType);
            xPositionLogService.saveFavorXLog(positionInfoId, OperateTypeEnums.新增.getCode());
        }
        return new ForceSubmitResultODTO("", null, positionInfoId, null, null);
    }

    /**
     * 横向位--修改
     * @param xPositionId
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ForceSubmitResultODTO updateXPosition(Long xPositionId,XdaFavorXPositionSaveIDTO vo) {
        favorService.checkBeforeInsertOrUpdate(vo,favorType);
        this.checkXParam(vo);
        xPositionId = null == xPositionId ? 0: xPositionId.longValue();
        XdaFavorXPosition xPositionDB = xPositionMapper.selectByPrimaryKey(xPositionId);
        QYAssert.notNull(xPositionDB,"未获得该记录的相关信息!");

        int positionId = vo.getPositionId().intValue();
        int termType = vo.getTermType().intValue();
        Date beginTime = DateUtil.parseDate(vo.getBeginTime(), "yyyy-MM-dd HH:mm:ss");
        Date endTime = DateUtil.parseDate(vo.getEndTime(), "yyyy-MM-dd HH:mm:ss");
        int oldTermType = xPositionDB.getTermType().intValue();
        if (oldTermType == TermTypeEnums.长期.getCode()) {
            // 长期类型的，不能更改的属性
            positionId = xPositionDB.getPositionId().intValue();
            termType = oldTermType;
            beginTime = null;
            endTime = null;
        }

        // 校验强制提交状态
        int forceStatus = null == vo.getForceStatus()? ForceSubmitResultODTO.FORCE_STATUS_NO: vo.getForceStatus().intValue();
        ForceSubmitResultODTO temp = favorService.checkForceStatus(xPositionId, positionId, termType, beginTime, endTime, forceStatus,favorType);
        if (forceStatus == ForceSubmitResultODTO.FORCE_STATUS_NO && null != temp.getMessage()) {
            return new ForceSubmitResultODTO(ForceSubmitResultODTO.FORCE_STATUS_YES + "", temp.getMessage(), null, null, null);
        }

        XdaFavorXPosition.forUpdate(xPositionDB, vo,beginTime,endTime);
        xPositionDB.setTermType(termType);
        int updateCount = xPositionMapper.updateByPrimaryKeySelective(xPositionDB);
        if (updateCount > 0) {
            // 适用门店
            Example deleteShopExample = new Example(XdaFavorXPositionStoreScope.class);
            deleteShopExample.createCriteria().andEqualTo("favorXPositionId", xPositionId);
            xPositionStoreScopeMapper.deleteByExample(deleteShopExample);
            if (vo.getIsAllStore().intValue() == IsAllShopTypeEnums.SPECIFIC_SHOP.getCode()) {
                this.insertFavorXPositionStore(xPositionId, vo.getStoreScopeList());
            }
            // 商品
            Example deleteCommodityExample = new Example(XdaFavorXPositionCommodity.class);
            deleteCommodityExample.createCriteria().andEqualTo("favorXPositionId", xPositionId);
            xPositionCommodityMapper.deleteByExample(deleteCommodityExample);
            this.insertFavorXPositionCommodity(xPositionId, vo.getCommodityList());
            // 长期类型：启用状态冲突的，批量停用
            favorService.batchDisableStatus(temp,favorType);
            xPositionLogService.saveFavorXLog(xPositionId, OperateTypeEnums.修改.getCode());
        }
        return new ForceSubmitResultODTO("", null, null, updateCount, null);
    }

    /**
     * 横向位-启用/停用
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ForceSubmitResultODTO updateXPositionStatus(XdaFavorPositionStatusIDTO vo) {
        QYAssert.isTrue(null != vo, "参数有误!");
        long xPositionId = null == vo.getFavorPositionId()? 0: vo.getFavorPositionId().longValue();
        XdaFavorPositionBaseInfoODTO baseInfoEntry = xPositionMapper.queryFavorXPositionById(xPositionId);
        QYAssert.notNull(baseInfoEntry,"未获得该记录的相关信息!");
        int newStatus = null == vo.getStatus()? 0: vo.getStatus().intValue();
        QYAssert.isTrue(newStatus == XSAppPositionInfoStatusEnums.启用.getCode() || newStatus == XSAppPositionInfoStatusEnums.停用.getCode(), "状态参数有误!");

//        if(newStatus==XSAppPositionInfoStatusEnums.停用.getCode()){
//            Example example = new Example(XdaFavorXPosition.class);
//            example.createCriteria().andEqualTo("termType", TermTypeEnums.长期.getCode()).andEqualTo("status",XSAppPositionInfoStatusEnums.启用.getCode()).andNotEqualTo("id",xPositionId);
//            int count = xPositionMapper.selectCountByExample(example);
//            QYAssert.isTrue(count>=4,"APP的首页猜横向位至少要有4个长期绑定！");
//        }

        ForceSubmitResultODTO resultEntry = favorService.updateFavorPositionStatus(vo,baseInfoEntry,favorType);
        if(resultEntry!=null && StringUtils.isEmpty(resultEntry.getCode())){
            int operateType = vo.getStatus() == XSAppPositionInfoStatusEnums.启用.getCode() ? OperateTypeEnums.启用.getCode() : OperateTypeEnums.停用.getCode();
            xPositionLogService.saveFavorXLog(xPositionId, operateType);
        }
        return resultEntry;
    }

    /**
     * 根据ID查询资源位详情
     * @param xPositionId
     * @return
     */
    public XdaFavorXPositionInfoODTO queryXPositionDetail(Long xPositionId) {
        QYAssert.isTrue(null != xPositionId && xPositionId.longValue() > 0, "参数有误!");
        XdaFavorXPosition xPosition = xPositionMapper.selectByPrimaryKey(xPositionId);
        QYAssert.notNull(xPosition,"未找到相应的横向资源位记录!");
        XdaFavorXPositionInfoODTO result = BeanCloneUtils.copyTo(xPosition, XdaFavorXPositionInfoODTO.class);
        result.setFavorPositionId(xPositionId);
        // 客户范围
        if (IsAllStoreTypeEnums.SPECIFIC_STORE.getCode().equals(xPosition.getIsAllStore())) {
            result.setSettlementList(xPositionStoreScopeMapper.selectSettlementList(xPositionId, XdaStoreScopeTypeEnums.结账客户.getCode()));
            result.setProductPriceModelList(xPositionStoreScopeMapper.selectProductPriceModelList(xPositionId, XdaStoreScopeTypeEnums.产品价格方案.getCode()));
            result.setStoreTypeList(xPositionStoreScopeMapper.selectDictionaryList(xPositionId, XdaStoreScopeTypeEnums.客户类型.getCode()));
            result.setStoreChannelList(xPositionStoreScopeMapper.selectDictionaryList(xPositionId, XdaStoreScopeTypeEnums.渠道.getCode()));
            result.setStoreList(xPositionStoreScopeMapper.selectStoreList(xPositionId, XdaStoreScopeTypeEnums.客户.getCode()));
        }
        result.setCommodityList(xPositionCommodityMapper.selectXPositionCommodityList(xPositionId));
        return result;
    }


    /**
     * 分页查询横向位
     * 1.查询时间和生效时间段有交集的就是查询结果（前置仓PRD）
     * @param vo
     * @return
     */
    @Transactional(readOnly = true)
    public PageInfo<XdaFavorXPositionInfoODTO> queryXPositionPage(XdaFavorXPositionPageIDTO vo) {
        QYAssert.isTrue(null != vo, "参数有误!");
        PageInfo<XdaFavorXPositionInfoODTO> pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            xPositionMapper.queryFavorXPositionPage(vo);
        });
        return pageDate;
    }

    /**
     * 新增/修改前的校验
     * @param vo
     */
    private void checkXParam(XdaFavorXPositionSaveIDTO vo) {
        //验证标题和副标题
        QYAssert.notNull(vo.getTitle(), "绑定标题不能为空!");
        QYAssert.notNull(vo.getSubTitle(), "绑定副标题不能为空!");
        List<XdaFavorXPositionCommodityIDTO> commodityList = new ArrayList<>();
        vo.getCommodityList().stream().filter(item->item.getCommodityId()!=null).collect(groupingBy(XdaFavorXPositionCommodityIDTO::getCommodityId,
                LinkedHashMap::new,Collectors.toList())).forEach((k,v)->{commodityList.add(v.get(0));
        });
        //验证商品列表
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "‘商品列表’不能为空!");
        QYAssert.isTrue(commodityList.size() >= 10 && commodityList.size() <= 100, "添加的商品数量需要在10到100之间！");
//        this.sortCommodityList(commodityList);
        vo.setCommodityList(commodityList);
    }

    // 排序商品列表
    private void sortCommodityList(List<XdaFavorXPositionCommodityIDTO> commodityList){
        Collections.sort(commodityList, new Comparator<XdaFavorXPositionCommodityIDTO>() {
            @Override
            public int compare(XdaFavorXPositionCommodityIDTO o1, XdaFavorXPositionCommodityIDTO o2) {
                if(o1.getSortNum()!=null && o2.getSortNum()!=null){
                    if(o1.getSortNum() > o2.getSortNum()){
                        return 1;
                    }
                    if(o1.getSortNum() == o2.getSortNum()){
                        return 0;
                    }
                }
                return -1;
            }
        });
    }

    // 插入门店List
    private void insertFavorXPositionStore(Long xPositionId, List<StoreScopeIDTO> storeScopeIDTOList) {
        List<XdaFavorXPositionStoreScope> storeList = new ArrayList<>();
        storeScopeIDTOList.forEach(scopeIDTO -> {
            storeList.add(new XdaFavorXPositionStoreScope(xPositionId, scopeIDTO));
        });
        xPositionStoreScopeMapper.insertList(storeList);
    }
    // 插入商品List
    private void insertFavorXPositionCommodity(Long xPositionId, List<XdaFavorXPositionCommodityIDTO> commodityVoList) {
        List<XdaFavorXPositionCommodity> commodityList = new ArrayList<>();
        for(int i=0;i<commodityVoList.size();i++){
            XdaFavorXPositionCommodityIDTO commodityVO = commodityVoList.get(i);
//            if(commodityVO.getSortNum()==null || commodityVO.getSortNum()==0){
                commodityVO.setSortNum(i+1);
//            }
            commodityList.add(new XdaFavorXPositionCommodity(xPositionId, Long.valueOf(commodityVO.getCommodityId()), commodityVO.getSortNum()));
        }
        xPositionCommodityMapper.insertList(commodityList);
    }

}
