package com.pinshang.qingyun.xda.cms.service.favorPosition;

import com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorPositionLogODTO;
import com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorYPositionLogMapper;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorYPositionLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * 鲜达-纵向位-日志service
 */
@Service
@Slf4j
public class XdaFavorYPositionLogService {

    @Autowired
    private XdaFavorYPositionLogMapper logMapper;


    @Transactional(rollbackFor = Exception.class)
    public void saveFavorYLog(Long yPositionId, Integer operateType) {
        XdaFavorYPositionLog log = new XdaFavorYPositionLog(yPositionId,operateType);
        logMapper.insertSelective(log);
    }

    public List<XdaFavorPositionLogODTO> queryYPositionLogList(Long yPositionId) {
        return logMapper.queryYPositionLogList(yPositionId);
    }


}