package com.pinshang.qingyun.xda.cms.service.favorPosition;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.*;
import com.pinshang.qingyun.base.enums.xda.XdaStoreScopeTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.xda.cms.dto.ForceSubmitResultODTO;
import com.pinshang.qingyun.xda.cms.dto.favorPosition.*;
import com.pinshang.qingyun.xda.cms.dto.position.StoreScopeIDTO;
import com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorYPositionMapper;
import com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorYPositionStoreScopeMapper;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorYPosition;
import com.pinshang.qingyun.xda.cms.model.favorPosition.XdaFavorYPositionStoreScope;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 首页猜你喜欢纵向位Service
 */
@Service
@Transactional
public class XdaFavorYPositionService {
    @Autowired
    private XdaFavorYPositionMapper yPositionMapper;
    @Autowired
    private XdaFavorYPositionStoreScopeMapper yPositionStoreScopeMapper;
    @Autowired
    private XdaFavorYPositionLogService yPositionLogService;
    @Autowired
    private XdaFavorPositionService favorService;
    public final static Integer favorType = XSAppPositionTypeEnums.猜你喜欢纵向位.getCode();

    /**
     * 新增纵向位
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ForceSubmitResultODTO addYPosition(XdaFavorYPositionSaveIDTO vo) {
        favorService.checkBeforeInsertOrUpdate(vo,favorType);
        this.checkYParam(vo);

        int forceStatus = null == vo.getForceStatus()? ForceSubmitResultODTO.FORCE_STATUS_NO: vo.getForceStatus().intValue();
        int positionId = vo.getPositionId().intValue();
        int termType = vo.getTermType().intValue();
        int isAllShop = vo.getIsAllStore().intValue();
        Date beginTime = DateUtil.parseDate(vo.getBeginTime(), "yyyy-MM-dd HH:mm:ss");
        Date endTime = DateUtil.parseDate(vo.getEndTime(), "yyyy-MM-dd HH:mm:ss");
        // 校验强制提交状态
        ForceSubmitResultODTO temp = favorService.checkForceStatus(0, positionId,termType, beginTime, endTime, forceStatus,favorType);
        if (forceStatus == ForceSubmitResultODTO.FORCE_STATUS_NO && null != temp.getMessage()) {
            return new ForceSubmitResultODTO(ForceSubmitResultODTO.FORCE_STATUS_YES + "", temp.getMessage(), null, null, null);
        }

        XdaFavorYPosition yPosition = XdaFavorYPosition.forInsert(vo,beginTime,endTime);
        yPositionMapper.insertSelective(yPosition);
        Long positionInfoId = yPosition.getId();
        if (positionInfoId > 0) {
            // 适用门店
            if (isAllShop == IsAllShopTypeEnums.SPECIFIC_SHOP.getCode()) {
                this.insertFavorYPositionStore(positionInfoId, vo.getStoreScopeList());
            }
            // 长期类型：启用状态冲突的，批量停用
            favorService.batchDisableStatus(temp,favorType);
            yPositionLogService.saveFavorYLog(positionInfoId, OperateTypeEnums.新增.getCode());
        }
        return new ForceSubmitResultODTO("", null, positionInfoId, null, null);
    }

    /**
     * 修改纵向位
     * @param yPositionId
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ForceSubmitResultODTO updateYPosition(Long yPositionId,XdaFavorYPositionSaveIDTO vo) {
        yPositionId = null == yPositionId ? 0: yPositionId.longValue();
        XdaFavorYPosition yPositionDB = yPositionMapper.selectByPrimaryKey(yPositionId);
        QYAssert.notNull(yPositionDB,"未获得该记录的相关信息!");

        favorService.checkBeforeInsertOrUpdate(vo,favorType);
        this.checkYParam(vo);

        int positionId = vo.getPositionId().intValue();
        int termType = vo.getTermType().intValue();
        Date beginTime = DateUtil.parseDate(vo.getBeginTime(), "yyyy-MM-dd HH:mm:ss");
        Date endTime = DateUtil.parseDate(vo.getEndTime(), "yyyy-MM-dd HH:mm:ss");
        int oldTermType = yPositionDB.getTermType().intValue();
        if (oldTermType == TermTypeEnums.长期.getCode()) {
            // 长期类型的，不能更改的属性
            positionId = yPositionDB.getPositionId().intValue();
            termType = oldTermType;
            beginTime = null;
            endTime = null;
        }

        // 校验强制提交状态
        int forceStatus = null == vo.getForceStatus()? ForceSubmitResultODTO.FORCE_STATUS_NO: vo.getForceStatus().intValue();
        ForceSubmitResultODTO temp = favorService.checkForceStatus(yPositionId, positionId, termType, beginTime, endTime, forceStatus,favorType);
        if (forceStatus == ForceSubmitResultODTO.FORCE_STATUS_NO && null != temp.getMessage()) {
            return new ForceSubmitResultODTO(ForceSubmitResultODTO.FORCE_STATUS_YES + "", temp.getMessage(), null, null, null);
        }

        XdaFavorYPosition.forUpdate(yPositionDB,vo,beginTime,endTime);
        int updateCount = yPositionMapper.updateByPrimaryKeySelective(yPositionDB);
        if (updateCount > 0) {
            // 适用门店
            Example deleteShopExample = new Example(XdaFavorYPositionStoreScope.class);
            deleteShopExample.createCriteria().andEqualTo("favorYPositionId", yPositionId);
            yPositionStoreScopeMapper.deleteByExample(deleteShopExample);
            if (vo.getIsAllStore().intValue() == IsAllShopTypeEnums.SPECIFIC_SHOP.getCode()) {
                this.insertFavorYPositionStore(yPositionId, vo.getStoreScopeList());
            }
            // 长期类型：启用状态冲突的，批量停用
            favorService.batchDisableStatus(temp,favorType);
            yPositionLogService.saveFavorYLog(yPositionId, OperateTypeEnums.修改.getCode());
        }
        return new ForceSubmitResultODTO("", null, null, updateCount, null);
    }

    /**
     * 纵向位--启用/停用
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ForceSubmitResultODTO updateYPositionStatus(XdaFavorPositionStatusIDTO vo) {
        QYAssert.isTrue(null != vo, "参数有误!");
        long favorPositionId = null == vo.getFavorPositionId()? 0: vo.getFavorPositionId().longValue();
        XdaFavorPositionBaseInfoODTO baseInfoEntry = yPositionMapper.queryFavorYPositionById(favorPositionId);
        QYAssert.notNull(baseInfoEntry,"未获得该记录的相关信息!");

        ForceSubmitResultODTO resultEntry = favorService.updateFavorPositionStatus(vo,baseInfoEntry,favorType);
        if(resultEntry!=null && StringUtils.isEmpty(resultEntry.getCode())){
            int operateType = vo.getStatus() == XSAppPositionInfoStatusEnums.启用.getCode() ? OperateTypeEnums.启用.getCode() : OperateTypeEnums.停用.getCode();
            yPositionLogService.saveFavorYLog(favorPositionId, operateType);
        }
        return resultEntry;
    }

    /**
     * 查询纵向位详情
     * @param yPositionId
     * @return
     */
    public XdaFavorYPositionInfoODTO queryYPositionDetail(Long yPositionId) {
        QYAssert.isTrue(null != yPositionId && yPositionId.longValue() > 0, "参数有误!");
        XdaFavorYPositionInfoODTO result = yPositionMapper.queryFavorYPositionDetail(yPositionId);
        int isAllShop = null == result.getIsAllStore()? 0: result.getIsAllStore().intValue();
        // 加载门店列表
        if (isAllShop == IsAllShopTypeEnums.SPECIFIC_SHOP.getCode()) {
            result.setSettlementList(yPositionStoreScopeMapper.selectSettlementList(yPositionId, XdaStoreScopeTypeEnums.结账客户.getCode()));
            result.setProductPriceModelList(yPositionStoreScopeMapper.selectProductPriceModelList(yPositionId, XdaStoreScopeTypeEnums.产品价格方案.getCode()));
            result.setStoreTypeList(yPositionStoreScopeMapper.selectDictionaryList(yPositionId, XdaStoreScopeTypeEnums.客户类型.getCode()));
            result.setStoreChannelList(yPositionStoreScopeMapper.selectDictionaryList(yPositionId, XdaStoreScopeTypeEnums.渠道.getCode()));
            result.setStoreList(yPositionStoreScopeMapper.selectStoreList(yPositionId, XdaStoreScopeTypeEnums.客户.getCode()));
        }
        return result;
    }

    /**
     * 分页查询
     * @param vo
     * @return
     */
    @Transactional(readOnly = true)
    public PageInfo<XdaFavorYPositionInfoODTO> queryYPositionPage(XdaFavorYPositionPageIDTO vo) {
        QYAssert.isTrue(null != vo, "参数有误!");
        PageInfo<XdaFavorYPositionInfoODTO> pageDate = PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            yPositionMapper.queryFavorYPositionPage(vo);
        });
        return pageDate;
    }

    // 插入门店List
    private void insertFavorYPositionStore(Long yPositionId, List<StoreScopeIDTO> storeScopeIDTOList) {
        List<XdaFavorYPositionStoreScope> storeList = new ArrayList<>();
        storeScopeIDTOList.forEach(shopId -> {
            storeList.add(new XdaFavorYPositionStoreScope(yPositionId, shopId));
        });
        yPositionStoreScopeMapper.insertList(storeList);
    }

    private void checkYParam(XdaFavorYPositionSaveIDTO vo){
        QYAssert.notNull(vo.getTargetType(),"‘标的类型’参数有误!");
        QYAssert.notNull(vo.getTargetTypeId(),"‘绑定标的’参数有误!");
        QYAssert.notNull(vo.getPicUrl(),"‘前台显示图片’参数有误!");
    }


}
