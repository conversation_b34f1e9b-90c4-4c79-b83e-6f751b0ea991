package com.pinshang.qingyun.xda.cms.service.h5;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.marketing.MarketSourceTypeEnum;
import com.pinshang.qingyun.base.enums.xd.H5ResourceTypeEnums;
import com.pinshang.qingyun.base.enums.xd.H5TemplateCodeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.marketing.dto.app.CommodityODTO;
import com.pinshang.qingyun.marketing.dto.app.CommodityPromotionIDTO;
import com.pinshang.qingyun.marketing.service.MtPromotionClient;
import com.pinshang.qingyun.price.service.StorePromotionClient;
import com.pinshang.qingyun.store.service.StoreManageClient;
import com.pinshang.qingyun.xda.cms.dto.h5.*;
import com.pinshang.qingyun.xda.cms.entry.h5.*;
import com.pinshang.qingyun.xda.cms.enums.GiftScopeEnum;
import com.pinshang.qingyun.xda.cms.mapper.h5.*;
import com.pinshang.qingyun.xda.cms.model.h5.*;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDeliveryTimeIDTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDeliveryTimeODTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityFrontClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.io.StringWriter;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/12/10 14:25:26
 */

@Slf4j
@Service
@Transactional
public class XdaH5TemplateRenderService {

    @Autowired
    private XdaH5TemplateMapper xdaH5TemplateMapper;
    @Autowired
    private XdaH5TemplateStyleCodeMapper xdaH5TemplateStyleCodeMapper;
    @Autowired
    private XdaH5PicMapper xdaH5PicMapper;
    @Autowired
    private XdaH5TemplateListMapper xdaH5TemplateListMapper;
    @Autowired
    private XdaH5TemplateTabCommodityMapper xdaH5TemplateTabCommodityMapper;
    @Autowired
    private XdaH5PicH5Mapper xdaH5PicH5Mapper;
    @Autowired
    private XdaH5TemplateUrlMapper xdaH5TemplateUrlMapper;
    @Autowired
    private XdaH5TemplateCommodityMapper xdaH5TemplateCommodityMapper;
    @Autowired
    private XdaCommodityFrontClient xdaCommodityFrontClient;
    @Autowired
    private StorePromotionClient storePromotionClient;
    @Autowired
    private MtPromotionClient mtPromotionClient;
    @Value("${pinshang.img-xd-server-url}")
    private String imgServerUrl; //商品图片url
    @Value("${pinshang.img-server-url}")
    private String nonCommodityimgServerUrl;//非商品图片url
    @Value("${pinshang.domain-name}")
    private String templatetargetUrl;
    @Autowired
    private StoreManageClient storeManageClient;

    public String getImgServerUrl() {
        return imgServerUrl;
    }

    public void setImgServerUrl(String imgServerUrl) {
        this.imgServerUrl = imgServerUrl;
    }

    public String getNonCommodityimgServerUrl() {
        return nonCommodityimgServerUrl;
    }

    public void setNonCommodityimgServerUrl(String nonCommodityimgServerUrl) {
        this.nonCommodityimgServerUrl = nonCommodityimgServerUrl;
    }

    /**
     * 加载H5数据
     * @param templateId
     * @param storeId
     * @return
     */
    public XdaH5TemplateDataEntry loadXdH5Data(Long templateId, Long storeId,String orderTime){
        QYAssert.isTrue(null != templateId," H5模板id 不能为null");
        QYAssert.isTrue(null != storeId," 客户id 不能为null");

        XdaH5TemplateDataEntry dataEntry = new XdaH5TemplateDataEntry();
        String content = "";
        String pageHtml= "";

        //1.通过模板id 获取模板数据(主数据，图片，图片H5，商品H5)
        XdaH5Template xdH5Template = xdaH5TemplateMapper.selectByPrimaryKey(templateId);
        QYAssert.isTrue(null != xdH5Template," H5模板未指定!");
        QYAssert.isTrue(null != xdH5Template.getTemplateCodeId()," H5模板样式未指定!");

        dataEntry.setTemplateId(xdH5Template.getTemplateCodeId());//模板id
        dataEntry.setTemplateName(xdH5Template.getTemplateName());//模板name
        dataEntry.setStatus(xdH5Template.getStatus());

        //模板停用 显示停用提醒页面
        if(xdH5Template.getStatus() == 0){
            //通过模板id 获取 模板样式-模板停用提醒
            XdaH5TemplateStyleCode xdH5TemplateStyleCode = xdaH5TemplateStyleCodeMapper.selectByPrimaryKey(5);
            if(null != xdH5TemplateStyleCode){
                pageHtml = xdH5TemplateStyleCode.getTemplateContent();
            }
            return dataEntry;
        }else {

            //2.通过模板id 获取 模板样式
            XdaH5TemplateStyleCode xdH5TemplateStyleCode = xdaH5TemplateStyleCodeMapper.selectByPrimaryKey(xdH5Template.getTemplateCodeId());
            if (null != xdH5TemplateStyleCode) {

                if (StringUtils.isNotEmpty(xdH5TemplateStyleCode.getTemplateCode())) {
                    //1.取H5模板1 头图
                    List<XdaH5TemplateListODTO> h5PicList = xdaH5TemplateListMapper.findXdH5PicListByH5TemplateId(templateId, H5ResourceTypeEnums.PIC.getCode(),1);
                    h5PicList.forEach(h5Pic ->{
                        if(org.apache.commons.lang.StringUtils.isNotBlank(h5Pic.getPicUrl()))
                            h5Pic.setPicUrl(imgServerUrl + h5Pic.getPicUrl());//组装头图显示地址
                    });
                    dataEntry.setH5PicList(h5PicList);

                    if (xdH5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE1.getCode())) {
                        //解析H5模板1
                        this.loadXdH5ModeDate_1(dataEntry, templateId, storeId,orderTime);
                    } else if (xdH5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE2.getCode())) {
                        //解析H5模板2
                        this.loadXdH5ModeDate_2(dataEntry, templateId, storeId,orderTime );
                    } else if (xdH5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE3.getCode())) {
                        //解析H5模板3
                        this.loadXdH5ModeDate_3(dataEntry, templateId, storeId,orderTime);
                    } else if (xdH5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE4.getCode())) {
                        //解析H5模板4
                        this.loadXdH5ModeDate_4(dataEntry, templateId, storeId,orderTime);
                    }else if (xdH5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE7.getCode())){
                        //解析H5模板7
                        this.loadXdH5ModeDate_7(dataEntry, templateId, storeId,orderTime);
                    }
                }
            }
        }
        return dataEntry;
    }

    /**
     * 生成H5
     * @param templateId
     * @param storeId
     * @return
     */
    public String generateXdH5(Long templateId,Long storeId,String isPreview,String orderTime) {
        QYAssert.isTrue(null != templateId, " H5模板id 不能为null");
        QYAssert.isTrue(null != storeId, " 客户id 不能为null");

        String content = "";
        String pageHtml = "";

        //1.通过模板id 获取模板数据(主数据，图片，图片H5，商品H5)
        XdaH5Template xdH5Template = xdaH5TemplateMapper.selectByPrimaryKey(templateId);
        QYAssert.isTrue(null != xdH5Template, " H5模板未指定!");
        QYAssert.isTrue(null != xdH5Template.getTemplateCodeId(), " H5模板样式未指定!");

        /**
         *  新H5模板107 后台系统预览时 不管模板是否停用都要展示页面
         *  isPreview是空或null 则非后台预览操作
         */

        if (StringUtils.isBlank(isPreview)) {

            //模板停用 显示停用提醒页面
            if (xdH5Template.getStatus() == 0) {
                //通过模板id 获取 模板样式-模板停用提醒
                XdaH5TemplateStyleCode xdH5TemplateStyleCode = xdaH5TemplateStyleCodeMapper.selectByPrimaryKey(5);
                if (null != xdH5TemplateStyleCode) {
                    pageHtml = xdH5TemplateStyleCode.getTemplateContent();
                }

                return pageHtml;
            }
       }


        //2.通过模板id 获取 模板样式
        XdaH5TemplateStyleCode xdH5TemplateStyleCode = xdaH5TemplateStyleCodeMapper.selectByPrimaryKey(xdH5Template.getTemplateCodeId());
        if (null != xdH5TemplateStyleCode) {
            content = xdH5TemplateStyleCode.getTemplateContent();

            //以下渲染模板
            VelocityEngine velocityEngine = new VelocityEngine();
            // 取得velocity的上下文context
            VelocityContext context = new VelocityContext();

            if (StringUtils.isNotEmpty(xdH5TemplateStyleCode.getTemplateCode())) {
                if(xdH5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE6.getCode())){
                    this.initXdH5Mode_6(context, templateId);
                }else{

                    //1.取H5模板1 头图
                    List<XdaH5TemplateListODTO> h5PicList = xdaH5TemplateListMapper.findXdH5PicListByH5TemplateId(templateId, H5ResourceTypeEnums.PIC.getCode(),1);
                    h5PicList.forEach(h5Pic ->{
                        if(org.apache.commons.lang.StringUtils.isNotBlank(h5Pic.getPicUrl()))
                            h5Pic.setPicUrl(nonCommodityimgServerUrl+h5Pic.getPicUrl());//组装头图显示地址
                    });
                    context.put("h5PicList", h5PicList);

                    if (xdH5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE1.getCode())) {
                        //解析H5模板1
                        this.initXdH5Mode_1(context, templateId, storeId,orderTime);
                    } else if (xdH5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE2.getCode())) {
                        //解析H5模板2
                        this.initXdH5Mode_2(context, templateId, storeId,orderTime);
                    } else if (xdH5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE3.getCode())) {
                        //解析H5模板3
                        this.initXdH5Mode_3(context, templateId, storeId,orderTime);
                    } else if (xdH5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE4.getCode())) {
                        //解析H5模板4
                        this.initXdH5Mode_4(context, templateId, storeId,orderTime);
                    }else if (xdH5TemplateStyleCode.getTemplateCode().equals(H5TemplateCodeEnums.TEMPLATE_STYLE7.getCode())){
                        //解析H5模板7
                        this.initXdH5ModeDate_7(context, templateId, storeId,orderTime);
                    }
                }
            }
            StringWriter writer = new StringWriter();
            velocityEngine.evaluate(context, writer, "", content);
            pageHtml = writer.toString();
            log.debug("-------------pageHtml----" + pageHtml);
        }

        return pageHtml;
    }


    /***
     * 获取改B端客户所属公司下的
     * 特价商品
     * 范围为:7结账客户 8产品价格方案 9门店
     * @param companyId
     * @param storeId 客户id
     * @param settlementCustomerId 结账客户id
     * @param productPriceModelId 产品价格方案id
     *
     * 相同的t_promotion_scope特价范围如9：客户，对应的客户id特价方案下，同一个商品有不同的特价方案，取最近创建的特价方案价格
     * 根据创建时间升序，相同商品存在多个特价方案，循环替换重复key的值，拿到最近的创建的特价方案商品特价
     * @return
     */
//    public Map<Integer,Map<String,XdaPromotionProductPriceODTO>> processPromotionToMap(Long companyId,Long storeId,Long settlementCustomerId,Long productPriceModelId,String orderTime){
//       String now=StringUtils.isNotBlank(orderTime) ? orderTime : LocalDate.now().toString();
//       XdaPromotionProductPriceIDTO productPriceIDTO=new XdaPromotionProductPriceIDTO(companyId,now,storeId,settlementCustomerId,productPriceModelId);
//       List<XdaPromotionProductPriceODTO> promotionCommodity=xdaH5TemplateCommodityMapper.selectPromotionProductPriceCommodity(productPriceIDTO);
//        Map<Integer,Map<String,XdaPromotionProductPriceODTO>> allMap=new HashMap<Integer,Map<String,XdaPromotionProductPriceODTO>>();
//       if(promotionCommodity==null || promotionCommodity.isEmpty()){
//           return allMap;
//       }
//
//
//       for(XdaPromotionProductPriceODTO p:promotionCommodity){
//           Integer scopeType=p.getScopeType();
//           Long typeId=p.getTypeId();
//           if(allMap.containsKey(scopeType)){
//               Map<String,XdaPromotionProductPriceODTO> map=  allMap.get(scopeType);
//               map.put(p.getCommodityId()+"_"+typeId,p);
//           }else{
//               /***
//                * 查询范围就是7结账客户 8产品价格方案 9门店
//                * map new 三次
//                */
//               Map<String,XdaPromotionProductPriceODTO> map=new HashMap<String,XdaPromotionProductPriceODTO>();
//               map.put(p.getCommodityId()+"_"+typeId,p);
//               allMap.put(scopeType,map);
//           }
//       }
//       return allMap;
//    }



    /***
     * 获取改B端客户所属公司下的
     * 赠送商品 促销商品
     * 范围为:1结账客户 2产品价格方案 3门店
     * @param companyId
     * @param storeId 客户id
     * @param settlementCustomerId 结账客户id
     * @param productPriceModelId 产品价格方案id
     * @return
     */
    public Map<Integer,Map<String,XdaGiftProductODTO>> processGiftToMap(Long companyId,Long storeId,Long settlementCustomerId,Long productPriceModelId,String orderTime){
        String now=StringUtils.isNotBlank(orderTime) ? orderTime : LocalDate.now().toString();
        XdaPromotionProductPriceIDTO productPriceIDTO=new XdaPromotionProductPriceIDTO(companyId,now,storeId,settlementCustomerId,productPriceModelId);
        List<XdaGiftProductODTO> giftCommodity=xdaH5TemplateCommodityMapper.selectGiftProductCommodity(productPriceIDTO);
        Map<Integer,Map<String,XdaGiftProductODTO>> allMap=new HashMap<Integer,Map<String,XdaGiftProductODTO>>();
        if(giftCommodity==null || giftCommodity.isEmpty()){
            return allMap;
        }


        for(XdaGiftProductODTO p:giftCommodity){
            Integer scopeType=p.getScopeType();
            Long typeId=p.getTypeId();
            if(allMap.containsKey(scopeType)){
                Map<String,XdaGiftProductODTO> map=  allMap.get(scopeType);
                map.put(p.getCommodityId()+"_"+typeId,p);
            }else{
                /***
                 * 查询范围就是1结账客户 2产品价格方案 3门店
                 * map new 三次
                 */
                Map<String,XdaGiftProductODTO> map=new HashMap<String,XdaGiftProductODTO>();
                map.put(p.getCommodityId()+"_"+typeId,p);
                allMap.put(scopeType,map);
            }
        }
        return allMap;
    }


    /**
     * 解析模板7 生成H5页面代码
     * @param context
     * @param templateId
     */
    public void initXdH5ModeDate_7(VelocityContext context, Long templateId, Long storeId,String orderTime) {
       XdaH5AutoTemplateAllDataEntry xdH5AutoTemplateAllDataEntry= this.getXdH5AutoTempData_7(templateId,storeId, OrderSourceTypeEnum.APP,orderTime);
       if(xdH5AutoTemplateAllDataEntry==null){
           xdH5AutoTemplateAllDataEntry=new XdaH5AutoTemplateAllDataEntry();
       }
       context.put("xdH5AutoTemplateAllDataEntry", JsonUtil.java2json(xdH5AutoTemplateAllDataEntry));
    }


    /***
     * 反射获取商品id
     * @param items
     * @param commodityId 商品id属性字段名称 反射获取值
     * @param <T>
     * @return
     */
    private <T> List<Long> getcommodityIds(List<T> items,String commodityId){
        List<Long> commodityIds=items.stream().map(commodity ->{
            try {
                Class c=commodity.getClass();
                Field commodityIdField= c.getDeclaredField(StringUtils.isNotBlank(commodityId) ? commodityId : "commodityId");
                commodityIdField.setAccessible(true);
                Object obj=commodityIdField.get(commodity);
                if(obj!=null){
                    Long id=Long.valueOf(obj.toString());
                    return id;
                }
            } catch (Exception e) {
                log.error("反射获取商品id异常");
                e.printStackTrace();
            }
            return 0L;
        }).distinct().collect(Collectors.toList());

        return commodityIds;
    }

    /***
     * 赋值picUrl处理
     * @param t
     * @param isSetPicUrl
     * @param <T>
     */
    private <T> void setPicUrl(T t,String isSetPicUrl,String imgUrl){
        if(StringUtils.isNotBlank(isSetPicUrl)){
            try {
                Class entryClass=t.getClass();
                Field picUrlField=entryClass.getDeclaredField(isSetPicUrl);
                picUrlField.setAccessible(true);
                Object objPicUrl=picUrlField.get(t);
                if(objPicUrl!=null){
                    String picUrl=objPicUrl.toString();
                    picUrlField.set(t,imgUrl+picUrl);//组装图版显示地址
                }
            } catch (Exception e) {
                log.error("反射赋值商品imgServerUrl+picUrl异常");
                e.printStackTrace();
            }
        }
    }


    /***
     * 赋值商品价格处理
     * @param t
     * @param isSetPrice
     * @param <T>
     */
    private <T> void setCommodityPrice(T t,String isSetPrice,String price){
        if(StringUtils.isNotBlank(isSetPrice)){
            try {
                Class entryClass=t.getClass();
                Field priceField=entryClass.getDeclaredField(isSetPrice);
                priceField.setAccessible(true);
                priceField.set(t,new BigDecimal(price));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    /***
     * 根据门店 过滤出门店下的商品 并处理需要赋值的字段
     * @param items 商品相关集合
     * @param storeId B端客户id
     * @param commodityId 商品id 属性字段名称用于反射获值
     * @param isSetPicUrl 商品url 属性字段名称用于反射赋值
     * @param isCommodityImg 是否商品图片 true 是 imgServerUrl  、false 否 nonCommodityimgServerUrl
     * @param <T>
     * @return
     */
    public <T> XdaAddAllEsDataAndH5Commodity<T> filterCommodityByShopId(List<T> items, Long storeId,
                                                                        String commodityId, String isSetPicUrl,String isSetCommodityPrice,boolean isCommodityImg,String orderTime){
        XdaAddAllEsDataAndH5Commodity<T> xdAddAllEsDataAndH5Commodity=new XdaAddAllEsDataAndH5Commodity<T>();
        if(items==null || items.isEmpty()){
            return xdAddAllEsDataAndH5Commodity;
        }

        /***
         * 根据b端客户获取t_store_settlement 下的产品价格方案id
         */
        List<XdaStoreSettODTO> storeStt= xdaH5TemplateCommodityMapper.selectStoreSettByStoreId(storeId);
        if(storeStt==null || storeStt.isEmpty()){
            log.error("该B端客户{},非正常客户，请确认",storeId);
            return xdAddAllEsDataAndH5Commodity;
        }
        if(storeStt.size()>1){
            log.error("B端客户指针对外部客户目前数据都是一对一的,该B端客户{},有多条记录，请确认客户表对应数据",storeId);
            return xdAddAllEsDataAndH5Commodity;
        }
        XdaStoreSettODTO xdaStoreSettODTO=storeStt.get(0);

        /***
         * 根据b端客户获取t_store_settlement 下的产品价格方案id
         * 获取产品价格方案下的商品
         */
        List<XdaStoreProductPriceODTO> commodityItmes=xdaH5TemplateCommodityMapper.selectStoreProductPriceCommodityById(xdaStoreSettODTO.getProductPriceModelId());
        if(commodityItmes==null || commodityItmes.isEmpty()){
            log.error("该B端客户{},未获取到对应产品价格方案：{},下的商品信息",storeId,xdaStoreSettODTO.getProductPriceModelId());
            return xdAddAllEsDataAndH5Commodity;
        }
        Map<Long,XdaStoreProductPriceODTO> maps=commodityItmes.stream().collect(Collectors.toMap(XdaStoreProductPriceODTO::getCommodityId,val -> val,(v1,v2) -> v2));

        if(SpringUtil.isNotEmpty(maps)){
            Iterator<T> iter=items.iterator();
            while (iter.hasNext()){
                T entry=iter.next();
                Class entryClass=entry.getClass();
                try {
                    Field commdityIdField=entryClass.getDeclaredField(commodityId);
                    commdityIdField.setAccessible(true);
                    Object obj=commdityIdField.get(entry);
                    if(obj==null){
                        iter.remove();
                        continue;
                    }
                    Long cId = Long.valueOf(obj.toString());
                    if(!maps.containsKey(cId)){
                        iter.remove();
                    }else {
                        XdaStoreProductPriceODTO storeProductPriceODTO=maps.get(cId);
                        /*** 是否赋值 picUrl */
                        if(StringUtils.isNoneBlank(isSetPicUrl)) {
                            String imgUrl=nonCommodityimgServerUrl;
                            if(isCommodityImg){
                                imgUrl=imgServerUrl;
                            }
                            this.setPicUrl(entry, isSetPicUrl,imgUrl);
                        }
                        /*** 是否赋值 commodityPrice */
                        if(StringUtils.isNoneBlank(isSetCommodityPrice)) {
                            this.setCommodityPrice(entry, isSetCommodityPrice, storeProductPriceODTO.getCommodityPrice());
                        }
                    }

                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            xdAddAllEsDataAndH5Commodity.setCommodityItem(items);
        }else{
            log.error("未获取到B端客户{},产品价格方案下的商品,请检查数据}",storeId);
            return xdAddAllEsDataAndH5Commodity;
        }
        List<Long> commodityIds=this.getcommodityIds(items,commodityId);
        xdAddAllEsDataAndH5Commodity.setCommodityIds(commodityIds);

        /***
         *获取B端客户属性公司下的特价商品
         */
        //   Map<Integer,Map<String,XdaPromotionProductPriceODTO>> promotionPriceMap=this.processPromotionToMap(xdaStoreSettODTO.getCompanyId(),storeId,xdaStoreSettODTO.getSettlementCustomerId(),xdaStoreSettODTO.getProductPriceModelId(),orderTime);

        /***
         *获取B端客户属性公司下的促销商品
         */
        //Map<Integer,Map<String,XdaGiftProductODTO>> giftMap=this.processGiftToMap(xdaStoreSettODTO.getCompanyId(),storeId,xdaStoreSettODTO.getSettlementCustomerId(),xdaStoreSettODTO.getProductPriceModelId(),orderTime);
        /***
         * 获取特价和促销商品
         */
        Map<Long, CommodityODTO> commodityPromotionMap = this.selectCommodityPromotionToMap(commodityIds.stream().distinct().collect(Collectors.toList()),storeId,orderTime);
        xdAddAllEsDataAndH5Commodity.setCommodityPromotionMap(commodityPromotionMap);
        //xdAddAllEsDataAndH5Commodity.setPromotionToMap(selectCommodityPromotion); //特价商品
        //xdAddAllEsDataAndH5Commodity.setGiftToMap(giftMap); //促销商品
        xdAddAllEsDataAndH5Commodity.setStoreSettODTO(xdaStoreSettODTO);
        return xdAddAllEsDataAndH5Commodity;
        }

    /**
     * 解析模板1 生成H5页面代码
     * @param context
     * @param templateId
     * @param storeId
     */
    public void initXdH5Mode_1(VelocityContext context,Long templateId,Long storeId,String orderTime){
        XdaH5TemplateResultBeanModel_1 resultBeanModel_1=this.processInitXdH5Mode_1(templateId,storeId,orderTime);
        context.put("h5PicCommodityList", resultBeanModel_1.getH5PicCommodityList());
        context.put("h5TemplateTabList", resultBeanModel_1.getH5TemplateTabList());
    }

    /***
     * 处理生成H5页面数据
     * @param templateId
     * @param storeId
     * @return
     */
    public XdaH5TemplateResultBeanModel_1 processInitXdH5Mode_1(Long templateId, Long storeId,String orderTime){
            XdaH5TemplateResultBeanModel_1 resultBeanModel_1=new XdaH5TemplateResultBeanModel_1();
            //2.取H5模板1 图片+商品
            List<XdaH5TemplateListODTO> h5PicCommodityList = xdaH5TemplateListMapper.findXdH5PicCommodityListByTemplateId(templateId, H5ResourceTypeEnums.PIC_COMMODITY.getCode());
            XdaAddAllEsDataAndH5Commodity xdAddAllEsDataAndH5Commodity = this.filterCommodityByShopId(h5PicCommodityList,storeId, "commodityId", "picUrl",null,false,orderTime);
            h5PicCommodityList = xdAddAllEsDataAndH5Commodity.getCommodityItem();
            resultBeanModel_1.setH5PicCommodityList(h5PicCommodityList);
            //3.取H5模板1  tab列表+tab明细
            List<XdaH5TemplateTabODTO> h5TemplateTabList = xdaH5TemplateListMapper.findXdH5TemplateTabListByTemplateId(templateId, H5ResourceTypeEnums.H5_TAB.getCode());
            if(null != h5TemplateTabList && h5TemplateTabList.size() > 0 ){
                h5TemplateTabList.forEach(tab->{
                    List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList = getTabCommodityList(templateId,tab.getId(),tab.getResourceType(),storeId,tab.getAppShowNum(),orderTime);
                    if(StringUtils.isNotBlank(orderTime)) {
                        /*** 处理不可订货商品 用于商品列表购物车标志置灰 **/
                        this.processNoDeliveryCommodity(h5TemplateTabCommodityList, orderTime, storeId);
                    }
                    if(null != h5TemplateTabCommodityList) {
                        tab.setCommodityCount(h5TemplateTabCommodityList.size());
                    }

                    //转换JSON
                    setXdH5TemplateTabCommodityEntryToJson(h5TemplateTabCommodityList);

                    //处理商品公斤转市斤
                   // this.gongjinTOshijin(h5TemplateTabCommodityList);


                    tab.setH5TemplateTabCommodityEntryList(h5TemplateTabCommodityList);

                });
            }
          resultBeanModel_1.setH5TemplateTabList(h5TemplateTabList);
          return resultBeanModel_1;
        }

    /**
     *取模板2 四个商品位
     * @param templateId
     * @param appShowNum
     * @param storeId
     * @param appShowNum
     * @return
     */
    private List<XdaH5TemplateTabCommodityODTO> getTabCommodity4List(Long templateId , Long storeId , Integer appShowNum,String orderTime){
        List<XdaH5TemplateTabCommodityODTO>  tabCommodityEntryList = xdaH5TemplateTabCommodityMapper.findXdH5TemplateCommodityListByH5TemplateId(templateId, H5ResourceTypeEnums.H5_TAB_COMMODITY.getCode(),null);
        tabCommodityEntryList =this.setProcessStock(tabCommodityEntryList,storeId,"commodityId","defaultPicUrl","commodityPrice",orderTime);

        List<XdaH5TemplateTabCommodityODTO>  tabCommodityEntrys = new ArrayList<XdaH5TemplateTabCommodityODTO>();
        if(null != tabCommodityEntryList && tabCommodityEntryList.size() > 0 ){

            for(int i=0; i < appShowNum; i++ ){
                XdaH5TemplateTabCommodityODTO entry=tabCommodityEntryList.get(i);
                tabCommodityEntrys.add(entry);
            }
        }

        return tabCommodityEntrys;
    }

    /**
     *处理商品信息
     * @param templateId
     * @param tabId
     * @param resourceType
     * @param storeId
     * @param appShowNum
     * @return
     */
    private List<XdaH5TemplateTabCommodityODTO> getTabCommodityList(Long templateId, Long tabId, Integer resourceType, Long storeId , Integer appShowNum,String orderTime){
        log.info("=======getTabCommodityList============templateId=" + templateId + "=====tabId=" + tabId + "=====resourceType=" + resourceType + "=====storeId=" + storeId);
        log.warn("=======getTabCommodityList============templateId=" + templateId + "=====tabId=" + tabId + "=====resourceType=" + resourceType + "=====storeId=" + storeId);
        log.debug("=======getTabCommodityList============templateId=" + templateId + "=====tabId=" + tabId + "=====resourceType=" + resourceType + "=====storeId=" + storeId);
        List<XdaH5TemplateTabCommodityODTO>  tabCommodityEntryList = xdaH5TemplateTabCommodityMapper.findXdH5TemplateTabCommodityListByTabIdAndShowNum(templateId,tabId,resourceType,50);
        tabCommodityEntryList =this.setProcessStock(tabCommodityEntryList,storeId,"commodityId","defaultPicUrl","commodityPrice",orderTime);
        List<XdaH5TemplateTabCommodityODTO> tabCommodityEntrys = new ArrayList<XdaH5TemplateTabCommodityODTO>();
        if(null != tabCommodityEntryList && tabCommodityEntryList.size() > 0 ) {

            if(tabCommodityEntryList.size() < appShowNum ) {
                appShowNum = tabCommodityEntryList.size();
            }

            if (null != tabCommodityEntryList && tabCommodityEntryList.size() > 0) {
                for (int i = 0; i < appShowNum; i++) {
                    XdaH5TemplateTabCommodityODTO entry=tabCommodityEntryList.get(i);
                    tabCommodityEntrys.add(entry);
                }
            }


        }

        return tabCommodityEntrys;
    }

    private void setXdH5TemplateTabCommodityEntryToJson(List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityEntryList){
        if(null != h5TemplateTabCommodityEntryList && h5TemplateTabCommodityEntryList.size() > 0 ){
            h5TemplateTabCommodityEntryList.forEach(tabCommodityEntry -> tabCommodityEntry.setJsonStr(JSON.toJSONString(tabCommodityEntry)));
        }
    }


    /**
     * 解析模板2 生成H5页面代码
     * @param context
     * @param templateId
     * @param storeId
     */
    public void initXdH5Mode_2(VelocityContext context,Long templateId,Long storeId,String orderTime){
         XdaH5TemplateResultBeanModel_2 resultBeanModel_2=this.processInitXdH5Mode_2(templateId,storeId,orderTime);
        context.put("h5Pic", resultBeanModel_2.getH5Pic());
        //2-2图片连接H5
        context.put("h5PicH5", resultBeanModel_2.getH5PicH5());
        context.put("h5PicH5.targetUrl", resultBeanModel_2.getH5PicH5()==null ? null : resultBeanModel_2.getH5PicH5().getTargetUrl());
        context.put("h5TemplateTabCommodityItem", resultBeanModel_2.getH5TemplateTabCommodityItem());
        context.put("h5TemplateTabList", resultBeanModel_2.getH5TemplateTabList());

    }

    /**
     * 处理模板2 生成H5页面数据
     * @param templateId
     * @param storeId
     */
    public XdaH5TemplateResultBeanModel_2 processInitXdH5Mode_2(Long templateId, Long storeId,String orderTime){
        XdaH5TemplateResultBeanModel_2 resultBeanModel_2=new XdaH5TemplateResultBeanModel_2();
        //2.取H5模板2 通栏区数据
        //2-1图片
        XdaH5PicODTO h5Pic = xdaH5PicMapper.findXdaH5PicEntryByH5TemplateId(templateId,2);
        if(null != h5Pic && !StringUtil.isNullOrEmpty(h5Pic.getPicUrl())){
            h5Pic.setPicUrl(nonCommodityimgServerUrl + h5Pic.getPicUrl().trim());
        }
        resultBeanModel_2.setH5Pic(h5Pic);
        //2-2图片连接H5
        XdaH5PicH5ODTO h5PicH5 = xdaH5PicH5Mapper.findXdaH5PicH5ByH5TemplateId(templateId);
        if(null != h5PicH5 && !StringUtil.isNullOrEmpty(h5PicH5.getPicUrl())){
            h5PicH5.setPicUrl(nonCommodityimgServerUrl + h5PicH5.getPicUrl().trim());
            h5PicH5.setShopId(storeId);
            h5PicH5.setTargetUrl(templatetargetUrl+"/gateXdaApi/xdaCms/xdaH5Render/renderXdH5?templateId="+h5PicH5.getTemplateId()+"&storeId="+storeId+"");
        }
        resultBeanModel_2.setH5PicH5(h5PicH5);

        List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityItem = getTabCommodity4List(templateId ,storeId ,4,orderTime);
        if(StringUtils.isNotBlank(orderTime)) {
            /*** 处理不可订货商品 用于商品列表购物车标志置灰 **/
            this.processNoDeliveryCommodity(h5TemplateTabCommodityItem, orderTime, storeId);
        }
        //转换JSON
        setXdH5TemplateTabCommodityEntryToJson(h5TemplateTabCommodityItem);

        resultBeanModel_2.setH5TemplateTabCommodityItem(h5TemplateTabCommodityItem);

        List<XdaH5TemplateTabODTO> h5TemplateTabList = xdaH5TemplateListMapper.findXdH5TemplateTabListByTemplateId(templateId, H5ResourceTypeEnums.H5_TAB.getCode());
        if(null != h5TemplateTabList && h5TemplateTabList.size() > 0 ){
            h5TemplateTabList.forEach(tab->{

                List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList = getTabCommodityList(templateId,tab.getId(),tab.getResourceType(),storeId,tab.getAppShowNum(),orderTime);
                if(StringUtils.isNotBlank(orderTime)) {
                    /*** 处理不可订货商品 用于商品列表购物车标志置灰 **/
                    this.processNoDeliveryCommodity(h5TemplateTabCommodityList, orderTime, storeId);
                }
                if(null != h5TemplateTabCommodityList) {
                    tab.setCommodityCount(h5TemplateTabCommodityList.size());
                }

                //转换JSON
                setXdH5TemplateTabCommodityEntryToJson(h5TemplateTabCommodityList);

                //处理商品公斤转市斤
               // this.gongjinTOshijin(h5TemplateTabCommodityList);


                tab.setH5TemplateTabCommodityEntryList(h5TemplateTabCommodityList);
            });
        }
        resultBeanModel_2.setH5TemplateTabList(h5TemplateTabList);
        return resultBeanModel_2;
    }


    /**
     * 解析模板3 生成H5页面代码
     * @param context
     * @param templateId
     */
    public void initXdH5Mode_3(VelocityContext context,Long templateId,Long storeId,String orderTime){
        XdaH5TemplateResultBeanModel_3 resultBeanModel_3=this.processInitXdH5Mode_3(templateId,storeId,orderTime);
        context.put("h5PicCommodityList",resultBeanModel_3.getH5PicCommodityList());
        context.put("h5TemplateTabList", resultBeanModel_3.getH5TemplateTabList());
    }

    /**
     * 解析模板3 生成H5页面代码
     * @param templateId
     */
    public XdaH5TemplateResultBeanModel_3 processInitXdH5Mode_3(Long templateId, Long storeId,String orderTime){
        XdaH5TemplateResultBeanModel_3 resultBeanModel_3=new XdaH5TemplateResultBeanModel_3();
        //2.取H5模板3 图片+商品
        List<XdaH5TemplateListODTO> h5PicCommodityList = xdaH5TemplateListMapper.findXdH5PicCommodityListByTemplateId(templateId,H5ResourceTypeEnums.PIC_COMMODITY.getCode());
        XdaAddAllEsDataAndH5Commodity xdAddAllEsDataAndH5Commodity = this.filterCommodityByShopId(h5PicCommodityList, storeId, "commodityId", "picUrl",null,false,orderTime);
        h5PicCommodityList = xdAddAllEsDataAndH5Commodity.getCommodityItem();
        resultBeanModel_3.setH5PicCommodityList(h5PicCommodityList);
        //3.取H5模板3  tab列表+tab明细

        List<XdaH5TemplateTabODTO> h5TemplateTabList = xdaH5TemplateListMapper.findXdH5TemplateTabListByTemplateId(templateId, H5ResourceTypeEnums.H5_TAB_TL.getCode());
        if(null != h5TemplateTabList && h5TemplateTabList.size() > 0 ){
            h5TemplateTabList.forEach(tab->{

                List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList = getTabCommodityList(templateId,tab.getId(),tab.getResourceType(),storeId,tab.getAppShowNum(),orderTime);

                if(null != h5TemplateTabCommodityList && h5TemplateTabCommodityList.size() > 0 ) {
                    if(StringUtils.isNotBlank(orderTime)) {
                        /*** 处理不可订货商品 用于商品列表购物车标志置灰 **/
                        this.processNoDeliveryCommodity(h5TemplateTabCommodityList, orderTime, storeId);
                    }
                    tab.setCommodityCount(h5TemplateTabCommodityList.size());

                    if (StringUtils.isNotBlank(tab.getPicUrl())) {
                        tab.setPicUrl(nonCommodityimgServerUrl + tab.getPicUrl());
                    }
                    //转换JSON
                    setXdH5TemplateTabCommodityEntryToJson(h5TemplateTabCommodityList);

                    //处理商品公斤转市斤
                    //this.gongjinTOshijin(h5TemplateTabCommodityList);
                    tab.setH5TemplateTabCommodityEntryList(h5TemplateTabCommodityList);
                }
            });
        }
        resultBeanModel_3.setH5TemplateTabList(h5TemplateTabList);
        return  resultBeanModel_3;
    }


    /**
     * 解析模板4 生成H5页面代码
     * @param context
     * @param templateId
     * @param storeId
     */
    public void initXdH5Mode_4(VelocityContext context,Long templateId,Long storeId,String orderTime){
        XdaH5TemplateResultBeanModel_4 resultBeanModel_4=this.processInitXdH5Mode_4(templateId,storeId,orderTime);
        context.put("h5TemplateTabList", resultBeanModel_4.getH5TemplateTabList());
    }



    /**
     * 处理模板4 生成H5页面数据
     * @param templateId
     * @param storeId
     */
    public XdaH5TemplateResultBeanModel_4 processInitXdH5Mode_4(Long templateId, Long storeId,String orderTime){
        XdaH5TemplateResultBeanModel_4 resultBeanModel_4=new XdaH5TemplateResultBeanModel_4();
        //2.取H5模板4  tab列表+tab明细
        List<XdaH5TemplateTabODTO> h5TemplateTabList = xdaH5TemplateListMapper.findXdH5TemplateTabListByTemplateId(templateId, H5ResourceTypeEnums.H5_TAB_COMMODITY.getCode());
        if(null != h5TemplateTabList && h5TemplateTabList.size() > 0 ){
            h5TemplateTabList.forEach(tab->{

                List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList = xdaH5TemplateTabCommodityMapper.findH5TemplateTabCommodityListByTabId(templateId,tab.getId(),tab.getResourceType());
                h5TemplateTabCommodityList =this.setProcessStock(h5TemplateTabCommodityList,storeId,"commodityId","defaultPicUrl","commodityPrice",orderTime);
                if(StringUtils.isNotBlank(orderTime)) {
                    /*** 处理不可订货商品 用于商品列表购物车标志置灰 **/
                    this.processNoDeliveryCommodity(h5TemplateTabCommodityList, orderTime, storeId);
                }
                if(null != h5TemplateTabCommodityList) {
                    tab.setCommodityCount(h5TemplateTabCommodityList.size());
                }
                //转换JSON
                setXdH5TemplateTabCommodityEntryToJson(h5TemplateTabCommodityList);

                //处理商品公斤转市斤
               // this.gongjinTOshijin(h5TemplateTabCommodityList);


                tab.setH5TemplateTabCommodityEntryList(h5TemplateTabCommodityList);
            });
        }
        resultBeanModel_4.setH5TemplateTabList(h5TemplateTabList);
        return resultBeanModel_4;
    }

    /**
     * 解析模板6 生成H5页面代码
     * @param context
     * @param templateId
     */
    public void initXdH5Mode_6(VelocityContext context,Long templateId){
        //通过H5模板id,获取H5模板对应的 url
        XdaH5TemplateUrlODTO h5TemplateUrlEntry = xdaH5TemplateUrlMapper.findXdaH5TemplateUrlByTemplateId(templateId);

        //判断URL
        String pageUrl = "" ;
        if(null != h5TemplateUrlEntry && org.apache.commons.lang.StringUtils.isNotBlank(h5TemplateUrlEntry.getPageUrl()))
            pageUrl = h5TemplateUrlEntry.getPageUrl();

        context.put("pageUrl", pageUrl);
    }

    /**
     * 商品公斤 转 市斤  显示
     * @param h5TemplateTabCommodityList
     * @return
     */
   /* public  List<XdaH5TemplateTabCommodityODTO> gongjinTOshijin(List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList){
        if(null != h5TemplateTabCommodityList && h5TemplateTabCommodityList.size() > 0 ) {
            h5TemplateTabCommodityList.forEach(commodity -> {
                //处理称重商品
                if(null != commodity.getIsWeight() &&  commodity.getIsWeight() == 1) {
                        commodity.setUnitName("份");
                        BigDecimal commodityPrice = commodity.getCommodityPrice();//.multiply(new BigDecimal(commodity.getCommodityPackageSpec())).setScale(2,BigDecimal.ROUND_HALF_UP);
                        commodity.setCommodityPrice(commodityPrice);
                }
            });
        }
        return h5TemplateTabCommodityList;
    }*/

    /**
     * 检查判断 促销互斥开关
     * @param h5TemplateTabCommodityList
     * @return
     */
   /* public  List<XdaH5TemplateTabCommodityODTO> tjCommodity(List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList){
        XdaBackSetting xdBackSetting =  xdaBackSettingService.findXdBackSettingDetails();
        if(null != xdBackSetting && null != xdBackSetting.getPromotionMutexSwitch() && xdBackSetting.getPromotionMutexSwitch() == 1 ) {
            if (null != h5TemplateTabCommodityList && h5TemplateTabCommodityList.size() > 0) {
                h5TemplateTabCommodityList.forEach(commodity -> {
                    //是否特价
                    if (!StringUtil.isNullOrEmpty(commodity.getLimitInfo())) {
                        commodity.setPromotionInfo("");
                    }
                });
            }
        }
        return h5TemplateTabCommodityList;
    }*/

    /**
     * 解析模板1 取获H5页面数据
     * @param dataEntry
     * @param templateId
     * @param storeId
     */
    public void loadXdH5ModeDate_1(XdaH5TemplateDataEntry dataEntry, Long templateId, Long storeId,String orderTime){

        XdaH5TemplateResultBeanModel_1 resultBeanModel_1=this.processInitXdH5Mode_1(templateId,storeId,orderTime);
        dataEntry.setH5PicCommodityList(resultBeanModel_1.getH5PicCommodityList());
        dataEntry.setH5TemplateTabList(resultBeanModel_1.getH5TemplateTabList());
    }


    /**
     * 解析模板2 取获H5页面数据
     * @param dataEntry
     * @param templateId
     * @param storeId
     */
    public void loadXdH5ModeDate_2(XdaH5TemplateDataEntry dataEntry, Long templateId, Long storeId,String orderTime){

        XdaH5TemplateResultBeanModel_2 resultBeanModel_2=this.processInitXdH5Mode_2(templateId,storeId,orderTime);
        dataEntry.setH5Pic(resultBeanModel_2.getH5Pic());
        //2-2图片连接H5
        dataEntry.setH5PicH5(resultBeanModel_2.getH5PicH5());
        dataEntry.setH5TemplateTabCommodityItem(resultBeanModel_2.getH5TemplateTabCommodityItem());
        dataEntry.setH5TemplateTabList(resultBeanModel_2.getH5TemplateTabList());
    }


    /**
     * 解析模板3 取获H5页面数据
     * @param dataEntry
     * @param templateId
     * @param storeId
     */
    public void loadXdH5ModeDate_3(XdaH5TemplateDataEntry dataEntry, Long templateId, Long storeId,String orderTime){

        XdaH5TemplateResultBeanModel_3 resultBeanModel_3=this.processInitXdH5Mode_3(templateId,storeId,orderTime);
        dataEntry.setH5PicCommodityList(resultBeanModel_3.getH5PicCommodityList());
        dataEntry.setH5TemplateTabList(resultBeanModel_3.getH5TemplateTabList());
    }


    /**
     * 解析模板4 取获H5页面数据
     * @param dataEntry
     * @param templateId
     * @param storeId
     */
    public void loadXdH5ModeDate_4(XdaH5TemplateDataEntry dataEntry, Long templateId, Long storeId,String orderTime){

        XdaH5TemplateResultBeanModel_4 resultBeanModel_4=this.processInitXdH5Mode_4(templateId,storeId,orderTime);
        dataEntry.setH5TemplateTabList(resultBeanModel_4.getH5TemplateTabList());
    }

    /***
     * 解析模板7 取获小程序数据
     * @param dataEntry
     * @param templateId
     * @param storeId
     */
    public void loadXdH5ModeDate_7(XdaH5TemplateDataEntry dataEntry, Long templateId, Long storeId,String orderTime) {
        XdaH5AutoTemplateAllDataEntry allDataEntry=this.getXdH5AutoTempData_7(templateId,storeId,OrderSourceTypeEnum.MINI,orderTime);
        dataEntry.setXdH5AutoTemplateAllDataEntry(allDataEntry);
    }


    /***
     * 获取特价商品
     * 获取促销商品
     * @param promotionToMap 特价
     * @param xdaStoreSettODTO
     * @param commodityId
     * @return
     */
//    private Object getPromotionProductPrice( Map<Long, StorePromotionCommodityPriceODTO> promotionToMap,XdaStoreSettODTO xdaStoreSettODTO,Long commodityId){
//        /*** 客户信息 **/
//        Long store=xdaStoreSettODTO.getStoreId();//客户id
//        Long storeSett=xdaStoreSettODTO.getSettlementCustomerId();//结账客户id
//        Long promotionPrice=xdaStoreSettODTO.getProductPriceModelId();//产品价格方案id
//
//        /***
//         * 特价匹配范围
//         * 7结账客户 8产品价格方案 9门店
//         */
//        Integer sopeCode=9;
//        Long typeId=store;
//        for(int i=9;i>=7;i--){
//            if(i==9){
//                sopeCode=PromotionScopeEnum.SRORE.getCode();
//                typeId=store;
//            }else if(i==8){
//                sopeCode=PromotionScopeEnum.PRODUCT_PRICE_MODEL.getCode();
//                typeId=promotionPrice;
//            }else if(i==7){
//                sopeCode=PromotionScopeEnum.SETTLEMENT_CUSTOMER.getCode();
//                typeId=storeSett;
//            }
//
//            /***
//             * 处理特价
//             */
//            if(promotionToMap!=null && promotionToMap.size()>0){
//                /***
//                 * 1:客户范围查找
//                 * 2:产品价格方案范围查找
//                 * 3:结账客户范围查找
//                 * */
//                if(promotionToMap.containsKey(sopeCode)) {
//                    Map<String, XdaPromotionProductPriceODTO> promotionMaps = promotionToMap.get(sopeCode);
//                    String key = commodityId + "_" + typeId;
//                    if (promotionMaps.containsKey(key)) {
//                        XdaPromotionProductPriceODTO xdaPromotionProductPriceODTO = promotionMaps.get(key);
//                        return new BigDecimal(xdaPromotionProductPriceODTO.getPrice());//特价
//                    }else{
//                        continue;
//                    }
//                }else {
//                    continue;
//                }
//            }
//        }
//        return null;
//    }


    /***
     * 获取促销商品
     * @param giftToMap 促销
     * @param xdaStoreSettODTO
     * @param commodityId
     * @return
     */
    private Object getGiftProduct(Map<Integer, Map<String, XdaGiftProductODTO>> giftToMap,XdaStoreSettODTO xdaStoreSettODTO,Long commodityId){
        /*** 客户信息 **/
        Long store=xdaStoreSettODTO.getStoreId();//客户id
        Long storeSett=xdaStoreSettODTO.getSettlementCustomerId();//结账客户id
        Long promotionPrice=xdaStoreSettODTO.getProductPriceModelId();//产品价格方案id

        /***
         * 促销匹配范围
         * 1结账客户 2产品价格方案 3门店
         */
        Integer sopeCode=3;
        Long typeId=store;
        for(int i=3;i>=1;i--){
            if(i==3){
                sopeCode= GiftScopeEnum.SRORE.getCode();
                typeId=store;
            }else if(i==2){
                sopeCode=GiftScopeEnum.PRODUCT_PRICE_MODEL.getCode();
                typeId=promotionPrice;
            }else if(i==1){
                sopeCode=GiftScopeEnum.SETTLEMENT_CUSTOMER.getCode();
                typeId=storeSett;
            }

            /***
             * 处理促销
             */
            if(giftToMap!=null && giftToMap.size()>0){
                /***
                 * 1:客户范围查找
                 * 2:产品价格方案范围查找
                 * 3:结账客户范围查找
                 * */
                if(giftToMap.containsKey(sopeCode)) {
                    Map<String, XdaGiftProductODTO> giftMaps = giftToMap.get(sopeCode);
                    String key = commodityId + "_" + typeId;
                    if (giftMaps.containsKey(key)) {
                        return Boolean.TRUE;
                    }else{
                        continue;
                    }
                }else {
                    continue;
                }
            }

        }
        return null;
    }




    /***
     * 处理商品
     * 库存,特价,赠品商品
     * @param items
     * @param storeId
     * @param commodityId 商品Id属性字段名称
     * @return
     */
    public List<XdaH5TemplateTabCommodityODTO> setProcessStock(List<XdaH5TemplateTabCommodityODTO> items, Long storeId, String commodityId, String isSetPicUrl,String isSetCommodityPrice,String orderTime) {
        if (items == null || items.isEmpty()) {
            return new ArrayList<XdaH5TemplateTabCommodityODTO>();
        }
        XdaAddAllEsDataAndH5Commodity xdAddAllEsDataAndH5Commodity = this.filterCommodityByShopId(items, storeId, commodityId, isSetPicUrl, isSetCommodityPrice,true,orderTime);
        items = xdAddAllEsDataAndH5Commodity.getCommodityItem();

        // 获取商品促销、特价信息
        Map<Long, CommodityODTO> commodityPromotionMap = xdAddAllEsDataAndH5Commodity.getCommodityPromotionMap();
       /* *//*** 获取特价数据 *//*
        Map<Long, StorePromotionCommodityPriceODTO> promotionToMap = xdAddAllEsDataAndH5Commodity.getPromotionToMap();
        *//*** 获取促销数据 *//*
        Map<Integer, Map<String, XdaGiftProductODTO>> giftToMap = xdAddAllEsDataAndH5Commodity.getGiftToMap();*/

        XdaStoreSettODTO xdaStoreSettODTO=xdAddAllEsDataAndH5Commodity.getStoreSettODTO();


        /*** 获取库存信息并处理需要的商品值 */
        if (items != null && items.size() > 0) {
            items.forEach(c -> {
                Long id = c.getCommodityIdLong();

                CommodityODTO commodityODTO = commodityPromotionMap.get(id);
                if(commodityODTO != null){
                    if(commodityODTO.getSpecialPrice() != null){
                        c.setSpecialPrice(commodityODTO.getSpecialPrice());
                        c.setPromation(1);
                    }else {
                        if(StringUtils.isNotBlank(commodityODTO.getPromotionTypeName())){
                            c.setPromotionInfo(commodityODTO.getPromotionTypeName());
                        }
                    }
                }
               /* *//***
                 * 处理商品特价
                 *//*
                  StorePromotionCommodityPriceODTO priceODTO = promotionToMap.get(id);
                  if(priceODTO!=null){
                      c.setSpecialPrice(priceODTO.getPrice());
                      c.setPromation(1);
                     // c.setLimitInfo("特价");
                  }

                *//***
                 * 处理商品促销
                 *//*
                Object objGift=this.getGiftProduct(giftToMap,xdaStoreSettODTO,id);
                if(objGift!=null){
                    c.setPromotionInfo("买赠");
                }*/
            });
         }else{
            return new ArrayList<XdaH5TemplateTabCommodityODTO>();
        }
        return  items;
    }


    /***
     * 获取H5新模板数据
     * @param templateId
     * @param storeId
     * @param isAppOrMini 是app 还是小程序： 后面量大走es服务有用
     * @return
     */
    public XdaH5AutoTemplateAllDataEntry getXdH5AutoTempData_7(Long templateId, Long storeId,OrderSourceTypeEnum isAppOrMini,String orderTime){
        //获取模板
        List<XdaH5TemplateTemplateCommoditysEntry>  templateCommdity= xdaH5TemplateCommodityMapper.getTemplateCommodityByTemplateId(templateId);
        XdaH5AutoTemplateAllDataEntry allDataEntry = new XdaH5AutoTemplateAllDataEntry();
        if(templateCommdity!=null && templateCommdity.size()>0) {
            allDataEntry.setH5AutoTemplateJson(templateCommdity.get(0).getTemplateContent());
            List<XdaH5AutoTemplateCommoditysEntry> commoditysEntrieList = new ArrayList<XdaH5AutoTemplateCommoditysEntry>();
            //获取(t_xda_h5_template_commodity)template_commodity_id
            List<Long> templateCommodityIds = templateCommdity.stream().map(XdaH5TemplateTemplateCommoditysEntry::getTemplateCommodityId).collect(Collectors.toList());

            //获取templateCommodityIds下所有的商品信息
            Boolean isPfsStore = storeManageClient.isPfsStore(storeId);
            List<XdaH5TemplateTabCommodityODTO> templateCommodityList = xdaH5TemplateCommodityMapper.getCommodityData(templateCommodityIds, isPfsStore);
            if (SpringUtil.isNotEmpty(templateCommodityList)) {

                XdaAddAllEsDataAndH5Commodity xdAddAllEsDataAndH5Commodity=this.filterCommodityByShopId(templateCommodityList,storeId,
                        "commodityId","defaultPicUrl","commodityPrice",true,orderTime);
                /*** 获取商品数据 */
                templateCommodityList=xdAddAllEsDataAndH5Commodity.getCommodityItem();

                // 获取商品促销、特价信息
                Map<Long, CommodityODTO> commodityPromotionMap = xdAddAllEsDataAndH5Commodity.getCommodityPromotionMap();

               /* *//*** 获取特价数据 *//*
                Map<Long, StorePromotionCommodityPriceODTO> promotionToMap = xdAddAllEsDataAndH5Commodity.getPromotionToMap();
                *//*** 获取促销数据 *//*
                Map<Integer, Map<String, XdaGiftProductODTO>> giftToMap = xdAddAllEsDataAndH5Commodity.getGiftToMap();*/

                XdaStoreSettODTO xdaStoreSettODTO=xdAddAllEsDataAndH5Commodity.getStoreSettODTO();

                /*** 获取库存信息并处理需要的商品值 */
              if(SpringUtil.isNotEmpty(templateCommodityList)){
                templateCommodityList.forEach(c ->{
                    Long id=c.getCommodityIdLong();

                    CommodityODTO commodityODTO = commodityPromotionMap.get(id);
                    if(commodityODTO != null){
                        if(commodityODTO.getSpecialPrice() != null){
                            c.setSpecialPrice(commodityODTO.getSpecialPrice());
                            c.setPromation(1);

                            Integer limitNumber = commodityODTO.getSpecialLimit();
                            String tagName;
                            if (99999 == limitNumber || 0 == limitNumber) {
                                //不限购
                                tagName = "特价";
                            } else {
                                tagName = "特价|每天限" + limitNumber + "份";
                            }
                            c.setTagName(tagName);
                        }else {
                            if(StringUtils.isNotBlank(commodityODTO.getPromotionTypeName())){
                                c.setPromotionInfo(commodityODTO.getPromotionTypeName());
                            }
                        }
                    }

                   /* *//***
                     * 处理商品特价
                     *//*
                    StorePromotionCommodityPriceODTO priceODTO = promotionToMap.get(id);
                    if(priceODTO!=null){
                        c.setSpecialPrice(priceODTO.getPrice());
                        c.setPromation(1);
                        Integer limitNumber = priceODTO.getLimitNumber();
                        String tagName;
                        if (0 == limitNumber) {
                            //不限购
                            tagName = "特价";
                        } else {
                            tagName = "特价|每天限[" + limitNumber + "]份";
                        }
                        c.setTagName(tagName);
                       // c.setLimitInfo("特价");

                    }

                    *//***
                     * 处理商品促销
                     *//*
                    Object objGift=this.getGiftProduct(giftToMap,xdaStoreSettODTO,id);
                    if(objGift!=null){
                        c.setPromotionInfo("买赠");
                    }*/
                });
              }else{
                  return allDataEntry;
              }

                Map<Long, List<XdaH5TemplateTabCommodityODTO>> mapCommodity = new HashMap<Long, List<XdaH5TemplateTabCommodityODTO>>();
                List<XdaH5TemplateTabCommodityODTO> commodityEntryItmes = null;
                for (XdaH5TemplateTabCommodityODTO commodityEntity : templateCommodityList) {
                   Long templateCommodityId=commodityEntity.getTemplateCommodityId();
                   if(mapCommodity.containsKey(templateCommodityId)){
                        List<XdaH5TemplateTabCommodityODTO>  list= mapCommodity.get(templateCommodityId);
                        list.add(commodityEntity);
                        mapCommodity.put(templateCommodityId,list);
                   }else{
                       commodityEntryItmes = new ArrayList<XdaH5TemplateTabCommodityODTO>();
                       commodityEntryItmes.add(commodityEntity);
                       mapCommodity.put(templateCommodityId,commodityEntryItmes);
                   }
                }

                XdaH5AutoTemplateCommoditysEntry commoditysEntry = new XdaH5AutoTemplateCommoditysEntry();
                List<XdaH5TemplateTabCommodityODTO> templateCommodityEntries = this.processCommodityByTemplate_7(mapCommodity,templateCommdity);

                //处理商品斤转份数
                //this.gongjinTOshijin(templateCommodityEntries);
                if(StringUtils.isNotBlank(orderTime)) {
                    /*** 处理不可订货商品 用于商品列表购物车标志置灰 **/
                    this.processNoDeliveryCommodity(templateCommodityEntries, orderTime, storeId);
                }
                commoditysEntry.setCommodityEntry(templateCommodityEntries);
                commoditysEntrieList.add(commoditysEntry);
                allDataEntry.setCommodity(commoditysEntrieList);

            }else{
                log.info("is method getXdH5AutoTempData_7 未查询到该模板对应t_x5_template_commodity_list信息templateId:{}",templateId);
            }
        }else{
            log.info("is method getXdH5AutoTempData_7 未查询到该模板对应t_x5_template_commodity信息templateId:{}",templateId);
        }

        return allDataEntry;
    }


    /**
     * 获取相同模板对应批次商品列表
     * @return
     */
    private List<XdaH5TemplateTabCommodityODTO> processCommodityByTemplate_7(Map<Long, List<XdaH5TemplateTabCommodityODTO>> mapCommodity, List<XdaH5TemplateTemplateCommoditysEntry>  templateCommdity){
        List<XdaH5TemplateTabCommodityODTO> tabCommodityEntrys = new ArrayList<XdaH5TemplateTabCommodityODTO>();
        templateCommdity.forEach(t -> {
            if(mapCommodity.containsKey(t.getTemplateCommodityId())) {
                int showCount = t.getShowCount();
                List<XdaH5TemplateTabCommodityODTO> templateCommodityEntries = mapCommodity.get(t.getTemplateCommodityId());

                int commondityCount = templateCommodityEntries.size();

                //获取到的商品数是否大于该模板的显示数量
                if (commondityCount > showCount) {
                    commondityCount = showCount;
                }

                for (int i = 0; i < commondityCount; i++) {
                    XdaH5TemplateTabCommodityODTO commodityEntry = templateCommodityEntries.get(i);
                    tabCommodityEntrys.add(commodityEntry);
                }
            }
        });
        return tabCommodityEntrys;
    }

    /****
     *
     * @param vo
     * @param isCheck  true:新建模板时预览,false:保存检验模板  /checkXdH5Template
     */
    public void checkVo(XdaH5TemplateSaveIDTO vo,boolean isCheck){
        QYAssert.isTrue(!StringUtil.isBlank(vo.getTemplateName()), " H5名称不能为空");
        if(!isCheck){
            QYAssert.isTrue(vo.getTemplateName().length() >= 3, "H5名称不能小于15个字!");
            QYAssert.isTrue(vo.getTemplateName().length() <= 15, "H5名称不能超过15个字!");
            //判断 H5名称不能为空
            Example example = new Example(XdaH5Template.class);
            example.createCriteria().andEqualTo("templateName",vo.getTemplateName());
            List<XdaH5Template> h5Templates = xdaH5TemplateMapper.selectByExample(example);
            QYAssert.isTrue(SpringUtil.isEmpty(h5Templates)," H5名称不能重复");
        }
        QYAssert.notNull(vo.getTemplateCodeId()," 模板不能为空!");

        //头图区
        if(vo.getTemplateCodeId()!= 6){
            QYAssert.notNull(vo.getH5PicHead()," 头图区: 图片不能为空");
            QYAssert.isTrue(StringUtils.isNotEmpty(vo.getH5PicHead().getPicUrl()), " 头图区: 图片不能为空");
        }

        //四个模板 四种情况
        /**模板-1**/
        if(vo.getTemplateCodeId() == 1){

            //模块区
            QYAssert.isTrue(SpringUtil.isNotEmpty(vo.getH5PicCommodityList())," 模块区: 图片和商品不能为空");
            for(XdaH5PicCommodity h5PicCommodity : vo.getH5PicCommodityList()){
                QYAssert.isTrue(!StringUtil.isBlank(h5PicCommodity.getPicUrl()), " 模块区: 缺少图片");
                QYAssert.notNull(h5PicCommodity.getCommodityId()," 模块区: 缺少商品");
            }

            //Tab区
            QYAssert.isTrue(SpringUtil.isNotEmpty(vo.getH5TemplateTabList())," Tab区: 请添加Tab");
            checkTab(vo.getH5TemplateTabList());

        }else if(vo.getTemplateCodeId() == 2){  /**模板-2**/
            //通栏区
            QYAssert.notNull(vo.getH5PicBanner()," 通栏区: 图片1不能为空");
            //Assert.isTrue(!StringUtil.isBlank(vo.getH5PicBanner().getPicName()), " 通栏区: 图片1不能为空");
            QYAssert.isTrue(!StringUtil.isBlank(vo.getH5PicBanner().getPicUrl()), " 通栏区: 图片1不能为空");

            QYAssert.notNull(vo.getH5PicH5()," 通栏区: 图片2和H5不能为空");
            //Assert.isTrue(!StringUtil.isBlank(vo.getH5PicH5().getPicName()), " 通栏区: 图片2不能为空");
            QYAssert.isTrue(!StringUtil.isBlank(vo.getH5PicH5().getPicUrl()), " 通栏区: 图片2不能为空");
            QYAssert.notNull(vo.getH5PicH5().getTemplateId(), " 通栏区: 图片2不能为空");

            //3 单独4个商品位
            QYAssert.isTrue(SpringUtil.isNotEmpty(vo.getH5TemplateTabCommodityList())," 模块区: 请添加商品");
            QYAssert.isTrue(vo.getH5TemplateTabCommodityList().size()>=4," 模块区: 请添加至少4个商品");

            //Tab区
            QYAssert.isTrue(SpringUtil.isNotEmpty(vo.getH5TemplateTabList())," Tab区: 请添加Tab");
            checkTab(vo.getH5TemplateTabList());

        }else if(vo.getTemplateCodeId() == 3){ /**模板-3**/
            //模块区
            QYAssert.isTrue(SpringUtil.isNotEmpty(vo.getH5PicCommodityList())," 模块区: 图片和商品不能为空");
            QYAssert.isTrue(vo.getH5PicCommodityList().size()== 1," 模块区: 缺少图片和商品");
            for(XdaH5PicCommodity h5PicCommodity : vo.getH5PicCommodityList()){
                //Assert.isTrue(!StringUtil.isBlank(h5PicCommodity.getPicName()), " 模块区: 缺少图片");
                QYAssert.isTrue(!StringUtil.isBlank(h5PicCommodity.getPicUrl()), " 模块区: 缺少图片");
                QYAssert.notNull(h5PicCommodity.getCommodityId()," 模块区: 缺少商品");
            }

            //通栏区
            QYAssert.isTrue(SpringUtil.isNotEmpty(vo.getH5TemplateTabList())," 通栏区: 请添加通栏");
            for(XdaH5TemplateTabIDTO h5TemplateTabVo : vo.getH5TemplateTabList()){
                QYAssert.isTrue(!StringUtil.isBlank(h5TemplateTabVo.getPicUrl()), " 通栏区: 通栏图片不能为空");
                QYAssert.notNull(h5TemplateTabVo.getAppShowNum()," 通栏区: 前台显示几个商品位不能为空");
                QYAssert.isTrue(h5TemplateTabVo.getAppShowNum()>0, " 通栏区: 前台显示几个商品位不能小于0");
                QYAssert.isTrue(h5TemplateTabVo.getAppShowNum()%2 ==0 , " 通栏区: 前台显示几个商品位只能是偶数");

                QYAssert.isTrue(SpringUtil.isNotEmpty(h5TemplateTabVo.getCommodityCodeList())," 通栏区: 商品数不能为空");
                QYAssert.isTrue(h5TemplateTabVo.getCommodityCodeList().size()>= h5TemplateTabVo.getAppShowNum()," 通栏区: 商品数量不能小于前台显示商品位");
            }
        }else if(vo.getTemplateCodeId() == 4){/**模板-4**/
            //商品区
            QYAssert.isTrue(SpringUtil.isNotEmpty(vo.getH5TemplateTabCommodityList())," 模块区: 请添加商品");
        }else if(vo.getTemplateCodeId() == 6){
            //url地址
            QYAssert.isTrue(null != vo.getH5TemplateUrl(),"缺少URL地址");
        }
    }

    /**
     * 验证Tab区
     * @param list
     */
    private void checkTab(List<XdaH5TemplateTabIDTO> list) {
        for(XdaH5TemplateTabIDTO h5TemplateTabVo :list){
            QYAssert.isTrue(StringUtils.isNotEmpty(h5TemplateTabVo.getTabName()), " Tab区: Tab标签名称不能为空");
            QYAssert.notNull(h5TemplateTabVo.getAppShowNum()," Tab区: 前台显示几个商品位不能为空");
            QYAssert.isTrue(h5TemplateTabVo.getAppShowNum()>0, " Tab区: 前台显示几个商品位不能小于0");
            QYAssert.isTrue(h5TemplateTabVo.getAppShowNum()%2 ==0 , " Tab区: 前台显示几个商品位只能是偶数");

            QYAssert.isTrue(SpringUtil.isNotEmpty(h5TemplateTabVo.getCommodityCodeList())," Tab区: 商品数不能为空");
            QYAssert.isTrue(h5TemplateTabVo.getCommodityCodeList().size()>= h5TemplateTabVo.getAppShowNum()," Tab区: 商品数量不能小于前台显示商品位");
        }
    }


    /***
     * 模板后台预览时选择客户
     * 获取B端客户(外部)客户
     * @return
     */
  public PageInfo<XdaStoreDataODTO> selectStoreDataByCodeOrName(XdaStoreDataIDTO idto){
      PageInfo<XdaStoreDataODTO> pageInfo= PageHelper.startPage(idto.getPageNo(),idto.getPageSize()).doSelectPageInfo(()->{
          xdaH5TemplateCommodityMapper.selectStoreDataByCodeOrName(idto);
      });
      return pageInfo;
  }

    /***
     * 获取不可订货的商品用于购物车标签置灰
     * @param orderTime
     * @param storeId
     * @param commodityIds
     * @return
     */
  public Map<Long, XdaCommodityDeliveryTimeODTO> queryXdaCommodityDeliveryTime(String orderTime,Long storeId,List<Long> commodityIds){
      Date date=DateUtil.parseDate(orderTime,"yyyy-MM-dd");
      XdaCommodityDeliveryTimeIDTO idto=new XdaCommodityDeliveryTimeIDTO();
      idto.setOrderTime(date);
      idto.setStoreId(storeId);
      idto.setCommodityIdList(commodityIds);
      Map<Long, XdaCommodityDeliveryTimeODTO> map=xdaCommodityFrontClient.queryXdaCommodityDeliveryTime(idto);
      if(map==null || map.isEmpty()){
          return new HashMap<Long, XdaCommodityDeliveryTimeODTO>();
      }
      return map;
  }

    /***
     * 处理不可订货商品
     * @param h5TemplateTabCommodityList
     * @return
     */
  public void  processNoDeliveryCommodity(List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList,String orderTime,Long storeId){
      if(h5TemplateTabCommodityList!=null && h5TemplateTabCommodityList.size()>0) {
          List<Long> ids = h5TemplateTabCommodityList.stream().map(XdaH5TemplateTabCommodityODTO::getCommodityIdLong).collect(Collectors.toList());
          Map<Long, XdaCommodityDeliveryTimeODTO> map = this.queryXdaCommodityDeliveryTime(orderTime, storeId, ids);
          h5TemplateTabCommodityList.forEach(c -> {
              Long commodityId = c.getCommodityIdLong();
              if (map.containsKey(commodityId)) {
                  XdaCommodityDeliveryTimeODTO odto = map.get(commodityId);
                  if (odto != null) {
                      if (!odto.getIsCanOrder()) {
                          c.setIcons(0);
                      }
                  }
              }
          });
          h5TemplateTabCommodityList.sort(Comparator.comparing(XdaH5TemplateTabCommodityODTO::getIcons,Comparator.reverseOrder()));
      }
  }


  private Map<Long, CommodityODTO> selectCommodityPromotionToMap(List<Long> commodityIdList,Long storeId,String orderTime) {
      if (SpringUtil.isEmpty(commodityIdList)) {
          return new HashMap<>();
      }
      orderTime = StringUtils.isNotBlank(orderTime) ? orderTime : LocalDate.now().toString();
      /*StorePromotionCommodityPriceSearchIDTO idto = new StorePromotionCommodityPriceSearchIDTO();
      idto.setStoreId(storeId);
      idto.setOrderTime(DateUtil.parseDate(orderTime, "yyyy-MM-dd"));
      Map<Long, StorePromotionCommodityPriceODTO> resultMap = new HashMap<>();
      List<Long> idList = new ArrayList<>();
      commodityIdList.forEach(c -> {
          if (idList.size() == 2000) {
              idto.setCommodityIdList(idList);
              Map<Long, StorePromotionCommodityPriceODTO> map = storePromotionClient.getStorePromotionCommodityMapByParams(idto);
              if (SpringUtil.isNotEmpty(map)) {
                  resultMap.putAll(map);
              }
              idList.clear();
          }
          idList.add(c);
      });

      if (SpringUtil.isNotEmpty(idList)) {
          idto.setCommodityIdList(idList);
          Map<Long, StorePromotionCommodityPriceODTO> map = storePromotionClient.getStorePromotionCommodityMapByParams(idto);
          if (SpringUtil.isNotEmpty(map)) {
              resultMap.putAll(map);
          }
          idList.clear();
      }*/

      // 获取特价和促销
      List<XdaH5TemplateTabCommodityODTO> commodityList = xdaH5TemplateTabCommodityMapper.findCommodityListByCommodityIds(commodityIdList);
      List<com.pinshang.qingyun.marketing.dto.app.CommodityCategoryIDTO> commodityCategoryIDTOList = new ArrayList<>();
      for(XdaH5TemplateTabCommodityODTO comm : commodityList){
          com.pinshang.qingyun.marketing.dto.app.CommodityCategoryIDTO cci = new com.pinshang.qingyun.marketing.dto.app.CommodityCategoryIDTO();
          cci.setCommodityId(comm.getCommodityIdLong());
          cci.setCategoryId(comm.getSecondCategoryId());
          cci.setIsWeight(comm.getIsWeight());
          commodityCategoryIDTOList.add(cci);
      }

      CommodityPromotionIDTO cpIDTO = new CommodityPromotionIDTO();
      cpIDTO.setChannelType(MarketSourceTypeEnum.XDA.getCode());
      cpIDTO.setShopId(storeId);
      cpIDTO.setOrderTime(DateUtil.parseDate(orderTime, "yyyy-MM-dd"));
      cpIDTO.setCommodityCategoryIDTOList(commodityCategoryIDTOList);
      cpIDTO.setNeedAvailableLimit(YesOrNoEnums.NO.getCode());
      Map<Long, CommodityODTO> commodityPromotionMap = mtPromotionClient.queryCommodityPromotion(cpIDTO);
      return commodityPromotionMap;
  }
}

