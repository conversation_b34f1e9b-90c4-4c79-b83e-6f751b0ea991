package com.pinshang.qingyun.xda.cms.service.h5;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.xd.XdH5OperateTypeEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.xda.cms.dto.h5.*;
import com.pinshang.qingyun.xda.cms.mapper.h5.*;
import com.pinshang.qingyun.xda.cms.model.h5.*;
import com.pinshang.qingyun.xda.cms.utils.FilePortUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestParam;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 鲜达H5模板
 * @author: hhf
 * @time: 2020/12/10 15:27
 */
@Slf4j
@Service
@Transactional
public class XdaH5TemplateService {

    @Autowired
    private XdaH5TemplateMapper xdaH5TemplateMapper;
    @Autowired
    private XdaH5TemplateLogMapper xdaH5TemplateLogMapper;
    @Autowired
    private XdaH5TemplateStyleCodeMapper xdaH5TemplateStyleCodeMapper;
    @Autowired
    private XdaH5PicH5Mapper xdaH5PicH5Mapper;
    @Autowired
    private XdaH5PicMapper xdaH5PicMapper;
    @Autowired
    private XdaH5TemplateCommodityMapper xdaH5TemplateCommodityMapper;
    @Autowired
    private XdaH5TemplateListMapper xdaH5TemplateListMapper;
    @Autowired
    private XdaH5TemplateTabCommodityMapper xdaH5TemplateTabCommodityMapper;
    @Autowired
    private XdaH5TemplateUrlMapper xdaH5TemplateUrlMapper;
    @Autowired
    private XdaH5PicCommodityMapper xdaH5PicCommodityMapper;
    @Autowired
    private XdaH5TemplateTabMapper xdaH5TemplateTabMapper;
    @Autowired
    private XdaH5TemplateCommodityListMapper xdaH5TemplateCommodityListMapper;
    @Autowired
    private XdaH5TemplateCouponPublishRuleMapper xdaH5TemplateCouponPublishRuleMapper;
    @Autowired
    private CodeClient codeClient;

    @Value("${pinshang.img-server-url}")
    private String imgServerUrl;


    /**
     * 鲜达-H5模板列表
     * @param idto
     * @return
     */
    public PageInfo<XdaH5TemplateODTO> findXdaH5TemplateListByParams(XdaH5TemplateIDTO idto){
        PageInfo<XdaH5TemplateODTO> pageData = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            xdaH5TemplateMapper.findXdaH5TemplateListByParams(idto);
        });
        return pageData;
    }

    /**
     * 鲜达-H5模板日志列表
     * @param idto
     * @return
     */
    public PageInfo<XdaH5TemplateLogODTO> findXdaH5TemplateLogListByParams(XdaH5TemplateLogIDTO idto){
        PageInfo<XdaH5TemplateLogODTO> pageData = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
            xdaH5TemplateLogMapper.findXdaH5TemplateLogListByParams(idto);
        });
        return pageData;
    }

    /**
     * 鲜达-H5模板列表 -导出
     * @param idto
     * @param response
     * @param request
     */
    public void exportXdaH5TemplateListByParams(XdaH5TemplateIDTO idto, HttpServletResponse response, HttpServletRequest request){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        List<XdaH5TemplateODTO> list = this.findXdaH5TemplateListByParams(idto).getList();

        //导出的表名
        String title = "鲜达H5模板列表" + System.currentTimeMillis();
        //表中第一行表头字段
        String[] headers = {"H5编码", "H5名称", "H5类型", "状态"};

        //具体需要写入excel需要哪些字段
        List<String> listColumn = Arrays.asList("templateCode", "templateName", "templateTypeName", "statusName");
        try {
            FilePortUtil.exportExcel(request, response, title, headers, list, listColumn);
        } catch (Exception e) {
            log.error("鲜达H5模板列表--导出异常：{}，异常详情：{}", e.getMessage(), e);
        }
    }


    /**
     * 鲜达-H5模板日志列表 -导出
     * @param idto
     * @param response
     * @param request
     */
    public void exportXdaH5TemplateLogListByParams(XdaH5TemplateLogIDTO idto, HttpServletResponse response, HttpServletRequest request){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        List<XdaH5TemplateLogODTO> list = this.findXdaH5TemplateLogListByParams(idto).getList();

        //导出的表名
        String title = "鲜达H5模板日志列表" + System.currentTimeMillis();
        //表中第一行表头字段
        String[] headers = {"H5编码", "H5名称", "H5类型", "操作类型","操作人","操作时间"};

        //具体需要写入excel需要哪些字段
        List<String> listColumn = Arrays.asList("templateCode", "templateName", "templateTypeName", "operateTypeName","createName","createTimeStr");
        try {
            FilePortUtil.exportExcel(request, response, title, headers, list, listColumn);
        } catch (Exception e) {
            log.error("鲜达H5模板日志列表--导出异常：{}，异常详情：{}", e.getMessage(), e);
        }
    }

    /**
     * 鲜达-选择H5模板-查询所有模板列表
     * @return
     */
    public List<XdaH5TemplateStyleCodeODTO> findXdaH5TemplateStyleCodeList(){
        return xdaH5TemplateStyleCodeMapper.findXdaH5TemplateStyleCodeList();
    }

    /**
     * 鲜达-h5模板状态修改-0停用/1启用
     * @param editIDTO
     * @return
     */
    public Long updateXdaH5TemplateStatus(XdaH5TemplateStatusEditIDTO editIDTO){
        QYAssert.isTrue(null != editIDTO,"参数异常");
        QYAssert.isTrue(null != editIDTO.getId()," id参数异常");
        QYAssert.isTrue(null != editIDTO.getStatus()," 状态参数异常");

        Integer status = editIDTO.getStatus();
        Date date = new Date();
        XdaH5Template xdaH5Template = XdaH5Template.forUpdateStatus(editIDTO.getId(), status, editIDTO.getUserId(),date);
        xdaH5TemplateMapper.updateByPrimaryKeySelective(xdaH5Template);

        //日志:0-停用,1-启用
        if(status.intValue() == 0){
            XdaH5TemplateLog xdaH5TemplateLog = new XdaH5TemplateLog(editIDTO.getId(), XdH5OperateTypeEnums.DISABLE.getCode(),date,editIDTO.getUserId());
            xdaH5TemplateLogMapper.insert(xdaH5TemplateLog);
        }else if(status.intValue() == 1){
            XdaH5TemplateLog xdaH5TemplateLog = new XdaH5TemplateLog(editIDTO.getId(), XdH5OperateTypeEnums.ENABLE.getCode(),date,editIDTO.getUserId());
            xdaH5TemplateLogMapper.insert(xdaH5TemplateLog);
        }
        return editIDTO.getId();
    }

    /**
     * 鲜达-保存H5大模板
     * @param saveIDTO
     * @return
     */
    public Long saveXdaH5Template( XdaH5TemplateSaveIDTO saveIDTO){
        checkVo(saveIDTO);
        //创建编码
        String code =codeClient.createCode("XDA_H5_TEMPLATE_CODE");
        QYAssert.notNull(code, "H5模板编码构建异常!");
        Date date = new Date();

        //1-保存主表
        XdaH5Template xdaH5Template = XdaH5Template.forInsertXdH5Template(code, saveIDTO.getTemplateName(), saveIDTO.getTemplateCodeId(),null, saveIDTO.getUserId(), date);
        xdaH5TemplateMapper.insert(xdaH5Template);
        //模板主表id
        Long h5TemplateId = xdaH5Template.getId();

        //2-头图区
        if(saveIDTO.getTemplateCodeId() != 6){
            XdaH5Pic h5PicHead = saveIDTO.getH5PicHead();
            h5PicHead.setId(null);
            xdaH5PicMapper.insert(h5PicHead);
            Long h5PicHeadId = h5PicHead.getId();
            //头图1中间表( 1:头图区 1:元素1-1 10:H5图片)
            XdaH5TemplateList list = new XdaH5TemplateList(h5TemplateId,1,1,10,h5PicHeadId);
            xdaH5TemplateListMapper.insert(list);
        }

        /**根据模板1,2,3,4判断保存对象**/
        if(saveIDTO.getTemplateCodeId() == 1){
            //模块区
            List<XdaH5PicCommodity> h5PicCommodityList = saveIDTO.getH5PicCommodityList();
            //具体位置
            int j = 2;
            for(XdaH5PicCommodity h5PicCommodity : h5PicCommodityList){
                h5PicCommodity.setId(null);
                xdaH5PicCommodityMapper.insert(h5PicCommodity);
                Long h5PicCommodityId = h5PicCommodity.getId();
                //模块2,3中间表
                XdaH5TemplateList h5TemplateList = new XdaH5TemplateList(h5TemplateId,2,j,12,h5PicCommodityId);
                xdaH5TemplateListMapper.insert(h5TemplateList);
                j++;
            }

            //Tab区
            List<XdaH5TemplateTabIDTO> h5TemplateTabList = saveIDTO.getH5TemplateTabList();
            for(XdaH5TemplateTabIDTO tabIDTO : h5TemplateTabList){
                Long h5TemplateTabId = getTabId(tabIDTO);
                //Tab4中间表
                XdaH5TemplateList h5TemplateList = new XdaH5TemplateList(h5TemplateId,2,4,20,h5TemplateTabId);
                xdaH5TemplateListMapper.insert(h5TemplateList);
            }
        }else if(saveIDTO.getTemplateCodeId() == 2){
            //通栏区2-1
            XdaH5Pic h5PicBanner = saveIDTO.getH5PicBanner();
            h5PicBanner.setId(null);
            xdaH5PicMapper.insert(h5PicBanner);
            Long h5PicBannerId = h5PicBanner.getId();

            //2-1中间表
            XdaH5TemplateList h5TemplateList = new XdaH5TemplateList(h5TemplateId,2,2,10,h5PicBannerId);
            xdaH5TemplateListMapper.insert(h5TemplateList);

            //2-2 图片+h5 //URL,名称,模板id
            XdaH5PicH5 h5PicH5 = saveIDTO.getH5PicH5();
            h5PicH5.setId(null);
            xdaH5PicH5Mapper.insert(h5PicH5);
            Long h5PicH5Id = h5PicH5.getId();

            //2-2 中间表
            XdaH5TemplateList h5TemplateList2 = new XdaH5TemplateList(h5TemplateId,2,3,11,h5PicH5Id);
            xdaH5TemplateListMapper.insert(h5TemplateList2);

            //2-3 4个商品位
            List<XdaH5TemplateTabCommodity> h5TemplateTabCommodityList = saveIDTO.getH5TemplateTabCommodityList();
            XdaH5TemplateTab h5TemplateTab = new XdaH5TemplateTab(null,0,3,null,null);
            xdaH5TemplateTabMapper.insert(h5TemplateTab);
            Long h5TemplateTabId = h5TemplateTab.getId();

            h5TemplateTabCommodityList.forEach(item -> {
                item.setId(null);
                item.setTabId(h5TemplateTabId);
            });

            xdaH5TemplateTabCommodityMapper.insertList(h5TemplateTabCommodityList);

            //2-3 中间表
            XdaH5TemplateList h5TemplateList3 = new XdaH5TemplateList(h5TemplateId,2,4,22,h5TemplateTabId);
            xdaH5TemplateListMapper.insert(h5TemplateList3);

            //Tab区
            List<XdaH5TemplateTabIDTO> h5TemplateTabList = saveIDTO.getH5TemplateTabList();
            for(XdaH5TemplateTabIDTO tabIDTO : h5TemplateTabList){
                Long tabId = getTabId(tabIDTO);
                //Tab4中间表
                XdaH5TemplateList h5TemplateList4 = new XdaH5TemplateList(h5TemplateId,2,5,20,tabId);
                xdaH5TemplateListMapper.insert(h5TemplateList4);
            }
        }else if (saveIDTO.getTemplateCodeId() == 3){
            //2-1 图片加商品
            List<XdaH5PicCommodity> h5PicCommodityList = saveIDTO.getH5PicCommodityList();
            for(XdaH5PicCommodity h5PicCommodity : h5PicCommodityList){
                h5PicCommodity.setId(null);
                xdaH5PicCommodityMapper.insert(h5PicCommodity);
                Long h5PicCommodityId = h5PicCommodity.getId();
                //2-1 中间表
                XdaH5TemplateList h5TemplateList = new XdaH5TemplateList(h5TemplateId,2,2,12,h5PicCommodityId);
                xdaH5TemplateListMapper.insert(h5TemplateList);
            }

            //通栏区
            List<XdaH5TemplateTabIDTO> h5TemplateTabList = saveIDTO.getH5TemplateTabList();
            for(XdaH5TemplateTabIDTO tabIDTO : h5TemplateTabList){
                Long tabId = getTabId(tabIDTO);
                //Tab4中间表
                XdaH5TemplateList h5TemplateList4 = new XdaH5TemplateList(h5TemplateId,2,3,21,tabId);
                xdaH5TemplateListMapper.insert(h5TemplateList4);
            }
        }else if (saveIDTO.getTemplateCodeId() == 4){

            //商品区
            List<XdaH5TemplateTabCommodity> h5TemplateTabCommodityList = saveIDTO.getH5TemplateTabCommodityList();
            XdaH5TemplateTab h5TemplateTab = new XdaH5TemplateTab(null,0,3,null,null);
            h5TemplateTab.setAppShowNum(0);
            h5TemplateTab.setTabType(3);
            xdaH5TemplateTabMapper.insert(h5TemplateTab);
            Long h5TemplateTabId = h5TemplateTab.getId();

            h5TemplateTabCommodityList.forEach(item -> {
                item.setTabId(h5TemplateTabId);
                item.setId(null);
            });
            xdaH5TemplateTabCommodityMapper.insertList(h5TemplateTabCommodityList);

            //2-3 中间表
            XdaH5TemplateList h5TemplateList3 = new XdaH5TemplateList(h5TemplateId,2,2,22,h5TemplateTabId);
            xdaH5TemplateListMapper.insert(h5TemplateList3);
        }else if(saveIDTO.getTemplateCodeId() == 6){
            XdaH5TemplateUrl h5TemplateUrl = saveIDTO.getH5TemplateUrl();
            h5TemplateUrl.setId(null);
            h5TemplateUrl.setTemplateId(h5TemplateId);
            xdaH5TemplateUrlMapper.insert(h5TemplateUrl);
        }
        saveXdaH5Log(date, h5TemplateId, saveIDTO.getOperateType(), saveIDTO.getUserId());
        return h5TemplateId;
    }


    /**
     * 验证必输项
     * @param saveIDTO
     */
    private void checkVo(XdaH5TemplateSaveIDTO saveIDTO){
        QYAssert.isTrue(!StringUtil.isBlank(saveIDTO.getTemplateName()), " H5名称不能为空");
        QYAssert.isTrue(saveIDTO.getTemplateName().length() >= 3, "H5名称不能小于15个字!");
        QYAssert.isTrue(saveIDTO.getTemplateName().length() <= 15, "H5名称不能超过15个字!");

        //判断 H5名称不能为空
        Example example = new Example(XdaH5Template.class);
        example.createCriteria().andEqualTo("templateName",saveIDTO.getTemplateName());
        List<XdaH5Template> h5Templates = xdaH5TemplateMapper.selectByExample(example);
        QYAssert.isTrue(SpringUtil.isEmpty(h5Templates)," H5名称不能重复");

        QYAssert.notNull(saveIDTO.getTemplateCodeId()," 模板不能为空!");

        //头图区
        if(saveIDTO.getTemplateCodeId()!= 6){
            QYAssert.notNull(saveIDTO.getH5PicHead()," 头图区: 图片不能为空");
            QYAssert.isTrue(StringUtils.isNotEmpty(saveIDTO.getH5PicHead().getPicUrl()), " 头图区: 图片不能为空");
        }

        //四个模板 四种情况
        /**模板-1**/
        if(saveIDTO.getTemplateCodeId() == 1){

            //模块区
            QYAssert.isTrue(SpringUtil.isNotEmpty(saveIDTO.getH5PicCommodityList())," 模块区: 图片和商品不能为空");
            for(XdaH5PicCommodity h5PicCommodity : saveIDTO.getH5PicCommodityList()){
                QYAssert.isTrue(!StringUtil.isBlank(h5PicCommodity.getPicUrl()), " 模块区: 缺少图片");
                QYAssert.notNull(h5PicCommodity.getCommodityId()," 模块区: 缺少商品");
            }

            //Tab区
            QYAssert.isTrue(SpringUtil.isNotEmpty(saveIDTO.getH5TemplateTabList())," Tab区: 请添加Tab");
            checkTab(saveIDTO.getH5TemplateTabList());

        }else if(saveIDTO.getTemplateCodeId() == 2){  /**模板-2**/
            //通栏区
            QYAssert.notNull(saveIDTO.getH5PicBanner()," 通栏区: 图片1不能为空");
            QYAssert.isTrue(!StringUtil.isBlank(saveIDTO.getH5PicBanner().getPicUrl()), " 通栏区: 图片1不能为空");

            QYAssert.notNull(saveIDTO.getH5PicH5()," 通栏区: 图片2和H5不能为空");
            QYAssert.isTrue(!StringUtil.isBlank(saveIDTO.getH5PicH5().getPicUrl()), " 通栏区: 图片2不能为空");
            QYAssert.notNull(saveIDTO.getH5PicH5().getTemplateId(), " 通栏区: H5模板不能为空");

            //3 单独4个商品位
            QYAssert.isTrue(SpringUtil.isNotEmpty(saveIDTO.getH5TemplateTabCommodityList())," 模块区: 请添加商品");
            QYAssert.isTrue(saveIDTO.getH5TemplateTabCommodityList().size()>=4," 模块区: 请添加至少4个商品");

            //Tab区
            QYAssert.isTrue(SpringUtil.isNotEmpty(saveIDTO.getH5TemplateTabList())," Tab区: 请添加Tab");
            checkTab(saveIDTO.getH5TemplateTabList());

        }else if(saveIDTO.getTemplateCodeId() == 3){ /**模板-3**/
            //模块区
            QYAssert.isTrue(SpringUtil.isNotEmpty(saveIDTO.getH5PicCommodityList())," 模块区: 图片和商品不能为空");
            QYAssert.isTrue(saveIDTO.getH5PicCommodityList().size()== 1," 模块区: 缺少图片和商品");
            for(XdaH5PicCommodity h5PicCommodity : saveIDTO.getH5PicCommodityList()){
                QYAssert.isTrue(!StringUtil.isBlank(h5PicCommodity.getPicUrl()), " 模块区: 缺少图片");
                QYAssert.notNull(h5PicCommodity.getCommodityId()," 模块区: 缺少商品");
            }

            //通栏区
            QYAssert.isTrue(SpringUtil.isNotEmpty(saveIDTO.getH5TemplateTabList())," 通栏区: 请添加通栏");
            for(XdaH5TemplateTabIDTO tabIDTO: saveIDTO.getH5TemplateTabList()){
                QYAssert.isTrue(!StringUtil.isBlank(tabIDTO.getPicUrl()), " 通栏区: 通栏图片不能为空");
                QYAssert.notNull(tabIDTO.getAppShowNum()," 通栏区: 前台显示几个商品位不能为空");
                QYAssert.isTrue(tabIDTO.getAppShowNum()>0, " 通栏区: 前台显示几个商品位不能小于0");
                QYAssert.isTrue(tabIDTO.getAppShowNum()%2 ==0 , " 通栏区: 前台显示几个商品位只能是偶数");

                QYAssert.isTrue(SpringUtil.isNotEmpty(tabIDTO.getCommodityCodeList())," 通栏区: 商品数不能为空");
                QYAssert.isTrue(tabIDTO.getCommodityCodeList().size()>= tabIDTO.getAppShowNum()," 通栏区: 商品数量不能小于前台显示商品位");
            }
        }else if(saveIDTO.getTemplateCodeId() == 4){/**模板-4**/
            //商品区
            QYAssert.isTrue(SpringUtil.isNotEmpty(saveIDTO.getH5TemplateTabCommodityList())," 模块区: 请添加商品");
        }else if(saveIDTO.getTemplateCodeId() == 6){
            //url地址
            QYAssert.isTrue(null != saveIDTO.getH5TemplateUrl(),"缺少URL地址");
        }
    }

    /**
     * 验证Tab区
     * @param list
     */
    private void checkTab(List<XdaH5TemplateTabIDTO> list) {
        for(XdaH5TemplateTabIDTO tabIDTO :list){
            QYAssert.isTrue(StringUtils.isNotEmpty(tabIDTO.getTabName()), " Tab区: Tab标签名称不能为空");
            QYAssert.notNull(tabIDTO.getAppShowNum()," Tab区: 前台显示几个商品位不能为空");
            QYAssert.isTrue(tabIDTO.getAppShowNum()>0, " Tab区: 前台显示几个商品位不能小于0");
            QYAssert.isTrue(tabIDTO.getAppShowNum()%2 ==0 , " Tab区: 前台显示几个商品位只能是偶数");

            QYAssert.isTrue(SpringUtil.isNotEmpty(tabIDTO.getCommodityCodeList())," Tab区: 商品数不能为空");
            QYAssert.isTrue(tabIDTO.getCommodityCodeList().size()>= tabIDTO.getAppShowNum()," Tab区: 商品数量不能小于前台显示商品位");
        }
    }

    /**
     * 保存tab列表以及商品明细列表,得到Tab主键id 用于保存关联表
     * @param tabIDTO
     * @return
     */
    private Long getTabId(XdaH5TemplateTabIDTO tabIDTO) {
        XdaH5TemplateTab h5TemplateTab = new XdaH5TemplateTab(tabIDTO.getTabName(),tabIDTO.getAppShowNum(),tabIDTO.getTabType(),tabIDTO.getPicUrl(),tabIDTO.getCommodityCodeList().size());
        xdaH5TemplateTabMapper.insert(h5TemplateTab);
        Long h5TemplateTabId = h5TemplateTab.getId();

        //商品编码查询商品id 集合
        List<XdaH5TemplateTabCommodity> xdH5TemplateTabCommodityList = xdaH5TemplateTabCommodityMapper.findCommodityIdByCommodityCodeList(tabIDTO.getCommodityCodeList());
        if(SpringUtil.isNotEmpty(xdH5TemplateTabCommodityList)){
            xdH5TemplateTabCommodityList.forEach(item -> item.setTabId(h5TemplateTabId));
            xdaH5TemplateTabCommodityMapper.insertList(xdH5TemplateTabCommodityList);
        }

        return h5TemplateTabId;
    }

    /**
     * 保存日志
     * @param date
     * @param h5TemplateId
     * @param operateType
     * @param userId
     */
    private void saveXdaH5Log(Date date, Long h5TemplateId, Integer operateType, Long userId) {
        XdaH5TemplateLog xdaH5TemplateLog = new XdaH5TemplateLog(h5TemplateId, operateType, date, userId);
        xdaH5TemplateLogMapper.insert(xdaH5TemplateLog);
    }

    /**
     * 鲜达-保存自定义H5
     * @param saveIDTO
     * @return
     */
    public Long saveXdaH5CustomTemplate(XdaH5CustomTemplateSaveIDTO saveIDTO){
        QYAssert.isTrue(null != saveIDTO,"参数异常");
        QYAssert.isTrue(StringUtils.isNotEmpty(saveIDTO.getTemplateName()), " H5名称不能为空");
        QYAssert.isTrue(saveIDTO.getTemplateName().length() >= 2, "H5名称须大于1个字且少于16个字!");
        QYAssert.isTrue(saveIDTO.getTemplateName().length() < 16, "H5名称须大于1个字且少于16个字!");

        //判断 H5名称不能为空
        Example example = new Example(XdaH5Template.class);
        example.createCriteria().andEqualTo("templateName",saveIDTO.getTemplateName());
        List<XdaH5Template> h5Templates = xdaH5TemplateMapper.selectByExample(example);
        QYAssert.isTrue(SpringUtil.isEmpty(h5Templates)," H5名称不能重复");

        QYAssert.isTrue(StringUtils.isNotEmpty(saveIDTO.getTemplateContent())," H5小模板详细信息异常");

        //创建编码
        String code =codeClient.createCode("XDA_H5_TEMPLATE_CODE");
        QYAssert.notNull(code, "H5模板编码构建异常!");

        Date date = new Date();

        //1-保存主表
        XdaH5Template xdH5Template = XdaH5Template.forInsertXdH5Template(code, saveIDTO.getTemplateName(), 7L,saveIDTO.getTemplateContent(), saveIDTO.getUserId(), date);
        xdaH5TemplateMapper.insert(xdH5Template);
        //模板主表id
        Long h5TemplateId = xdH5Template.getId();

        //商品集合
        List<XdaH5TemplateCommoditySaveIDTO> commodityList = saveIDTO.getCommodityList();
        saveXdaH5TemplateCommodity(h5TemplateId, commodityList);

        //优惠券发放规则集合
        List<Long> couponPublishRuleIdList = saveIDTO.getCouponPublishRuleIdList();
        saveXdaH5TemplateCouponPublishRule(h5TemplateId, couponPublishRuleIdList);

        //保存日志
        saveXdaH5Log(date, h5TemplateId, saveIDTO.getOperateType(), saveIDTO.getUserId());
        return h5TemplateId;
    }

    /**
     * 保存自定义H5模板商品
     * @param h5TemplateId
     * @param commodityList
     */
    private void saveXdaH5TemplateCommodity(Long h5TemplateId, List<XdaH5TemplateCommoditySaveIDTO> commodityList) {
        if(SpringUtil.isNotEmpty(commodityList)){
            for(XdaH5TemplateCommoditySaveIDTO commoditySaveVo: commodityList){
                List<Long> commodityIdList = commoditySaveVo.getCommodityIdList();
                Integer showCount = commoditySaveVo.getShowCount();
                //保存t_xda_h5_template_commodity
                XdaH5TemplateCommodity xdH5TemplateCommodity = new XdaH5TemplateCommodity(h5TemplateId,showCount);
                xdaH5TemplateCommodityMapper.insertSelective(xdH5TemplateCommodity);
                Long xdH5TemplateCommodityId = xdH5TemplateCommodity.getId();
                if(SpringUtil.isNotEmpty(commodityIdList)){
                    List<XdaH5TemplateCommodityList> xdH5TemplateCommodityLists = new ArrayList<>();
                    commodityIdList.forEach(id->{
                        if(null != id){
                            XdaH5TemplateCommodityList xdH5TemplateCommodityList = new XdaH5TemplateCommodityList(xdH5TemplateCommodityId,id);
                            xdH5TemplateCommodityLists.add(xdH5TemplateCommodityList);
                        }
                    });
                    xdaH5TemplateCommodityListMapper.insertList(xdH5TemplateCommodityLists);
                }
            }
        }
    }

    /**
     * 保存自定义H5 优惠券
     * @param h5TemplateId
     * @param couponPublishRuleIdList
     */
    private void saveXdaH5TemplateCouponPublishRule(Long h5TemplateId, List<Long> couponPublishRuleIdList) {
        if (SpringUtil.isNotEmpty(couponPublishRuleIdList)) {
            List<XdaH5TemplateCouponPublishRule> couponPublishRuleList = new ArrayList<>();
            couponPublishRuleIdList.forEach(id -> {
                if (null != id) {
                    XdaH5TemplateCouponPublishRule couponPublishRule = new XdaH5TemplateCouponPublishRule(h5TemplateId, id);
                    couponPublishRuleList.add(couponPublishRule);
                }
            });
            xdaH5TemplateCouponPublishRuleMapper.insertList(couponPublishRuleList);
        }
    }

    /**
     * 鲜达-修改自定义H5
     * @param editIDTO
     * @return
     */
    public Long editXdaH5CustomTemplate(XdaH5CustomTemplateEditIDTO editIDTO){
        QYAssert.isTrue(null != editIDTO,"参数异常");
        QYAssert.isTrue(null != editIDTO.getId()," id参数异常");
        QYAssert.isTrue(StringUtils.isNotEmpty(editIDTO.getTemplateName()), " H5名称不能为空");
        QYAssert.isTrue(editIDTO.getTemplateName().length() >= 2, "H5名称须大于1个字且少于16个字!");
        QYAssert.isTrue(editIDTO.getTemplateName().length() < 16, "H5名称须大于1个字且少于16个字!");


        //判断 H5名称不能重复
        Example example = new Example(XdaH5Template.class);
        example.createCriteria().andEqualTo("templateName",editIDTO.getTemplateName()).andNotEqualTo("id",editIDTO.getId());
        List<XdaH5Template> h5Templates = xdaH5TemplateMapper.selectByExample(example);
        QYAssert.isTrue(SpringUtil.isEmpty(h5Templates)," H5名称不能重复");

        Date date = new Date();
        //修改主表
        XdaH5Template xdaH5Template = XdaH5Template.forUpdate(editIDTO.getId(), editIDTO.getTemplateName(), editIDTO.getTemplateContent(), editIDTO.getUserId(), date);
        xdaH5TemplateMapper.updateByPrimaryKeySelective(xdaH5Template);

        //删除之前的商品
        deleteXdaH5TemplateCommodity(editIDTO.getId());
        //商品信息
        List<XdaH5TemplateCommoditySaveIDTO> commodityList = editIDTO.getCommodityList();
        saveXdaH5TemplateCommodity(editIDTO.getId(),commodityList);

        //删除之前的优惠券
        Example example1 = new Example(XdaH5TemplateCouponPublishRule.class);
        example1.createCriteria().andEqualTo("templateId",editIDTO.getId());
        List<XdaH5TemplateCouponPublishRule> couponPublishRules = xdaH5TemplateCouponPublishRuleMapper.selectByExample(example1);
        if(SpringUtil.isNotEmpty(couponPublishRules)){
            xdaH5TemplateCouponPublishRuleMapper.deleteByExample(example1);
        }
        //优惠券发放规则集合
        List<Long> couponPublishRuleIdList = editIDTO.getCouponPublishRuleIdList();
        saveXdaH5TemplateCouponPublishRule(editIDTO.getId(), couponPublishRuleIdList);

        //日志
        XdaH5TemplateLog xdaH5TemplateLog = new XdaH5TemplateLog(editIDTO.getId(), XdH5OperateTypeEnums.EDIT.getCode(), date, editIDTO.getUserId());
        xdaH5TemplateLogMapper.insert(xdaH5TemplateLog);

        return editIDTO.getId();
    }

    /**
     * 删除自定义H5模板商品
     * @param id
     */
    private void deleteXdaH5TemplateCommodity(Long id) {
        Example example2 = new Example(XdaH5TemplateCommodity.class);
        example2.createCriteria().andEqualTo("templateId",id);
        List<XdaH5TemplateCommodity> list = xdaH5TemplateCommodityMapper.selectByExample(example2);
        if(SpringUtil.isNotEmpty(list)){
            List<Long> idList = list.stream().mapToLong(p -> p.getId()).boxed().collect(Collectors.toList());
            Example example3 = new Example(XdaH5TemplateCommodityList.class);
            example3.createCriteria().andIn("templateCommodityId",idList);
            List<XdaH5TemplateCommodityList> xdH5TemplateCommodityLists = xdaH5TemplateCommodityListMapper.selectByExample(example3);
            if(SpringUtil.isNotEmpty(xdH5TemplateCommodityLists)){
                xdaH5TemplateCommodityListMapper.deleteByExample(example3);
            }
            xdaH5TemplateCommodityMapper.deleteByExample(example2);
        }
    }

    /**
     * 鲜达-h5 详情
     * @param id
     * @return
     */
    public XdaH5TemplateDetailsODTO viewXdaH5TemplateDetails(Long id){
        QYAssert.isTrue(null != id ,"id 参数异常");
        //1.查询主表
        XdaH5TemplateDetailsODTO entry = xdaH5TemplateMapper.findXdaH5TemplateDetailsById(id);

        //查询头图
        if(entry.getTemplateCodeId() < 6){
            XdaH5PicODTO h5PicHead = xdaH5PicMapper.findXdaH5PicEntryByH5TemplateId(id,1);
            if(StringUtils.isNotEmpty(h5PicHead.getPicUrl())){
                h5PicHead.setImgUrl(imgServerUrl + "/" + h5PicHead.getPicUrl().trim());
            }
            entry.setH5PicHead(h5PicHead);
        }

        //4种模板
        if(entry.getTemplateCodeId() == 1 || entry.getTemplateCodeId() == 3){

            //模块区:2-1,2-1
            List<XdaH5PicCommodityODTO> h5PicCommodityList = xdaH5PicCommodityMapper.findXdaH5PicCommodityListByH5TemplateId(id);
            if(SpringUtil.isNotEmpty(h5PicCommodityList)){
                for(XdaH5PicCommodityODTO h5PicCommodityEntry : h5PicCommodityList){
                    if(!StringUtil.isNullOrEmpty(h5PicCommodityEntry.getPicUrl())){
                        h5PicCommodityEntry.setImgUrl(imgServerUrl + "/" + h5PicCommodityEntry.getPicUrl().trim());
                    }
                }
            }
            entry.setH5PicCommodityList(h5PicCommodityList);

            //Tab区
            List<XdaH5TemplateTabODTO> h5TemplateTabList = xdaH5TemplateTabMapper.findXdaH5TemplateTabListByByH5TemplateId(id);
            setTabCommodity(h5TemplateTabList);
            entry.setH5TemplateTabList(h5TemplateTabList);

        }else if(entry.getTemplateCodeId() == 2){

            //通栏区2-1
            XdaH5PicODTO h5PicBanner = xdaH5PicMapper.findXdaH5PicEntryByH5TemplateId(id, 2);
            if(!StringUtil.isNullOrEmpty(h5PicBanner.getPicUrl())){
                h5PicBanner.setImgUrl(imgServerUrl + "/" + h5PicBanner.getPicUrl().trim());
            }
            entry.setH5PicBanner(h5PicBanner);

            //2-2 图片 + h5
            XdaH5PicH5ODTO h5PicH5 = xdaH5PicH5Mapper.findXdaH5PicH5ByH5TemplateId(id);
            if(!StringUtil.isNullOrEmpty(h5PicH5.getPicUrl())){
                h5PicH5.setImgUrl(imgServerUrl + "/" + h5PicH5.getPicUrl().trim());
            }
            entry.setH5PicH5(h5PicH5);

            //2-3 4个商品位
            List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList = xdaH5TemplateTabCommodityMapper.findXdaH5TemplateTabCommodityListByH5TemplateId(id);
            entry.setH5TemplateTabCommodityList(h5TemplateTabCommodityList);

            //Tab区
            List<XdaH5TemplateTabODTO> h5TemplateTabList = xdaH5TemplateTabMapper.findXdaH5TemplateTabListByByH5TemplateId(id);
            setTabCommodity(h5TemplateTabList);
            entry.setH5TemplateTabList(h5TemplateTabList);


        }else if(entry.getTemplateCodeId() == 4){

            //商品区
            List<XdaH5TemplateTabCommodityODTO> h5TemplateTabCommodityList = xdaH5TemplateTabCommodityMapper.findXdaH5TemplateTabCommodityListByH5TemplateId(id);
            entry.setH5TemplateTabCommodityList(h5TemplateTabCommodityList);

        }else if(entry.getTemplateCodeId() == 6){
            //URL地址
            XdaH5TemplateUrlODTO h5TemplateUrl = xdaH5TemplateUrlMapper.findXdaH5TemplateUrlByTemplateId(id);
            entry.setH5TemplateUrl(h5TemplateUrl);

        }else if(entry.getTemplateCodeId() == 7){
            //商品明细
            Example example2 = new Example(XdaH5TemplateCommodity.class);
            example2.createCriteria().andEqualTo("templateId",id);
            List<XdaH5TemplateCommodity> list = xdaH5TemplateCommodityMapper.selectByExample(example2);
            if(SpringUtil.isNotEmpty(list)){
                List<XdaH5TemplateCommodityODTO> commodityList = new ArrayList<>();
                for(XdaH5TemplateCommodity commodity : list){
                    XdaH5TemplateCommodityODTO commodityEntry = new XdaH5TemplateCommodityODTO();
                    commodityEntry.setShowCount(commodity.getShowCount());
                    //查询商品id
                    List<XdaH5TemplateCommodityListODTO> xdH5TemplateCommodityLists = xdaH5TemplateCommodityListMapper.findXdaH5TemplateCommodityListByTemplateCommodityId(commodity.getId());
                    if(SpringUtil.isNotEmpty(xdH5TemplateCommodityLists)){
                        List<String> commodityIdList = xdH5TemplateCommodityLists.stream().map(p -> p.getCommodityIdStr()).collect(Collectors.toList());
                        //商品id集合
                        commodityEntry.setCommodityIdList(commodityIdList);
                    }
                    commodityList.add(commodityEntry);
                }
                entry.setCommodityList(commodityList);
            }

            //优惠券规则明细
            Example example1 = new Example(XdaH5TemplateCouponPublishRule.class);
            example1.createCriteria().andEqualTo("templateId",id);
            List<XdaH5TemplateCouponPublishRule> couponPublishRules = xdaH5TemplateCouponPublishRuleMapper.selectByExample(example1);
            if(SpringUtil.isNotEmpty(couponPublishRules)){
                List<Long> couponPublishRuleIdList = couponPublishRules.stream().mapToLong(p -> p.getCouponPublishRuleId()).boxed().collect(Collectors.toList());
                entry.setCouponPublishRuleIdList(couponPublishRuleIdList);
            }
        }
        return entry;
    }

    /**
     * 查询模块下的商品编码
     * @param h5TemplateTabList
     */
    private void setTabCommodity(List<XdaH5TemplateTabODTO> h5TemplateTabList) {
        if(SpringUtil.isNotEmpty(h5TemplateTabList)){
            for(XdaH5TemplateTabODTO entry : h5TemplateTabList){
                if(!StringUtil.isNullOrEmpty(entry.getPicUrl())){
                    entry.setImgUrl(imgServerUrl + "/" + entry.getPicUrl().trim());
                }
                List<XdaH5TemplateTabCommodityODTO> commodityList = xdaH5TemplateTabCommodityMapper.findXdaH5TemplateTabCommodityListByTabId(entry.getId());
                if(SpringUtil.isNotEmpty(commodityList)){
                    List<String> commodityCodeList = new ArrayList<>();
                    for(XdaH5TemplateTabCommodityODTO selectShopCommodity : commodityList){
                        commodityCodeList.add(selectShopCommodity.getCommodityCode());
                    }
                    entry.setCommodityCodeList(commodityCodeList);
                }
            }
        }
    }

    /**
     * 鲜达H5-查看详情页面-根据tabId查询tab下商品的明细列表
     * @param tabId
     * @return
     */
    public List<XdaH5TemplateTabCommodityODTO> findXdaH5TemplateTabCommodityListByTabId(Long tabId){
        return xdaH5TemplateTabCommodityMapper.findXdaH5TemplateTabCommodityListByTabId(tabId);
    }

    /**
     * 鲜达H5-选择商品,条件查询商品列表
     * @param searchIDTO
     * @return
     */
    public PageInfo<XdaH5AddSelectCommodityODTO> addXdaH5SelectCommodityByParams(XdaH5AddSelectCommoditySearchIDTO searchIDTO){
        PageInfo<XdaH5AddSelectCommodityODTO> pageData = PageHelper.startPage(searchIDTO.getPageNo(), searchIDTO.getPageSize()).doSelectPageInfo(() -> {
            xdaH5TemplateMapper.addXdaH5SelectCommodityByParams(searchIDTO);
        });
        return pageData;
    }

    /**
     * 鲜达H5-添加H5-根据商品编码集合查询商品信息
     * @param commodityCodeList
     * @return
     */
    public List<XdaH5AddSelectCommodityODTO> addXdaH5QueryCommodityDetailsByCommodityCodeList(@RequestParam(value = "commodityCodeList",required = false)List<String> commodityCodeList){
        return xdaH5TemplateMapper.addXdaH5QueryCommodityDetailsByCommodityCodeList(commodityCodeList);
    }
    /**
     * 鲜达H5-添加H5-根据商品编码集合查询商品信息
     * @param commodityCodeList
     * @return
     */
    public List<XdaH5AddSelectCommodityODTO> addPfH5QueryCommodityDetailsByCommodityCodeList(@RequestParam(value = "commodityCodeList",required = false)List<String> commodityCodeList){
        List<XdaH5AddSelectCommodityODTO> xdaH5AddSelectCommodityODTOS = xdaH5TemplateMapper.addPfH5QueryCommodityDetailsByCommodityCodeList(commodityCodeList);
        QYAssert.isTrue(SpringUtil.isNotEmpty(xdaH5AddSelectCommodityODTOS), "商品编码不存在");
        return xdaH5AddSelectCommodityODTOS;
    }
     /** 批量添加商品确定时判断:根据商品编码判断商品是否存在
     * @param commodityCodeList
     * @return
     *
     * 目前取商品取总的商品池 不区分是否前置仓
     */
    public boolean addXdaH5JudgeCommodityExistsByCommodityCodeList(List<String> commodityCodeList){
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityCodeList)," 请添加商品!");
        Set<String> idSet = new HashSet<>(commodityCodeList);
        QYAssert.isTrue(commodityCodeList.size() == idSet.size(), "有重复商品编码,请检查数据");
        for(String commodityCode : commodityCodeList){
            List<XdaH5AddSelectCommodityODTO> commodityList = xdaH5TemplateMapper.addXdaH5QueryCommodityByCommodityCode(commodityCode);
            QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList),"商品:"+commodityCode+"不存在,请检查商品编码!");
        }
        return true;
    }
}
