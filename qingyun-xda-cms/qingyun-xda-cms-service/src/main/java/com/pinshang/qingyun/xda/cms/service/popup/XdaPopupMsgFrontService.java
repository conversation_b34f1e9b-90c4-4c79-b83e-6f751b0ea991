package com.pinshang.qingyun.xda.cms.service.popup;

import java.util.Date;

import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.pinshang.qingyun.base.enums.xd.XdPopupMagWayEnums;
import com.pinshang.qingyun.xda.cms.dto.home.XdaPopupMsgAppODTO;
import com.pinshang.qingyun.xda.cms.mapper.popup.XdaPopupMsgMapper;


@Service
@Transactional
public class XdaPopupMsgFrontService {

    @Autowired
    private XdaPopupMsgMapper xdaPopupMsgMapper;

    @Value("${pinshang.img-server-url}")
    private String imgServerUrl;
    @Value("${pinshang.domain-name}")
    private String domainName;
    private String targetUrl = "/gateXdaApi/xdaCms/xdaH5Render/renderXdH5?templateId=";

    /**
     * 首页弹框通知：同一时间取最新创建的一条
     * @return
     */
    public XdaPopupMsgAppODTO queryXdPopupMsgForApp(Long storeId, Date date){
        XdaPopupMsgAppODTO popupMsgAppODTO = xdaPopupMsgMapper.queryXdPopupMsgForApp(storeId, date);
        if(popupMsgAppODTO==null){
            return null;
        }
        //处理图片通知的图片地址和H5url
        if(popupMsgAppODTO.getMsgWay()!=null && popupMsgAppODTO.getMsgWay().intValue()== XdPopupMagWayEnums.PICTURE.getCode()){
            if(StringUtils.isNotEmpty(popupMsgAppODTO.getMsgPicUrl())){
                String msgPicUrl = popupMsgAppODTO.getMsgPicUrl();
                popupMsgAppODTO.setMsgPicUrl(imgServerUrl.concat(msgPicUrl));
            }
            if(popupMsgAppODTO.getMsgTargetTypeId()!=null){
                popupMsgAppODTO.setMsgTargetTypeUrl(domainName.concat(targetUrl.concat(String.valueOf(popupMsgAppODTO.getMsgTargetTypeId())).concat("&storeId=").concat(String.valueOf(storeId)))
                										.concat("&orderTime=").concat(DateFormatUtils.format(date, "yyyy-MM-dd")));
            }
        }
        return popupMsgAppODTO;
    }

}
