package com.pinshang.qingyun.xda.cms.service.popup;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IsAllShopTypeEnums;
import com.pinshang.qingyun.base.enums.IsAllStoreTypeEnums;
import com.pinshang.qingyun.base.enums.OperateTypeEnums;
import com.pinshang.qingyun.base.enums.xd.XdPopupMagIsJumpEnums;
import com.pinshang.qingyun.base.enums.xd.XdPopupMagWayEnums;
import com.pinshang.qingyun.base.enums.xd.XdPopupMsgStatusEnums;
import com.pinshang.qingyun.base.enums.xda.XdaStoreScopeTypeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.xda.cms.dto.popup.*;
import com.pinshang.qingyun.xda.cms.dto.storeScope.DictionaryODTO;
import com.pinshang.qingyun.xda.cms.dto.storeScope.ProductPriceModelODTO;
import com.pinshang.qingyun.xda.cms.dto.storeScope.SettlementODTO;
import com.pinshang.qingyun.xda.cms.dto.storeScope.StoreODTO;
import com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5TemplateMapper;
import com.pinshang.qingyun.xda.cms.mapper.popup.XdaPopupMsgLogMapper;
import com.pinshang.qingyun.xda.cms.mapper.popup.XdaPopupMsgMapper;
import com.pinshang.qingyun.xda.cms.mapper.popup.XdaPopupMsgStoreScopeMapper;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5Template;
import com.pinshang.qingyun.xda.cms.model.popup.XdaPopupMsg;
import com.pinshang.qingyun.xda.cms.model.popup.XdaPopupMsgLog;
import com.pinshang.qingyun.xda.cms.model.popup.XdaPopupMsgStoreScope;
import com.pinshang.qingyun.xda.cms.utils.FilePortUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @description: 鲜达-弹框通知 service
 * @author: hhf
 * @time: 2020/12/9 13:36
 */
@Slf4j
@Service
@Transactional
public class XdaPopupMsgService {

    @Autowired
    private XdaPopupMsgMapper xdaPopupMsgMapper;
    @Autowired
    private XdaPopupMsgLogMapper xdaPopupMsgLogMapper;
    @Autowired
    private XdaPopupMsgStoreScopeMapper xdaPopupMsgStoreScopeMapper;
    @Autowired
    private XdaH5TemplateMapper xdaH5TemplateMapper;
    @Autowired
    private CodeClient codeClient;
    @Value("${pinshang.img-server-url}")
    private String imgServerUrl;


    /**
     * 弹框通知列表
     * @param xdaPopupMsgIDTO
     * @return
     */
    public PageInfo<XdaPopupMsgODTO> findXdaPopupMsgListByParams(XdaPopupMsgIDTO xdaPopupMsgIDTO){
        if(null != xdaPopupMsgIDTO && null != xdaPopupMsgIDTO.getStoreId()){
            //查询客户id-> 渠道id,客户类型id
            XdaPopupMsgStoreScopeIDTO scopeIDTO = xdaPopupMsgMapper.selectStoreTypeIdAndStoreChannelIdInfoById(xdaPopupMsgIDTO.getStoreId());
            if(null != scopeIDTO){
                xdaPopupMsgIDTO.setStoreTypeId(scopeIDTO.getStoreTypeId());
                xdaPopupMsgIDTO.setStoreChannelId(scopeIDTO.getStoreChannelId());
            }
            //查询客户id-> 产品价格方案id,结账客户id
            List<XdaPopupMsgStoreScopeIDTO> list = xdaPopupMsgMapper.selectSettlementIdAndProductPriceModelIdById(xdaPopupMsgIDTO.getStoreId());
            if(SpringUtil.isNotEmpty(list)){
                List<Long> settlementIdList = new ArrayList<>();
                List<Long> productPriceModelIdList = new ArrayList<>();
                for (XdaPopupMsgStoreScopeIDTO xdaPopupMsgStoreScopeIDTO : list) {
                    if(null != xdaPopupMsgStoreScopeIDTO){
                        if(null != xdaPopupMsgStoreScopeIDTO.getProductPriceModelId()){
                            productPriceModelIdList.add(xdaPopupMsgStoreScopeIDTO.getProductPriceModelId());
                        }
                        if(null != xdaPopupMsgStoreScopeIDTO.getSettlementId()){
                            settlementIdList.add(xdaPopupMsgStoreScopeIDTO.getSettlementId());
                        }
                    }
                }
                xdaPopupMsgIDTO.setSettlementIdList(settlementIdList);
                xdaPopupMsgIDTO.setProductPriceModelIdList(productPriceModelIdList);
            }
        }

        PageInfo<XdaPopupMsgODTO> pageData = PageHelper.startPage(xdaPopupMsgIDTO.getPageNo(), xdaPopupMsgIDTO.getPageSize()).doSelectPageInfo(() -> {
            xdaPopupMsgMapper.findXdPopupMsgListByParams(xdaPopupMsgIDTO);
        });
        return pageData;
    }

    /**
     * 弹框通知id 查询日志
     * @param popupMsgId
     * @return
     */
    public List<XdaPopupMsgLogODTO> findXdaPopupMsgLogListByPopupMsgId(Long popupMsgId){
        QYAssert.isTrue(null !=popupMsgId," id参数异常!");
        return xdaPopupMsgLogMapper.findXdaPopupMsgLogListByPopupMsgId(popupMsgId);
    }


    /**
     * 鲜达-弹框通知-修改状态:1-启用,0-停用
     * @param editIDTO
     * @return
     */
    public Long editXdaPopupMsgStatus(XdaPopupMsgStatusEditIDTO editIDTO){
        QYAssert.isTrue(null != editIDTO," 修改参数异常!");
        QYAssert.isTrue(null != editIDTO.getId()," id参数异常!");
        QYAssert.isTrue(null != editIDTO.getStatus()," 状态参数异常!");
        this.getXdaPopupMsgInfo(editIDTO.getId());

        XdaPopupMsg xdaPopupMsg = XdaPopupMsg.forUpdateStatus(editIDTO.getId(),editIDTO.getStatus(),editIDTO.getUserId());
        xdaPopupMsgMapper.updateByPrimaryKeySelective(xdaPopupMsg);

        //日志: 1-启用,0-停用
        Integer status = editIDTO.getStatus();
        if(status.equals(XdPopupMsgStatusEnums.DISABLE.getCode())){
            XdaPopupMsgLog xdaPopupMsgLog = XdaPopupMsgLog.forInsert(editIDTO.getId(), OperateTypeEnums.停用.getCode(), new Date(), editIDTO.getUserId());
            xdaPopupMsgLogMapper.insert(xdaPopupMsgLog);
        }else{
            XdaPopupMsgLog xdaPopupMsgLog = XdaPopupMsgLog.forInsert(editIDTO.getId(), OperateTypeEnums.启用.getCode(), new Date(), editIDTO.getUserId());
            xdaPopupMsgLogMapper.insert(xdaPopupMsgLog);
        }


        return editIDTO.getId();
    }

    /**
     * 鲜达-弹框通知-id查询弹框通知
     * @param popupMsgId
     * @return
     */
    private XdaPopupMsg getXdaPopupMsgInfo(Long popupMsgId) {
        XdaPopupMsg xdaPopupMsg = xdaPopupMsgMapper.selectOne(new XdaPopupMsg(popupMsgId));
        QYAssert.isTrue(null != xdaPopupMsg, "未查询到相关信息!");
        return xdaPopupMsg;
    }

    /**
     * 新增弹框通知
     * @param saveIDTO
     * @return
     */
    public Long saveXdaPopupMsg(XdaPopupMsgSaveIDTO saveIDTO){
        QYAssert.isTrue(null != saveIDTO," 保存对象异常!");
        //验证对象
        validXdaPopupMsg(saveIDTO);
        //保存
        Date date = new Date();

        //编码 TZ + YYYYMMDD + 4位流水号
        String code = codeClient.createCode("XDA_POPUP_MSG_CODE");
        QYAssert.notNull(code, "鲜达-弹框通知 构建异常!");
        saveIDTO.setMsgNo( "TZ" + code);
        XdaPopupMsg xdPopupMsg = XdaPopupMsg.forInsert(saveIDTO,date);
        xdaPopupMsgMapper.insert(xdPopupMsg);
        Long xdPopupMsgId = xdPopupMsg.getId();

        // 门店
        if (IsAllShopTypeEnums.SPECIFIC_SHOP.getCode().equals(saveIDTO.getIsAllStore())) {
            this.insertPopupMagShopScope(xdPopupMsgId, saveIDTO.getScopeList(),saveIDTO.getUserId(),date);
        }

        //日志
        XdaPopupMsgLog xdaPopupMsgLog = XdaPopupMsgLog.forInsert(xdPopupMsgId, OperateTypeEnums.新增.getCode(), date, saveIDTO.getUserId());
        xdaPopupMsgLogMapper.insert(xdaPopupMsgLog);

        return xdPopupMsgId;
    }

    /**
     * 插入弹框通知客户集合
     * @param popupMsgId
     * @param scopeList
     */
    private void insertPopupMagShopScope(Long popupMsgId, List<XdaPopupMsgStoreScope> scopeList,Long userId,Date date) {
        scopeList.forEach(e-> {
            e.setPopupMsgId(popupMsgId);
            e.setCreateId(userId);
            e.setCreateTime(date);
        });
        xdaPopupMsgStoreScopeMapper.insertList(scopeList);
    }

    /**
     * 验证对象
     * @param saveIDTO
     */
    private void validXdaPopupMsg(XdaPopupMsgSaveIDTO saveIDTO) {
        QYAssert.isTrue(StringUtils.isNotEmpty(saveIDTO.getMsgSummary())," 通知概要不能为空!");
        QYAssert.isTrue(saveIDTO.getMsgSummary().length()< 11," 通知概要最多10个字符!");
        QYAssert.isTrue(StringUtils.isNotEmpty(saveIDTO.getBeginTime()) ," 生效开始时间不能为空!");
        QYAssert.isTrue(StringUtils.isNotEmpty(saveIDTO.getEndTime())," 生效结束时间不能为空!");
        QYAssert.isTrue(null != saveIDTO.getFrequency()," 弹框频率不能为空!");

        Integer isAllShop = saveIDTO.getIsAllStore();
        QYAssert.isTrue(IsAllStoreTypeEnums.isAllStoreTypeIntList().contains(isAllShop), "请设置有效的‘客户范围’类型!");
        //根据客户范围: 设置客户
        if (IsAllStoreTypeEnums.ALL_STORE.getCode().equals(isAllShop)) {
            saveIDTO.setScopeList(null);
        } else if (IsAllStoreTypeEnums.SPECIFIC_STORE.getCode().equals(isAllShop)) {
            List<XdaPopupMsgStoreScope> scopeList = saveIDTO.getScopeList();
            if (SpringUtil.isNotEmpty(scopeList)) {
                scopeList.removeIf(e -> (null == e.getRefObjId()));
            }
            QYAssert.isTrue(SpringUtil.isNotEmpty(scopeList), "请选择‘客户范围’!");
            saveIDTO.setScopeList(scopeList);
        }

        //通知方式
        Integer msgWay = saveIDTO.getMsgWay();
        QYAssert.isTrue(null != msgWay," 请选择通知方式!");
        QYAssert.isTrue(XdPopupMagWayEnums.isAllXdPopupMagWayIntList().contains(msgWay)," 请选择有效的通知方式!");
        if(XdPopupMagWayEnums.PICTURE.getCode().equals(msgWay)){
            QYAssert.isTrue(StringUtils.isNotEmpty(saveIDTO.getPicUrl())," 请上传‘图片’!");

            Integer isJump = saveIDTO.getIsJump();
            QYAssert.isTrue(null != isJump," 请选择是否跳转!");
            QYAssert.isTrue(XdPopupMagIsJumpEnums.isAllXdPopupMagIsJumpIntList().contains(isJump)," 请选择有效的跳转方式!");
            if(XdPopupMagIsJumpEnums.H5.getCode().equals(isJump)){
                QYAssert.isTrue(null != saveIDTO.getH5Id()," 请选择H5!");
            }

        }else if(XdPopupMagWayEnums.TEXT.getCode().equals(msgWay)){
            QYAssert.isTrue(StringUtils.isNotEmpty(saveIDTO.getAppMsgTitle())," 前台通知标题不能为空!");
            QYAssert.isTrue(saveIDTO.getAppMsgTitle().trim().length()< 11," 前台通知标题最多10个字符!");

            QYAssert.isTrue(StringUtils.isNotEmpty(saveIDTO.getAppMsgDetails())," 前台通知详情不能为空!");
            QYAssert.isTrue(saveIDTO.getAppMsgTitle().length()< 201," 前台通知详情最多200个字符!");
        }
        //时间
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            saveIDTO.setBTime(simpleDateFormat.parse(saveIDTO.getBeginTime()));
            saveIDTO.setETime(simpleDateFormat.parse(saveIDTO.getEndTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }


    /**
     * 修改弹框通知
     * @param editIDTO
     * @return
     */
    public Long editXdaPopupMsg(XdaPopupMsgEditIDTO editIDTO){
        QYAssert.isTrue(null != editIDTO," 保存对象异常!");
        QYAssert.isTrue(null != editIDTO.getId()," 对象Id异常!");
        this.getXdaPopupMsgInfo(editIDTO.getId());
        //验证对象
        validXdaPopupMsg(editIDTO);
        //保存
        Date date = new Date();
        XdaPopupMsg xdPopupMsg = XdaPopupMsg.forUpdate(editIDTO,date,editIDTO.getId());
        xdaPopupMsgMapper.updateByPrimaryKeySelective(xdPopupMsg);
        // 门店
        xdaPopupMsgStoreScopeMapper.delete(new XdaPopupMsgStoreScope(editIDTO.getId()));
        if (IsAllShopTypeEnums.SPECIFIC_SHOP.getCode().equals(editIDTO.getIsAllStore())) {
            this.insertPopupMagShopScope(editIDTO.getId(), editIDTO.getScopeList(),editIDTO.getUserId(),date);
        }
        //日志
        XdaPopupMsgLog log = XdaPopupMsgLog.forInsert(editIDTO.getId(), OperateTypeEnums.修改.getCode(), date, editIDTO.getUserId());
        xdaPopupMsgLogMapper.insert(log);

        return editIDTO.getId();
    }

    /**
     * 弹框通知详情
     * @param popupMsgId
     * @return
     */
    public XdaPopupMsgDetailsODTO findXdaPopupMsgDetailsById(Long popupMsgId){
        QYAssert.isTrue(null != popupMsgId," 对象Id异常");
        XdaPopupMsg xdPopupMsgInfo = this.getXdaPopupMsgInfo(popupMsgId);
        XdaPopupMsgDetailsODTO detailsODTO = BeanCloneUtils.copyTo(xdPopupMsgInfo, XdaPopupMsgDetailsODTO.class);
        // 门店
        if (IsAllShopTypeEnums.SPECIFIC_SHOP.getCode().equals(detailsODTO.getIsAllStore())) {
            //1.结账客户
            List<SettlementODTO> settlementODTOList = xdaPopupMsgStoreScopeMapper.findSettlementByPopupMsgIdAndRefObjType(popupMsgId, XdaStoreScopeTypeEnums.结账客户.getCode());
            detailsODTO.setSettlementList(settlementODTOList);
            //2.产品价格方案
            List<ProductPriceModelODTO> productPriceModelODTOList = xdaPopupMsgStoreScopeMapper.findProductPriceModelByPopupMsgIdAndRefObjType(popupMsgId, XdaStoreScopeTypeEnums.产品价格方案.getCode());
            detailsODTO.setProductPriceModelList(productPriceModelODTOList);
            //3.客户类型
            List<DictionaryODTO> storeTypeList = xdaPopupMsgStoreScopeMapper.findDictionaryByPopupMsgIdAndRefObjType(popupMsgId, XdaStoreScopeTypeEnums.客户类型.getCode());
            detailsODTO.setStoreTypeList(storeTypeList);
            //4.渠道
            List<DictionaryODTO> storeChannelList = xdaPopupMsgStoreScopeMapper.findDictionaryByPopupMsgIdAndRefObjType(popupMsgId, XdaStoreScopeTypeEnums.渠道.getCode());
            detailsODTO.setStoreChannelList(storeChannelList);
            //8.客户
            List<StoreODTO> storeODTOList = xdaPopupMsgStoreScopeMapper.findStoreByPopupMsgIdAndRefObjType(popupMsgId, XdaStoreScopeTypeEnums.客户.getCode());
            detailsODTO.setStoreList(storeODTOList);
            //9 部门
            List<DictionaryODTO> departmentList = xdaPopupMsgStoreScopeMapper.findDictionaryByPopupMsgIdAndRefObjType(popupMsgId, XdaStoreScopeTypeEnums.部门.getCode());
            detailsODTO.setDepartmentList(departmentList);
        }

        //图片
        detailsODTO.setVisitPicUrl(this.getVisitPicUrl(detailsODTO.getPicUrl()));

        //跳转 h5
        Integer isJump = xdPopupMsgInfo.getIsJump();
        if(XdPopupMagIsJumpEnums.H5.getCode().equals(isJump)){
            Long h5Id = xdPopupMsgInfo.getH5Id();
            XdaH5Template h5Template = xdaH5TemplateMapper.selectByPrimaryKey(h5Id);
            if (null != h5Template) {
                detailsODTO.setH5Name(h5Template.getTemplateCode() + "（" + h5Template.getTemplateName() + "）");
            }
        }
        return detailsODTO;
    }

    /**
     * 图片地址
     * @param picUrl
     * @return
     */
    private String getVisitPicUrl (String picUrl) {
        String visitPicUrl = null;
        if (!StringUtil.isNullOrEmpty(picUrl)) {
            visitPicUrl = imgServerUrl + "/" + picUrl.trim();
        }
        return visitPicUrl;
    }


    /**
     * 导出-鲜达弹框通知
     * @param idto
     * @param response
     * @param request
     */
    public void exportXdaPopupMsgByParams(XdaPopupMsgIDTO idto, HttpServletResponse response, HttpServletRequest request){
        idto.setPageNo(1);
        idto.setPageSize(Integer.MAX_VALUE);
        List<XdaPopupMsgODTO> list = this.findXdaPopupMsgListByParams(idto).getList();

        //导出的表名
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String title = "鲜达弹框通知"+"_"+ sdf.format(new Date());

        //表中第一行表头字段
        String[] headers = {"通知编号","通知概要","通知方式","生效时间","适用客户","状态"};

        //具体需要写入excel需要哪些字段
        List<String> listColumn = Arrays.asList("msgNo", "msgSummary", "msgWayName", "validTime","storeQuantity","statusName");
        try {
            FilePortUtil.exportExcel(request, response, title, headers, list, listColumn);
        } catch (Exception e) {
            log.error("鲜达弹框通知--导出异常：{}，异常详情：{}", e.getMessage(), e);
        }
    }

}
