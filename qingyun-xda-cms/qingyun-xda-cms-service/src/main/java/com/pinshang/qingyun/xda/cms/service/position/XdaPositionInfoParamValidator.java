package com.pinshang.qingyun.xda.cms.service.position;

import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Component;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.IsAllStoreTypeEnums;
import com.pinshang.qingyun.base.enums.TermTypeEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionBlockIdEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionIdEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionInfoTargetTypeEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionTypeEnums;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.xda.cms.dto.position.InsertPositionInfoIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionCommodityInfoIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionInfoBlockIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.StoreScopeIDTO;

/**
 * 鲜达资源位
 */
@Component
public class XdaPositionInfoParamValidator {
	
	private final String DATE_FORMAT_4yMdHms = "yyyy-MM-dd HH:mm:ss";
	private final List<Integer> TARGET_TYPE_LINK_LIST = XSAppPositionInfoTargetTypeEnums.xdLinkIntList();
	private final List<Integer> TARGET_TYPE_LINK_LIST_2 = XSAppPositionInfoTargetTypeEnums.xdLinkIntList2();
	private final List<Integer> TARGET_TYPE_NOT_LINK_LIST = XSAppPositionInfoTargetTypeEnums.xdNotLinkIntList();
    
    /**
     * 新增/修改前的参数校验
     * 
     * @param idto
     */
    protected void checkBeforeInsert(InsertPositionInfoIDTO idto) {
    	QYAssert.isTrue(null != idto, "参数有误!");
		Integer positionType = idto.getPositionType();
		Integer positionId = idto.getPositionId();
		QYAssert.isTrue(XSAppPositionTypeEnums.xdPositionTypeIntList().contains(positionType), "请设置有效的‘资源位’类型!");
		
		switch (XSAppPositionTypeEnums.get(positionType)) {
			case BANNER:
				this.checkPositionId(XSAppPositionIdEnums.xdBannerIntList(), positionId);
				this.checkBannerBeforeInsert(idto);
				break;
			case ICON:
				this.checkPositionId(XSAppPositionIdEnums.xdIconIntList(), positionId);
				this.checkIconBeforeInsert(idto);
				break;
			case 积木组:
				this.checkPositionId(XSAppPositionIdEnums.xdJmzIntList(), positionId);
				this.checkJmzBeforeInsert(idto);
				break;
			case 推荐组:
				this.checkPositionId(XSAppPositionIdEnums.tjzIntList(), positionId);
				this.checkTjzBeforeInsert(idto);
				break;
			case 通栏:
				this.checkPositionId(XSAppPositionIdEnums.xdTlIntList(), positionId);
				this.checkTlBeforeInsert(idto);
				break;
			case 横栏:
				this.checkPositionId(XSAppPositionIdEnums.xdHlIntList(), positionId);
				this.checkHlBeforeInsert(idto);
				break;
			case 头图:
				this.checkPositionId(XSAppPositionIdEnums.xdSyttIntList(), positionId);
				this.checkSyttBeforeInsert(idto);
				break;
			case 服务说明栏:
				this.checkPositionId(XSAppPositionIdEnums.xdFwsmlIntList(), positionId);
				this.checkFwsmlBeforeInsert(idto);
				break;
			case 弹框广告:
				this.checkPositionId(XSAppPositionIdEnums.xdTkggIntList(), positionId);
				this.checkTkggBeforeInsert(idto);
				break;
			 default:
                 break;
		}
		idto.setPositionType(positionType);
		idto.setPositionId(positionId);
    }
    private void checkPositionId(List<Integer> positionIdList, Integer positionId) {
    	QYAssert.isTrue(positionIdList.contains(positionId), "请设置有效的‘资源位’!");
    }
    // 1、校验参数- BANNER
    private void checkBannerBeforeInsert(InsertPositionInfoIDTO idto) {
    	this.checkTimeAnds(idto, TermTypeEnums.termTypeIntList());
    	
    	idto.setMinInterval(0);
    	
    	this.checkTarget(TARGET_TYPE_LINK_LIST, idto.getTargetType(), idto.getTargetTypeId(), "");
		idto.setLabel(null);
    	
		QYAssert.isTrue(!StringUtil.isNullOrEmpty(idto.getPicUrl()), "请上传‘图片’!");
		idto.setPicUrl(idto.getPicUrl().trim());
    	idto.setPicTargetType(0);
		idto.setPicTargetTypeId(0L);
		
		idto.setCommodityList(null);
		idto.setBlockList(null);
    }
    // 2、校验参数-ICON
    private void checkIconBeforeInsert(InsertPositionInfoIDTO idto) {
    	this.checkTimeAnds(idto, TermTypeEnums.termTypeIntList());
    	
    	idto.setMinInterval(0);
    	
    	this.checkTarget(TARGET_TYPE_LINK_LIST, idto.getTargetType(), idto.getTargetTypeId(), "");
    	this.checkLabel(idto, "‘ICON名称’");
    	
    	QYAssert.isTrue(!StringUtil.isNullOrEmpty(idto.getPicUrl()), "请上传‘图片’!");
		idto.setPicUrl(idto.getPicUrl().trim());
    	idto.setPicTargetType(0);
		idto.setPicTargetTypeId(0L);
		
		idto.setCommodityList(null);
		idto.setBlockList(null);
    }
    // 3、校验参数-积木组
    private void checkJmzBeforeInsert(InsertPositionInfoIDTO idto) {
    	this.checkTimeAnds(idto, TermTypeEnums.shortTermTypeIntList());
    	
    	idto.setMinInterval(0);
    	
    	Integer positionId = idto.getPositionId();
    	if (XSAppPositionIdEnums.积木组01.getCode().equals(positionId)) {
    		this.checkTarget(TARGET_TYPE_LINK_LIST_2, idto.getTargetType(), idto.getTargetTypeId(), "");
    	} else {
    		idto.setTargetType(null);
			idto.setTargetTypeId(null);
    	} 
		if ( XSAppPositionIdEnums.积木组03.getCode().equals(positionId)) {
			this.checkLabel(idto, "‘标题文案’");
		} else {
			idto.setLabel(null);
		}

		if (XSAppPositionIdEnums.积木组01.getCode().equals(positionId)) {
			QYAssert.isTrue(!StringUtil.isNullOrEmpty(idto.getPicUrl()), "请上传‘图片’!");
			idto.setPicUrl(idto.getPicUrl().trim());
    	} else {
    		idto.setPicUrl(null);
    	}
		idto.setPicTargetType(0);
		idto.setPicTargetTypeId(0L);
		
		if (XSAppPositionIdEnums.积木组01.getCode().equals(positionId)) {
			idto.setBlockList(null);
		} else if (XSAppPositionIdEnums.积木组02.getCode().equals(positionId)) {
			this.checkBlock(idto, "积木组02：", 3, XSAppPositionBlockIdEnums.xdJmz02IntList());
		} else if (XSAppPositionIdEnums.积木组03.getCode().equals(positionId)) {
			this.checkBlock(idto, "积木组03：", 5, XSAppPositionBlockIdEnums.xdJmz03IntList());
		}
		idto.setCommodityList(null);
    }
    // 4、校验参数-推荐组
    private void checkTjzBeforeInsert(InsertPositionInfoIDTO idto) {
    	this.checkTimeAnds(idto, TermTypeEnums.shortTermTypeIntList());
    	
    	idto.setMinInterval(0);
    	
    	this.checkTarget(TARGET_TYPE_NOT_LINK_LIST, idto.getTargetType(), idto.getTargetTypeId(), "");
    	this.checkLabel(idto, "‘显示文案’");
    	
    	idto.setPicUrl(null);
    	idto.setPicTargetType(0);
		idto.setPicTargetTypeId(0L);
		
		this.setCommodityList(idto);
		idto.setBlockList(null);
    }
    // 5、校验参数-通栏
    private void checkTlBeforeInsert(InsertPositionInfoIDTO idto) {
    	this.checkTimeAnds(idto, TermTypeEnums.termTypeIntList());
    	
    	idto.setMinInterval(0);
    	
    	this.checkTarget(TARGET_TYPE_NOT_LINK_LIST, idto.getTargetType(), idto.getTargetTypeId(), "");
    	idto.setLabel(null);
    	
    	QYAssert.isTrue(!StringUtil.isNullOrEmpty(idto.getPicUrl()), "请上传‘图片’!");
		idto.setPicUrl(idto.getPicUrl().trim());
		QYAssert.isTrue(TARGET_TYPE_LINK_LIST_2.contains(idto.getPicTargetType()), "请设置有效的‘通栏绑定类型’!");
		QYAssert.isTrue(null != idto.getPicTargetTypeId(), "请设置‘绑定标的’!");
		
		this.setCommodityList(idto);
		idto.setBlockList(null);
    }
    // 6、校验参数-横栏
    private void checkHlBeforeInsert(InsertPositionInfoIDTO idto) {
    	this.checkTimeAnds(idto, TermTypeEnums.shortTermTypeIntList());
    	
    	idto.setMinInterval(0);
    	
    	this.checkTarget(TARGET_TYPE_LINK_LIST_2, idto.getTargetType(), idto.getTargetTypeId(), "");
    	idto.setLabel(null);
    	
    	QYAssert.isTrue(!StringUtil.isNullOrEmpty(idto.getPicUrl()), "请上传‘图片’!");
		idto.setPicUrl(idto.getPicUrl().trim());
    	idto.setPicTargetType(0);
		idto.setPicTargetTypeId(0L);
		
		idto.setCommodityList(null);
		idto.setBlockList(null);
    }
    // 19、校验参数-头图
    private void checkSyttBeforeInsert(InsertPositionInfoIDTO idto) {
    	this.checkTimeAnds(idto, TermTypeEnums.termTypeIntList());
    	
    	idto.setMinInterval(0);
    	
    	idto.setTargetType(0);
    	idto.setTargetTypeId(0L);
    	idto.setLabel(null);
    	
    	QYAssert.isTrue(!StringUtil.isNullOrEmpty(idto.getPicUrl()), "请上传‘图片’!");
		idto.setPicUrl(idto.getPicUrl().trim());
    	idto.setPicTargetType(0);
		idto.setPicTargetTypeId(0L);
		
		idto.setCommodityList(null);
		idto.setBlockList(null);
    }
    // 20、校验参数-服务说明栏
    private void checkFwsmlBeforeInsert(InsertPositionInfoIDTO idto) {
    	this.checkTimeAnds(idto, TermTypeEnums.termTypeIntList());
    	
    	idto.setMinInterval(0);
    	
    	idto.setTargetType(0);
    	idto.setTargetTypeId(0L);
    	idto.setLabel(null);
    	
    	QYAssert.isTrue(!StringUtil.isNullOrEmpty(idto.getPicUrl()), "请上传‘图片’!");
		idto.setPicUrl(idto.getPicUrl().trim());
    	idto.setPicTargetType(0);
		idto.setPicTargetTypeId(0L);
		
		idto.setCommodityList(null);
		idto.setBlockList(null);
    }
    // 21、校验参数-弹框广告
    private void checkTkggBeforeInsert(InsertPositionInfoIDTO idto) {
    	this.checkTimeAnds(idto, TermTypeEnums.shortTermTypeIntList());
    	
    	Integer minInterval = idto.getMinInterval();
    	QYAssert.isTrue(null != minInterval, "请输入‘弹框频率’!");
    	QYAssert.isTrue(minInterval.intValue() >=0 && minInterval.intValue() < 25, "‘弹框频率’取值范围为[0,24]!");
    	
    	this.checkTarget(TARGET_TYPE_LINK_LIST_2, idto.getTargetType(), idto.getTargetTypeId(), "");
    	idto.setLabel(null);
    	
    	QYAssert.isTrue(!StringUtil.isNullOrEmpty(idto.getPicUrl()), "请上传‘图片’!");
		idto.setPicUrl(idto.getPicUrl().trim());
    	idto.setPicTargetType(0);
		idto.setPicTargetTypeId(0L);
		
		idto.setCommodityList(null);
		idto.setBlockList(null);
    }
    
    // 校验  时间类型相关的参数
    private void checkTimeAnds(InsertPositionInfoIDTO idto, List<Integer> termTypeList) {
		// 判断时间
		Date bTime = null;
		Date eTime = null;
		Integer termType = idto.getTermType();
		QYAssert.isTrue(termTypeList.contains(termType), "请设置有效的‘绑定期限’!");
		if (TermTypeEnums.短期.getCode().equals(termType)) {
			bTime = DateUtil.parseDate(idto.getBeginTime(), DATE_FORMAT_4yMdHms);
			eTime = DateUtil.parseDate(idto.getEndTime(), DATE_FORMAT_4yMdHms);
			if (null == bTime || null == eTime || bTime.after(eTime)) {
				QYAssert.isTrue(false, "生效时间输入不合法!");
			} else if (new Date().after(eTime)) {
				// 这个是产品特地要求的提示：http://192.168.0.213/zentao/bug-view-10141.html
				QYAssert.isTrue(false, "生效时间在当前时间点之前！");
			}
		}
		idto.setBTime(bTime);
		idto.setETime(eTime);
		idto.setTermType(termType);
		
		// 判断【客户范围】
		Integer isAllStore = idto.getIsAllStore();
		QYAssert.isTrue(IsAllStoreTypeEnums.isAllStoreTypeIntList().contains(isAllStore), "请选择客户范围!");
		if (TermTypeEnums.长期.getCode().equals(termType)) {
			QYAssert.isTrue(IsAllStoreTypeEnums.ALL_STORE.getCode().equals(isAllStore), "绑定期限为长期时，客户范围必须选择所有客户!");
		}
		if (IsAllStoreTypeEnums.ALL_STORE.getCode().equals(isAllStore)) {
			idto.setStoreScopeList(null);
		} else if (IsAllStoreTypeEnums.SPECIFIC_STORE.getCode().equals(isAllStore)) {
			List<StoreScopeIDTO> storeScopeList = idto.getStoreScopeList();
			if (SpringUtil.isNotEmpty(storeScopeList)) {
				storeScopeList.removeIf(storeScope -> (null == storeScope.getRefObjType() || null == storeScope.getRefObjId()));
			}
			QYAssert.isTrue(SpringUtil.isNotEmpty(idto.getStoreScopeList()), "请选择客户范围!");
			idto.setStoreScopeList(storeScopeList);
		}
		idto.setIsAllStore(isAllStore);
    }
    // 校验  label
    private void checkLabel(InsertPositionInfoIDTO idto, String labelTip) {
    	String label = idto.getLabel();
    	QYAssert.isTrue(!StringUtil.isNullOrEmpty(label), labelTip + "不能为空!");
		QYAssert.isTrue(label.trim().length() < 9, labelTip + "不能多于8个字符!");
		idto.setLabel(label.trim());
    }
    // 设置  商品集合
    private void setCommodityList(InsertPositionInfoIDTO idto) {
    	List<PositionCommodityInfoIDTO> commodityList = idto.getCommodityList();
		if (SpringUtil.isNotEmpty(commodityList)) {
			commodityList.removeIf(commodity -> (null == commodity || null == commodity.getCommodityId()));
		}
		QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "‘商品列表’不能为空!");
		QYAssert.isTrue(commodityList.size() > 19 && commodityList.size() < 101, "‘商品’记录数必须介于[20, 100]!");
		this.sortCommodityList(commodityList);
		idto.setCommodityList(commodityList);
		idto.setTargetTypeId(null);
    }
	// 校验  积木相关属性
	private void checkBlock(InsertPositionInfoIDTO idto, String pre, int blockSize, List<Integer> blockIdList) {
		List<PositionInfoBlockIDTO> blockList = idto.getBlockList();
		QYAssert.isTrue(SpringUtil.isNotEmpty(blockList) && blockList.size() == blockSize, pre + "必须有" + blockSize + "条位置信息!");
		for(PositionInfoBlockIDTO block: blockList) {
			QYAssert.isTrue(null != block, pre + "请设置有效的积木组信息!");
			Integer blockPositionId = block.getPositionId();
			QYAssert.isTrue(blockIdList.contains(blockPositionId), pre + "请设置有效的‘资源位’!");
			
			Integer blockTargetType = block.getTargetType();
			Long blockTargetTypeId = block.getTargetTypeId();
			this.checkTarget(TARGET_TYPE_LINK_LIST_2, blockTargetType, blockTargetTypeId, pre);
			QYAssert.isTrue(!StringUtil.isNullOrEmpty(block.getPicUrl()), pre + "请上传‘图片’!");
			
			block.setPositionId(blockPositionId);
			block.setTargetType(blockTargetType);
			block.setTargetTypeId(blockTargetTypeId);
			block.setPicUrl(block.getPicUrl().trim());
		}
		// 排序
		this.sortBlockList(blockList);
		idto.setBlockList(blockList);
		idto.setTargetType(0);
		idto.setTargetTypeId(0L);
		idto.setPicUrl(null);
	}
	// 检验标的相关属性
	private void checkTarget(List<Integer> targetTypeList, Integer targetType, Long targetTypeId, String pre) {
		QYAssert.isTrue(targetTypeList.contains(targetType), pre + "请设置有效的‘标的类型’!");
		if (!XSAppPositionInfoTargetTypeEnums.组合.getCode().equals(targetType)) {
			QYAssert.isTrue(null != targetTypeId, pre + "请设置有效的‘绑定标的’!");
		}
	}
	
	// 排序商品列表
	private void sortCommodityList(List<PositionCommodityInfoIDTO> commodityList){  
        Collections.sort(commodityList, new Comparator<PositionCommodityInfoIDTO>() {  
            @Override  
            public int compare(PositionCommodityInfoIDTO o1, PositionCommodityInfoIDTO o2) {  
                if(o1.getSortNum().intValue() > o2.getSortNum().intValue()){  
                    return 1;  
                }  
                if(o1.getSortNum().equals(o2.getSortNum())){  
                    return 0;  
                }  
                return -1;  
            }  
        });  
    }
	// 排序积木信息
	private void sortBlockList(List<PositionInfoBlockIDTO> blockList) {
		Collections.sort(blockList, new Comparator<PositionInfoBlockIDTO>() {
            @Override
            public int compare(PositionInfoBlockIDTO o1, PositionInfoBlockIDTO o2) {
                if(o1.getPositionId().intValue() > o2.getPositionId().intValue()){
                    return 1;
                }  
                if(o1.getPositionId().equals(o2.getPositionId())){
                    return 0;
                }
                return -1;
            }
        });
	}
    
}
