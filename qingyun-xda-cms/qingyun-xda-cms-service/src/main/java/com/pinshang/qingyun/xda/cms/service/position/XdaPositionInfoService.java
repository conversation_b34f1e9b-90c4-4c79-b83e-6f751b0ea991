package com.pinshang.qingyun.xda.cms.service.position;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;
import java.util.stream.Collectors;

import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import tk.mybatis.mapper.entity.Example;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.CodeTypeEnum;
import com.pinshang.qingyun.base.enums.IsAllStoreTypeEnums;
import com.pinshang.qingyun.base.enums.OperateTypeEnums;
import com.pinshang.qingyun.base.enums.TermTypeEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionBlockTypeEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionIdEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionInfoStatusEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionInfoTargetTypeEnums;
import com.pinshang.qingyun.base.enums.XSAppPositionTypeEnums;
import com.pinshang.qingyun.base.enums.xda.XdaStoreScopeTypeEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.xda.cms.dto.ForceSubmitResultODTO;
import com.pinshang.qingyun.xda.cms.dto.position.InsertPositionInfoIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionCommodityInfoIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionCommodityInfoODTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionDetailODTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionInfoBlockIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionInfoBlockODTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionInfoLogInfoODTO;
import com.pinshang.qingyun.xda.cms.dto.position.PositionInfoODTO;
import com.pinshang.qingyun.xda.cms.dto.position.SelectConflictPositionInfoIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.SelectPositionInfoPageIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.StoreScopeIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.UpdatePositionInfoIDTO;
import com.pinshang.qingyun.xda.cms.dto.position.UpdatePositionInfoStatusIDTO;
import com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5TemplateMapper;
import com.pinshang.qingyun.xda.cms.mapper.position.XdaPositionInfoBlockMapper;
import com.pinshang.qingyun.xda.cms.mapper.position.XdaPositionInfoCommodityMapper;
import com.pinshang.qingyun.xda.cms.mapper.position.XdaPositionInfoLogMapper;
import com.pinshang.qingyun.xda.cms.mapper.position.XdaPositionInfoMapper;
import com.pinshang.qingyun.xda.cms.mapper.position.XdaPositionInfoStoreScopeMapper;
import com.pinshang.qingyun.xda.cms.model.h5.XdaH5Template;
import com.pinshang.qingyun.xda.cms.model.position.XdaPositionInfo;
import com.pinshang.qingyun.xda.cms.model.position.XdaPositionInfoBlock;
import com.pinshang.qingyun.xda.cms.model.position.XdaPositionInfoCommodity;
import com.pinshang.qingyun.xda.cms.model.position.XdaPositionInfoLog;
import com.pinshang.qingyun.xda.cms.model.position.XdaPositionInfoStoreScope;
import com.pinshang.qingyun.xda.product.dto.category.XdaCategoryODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectXdaCommodityDropdownInfoListIDTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.XdaCommodityDropdownInfoODTO;
import com.pinshang.qingyun.xda.product.service.XdaCategoryClient;
import com.pinshang.qingyun.xda.product.service.XdaCommodityTextClient;

/**
 * 鲜达资源位
 */
@Service
public class XdaPositionInfoService {
	
	@Value("${pinshang.img-server-url}")
    private String imgServerUrl;
	
	@Autowired
    private CodeClient codeClient;
	
	@Autowired
    private RedissonClient redissonClient;
	
	@Autowired
	private XdaCategoryClient xdaCategoryClient;
	
	@Autowired
	private XdaH5TemplateMapper xdaH5TemplateMapper;
	
    @Autowired
    private XdaPositionInfoMapper xdaPositionInfoMapper;
    
	@Autowired
	private XdaCommodityTextClient xdaCommodityTextClient;

    @Autowired
	private XdaPositionInfoLogMapper xdaPositionInfoLogMapper;
    
    @Autowired
    private XdaPositionInfoBlockMapper xdaPositionInfoBlockMapper;
    
    @Autowired
    private XdaPositionInfoParamValidator xdaPositionInfoParamValidator;
    
    @Autowired
    private XdaPositionInfoCommodityMapper xdaPositionInfoCommodityMapper;
    
    @Autowired
	private XdaPositionInfoStoreScopeMapper xdaPositionInfoStoreScopeMapper;
    
	/**
	 * 分页查询  资源位信息  列表
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(readOnly = true)
	public PageInfo<PositionInfoODTO> selectPositionInfoPage(SelectPositionInfoPageIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		PageInfo<PositionInfoODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
			xdaPositionInfoMapper.selectPositionInfoList(idto);
		});
		return pageInfo;
	}
	
	/**
	 * 查询  资源位信息日志信息  列表
	 * 
	 * @param positionInfoId
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<PositionInfoLogInfoODTO> selectPositionInfoLogInfoList(Long positionInfoId) {
		if (null == positionInfoId) {
			return Collections.emptyList();
		}
		return xdaPositionInfoLogMapper.selectPositionInfoLogInfoList(positionInfoId);
	}

	/**
	 * 查询  资源位详情
	 * 
	 * @param positionInfoId
	 * @return
	 */
	@Transactional(readOnly = true)
	public PositionDetailODTO selectPositionDetail(Long positionInfoId) {
		QYAssert.isTrue(null != positionInfoId, "参数有误!");
		XdaPositionInfo positionInfo = this.getPositionInfo(positionInfoId);
		PositionDetailODTO result = BeanCloneUtils.copyTo(positionInfo, PositionDetailODTO.class);
		result.setTargetTypeId(null == positionInfo.getTargetTypeId()? null: positionInfo.getTargetTypeId() + "");
		result.setPicTargetTypeId(null == positionInfo.getPicTargetTypeId()? null: positionInfo.getPicTargetTypeId() + "");
		
		Integer positionType = positionInfo.getPositionType();
		this.setTargetTypeTitle(result);
		
		// 客户范围
		if (IsAllStoreTypeEnums.SPECIFIC_STORE.getCode().equals(positionInfo.getIsAllStore())) {
            result.setSettlementList(xdaPositionInfoStoreScopeMapper.selectSettlementList(positionInfoId, XdaStoreScopeTypeEnums.结账客户.getCode()));
            result.setProductPriceModelList(xdaPositionInfoStoreScopeMapper.selectProductPriceModelList(positionInfoId, XdaStoreScopeTypeEnums.产品价格方案.getCode()));
			result.setStoreTypeList(xdaPositionInfoStoreScopeMapper.selectDictionaryList(positionInfoId, XdaStoreScopeTypeEnums.客户类型.getCode()));
			result.setStoreChannelList(xdaPositionInfoStoreScopeMapper.selectDictionaryList(positionInfoId, XdaStoreScopeTypeEnums.渠道.getCode()));
			result.setStoreList(xdaPositionInfoStoreScopeMapper.selectStoreList(positionInfoId, XdaStoreScopeTypeEnums.客户.getCode()));
		}
		// 积木
		if (XSAppPositionTypeEnums.积木组.getCode().equals(positionType)) {
			Example example = new Example(XdaPositionInfoBlock.class);
			example.createCriteria().andEqualTo("positionInfoId", positionInfoId).andEqualTo("positionType", positionInfo.getPositionId());
			example.orderBy("positionId").asc();
			List<XdaPositionInfoBlock> blockList = xdaPositionInfoBlockMapper.selectByExample(example);
			if (SpringUtil.isNotEmpty(blockList)) {
				List<PositionInfoBlockODTO> blockODTOList = new ArrayList<>();
				blockList.forEach(block -> {
					PositionInfoBlockODTO entry = BeanCloneUtils.copyTo(block, PositionInfoBlockODTO.class);
					entry.setTargetTypeId(null == block.getTargetTypeId()? null: block.getTargetTypeId() + "");
					entry.setTargetTypeTitle(this.getTargetTypeTitle(entry.getTargetType(), entry.getTargetTypeId()));
					entry.setVisitPicUrl(this.getVisitPicUrl(entry.getPicUrl()));
					blockODTOList.add(entry);
				});
				result.setBlockList(blockODTOList);
			}
		}
		// 商品
		if (XSAppPositionTypeEnums.推荐组.getCode().equals(positionType) || XSAppPositionTypeEnums.通栏.getCode().equals(positionType)) {
			result.setCommodityList(xdaPositionInfoCommodityMapper.selectPositionCommodityInfoList(positionInfoId));
		}
		
		result.setVisitPicUrl(this.getVisitPicUrl(result.getPicUrl()));
		return result;
	}
	private String getVisitPicUrl(String picUrl) {
    	String visitPicUrl = null;
    	if (!StringUtil.isNullOrEmpty(picUrl)) {
    		visitPicUrl = imgServerUrl + "/" + picUrl.trim();
		}
    	return visitPicUrl;
    }
	// 根据ID和企业ID查询资源位绑定信息
	private XdaPositionInfo getPositionInfo(Long positionInfoId) {
		XdaPositionInfo positionInfo = xdaPositionInfoMapper.selectOne(new XdaPositionInfo(positionInfoId));
		QYAssert.isTrue(null != positionInfo, "未查询到相关信息!");
		return positionInfo;
	}
	// 设置TargetTypeTitle
	private void setTargetTypeTitle(PositionDetailODTO result) {
		result.setTargetTypeTitle(this.getTargetTypeTitle(result.getTargetType(), result.getTargetTypeId()));
		result.setPicTargetTypeTitle(this.getTargetTypeTitle(result.getPicTargetType(), result.getPicTargetTypeId()));
	}
	// 获得标的原来名称
	private String getTargetTypeTitle(Integer targetType, String targetTypeId) {
		String targetTypeTitle = null;
		if (null != targetTypeId) {
			if (XSAppPositionInfoTargetTypeEnums.前台类目.getCode().equals(targetType)) {
				XdaCategoryODTO category = xdaCategoryClient.selectXdaCategory(Long.valueOf(targetTypeId));
				if (null != category) {
					targetTypeTitle = category.getCateName();
				}
			} else if (XSAppPositionInfoTargetTypeEnums.H5页面.getCode().equals(targetType)) {
				XdaH5Template h5Template = xdaH5TemplateMapper.selectByPrimaryKey(Long.valueOf(targetTypeId));
				if (null != h5Template) {
					targetTypeTitle = h5Template.getTemplateCode() + "（" + h5Template.getTemplateName() + "）";
				}
			} else if (XSAppPositionInfoTargetTypeEnums.单品详情页.getCode().equals(targetType)) {
				SelectXdaCommodityDropdownInfoListIDTO idto = new SelectXdaCommodityDropdownInfoListIDTO();
				idto.setCommodityId(Long.valueOf(targetTypeId));
				List<XdaCommodityDropdownInfoODTO> list = xdaCommodityTextClient.selectXdaCommodityDropdownInfoList(idto);
				if (SpringUtil.isNotEmpty(list)) {
					XdaCommodityDropdownInfoODTO odto = list.get(0);
					targetTypeTitle = odto.getCommodityCode() + "-" + odto.getCommodityAppName();
				}
			}
		}
		return targetTypeTitle;
	}

	/**
	 * 插入资源位绑定信息
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public ForceSubmitResultODTO insertPositionInfo(InsertPositionInfoIDTO idto) {
		xdaPositionInfoParamValidator.checkBeforeInsert(idto);
		
		Integer positionType = idto.getPositionType();
		Integer positionId = idto.getPositionId();
		Integer forceStatus = null == idto.getForceStatus()? ForceSubmitResultODTO.FORCE_STATUS_NO: idto.getForceStatus();
		
		// 校验强制提交状态
		ForceSubmitResultODTO temp = this.checkForceStatus(new SelectConflictPositionInfoIDTO(0L, idto), forceStatus);
		if (ForceSubmitResultODTO.FORCE_STATUS_NO.equals(forceStatus) && null != temp.getMessage()) {
			return new ForceSubmitResultODTO(ForceSubmitResultODTO.FORCE_STATUS_YES + "", temp.getMessage(), null, null, null);
		}
		
		Long createId = idto.getUserId();
		Date createTime = new Date(System.currentTimeMillis());
		
		String code = codeClient.createCode(CodeTypeEnum.XD_POSITION_CODE);
        QYAssert.notNull(code, "编号构建异常!");
        code = code.startsWith(CodeTypeEnum.XD_POSITION_CODE.getCodePrefix())? code.substring(CodeTypeEnum.XD_POSITION_CODE.getCodePrefix().length()): code;
        String positionCode = "BD" + code;
        
		XdaPositionInfo positionInfo = XdaPositionInfo.forInsert(positionCode, idto, createTime);
		xdaPositionInfoMapper.insert(positionInfo);
		Long positionInfoId = positionInfo.getId();
		if (positionInfoId > 0) {
			// 客户范围
			if (IsAllStoreTypeEnums.SPECIFIC_STORE.getCode().equals(idto.getIsAllStore())) {
				this.insertPositionInfoStoreScope(positionInfoId, idto.getStoreScopeList(), createId, createTime);
			}
			// 积木
			if (XSAppPositionTypeEnums.积木组.getCode().equals(positionType)) {
				if (XSAppPositionIdEnums.积木组02.getCode().equals(positionId)) {
					this.insertPositionInfoBlockList(positionInfoId, XSAppPositionBlockTypeEnums.积木组02.getCode(), idto.getBlockList());
				} else if (XSAppPositionIdEnums.积木组03.getCode().equals(positionId)) {
					this.insertPositionInfoBlockList(positionInfoId, XSAppPositionBlockTypeEnums.积木组03.getCode(), idto.getBlockList());
				}
			}
			// 商品
			if (XSAppPositionTypeEnums.推荐组.getCode().equals(positionType) || XSAppPositionTypeEnums.通栏.getCode().equals(positionType)) {
				this.insertPositionInfoCommodityList(positionInfoId, idto.getCommodityList());
			}
			// 长期类型：启用状态冲突的，批量停用
			this.batchDisableStatus(temp, createId, createTime);
			this.insertPositionInfoLog(positionInfoId, OperateTypeEnums.新增.getCode(), createId, createTime);
		}
		return new ForceSubmitResultODTO("", null, positionInfoId, null, null);
	}

    // 插入客户范围List
    private void insertPositionInfoStoreScope(Long positionInfoId, List<StoreScopeIDTO> storeScopeList, Long createId, Date createTime) {
    	List<XdaPositionInfoStoreScope> positionInfoStoreScopeList = new ArrayList<>();
    	storeScopeList.forEach(storeScope -> {
    		positionInfoStoreScopeList.add(new XdaPositionInfoStoreScope(positionInfoId, storeScope, createId, createTime));
    	});
    	xdaPositionInfoStoreScopeMapper.insertList(positionInfoStoreScopeList);
    }
    // 插入商品List
    private void insertPositionInfoCommodityList(Long positionInfoId, List<PositionCommodityInfoIDTO> commodityIDTOList) {
    	List<XdaPositionInfoCommodity> commodityList = new ArrayList<>();
    	commodityIDTOList.forEach(commodityIDTO -> {
			commodityList.add(new XdaPositionInfoCommodity(positionInfoId, commodityIDTO.getCommodityId(), commodityIDTO.getSortNum()));
		});
    	xdaPositionInfoCommodityMapper.insertList(commodityList);
    }
    // 插入积木组List
    private void insertPositionInfoBlockList(Long positionInfoId, Integer positionType, List<PositionInfoBlockIDTO> blockIDTOList) {
    	List<XdaPositionInfoBlock> blockList = new ArrayList<>();
    	blockIDTOList.forEach(blockIDTO -> {
			blockList.add(new XdaPositionInfoBlock(positionInfoId, positionType, blockIDTO.getPositionId(), blockIDTO.getTargetType(), blockIDTO.getTargetTypeId(), blockIDTO.getPicUrl()));
		});
    	xdaPositionInfoBlockMapper.insertList(blockList);
    }
    // 插入日志
    private void insertPositionInfoLog(Long positionInfoId, Integer operateType, Long createId, Date createTime) {
    	XdaPositionInfoLog appPositionInfoLog = new XdaPositionInfoLog(positionInfoId, operateType, createId, createTime);
    	xdaPositionInfoLogMapper.insert(appPositionInfoLog);
    }

	/**
	 * 更新资源位绑定信息
	 * 
	 * @param idto
	 * @return
	 */
    @Transactional(rollbackFor = Exception.class)
	public ForceSubmitResultODTO updatePositionInfo(UpdatePositionInfoIDTO idto) {
    	xdaPositionInfoParamValidator.checkBeforeInsert(idto);
		
		Long positionInfoId = idto.getId();
		XdaPositionInfo positionInfo = this.getPositionInfo(positionInfoId);
		
		Integer positionType = positionInfo.getPositionType();
		Integer positionId = idto.getPositionId();
		Integer termType = idto.getTermType();
		Date beginTime = idto.getBTime();
		Date endTime = idto.getETime();
		Integer oldTermType = positionInfo.getTermType();
		if (TermTypeEnums.长期.getCode().equals(oldTermType)) {
			// 长期类型的，不能更改的属性
			positionId = positionInfo.getPositionId();
			termType = oldTermType;
			beginTime = null;
			endTime = null;
		}
		
		// 校验强制提交状态
		Integer forceStatus = null == idto.getForceStatus()? ForceSubmitResultODTO.FORCE_STATUS_NO: idto.getForceStatus();
		ForceSubmitResultODTO temp = this.checkForceStatus(new SelectConflictPositionInfoIDTO(positionInfoId, idto), forceStatus);
		if (ForceSubmitResultODTO.FORCE_STATUS_NO.equals(forceStatus) && null != temp.getMessage()) {
			return new ForceSubmitResultODTO(ForceSubmitResultODTO.FORCE_STATUS_YES + "", temp.getMessage(), null, null, null);
		}
		
		Long updateId = idto.getUserId();
		Date updateTime = new Date(System.currentTimeMillis());
		XdaPositionInfo.forUpdate(positionInfo, idto, updateTime);
    	int updateCount = xdaPositionInfoMapper.updateByPrimaryKey(positionInfo);
		if (updateCount > 0) {
			// 客户范围
			xdaPositionInfoStoreScopeMapper.delete(new XdaPositionInfoStoreScope(positionInfoId));
			if (IsAllStoreTypeEnums.SPECIFIC_STORE.getCode().equals(idto.getIsAllStore())) {
				this.insertPositionInfoStoreScope(positionInfoId, idto.getStoreScopeList(), updateId, updateTime);
			}
			// 积木
			xdaPositionInfoBlockMapper.delete(new XdaPositionInfoBlock(positionInfoId));
			if (XSAppPositionTypeEnums.积木组.getCode().equals(positionType)) {
				if (XSAppPositionIdEnums.积木组02.getCode().equals(positionId)) {
					this.insertPositionInfoBlockList(positionInfoId, XSAppPositionBlockTypeEnums.积木组02.getCode(), idto.getBlockList());
				} else if (XSAppPositionIdEnums.积木组03.getCode().equals(positionId)) {
					this.insertPositionInfoBlockList(positionInfoId, XSAppPositionBlockTypeEnums.积木组03.getCode(), idto.getBlockList());
				}
			}
			// 商品
			xdaPositionInfoCommodityMapper.delete(new XdaPositionInfoCommodity(positionInfoId));
			if (XSAppPositionTypeEnums.推荐组.getCode().equals(positionType) || XSAppPositionTypeEnums.通栏.getCode().equals(positionType)) {
				this.insertPositionInfoCommodityList(positionInfoId, idto.getCommodityList());
			}
			// 长期类型：启用状态冲突的，批量停用
			this.batchDisableStatus(temp, updateId, updateTime);
			this.insertPositionInfoLog(positionInfoId, OperateTypeEnums.修改.getCode(), updateId, updateTime);
		}
		return new ForceSubmitResultODTO("", null, null, updateCount, null);
	}
	// 校验强制提交状态（新增/修改/修改状态）
	private ForceSubmitResultODTO checkForceStatus(SelectConflictPositionInfoIDTO idto, Integer forceStatus) {
		String message = null;
    	List<Long> statusConflictIdList = null;
    	Integer termType = idto.getTermType();
    	if (TermTypeEnums.长期.getCode().equals(termType)) {
    		// 长期-状态冲突
    		Example example = new Example(XdaPositionInfo.class);
    		example.createCriteria().andEqualTo("positionType", idto.getPositionType()).andEqualTo("positionId", idto.getPositionId()).andEqualTo("termType", termType).andEqualTo("status", XSAppPositionInfoStatusEnums.启用.getCode());
    		List<XdaPositionInfo> statusConflictList = xdaPositionInfoMapper.selectByExample(example);
    		if (SpringUtil.isNotEmpty(statusConflictList)) {
    			statusConflictIdList = statusConflictList.stream().map(XdaPositionInfo::getId).collect(Collectors.toList());
    			statusConflictIdList.removeIf(statusConflictId -> (null == statusConflictId || statusConflictId.equals(idto.getId())));
//    			if (ForceSubmitResultODTO.FORCE_STATUS_NO.equals(forceStatus)) {
//	    			if (SpringUtil.isNotEmpty(statusConflictIdList)) {
//	    				message = "发现该位置有其他长期绑定，启用后将以当前设置为准并且老的长期绑定将自动停用。\n确定要继续吗？";
//	    			}
//    			}
    		}
    	} else if (TermTypeEnums.短期.getCode().equals(termType)) {
    		// 短期-时间交叉
//    		if (ForceSubmitResultODTO.FORCE_STATUS_NO.equals(forceStatus)) {
//	    		List<PositionInfoODTO> conflictPositionInfoList = this.selectConflictPositionInfoList(idto);
//	    		if (SpringUtil.isNotEmpty(conflictPositionInfoList)) {
//	    			message = "发现该位置的其他绑定跟当前绑定有时间交集，交集时间段将以当前的设置为准。\n确定要继续吗？";
//	    		}
//    		}
    	}
    	return new ForceSubmitResultODTO(null, message, null, null, statusConflictIdList);
	}
	
	/**
	 * 更新资源位绑定信息状态
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public ForceSubmitResultODTO updatePositionInfoStatus(UpdatePositionInfoStatusIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
    	Long positionInfoId = idto.getId();
    	Integer newStatus = idto.getStatus();
    	QYAssert.isTrue(XSAppPositionInfoStatusEnums.启用.getCode().equals(newStatus) || XSAppPositionInfoStatusEnums.停用.getCode().equals(newStatus), "状态参数有误!");
    	
    	XdaPositionInfo positionInfo = this.getPositionInfo(positionInfoId);
    	// QYAssert.isTrue(this.isAllowUpdateStatus(positionInfo), "该记录不允许修改状态!");
    	
    	Integer originalStatus = this.getCorrectOriginalStatus(positionInfo);
    	QYAssert.isTrue(!XSAppPositionInfoStatusEnums.已过期.getCode().equals(originalStatus), "该记录已过期!");
    	
    	Integer positionType = positionInfo.getPositionType();
    	Integer positionId = positionInfo.getPositionId();
    	Integer termType = positionInfo.getTermType();
    	
    	Integer operateType = 0;
    	if (XSAppPositionInfoStatusEnums.启用.getCode().equals(newStatus)) {
    		QYAssert.isTrue(XSAppPositionInfoStatusEnums.停用.getCode().equals(originalStatus), "该记录已经处于启用状态!");
    		operateType = OperateTypeEnums.启用.getCode();
    	} else if (XSAppPositionInfoStatusEnums.停用.getCode().equals(newStatus)) {
    		QYAssert.isTrue(XSAppPositionInfoStatusEnums.启用.getCode().equals(originalStatus), "该记录已经处于停用状态!");
    		if (TermTypeEnums.长期.getCode().equals(termType)) {
    			if (XSAppPositionTypeEnums.ICON.getCode().equals(positionType)) {
    				this.checkDisableStatus(positionType, positionId, positionInfoId);
    			} else if (XSAppPositionTypeEnums.BANNER.getCode().equals(positionType)) {
    				this.checkDisableStatus(positionType, null, positionInfoId);
    			} else if (XSAppPositionTypeEnums.通栏.getCode().equals(positionType)) {
    				// 通栏，不论长期还是短期，都允许停用	—— @20200110 ByLiGang
    			} else if (XSAppPositionTypeEnums.头图.getCode().equals(positionType)) {
    				this.checkDisableStatus(positionType, positionId, positionInfoId);
    			} else if (XSAppPositionTypeEnums.服务说明栏 .getCode().equals(positionType)) {
    				this.checkDisableStatus(positionType, positionId, positionInfoId);
    			} else if (XSAppPositionTypeEnums.积木组.getCode().equals(positionType) || XSAppPositionTypeEnums.推荐组.getCode().equals(positionType) || XSAppPositionTypeEnums.横栏.getCode().equals(positionType) || XSAppPositionTypeEnums.弹框广告.getCode().equals(positionType)) {
    				// 积木组/推荐组/横栏  只有短期
    			} else {
    				QYAssert.isTrue(false, "不支持的‘资源位类型’!");
    			}
    		}
    		operateType = OperateTypeEnums.停用.getCode();
    	}
    	
		// 校验强制提交状态
    	Integer forceStatus = null == idto.getForceStatus()? ForceSubmitResultODTO.FORCE_STATUS_NO: idto.getForceStatus();
    	ForceSubmitResultODTO temp = this.checkForceStatus(this.buildSelectConflictPositionInfoIDTO(positionInfo), forceStatus);
		if (ForceSubmitResultODTO.FORCE_STATUS_NO.equals(forceStatus) && null != temp.getMessage() && XSAppPositionInfoStatusEnums.启用.getCode().equals(newStatus)) {
			return new ForceSubmitResultODTO(ForceSubmitResultODTO.FORCE_STATUS_YES + "", temp.getMessage(), null, null, null);
		}
    	
    	Long userId = idto.getUserId();
    	Date now = new Date(System.currentTimeMillis());
        int updateCount = this.updatePositionInfoStatus(positionInfoId, newStatus, userId, now);
        if (updateCount > 0) {
        	// 长期类型：启用状态冲突的，批量停用
        	if (XSAppPositionInfoStatusEnums.启用.getCode().equals(newStatus)) {
        		this.batchDisableStatus(temp, userId, now);
        	}
        	this.insertPositionInfoLog(positionInfoId, operateType, userId, now);
        }
        return new ForceSubmitResultODTO("", null, null, updateCount, null);
	}
	
	// 根据期限类型，判断是否允许停用
	private void checkDisableStatus(Integer positionType, Integer positionId, Long thisId) {
		String msg = "该资源位类型";
		Example example = new Example(XdaPositionInfo.class);
		if (null != positionId) {
			example.createCriteria()
			.andEqualTo("positionType", positionType)
			.andEqualTo("termType", TermTypeEnums.长期.getCode())
			.andEqualTo("status", XSAppPositionInfoStatusEnums.启用.getCode())
			.andEqualTo("positionId", positionId);
			msg = "该资源位";
		} else {
			example.createCriteria()
			.andEqualTo("positionType", positionType)
			.andEqualTo("termType", TermTypeEnums.长期.getCode())
			.andEqualTo("status", XSAppPositionInfoStatusEnums.启用.getCode())
			;
		}
		List<XdaPositionInfo> positionInfoList = xdaPositionInfoMapper.selectByExample(example);
		List<Long> positionInfoIdList = positionInfoList.stream().map(XdaPositionInfo:: getId).collect(Collectors.toList());
		positionInfoIdList.removeIf(piId -> (null == piId || piId.equals(thisId)));
		QYAssert.isTrue(positionInfoIdList.size() > 0, "该记录是" + msg + "的唯一长期绑定，不能停用!");
	}
	// 判断是否允许更新状态
	private boolean isAllowUpdateStatus(XdaPositionInfo positionInfo) {
		boolean isUpdateStatus = false;
		Integer termType = positionInfo.getTermType();
		if (TermTypeEnums.长期.getCode().equals(termType)) {
			isUpdateStatus = true;
		} else if (TermTypeEnums.短期.getCode().equals(termType)) {
			if (!XSAppPositionInfoStatusEnums.已过期.getCode().equals(positionInfo.getStatus())) {
				if (new Date(System.currentTimeMillis()).before(positionInfo.getEndTime())) {
					isUpdateStatus = true;
				}
			}
		}
		return isUpdateStatus;
	}
	// 修正后的原状态
	private Integer getCorrectOriginalStatus(XdaPositionInfo positionInfo) {
		Integer status = positionInfo.getStatus();
		if (XSAppPositionInfoStatusEnums.启用.getCode().equals(status)) {
			if (TermTypeEnums.短期.getCode().equals(positionInfo.getTermType())) {
				if (new Date(System.currentTimeMillis()).after(positionInfo.getEndTime())) {
					status = XSAppPositionInfoStatusEnums.已过期.getCode();
				}
			}
		}
		return status;
	}
	// 鲜食资源位绑定信息：批量停用
	private void batchDisableStatus(ForceSubmitResultODTO temp, Long userId, Date now) {
		List<Long> statusConflictIdList = (List<Long>)temp.getObjectValue();
		if (SpringUtil.isNotEmpty(statusConflictIdList)) {
			Integer disableStatus = XSAppPositionInfoStatusEnums.停用.getCode();
			statusConflictIdList.forEach(positionInfoId -> {
				this.updatePositionInfoStatus(positionInfoId, disableStatus, userId, now);
				this.insertPositionInfoLog(positionInfoId, OperateTypeEnums.停用.getCode(), userId, now);
    		});
    	}
	}
	// 鲜食资源位绑定信息：更新状态
	private int updatePositionInfoStatus(Long positionInfoId, Integer newStatus, Long updateId, Date updateTime) {
		XdaPositionInfo positionInfo = XdaPositionInfo.forUpdateStatus(positionInfoId, newStatus, updateId, updateTime);
    	return xdaPositionInfoMapper.updateByPrimaryKeySelective(positionInfo);
//    	Example example = new Example(XdaPositionInfo.class);
//    	example.createCriteria().andEqualTo("id", positionInfoId);
//     	return positionInfoMapper.updateByExampleSelective(positionInfo, example);
	}

	/**
	 * 导入查询  资源位商品信息  列表
	 * 
	 * 20191219和产品确认：
	 * 1、【商品编码】重复，无需提示用户，直接以【排序号小】的商品编码为准；
	 * 2、【排序号】仅仅是优先级，后端大小重排（这也就避免了【排序号】重复的问题）；
	 * 
	 * @param commodityList
	 * @return
	 */
	@Transactional(readOnly = true)
	public List<PositionCommodityInfoODTO> importSelectPositionCommodityInfoList(List<PositionCommodityInfoIDTO> commodityList) {
		List<PositionCommodityInfoODTO> result = new ArrayList<>();
		if (SpringUtil.isNotEmpty(commodityList)) {
			// 校验：商品编码是否重复
//			List<String> commodityCodeList = commodityList.stream().map(PositionCommodityInfoIDTO::getCommodityCode).collect(Collectors.toList());
//			this.checkCommodityCodeRepeated(commodityCodeList);
			
			// 商品编码重复，保留排序号最小的
			commodityList = trimCommodityList(commodityList);
			
			// 校验：排序值是否重复
//			List<Integer> sortNumList = commodityList.stream().map(PositionCommodityInfoIDTO::getSortNum).collect(Collectors.toList());
//			this.checkSortNumRepeated(sortNumList);
			
			// 按照排序值，重排commodityList
			this.sortCommodityList(commodityList);

			// 校验：商品编码是否存在
			List<String> commodityCodeNotExistList = new ArrayList<>();
			Map<String, Object> map = new HashMap<String, Object>();
			for (int i = 0; i < commodityList.size(); i ++) {
				PositionCommodityInfoIDTO commodity = commodityList.get(i);
				String commodityCode = commodity.getCommodityCode();
				map.put("commodityCode", commodityCode);
				PositionCommodityInfoODTO entry = xdaPositionInfoCommodityMapper.selectCommodityInfoList(map);
				if (null != entry) {
					entry.setSortNum(i + 1);
					result.add(entry);
				} else {
					commodityCodeNotExistList.add(commodityCode);
				}
			}

			// 校验：商品编码是否存在
			this.checkCommodityCodeNotExist(commodityCodeNotExistList);
		}
		return result;
	}
	// 商品编码去重：商品编码重复，保留排序号最小的
	private static List<PositionCommodityInfoIDTO> trimCommodityList(List<PositionCommodityInfoIDTO> commodityList) {
		return commodityList.stream().sorted(Comparator.comparing(o -> o.getCommodityCode() + "_" + o.getSortNum()))
				.collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<PositionCommodityInfoIDTO>(Comparator.comparing(p -> p.getCommodityCode()))), ArrayList<PositionCommodityInfoIDTO>::new));
	}
	public static void main(String[] args) {
		List<PositionCommodityInfoIDTO> commodityList = new ArrayList<>();
		commodityList.add(buildPositionCommodityInfoIDTO("002", 3));
		commodityList.add(buildPositionCommodityInfoIDTO("002", 4));
		commodityList.add(buildPositionCommodityInfoIDTO("002", 4));
		commodityList.add(buildPositionCommodityInfoIDTO("001", 2));
		commodityList.add(buildPositionCommodityInfoIDTO("001", 1));
		commodityList.add(buildPositionCommodityInfoIDTO("003", 1));
		System.out.println(commodityList);
		System.out.println(trimCommodityList(commodityList));
	}
	private static PositionCommodityInfoIDTO buildPositionCommodityInfoIDTO(String commodityCode, Integer sortNum) {
		PositionCommodityInfoIDTO o = new PositionCommodityInfoIDTO();
		o.setCommodityCode(commodityCode);
		o.setSortNum(sortNum);
		return o;
	}
	// 断言商品排序是否重复
	private void checkSortNumRepeated(List<Integer> sortNumList) {
		List<Integer> sortNumRepeatedList = sortNumList.stream()
			.collect(Collectors.toMap(e -> e,  e -> 1, (a, b) -> a + b))// 获得元素出现频率的 Map，键为元素，值为元素出现的次数
			.entrySet().stream().filter(entry -> entry.getValue() > 1)	// 过滤出元素出现次数大于 1 的 entry
			.map(entry -> entry.getKey())								// 获得 entry 的键（重复元素）对应的 Stream
			.collect(Collectors.toList());
		String repeatedErrMsg = this.getSortNumErrMsg(sortNumRepeatedList);
		QYAssert.isTrue(null == repeatedErrMsg, "以下序号重复：" + repeatedErrMsg);
	}
	// 获得商品排序重复的商品编码信息
	private String getSortNumErrMsg(List<Integer> errList) {
		String errMsg = null;
		if (SpringUtil.isNotEmpty(errList)) {
			if (errList.size() > 5) {
				String tempMsg = errList.subList(0, 5).toString();
				int length = tempMsg.length();
				errMsg = tempMsg.substring(0, length - 1) + ",..." + tempMsg.substring(length - 1);
			} else {
				errMsg = errList.toString();
			}
		}
		return errMsg;
	}
	// 排序商品列表
	private void sortCommodityList(List<PositionCommodityInfoIDTO> commodityList){  
        Collections.sort(commodityList, new Comparator<PositionCommodityInfoIDTO>() {  
            @Override  
            public int compare(PositionCommodityInfoIDTO o1, PositionCommodityInfoIDTO o2) {  
                if(o1.getSortNum().intValue() > o2.getSortNum().intValue()){  
                    return 1;  
                }  
                if(o1.getSortNum().equals(o2.getSortNum())){  
                    return 0;  
                }  
                return -1;  
            }  
        });  
    }

	// 构建  SelectConflictPositionInfoIDTO
	private SelectConflictPositionInfoIDTO buildSelectConflictPositionInfoIDTO(XdaPositionInfo positionInfo) {
		// 不校验  客户范围
		return new SelectConflictPositionInfoIDTO(positionInfo, null);
	}
    
    /**
     * 轮询：到期，更改状态
     */
	@Transactional(rollbackFor = Exception.class)
    public void batchExpireJob() {
    	Date now = new Date(System.currentTimeMillis());
    	Example selectExample = new Example(XdaPositionInfo.class);
    	selectExample.createCriteria().andEqualTo("termType", TermTypeEnums.短期.getCode()).andEqualTo("status", XSAppPositionInfoStatusEnums.启用.getCode()).andLessThan("endTime", now);
    	selectExample.selectProperties("id");
    	List<XdaPositionInfo> positionInfoList = xdaPositionInfoMapper.selectByExample(selectExample);
    	if (SpringUtil.isNotEmpty(positionInfoList)) {
    		List<Long> positionInfoIdList = positionInfoList.stream().map(XdaPositionInfo:: getId).collect(Collectors.toList());
    		
    		XdaPositionInfo updatePositionInfo = XdaPositionInfo.forUpdateStatus(null, XSAppPositionInfoStatusEnums.已过期.getCode(), -1L, now);
			Example updateExample = new Example(XdaPositionInfo.class);
			updateExample.createCriteria().andIn("id", positionInfoIdList);
			xdaPositionInfoMapper.updateByExampleSelective(updatePositionInfo, updateExample);
    	}
    }

	// 校验：商品编码是否重复
	protected void checkCommodityCodeRepeated(List<String> commodityCodeList) {
		List<String> commodityCodeRepeatedList = commodityCodeList.stream()
			.collect(Collectors.toMap(e -> e,  e -> 1, (a, b) -> a + b))// 获得元素出现频率的 Map，键为元素，值为元素出现的次数
			.entrySet().stream().filter(entry -> entry.getValue() > 1)	// 过滤出元素出现次数大于 1 的 entry
			.map(entry -> entry.getKey())								// 获得 entry 的键（重复元素）对应的 Stream
			.collect(Collectors.toList());
		String repeatedErrMsg = this.getCommodityCodeErrMsg(commodityCodeRepeatedList);
		QYAssert.isTrue(null == repeatedErrMsg, "以下商品编码重复：" + repeatedErrMsg);
	}
	// 校验：商品编码是否存在
	protected void checkCommodityCodeNotExist(List<String> commodityCodeNotExistList) {
		String notExistErrMsg = this.getCommodityCodeErrMsg(commodityCodeNotExistList);
		QYAssert.isTrue(null == notExistErrMsg, "以下商品编码不存在：" + notExistErrMsg);
	}
	// 商品编码有误时的组织形式：多于5个则枚举5个其余省略
	protected String getCommodityCodeErrMsg(List<String> errList) {
		String errMsg = null;
		if (SpringUtil.isNotEmpty(errList)) {
			if (errList.size() > 5) {
				String tempMsg = errList.subList(0, 5).toString();
				int length = tempMsg.length();
				errMsg = tempMsg.substring(0, length - 1) + ",..." + tempMsg.substring(length - 1);
			} else {
				errMsg = errList.toString();
			}
		}
		return errMsg;
	}

}
