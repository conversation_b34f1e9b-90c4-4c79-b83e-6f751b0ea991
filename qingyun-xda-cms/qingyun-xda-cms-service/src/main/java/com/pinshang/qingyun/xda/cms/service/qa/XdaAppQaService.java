package com.pinshang.qingyun.xda.cms.service.qa;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.XdOperateTypeEnums;
import com.pinshang.qingyun.base.enums.settlement.StatusEnum;
import com.pinshang.qingyun.xda.cms.dto.qa.*;
import com.pinshang.qingyun.xda.cms.mapper.qa.XdaAppQaLogMapper;
import com.pinshang.qingyun.xda.cms.mapper.qa.XdaAppQaMapper;
import com.pinshang.qingyun.xda.cms.model.qa.XdaAppQa;
import com.pinshang.qingyun.xda.cms.model.qa.XdaAppQaLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;

/**
 * @description: 鲜达App常见问题
 * @author: hhf
 * @time: 2020/12/16 10:16
 */
@Slf4j
@Service
public class XdaAppQaService {

    @Autowired
    private XdaAppQaMapper xdaAppQaMapper;
    @Autowired
    private XdaAppQaLogMapper xdaAppQaLogMapper;

    /**
     * 鲜达App常见问题列表
     * @param xdAppQaIDTO
     * @return
     */
    public PageInfo<XdaAppQaODTO> findXdaAppQaList(XdaAppQaIDTO xdAppQaIDTO){
        PageInfo<XdaAppQaODTO> pageData = PageHelper.startPage(xdAppQaIDTO.getPageNo(), xdAppQaIDTO.getPageSize()).doSelectPageInfo(() -> {
            xdaAppQaMapper.findXdaAppQaList(xdAppQaIDTO);
        });
        return pageData;
    }

    /**
     * 鲜达常见问题详细
     * @param id
     * @return
     */
    public XdaAppQa findXdaAppQaDetailsById(@RequestParam(value = "id",required = false) Long id){
        QYAssert.isTrue(null != id ," id参数异常!");
        return xdaAppQaMapper.selectByPrimaryKey(id);
    }

    /**
     * 鲜达常见问题-保存
     * @param saveIDTO
     * @return
     */
    public Long saveXdaAppQa(XdaAppQaSaveIDTO saveIDTO){
        QYAssert.isTrue(null != saveIDTO," 保存参数异常");
        QYAssert.isTrue(StringUtils.isNotEmpty(saveIDTO.getTitle())," 标题不能为空");
        QYAssert.isTrue(StringUtils.isNotEmpty(saveIDTO.getContent())," 内容不能为空");
        QYAssert.isTrue(null != saveIDTO.getSortNum()," 顺序不能为空");

        Date now = new Date();
        XdaAppQa xdaAppQa = XdaAppQa.forInsert(saveIDTO.getTitle(),saveIDTO.getContent(),saveIDTO.getSortNum(),saveIDTO.getUserId(),now);
        xdaAppQaMapper.insert(xdaAppQa);
        Long qaId = xdaAppQa.getId();

        XdaAppQaLog xdAppQaLog = XdaAppQaLog.forInsert(qaId, XdOperateTypeEnums.ADD.getCode(),saveIDTO.getUserId(),now);
        xdaAppQaLogMapper.insert(xdAppQaLog);
        return qaId;
    }

    /**
     * 鲜达常见问题-修改
     * @param editIDTO
     * @return
     */
    public Long updateXdaAppQa(@RequestBody XdaAppQaEditIDTO editIDTO){
        QYAssert.isTrue(null != editIDTO," 保存参数异常");
        QYAssert.isTrue(null != editIDTO.getId()," id参数异常");
        QYAssert.isTrue(StringUtils.isNotEmpty(editIDTO.getTitle())," 标题不能为空");
        QYAssert.isTrue(StringUtils.isNotEmpty(editIDTO.getContent())," 内容不能为空");

        Date now = new Date();
        XdaAppQa xdAppQa = XdaAppQa.forUpdate(editIDTO.getId(),editIDTO.getTitle(),editIDTO.getContent(),editIDTO.getUserId(),now);
        xdaAppQaMapper.updateByPrimaryKeySelective(xdAppQa);
        XdaAppQaLog xdAppQaLog = XdaAppQaLog.forInsert(xdAppQa.getId(),XdOperateTypeEnums.UPDATE.getCode(),editIDTO.getUserId(),now);
        xdaAppQaLogMapper.insert(xdAppQaLog);

        return editIDTO.getId();
    }

    /**
     * 鲜达常见问题-修改排序
     * @param idto
     * @return
     */
    public Long updateXdaAppQaSortNum(XdaAppQaEditSortIDTO idto){
        QYAssert.isTrue(null != idto," 参数异常");
        QYAssert.isTrue(null != idto.getId()," id参数异常");
        QYAssert.isTrue(null != idto.getSortNum()," 顺序不能为空");

        Date now = new Date();
        XdaAppQa xdAppQa = XdaAppQa.forUpdateSortNum(idto.getId(),idto.getSortNum(),idto.getUserId(),now);
        xdaAppQaMapper.updateByPrimaryKeySelective(xdAppQa);
        XdaAppQaLog xdAppQaLog = XdaAppQaLog.forInsert(xdAppQa.getId(),XdOperateTypeEnums.ORDERBY.getCode(),idto.getUserId(),now);
        xdaAppQaLogMapper.insert(xdAppQaLog);

        return idto.getId();
    }

    /**
     * 鲜达常见问题-修改状态
     * @param idto
     * @return
     */
    public Long updateXdaAppQaStatus(XdaAppQaEditStatusIDTO idto){
        QYAssert.isTrue(null != idto," 参数异常");
        QYAssert.isTrue(null != idto.getId()," id参数异常");
        QYAssert.isTrue(null != idto.getStatus()," 状态不能为空");

        Date now = new Date();
        XdaAppQa xdAppQa = XdaAppQa.forUpdateStatus(idto.getId(),idto.getStatus(),idto.getUserId(),now);
        xdaAppQaMapper.updateByPrimaryKeySelective(xdAppQa);

        //保存日志
        if(idto.getStatus().equals(StatusEnum.ENABLE.getCode())){
            XdaAppQaLog xdAppQaLog = XdaAppQaLog.forInsert(xdAppQa.getId(),XdOperateTypeEnums.ENABLED.getCode(),idto.getUserId(),now);
            xdaAppQaLogMapper.insert(xdAppQaLog);
        }else{
            XdaAppQaLog xdAppQaLog = XdaAppQaLog.forInsert(xdAppQa.getId(),XdOperateTypeEnums.DISABLED.getCode(),idto.getUserId(),now);
            xdaAppQaLogMapper.insert(xdAppQaLog);
        }

        return idto.getId();
    }
}
