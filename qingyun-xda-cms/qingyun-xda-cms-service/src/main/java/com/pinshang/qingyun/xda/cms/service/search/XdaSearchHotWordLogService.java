package com.pinshang.qingyun.xda.cms.service.search;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordLogIDTO;
import com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordLogODTO;
import com.pinshang.qingyun.xda.cms.mapper.search.XdaSearchHotWordLogMapper;
import com.pinshang.qingyun.xda.cms.model.search.XdaSearchHotWord;
import com.pinshang.qingyun.xda.cms.model.search.XdaSearchHotWordLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 热搜词日志service
 */
@Service
@Slf4j
public class XdaSearchHotWordLogService {

    @Autowired
    private XdaSearchHotWordLogMapper logMapper;

    /**
     * 保存热搜词日志
     * @param operateType
     * @param oldObj
     * @param newObj
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveHotWordLog(Integer operateType, XdaSearchHotWord oldObj, XdaSearchHotWord newObj) {
        String oldValue = processSaveValue(oldObj);
        String newValue = processSaveValue(newObj);
        XdaSearchHotWordLog log = new XdaSearchHotWordLog(operateType,oldValue,newValue);
        logMapper.insertSelective(log);
    }

    private String processSaveValue(XdaSearchHotWord xdSearchHotWord){
        if(xdSearchHotWord==null){
            return null;
        }
        StringBuffer saveStr = new StringBuffer();
        saveStr.append("热搜词：");
        saveStr.append(xdSearchHotWord.getHotWord()==null?"":xdSearchHotWord.getHotWord()+"<br/>");
        saveStr.append("火焰特效：");
        saveStr.append(xdSearchHotWord.getEffectStatusDesc()==null?"":xdSearchHotWord.getEffectStatusDesc()+"<br/>");
        saveStr.append("前台序号：");
        saveStr.append(xdSearchHotWord.getSortNum());
        return saveStr.toString();
    }

    /**
     * 分页查询热搜词日志
     * @param vo
     * @return
     */
    public PageInfo<XdaSearchHotWordLogODTO> queryHotWordLogPage(XdaSearchHotWordLogIDTO vo) {
        QYAssert.isTrue(null != vo, "参数有误!");
        return PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            logMapper.queryHotWordLogPage(vo);
        });
    }


}