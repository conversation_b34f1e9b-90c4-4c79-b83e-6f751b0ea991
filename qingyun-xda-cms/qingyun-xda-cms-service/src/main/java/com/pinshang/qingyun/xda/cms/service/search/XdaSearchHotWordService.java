package com.pinshang.qingyun.xda.cms.service.search;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordAppODTO;
import com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordODTO;
import com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordSaveIDTO;
import com.pinshang.qingyun.xda.cms.mapper.search.XdaSearchHotWordMapper;
import com.pinshang.qingyun.xda.cms.model.search.XdaSearchHotWord;
import com.pinshang.qingyun.xda.cms.model.search.XdaSearchHotWordLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 热搜词Service
 */
@Service
@Slf4j
public class XdaSearchHotWordService {
    @Autowired
    private XdaSearchHotWordMapper hotWordMapper;
    @Autowired
    private XdaSearchHotWordLogService logService;

    /**
     * 新增热搜词
     * 1.热搜词最多40个
     * 2.热搜词不能为空，不能重复
     * 3.特效不能为空
     * 4.序号默认为现有最大序号+1
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer addHotWord(XdaSearchHotWordSaveIDTO vo) {
        int count = hotWordMapper.queryCount(null);
        QYAssert.isTrue(count<40,"热搜词最多40个！");
        checkParam(vo);
        XdaSearchHotWord xdaSearchHotWord = new XdaSearchHotWord(vo.getHotWord(),vo.getEffectStatus(),vo.getSortNum());
        xdaSearchHotWord.setCreateId(FastThreadLocalUtil.getQY().getUserId());
        int saveCount = hotWordMapper.insertSelective(xdaSearchHotWord);
        //记录日志
        logService.saveHotWordLog(XdaSearchHotWordLog.XdaSearchHotWordLogTypeEnums.ADD.getCode(),null,xdaSearchHotWord);
        return saveCount;
    }

    /**
     * 修改热搜词
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer updateHotWord(XdaSearchHotWordSaveIDTO vo){
        checkParam(vo);
        XdaSearchHotWord oldObj = hotWordMapper.selectByPrimaryKey(vo.getId());
        XdaSearchHotWord xdSearchHotWord = new XdaSearchHotWord(vo.getHotWord(),vo.getEffectStatus(),vo.getSortNum());
        xdSearchHotWord.setId(vo.getId());
        Integer saveCount = hotWordMapper.updateByPrimaryKeySelective(xdSearchHotWord);
        //记录日志
        logService.saveHotWordLog(XdaSearchHotWordLog.XdaSearchHotWordLogTypeEnums.EDIT.getCode(),oldObj,xdSearchHotWord);
        return saveCount;
    }

    /**
     * 删除热搜词
     * @param hotWordId
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteHotWord(Long hotWordId){
       XdaSearchHotWord oldObj = hotWordMapper.selectByPrimaryKey(hotWordId);
       QYAssert.isTrue(oldObj != null ,"未找到热搜词！");
       Integer count = hotWordMapper.deleteByPrimaryKey(hotWordId);
       //记录日志
       logService.saveHotWordLog(XdaSearchHotWordLog.XdaSearchHotWordLogTypeEnums.DELETE.getCode(),oldObj,null);
        return count;
    }

    /**
     * 验证请求参数
     * @param vo
     */
    private void checkParam(XdaSearchHotWordSaveIDTO vo){
        QYAssert.isTrue(vo!=null && StringUtils.isNotEmpty(vo.getHotWord()),"热搜词不能为空！");
        QYAssert.notNull(vo.getEffectStatus(), "火焰特效不能为空！");
        QYAssert.notNull(vo.getSortNum(), "前台序号不能为空！");
        int hotWordCount = hotWordMapper.queryCount(vo.getHotWord());
        if(vo.getId()==null){
            QYAssert.isTrue(hotWordCount==0,"热搜词已经存在！");
        }else{
            XdaSearchHotWord xdSearchHotWord = hotWordMapper.selectByPrimaryKey(vo.getId());
            if(!xdSearchHotWord.getHotWord().equals(vo.getHotWord())){
                QYAssert.isTrue(hotWordCount==0,"热搜词已经存在！");
            }
        }
        QYAssert.isTrue(vo.getSortNum()>0 && vo.getSortNum()<=9999,"前台序号只允许输入正整数，最大9999");
    }

    /**
     * 新增热搜词默认最大序号
     * @return
     */
    public Integer queryHotWordMaxNum() {
        Integer maxNum = hotWordMapper.getMaxSortNum();
        return maxNum==9999?9999:maxNum+1;
    }

    /**
     * 热搜词列表
     * @return
     */
    public List<XdaSearchHotWordODTO> queryHotWordList() {
        return hotWordMapper.queryHotWordList();
    }

    /**
     * 热搜词列表
     * @return
     */
    public List<XdaSearchHotWordAppODTO> queryHotWordListFront() {
        List<XdaSearchHotWordODTO> entryList = hotWordMapper.queryHotWordList();
        if(SpringUtil.isNotEmpty(entryList)){
            return entryList.stream().map(XdaSearchHotWordAppODTO::convert).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
}