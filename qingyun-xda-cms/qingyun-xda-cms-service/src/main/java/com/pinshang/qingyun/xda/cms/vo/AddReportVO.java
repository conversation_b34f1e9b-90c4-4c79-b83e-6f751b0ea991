package com.pinshang.qingyun.xda.cms.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel(description = "上传检验报告参数")
public class AddReportVO implements Serializable {
    @ApiModelProperty(value = "检验报告名称")
    private String reportName;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "报告日期")
    private Date reportTime;
    @ApiModelProperty(value = "报告类型")
    private String dictCode;
    @ApiModelProperty(value = "检验人")
    private String checkUser;
    @ApiModelProperty(value = "复核人")
    private String reviewUser;
    @ApiModelProperty(value = "文件地址")
    private String fileUrl;
    @ApiModelProperty(value = "文件大小")
    private BigDecimal fileSize;

    public AddReportVO() {
    }

    public AddReportVO(String reportName, Date reportTime, String dictCode, String checkUser, String reviewUser, String fileUrl, BigDecimal fileSize) {
        this.reportName = reportName;
        this.reportTime = reportTime;
        this.dictCode = dictCode;
        this.checkUser = checkUser;
        this.reviewUser = reviewUser;
        this.fileUrl = fileUrl;
        this.fileSize = fileSize;
    }

    public String getReportName() {
        return reportName;
    }

    public void setReportName(String reportName) {
        this.reportName = reportName;
    }

    public Date getReportTime() {
        return reportTime;
    }

    public void setReportTime(Date reportTime) {
        this.reportTime = reportTime;
    }

    public String getDictCode() {
        return dictCode;
    }

    public void setDictCode(String dictCode) {
        this.dictCode = dictCode;
    }

    public String getCheckUser() {
        return checkUser;
    }

    public void setCheckUser(String checkUser) {
        this.checkUser = checkUser;
    }

    public String getReviewUser() {
        return reviewUser;
    }

    public void setReviewUser(String reviewUser) {
        this.reviewUser = reviewUser;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public BigDecimal getFileSize() {
        return fileSize;
    }

    public void setFileSize(BigDecimal fileSize) {
        this.fileSize = fileSize;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"reportName\":\"")
                .append(reportName).append('\"');
        sb.append(",\"reportTime\":\"")
                .append(reportTime).append('\"');
        sb.append(",\"dictCode\":\"")
                .append(dictCode).append('\"');
        sb.append(",\"checkUser\":\"")
                .append(checkUser).append('\"');
        sb.append(",\"reviewUser\":\"")
                .append(reviewUser).append('\"');
        sb.append(",\"fileUrl\":\"")
                .append(fileUrl).append('\"');
        sb.append(",\"fileSize\":\"")
                .append(fileSize).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
