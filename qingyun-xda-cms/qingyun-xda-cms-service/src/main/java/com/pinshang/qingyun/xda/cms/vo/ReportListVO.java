package com.pinshang.qingyun.xda.cms.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.util.Date;

@ApiModel(description = "列表搜索的参数")
public class ReportListVO implements Serializable {
    @ApiModelProperty(value = "页码",required = true)
    private Integer pageNo;
    @ApiModelProperty(value = "每页长度",required = true)
    private Integer pageSize;
    @ApiModelProperty(value = "报告名称")
    private String reportName;
    @ApiModelProperty(value = "操作人")
    private Integer createId;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "报告日期开始时间")
    private Date startTime;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    @ApiModelProperty(value = "报告日期结束时间")
    private Date endTime;

    public ReportListVO() {
    }


    public ReportListVO(Integer pageNo, Integer pageSize, String reportName, Integer createId, Date startTime, Date endTime) {
        this.pageNo = pageNo;
        this.pageSize = pageSize;
        this.reportName = reportName;
        this.createId = createId;
        this.startTime = startTime;
        this.endTime = endTime;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getReportName() {
        return reportName;
    }

    public void setReportName(String reportName) {
        this.reportName = reportName;
    }

    public Integer getCreateId() {
        return createId;
    }

    public void setCreateId(Integer createId) {
        this.createId = createId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"pageNo\":")
                .append(pageNo);
        sb.append(",\"pageSize\":")
                .append(pageSize);
        sb.append(",\"reportName\":\"")
                .append(reportName).append('\"');
        sb.append(",\"createId\":")
                .append(createId);
        sb.append(",\"startTime\":\"")
                .append(startTime).append('\"');
        sb.append(",\"endTime\":\"")
                .append(endTime).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
