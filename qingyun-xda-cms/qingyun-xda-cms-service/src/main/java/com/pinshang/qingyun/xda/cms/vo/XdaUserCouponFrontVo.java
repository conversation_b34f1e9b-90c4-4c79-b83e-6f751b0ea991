package com.pinshang.qingyun.xda.cms.vo;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.base.page.XsApiPagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class XdaUserCouponFrontVo extends Pagination {

    @ApiModelProperty("状态:0-未使用,1-已使用,2-已过期")
    private Integer status;
    @ApiModelProperty("我的优惠券类型: null = 全部,1-满减券, 2-赠品券  XSCouponClassEnum")
    private Integer type;
}
