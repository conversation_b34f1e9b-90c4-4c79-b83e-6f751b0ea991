<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration
        PUBLIC "-//mybatis.org//DTD Config 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
    <properties>
        <property name="dialect" value="mysql"/>
    </properties>
    <settings>
        <setting name="useGeneratedKeys" value="true" />
        <setting name="mapUnderscoreToCamelCase" value="true" />
<!--        <setting name="logImpl" value="STDOUT_LOGGING"/>-->
    </settings>

    <typeHandlers>
        <typeHandler handler="com.pinshang.qingyun.base.enums.EnumHandler" javaType="com.pinshang.qingyun.base.enums.YesOrNoEnums"/>
        <typeHandler handler="com.pinshang.qingyun.base.enums.EnumHandler" javaType="com.pinshang.qingyun.base.enums.xd.XdPayTypeEnum"/>
    </typeHandlers>

</configuration>