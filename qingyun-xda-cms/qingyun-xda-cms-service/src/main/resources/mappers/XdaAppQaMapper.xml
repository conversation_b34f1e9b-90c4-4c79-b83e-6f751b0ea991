<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.qa.XdaAppQaMapper">

    <!--鲜达App常见问题列表-->
    <select id="findXdaAppQaList" resultType="com.pinshang.qingyun.xda.cms.dto.qa.XdaAppQaODTO">
        SELECT
            xaq.id as id,
            xaq.title as title,
            xaq.content as content,
            xaq.status as status,
            (CASE xaq.`status` WHEN 2 THEN '停用' ELSE '启用' END) AS statusName,
            xaq.sort_num as sortNum
        FROM
            t_xda_app_qa  xaq
        <where>
            <if test="title != null and title != ''">
                AND xaq.title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="null != status">
                AND xaq.status = #{status}
            </if>
        </where>
        order by xaq.sort_num

    </select>

    <select id="findListForFront" resultType="com.pinshang.qingyun.xda.cms.dto.qa.XdaAppQaFrontODTO">
        select  id , title from t_xda_app_qa where status=1 order by sort_num
    </select>
</mapper>