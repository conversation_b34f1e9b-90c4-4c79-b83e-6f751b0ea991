<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.checkReport.CheckReportDao">
    <!-- CheckReport的resultMap,column是给数据库列起的别名,它对应property类的属性-->
    <resultMap id="result_CheckReport_Map" type="com.pinshang.qingyun.xda.cms.dto.checkReport.ReportListDTO">
        <id column="id" property="id" />
        <result column="file_url" property="fileUrl" />
        <result column="file_size" property="fileSize" />
        <result column="create_time" property="createTime" />
        <result column="dict_name" property="dictName" />
        <result column="report_time" property="reportTime" />
        <result column="check_user" property="checkUser" />
        <result column="check_name" property="reportName" />
        <result column="create_user" property="createUser" />
        <result column="review_user" property="reviewUser" />
    </resultMap>

    <!-- 数据库中表名为:t_xda_check_report的列名,as前是数据库的列明,as后是列的别名用于映射成实体类中的属性,需要注意的是别名必须与resultMap中的column别名一致 -->
    <sql id="t_xda_check_report_Column">
        t_xda_check_report.id as id
        ,t_xda_check_report.update_id as update_id
        ,t_xda_check_report.file_url as file_url
        ,t_xda_check_report.file_size as file_size
        ,t_xda_check_report.create_time as create_time
        ,t_xda_check_report.dict_code as dict_code
        ,t_xda_check_report.update_time as update_time
        ,t_xda_check_report.dict_name as dict_name
        ,t_xda_check_report.create_id as create_id
        ,t_xda_check_report.report_time as report_time
        ,t_xda_check_report.check_user as check_user
        ,t_xda_check_report.check_name as check_name
        ,t_xda_check_report.create_user as create_user
        ,t_xda_check_report.review_user as review_user
    </sql>

    <!-- 获得类名为:CheckReport对应数据库中表的数据集合 -->
    <select id="list" parameterType="com.pinshang.qingyun.xda.cms.vo.ReportListVO" resultMap="result_CheckReport_Map">
        select 
        <include refid="t_xda_check_report_Column" /> 
        from t_xda_check_report
        <where>
            <if test="reportName != null "> and check_name like concat('%',#{reportName},'%')</if>
            <if test="createId != null "> and create_id = #{createId}</if>
            <if test="startTime != null and endTime != null "> and (report_time between #{startTime} and #{endTime})</if>
            and is_delete = 0
        </where>
        order by report_time desc,dict_name asc
    </select>

    <!-- 我的检验报告 -->
    <select id="queryMyCheckReport" parameterType="com.pinshang.qingyun.xda.cms.vo.XdaMyCheckReportVO" resultMap="result_CheckReport_Map">
        select
        <include refid="t_xda_check_report_Column" />
        from t_xda_check_report t_xda_check_report
        left join t_dictionary dic on t_xda_check_report.dict_code = dic.option_code
        <where>
                1=1
            <if test="startTime != null and endTime != null ">
                and (t_xda_check_report.report_time between #{startTime} and #{endTime})
             </if>
                and t_xda_check_report.report_time > date_sub(curdate(), interval 5 day)
                and t_xda_check_report.is_delete = 0
        </where>
        order by t_xda_check_report.report_time desc, dic.id asc
    </select>

    <!-- 通过CheckReport的name获得对应数据库中表的数据对象-->
    <select id="selectByName" parameterType="java.lang.String" resultType="java.lang.String">
        select check_name from t_xda_check_report where check_name = #{name} and is_delete = 0
    </select> 

    <!-- 将CheckReport插入到对应数据库的表中,包括属性值为null的数据-->

    <insert id="insert" parameterType="com.pinshang.qingyun.xda.cms.model.checkReport.CheckReport">
        insert into t_xda_check_report(file_url,create_time,dict_code,dict_name,create_id,report_time,check_user,check_name,create_user,review_user,file_size)
        values(#{fileUrl},#{createTime},#{dictCode},#{dictName},#{createId},#{reportTime},#{checkUser},#{checkName},#{createUser},#{reviewUser},#{fileSize})
    </insert>

    <!-- 修改数据-->
    <update id="updateDelete" parameterType="com.pinshang.qingyun.xda.cms.model.checkReport.CheckReport">
        update t_xda_check_report set is_delete = 1,update_id=#{updateId},update_user=#{updateUser},update_time=#{updateTime} where id=#{id}
    </update>
    
    <select id="selectDictName" parameterType="java.lang.String" resultType="java.lang.String">
        select option_name from t_dictionary where option_code = #{dictCode}
    </select>

</mapper>