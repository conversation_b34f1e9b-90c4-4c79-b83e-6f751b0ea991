<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorXPositionCommodityMapper" >

    <select id="selectXPositionCommodityList" resultType="com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorXPositionCommodityODTO">
        SELECT
			c.id AS commodityId,
			c.commodity_code AS commodityCode,
			c.commodity_name AS commodityName,
			c.commodity_spec AS commoditySpec,
			apic.sort_num AS sortNum
		FROM t_xda_favor_x_position_commodity apic
		LEFT JOIN t_commodity c ON c.id = apic.commodity_id
		WHERE apic.favor_x_position_id = #{xPositionId}
		ORDER BY apic.sort_num ASC, LENGTH(c.commodity_code) ASC, c.commodity_code ASC
    </select>

</mapper>