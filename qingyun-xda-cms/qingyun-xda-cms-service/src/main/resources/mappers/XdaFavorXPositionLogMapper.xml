<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorXPositionLogMapper" >

    <select id="queryXPositionLogList" resultType="com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorPositionLogODTO">
        SELECT
            l.operate_type AS operateType,
            u.employee_name AS createName,
            l.create_time
        FROM t_xda_favor_x_position_log l
        LEFT JOIN t_employee_user u ON l.create_id = u.user_id
        WHERE l.favor_x_position_id = #{xPositionId}
        ORDER BY l.create_time DESC
    </select>

</mapper>