<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorXPositionMapper" >

    <select id="queryFavorXPosition" resultType="com.pinshang.qingyun.xda.cms.dto.home.XdaFavorXPositionODTO">
        SELECT
            xp.id,
            xp.position_id,
            xp.term_type,
            xp.title,
            xp.sub_title,
            xp.begin_time,
            xp.end_time,
            IFNULL(#{storeId},'') storeId,
            pc.commodity_id
        FROM
            t_xda_favor_x_position xp LEFT JOIN
            t_xda_favor_x_position_commodity pc ON pc.favor_x_position_id = xp.id
        WHERE
            xp.`status` = 1 AND
            (xp.term_type =1 OR (xp.begin_time <![CDATA[ <= ]]> now() AND xp.end_time > now()))
            <if test="storeId != null">
                <include refid="storeScopeCondition"></include>
            </if>
        ORDER BY xp.position_id ASC, xp.term_type DESC, xp.update_time DESC
    </select>

    <select id="queryFavorXPositionCommodity" resultType="com.pinshang.qingyun.xda.cms.dto.home.XdaPositionInfoCommodityODTO">
        SELECT
            pc.favor_x_position_id AS positionInfoId,
            pc.commodity_id,
            pc.sort_num
        FROM
            t_xda_favor_x_position_commodity pc
            INNER JOIN t_product_price_model_list ppml ON ppml.commodity_id = pc.commodity_id
            INNER JOIN t_store_settlement tss ON ppml.product_price_model_id = tss.product_price_model_id
            INNER JOIN t_xda_commodity_app_status cas ON cas.commodity_id = ppml.commodity_id AND cas.app_status = 0
            INNER JOIN t_product_price_model ppm ON ppm.id = ppml.product_price_model_id AND ppm.price_model_state = 1
            INNER JOIN (
                SELECT commodity_id,
                CASE WHEN DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1) THEN 1 ELSE 0 END AS isCanOrder
                FROM t_xda_order_commodity
		    ) xoc ON pc.commodity_id = xoc.commodity_id
        WHERE
            pc.favor_x_position_id = #{favorXPositionId} AND tss.store_id = #{storeId}
        ORDER BY xoc.isCanOrder DESC,pc.sort_num ASC
    </select>

    <!-- 查询特定资源位生效时间有交叉的短期记录ID列表 -->
    <select id="selectXPositionWithTimeCrossList" resultType="Long">
        SELECT api.id
        FROM t_xda_favor_x_position api
        WHERE api.term_type = 2
        AND (
        (api.begin_time BETWEEN #{beginTime} AND #{endTime}) OR
        (api.end_time   BETWEEN #{beginTime} AND #{endTime})
        )
        <![CDATA[ AND (api.status = 1 AND api.end_time >= NOW()) ]]>
        AND api.position_id = #{positionId}
    </select>

    <select id="queryFavorXPositionPage" resultType="com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorXPositionInfoODTO"
            parameterType="com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorXPositionPageIDTO">
        SELECT
            xp.id AS favorPositionId,
            xp.position_id AS positionId,
            xp.term_type AS termType,
            xp.app_channel,
            xp.mini_channel,
            xp.is_all_store AS isAllStore,
            xp.title,
            xp.sub_title,
            xp.begin_time AS beginTime,
            xp.end_time AS endTime,
            xp.update_time AS updateTime,
            xp.status
        FROM t_xda_favor_x_position xp
        <where>
            <if test="positionId != null and positionId > 0">
                AND xp.position_id= #{positionId}
            </if>
            <if test="termType != null and termType > 0">
                AND xp.term_type = #{termType}
            </if>
            <if test="storeId != null">
                <include refid="storeScopeCondition"></include>
            </if>
            <if test="title != null and title != ''">
                AND xp.title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="status != null">
                <if test="status == 1"><!-- 启用 -->
                    <![CDATA[AND (
					(xp.status = 1 AND xp.term_type = 1) OR
					(xp.status = 1 AND xp.term_type = 2 AND xp.end_time > NOW())
				)]]>
                </if>
                <if test="status == 2"><!-- 停用 -->
                    AND xp.status = 2
                </if>
                <if test="status == 3"><!-- 过期 -->
                    <![CDATA[AND (
					xp.status = 3 OR
					(xp.status = 1 AND xp.term_type = 2 AND xp.end_time < NOW())
				)]]>
                </if>
            </if>
            <if test="channel != null">
                <if test="channel == 1"><!-- APP -->
					AND xp.app_channel = 1
                </if>
                <if test="channel == 2"><!-- 小程序 -->
                    AND xp.mini_channel = 1
                </if>
            </if>
            <if test="beginTime != null and beginTime != ''">
                <![CDATA[
                AND (
                    xp.term_type = 1
                    OR (
                        xp.term_type = 2
                        AND (xp.begin_time >= #{beginTime} OR xp.end_time >= #{beginTime})
                    )
                )
                ]]>
            </if>
            <if test="endTime != null and endTime != ''">
                <![CDATA[
                AND (
                    xp.term_type = 1
                    OR (
                        xp.term_type = 2
                        AND (xp.begin_time <= #{endTime} OR xp.end_time <= #{endTime})
                    )
                )
                ]]>
            </if>
            <if test="createBeginTime != null and createBeginTime != ''">
                <![CDATA[
                    AND xp.create_time >= #{createBeginTime}
                 ]]>
            </if>
            <if test="createEndTime != null and createEndTime != ''">
                <![CDATA[
			        AND xp.create_time <= #{createEndTime}
			    ]]>
            </if>
        </where>
        ORDER BY  xp.position_id ASC, xp.update_time DESC
    </select>

    <select id="queryFavorXPositionById" resultType="com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorPositionBaseInfoODTO" >
        SELECT
            xp.id AS favorPositionId,
            xp.position_id AS positionId,
            xp.term_type AS termType,
            xp.app_channel,
            xp.mini_channel,
            xp.is_all_store AS isAllStore,
            xp.begin_time AS beginTime,
            xp.end_time AS endTime,
            xp.update_time AS updateTime,
            xp.status
        FROM t_xda_favor_x_position xp
        WHERE xp.id = #{favorPositionId}
    </select>

    <sql id = "storeScopeCondition">
        AND (
          xp.is_all_store = 0
          OR
          (xp.is_all_store = 1
            AND (
                EXISTS (
                SELECT 1
                FROM t_xda_favor_x_position_store_scope xss, t_store_settlement ss
                WHERE xp.id = xss.favor_x_position_id AND xss.ref_obj_type = 1 AND xss.ref_obj_id = ss.settlement_customer_id
                AND ss.store_id = #{storeId}
                )
                OR EXISTS(
                SELECT 1
                FROM t_xda_favor_x_position_store_scope xss, t_store_settlement ss
                WHERE xp.id = xss.favor_x_position_id AND xss.ref_obj_type = 2 AND xss.ref_obj_id = ss.product_price_model_id
                AND ss.store_id = #{storeId}
                )
                OR EXISTS(
                SELECT 1
                FROM t_xda_favor_x_position_store_scope xss, t_store s
                WHERE xp.id = xss.favor_x_position_id AND xss.ref_obj_type = 3 AND xss.ref_obj_id = s.store_type_id
                AND s.id = #{storeId}
                )
                OR EXISTS(
                SELECT 1
                FROM t_xda_favor_x_position_store_scope xss, t_store s
                WHERE xp.id = xss.favor_x_position_id AND xss.ref_obj_type = 4 AND xss.ref_obj_id = s.store_channel_id
                AND s.id = #{storeId}
                )
                OR EXISTS(
                SELECT 1
                FROM t_xda_favor_x_position_store_scope xss
                WHERE xp.id = xss.favor_x_position_id AND xss.ref_obj_type = 8 AND xss.ref_obj_id = #{storeId}
                )
            )
          )
        )
    </sql>

</mapper>