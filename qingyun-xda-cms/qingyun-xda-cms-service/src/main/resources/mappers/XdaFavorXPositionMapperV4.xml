<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorXPositionMapperV4" >
    
    <select id="queryFavorXPositionCommodityV4" resultType="com.pinshang.qingyun.xda.cms.dto.home.XdaPositionInfoCommodityODTO">
    	select outerT.* from (
        SELECT
            pc.favor_x_position_id AS positionInfoId,
            pc.commodity_id,
            pc.sort_num
            ,xoc.salesStatus
        FROM
            t_xda_favor_x_position_commodity pc
            INNER JOIN t_product_price_model_list ppml ON ppml.commodity_id = pc.commodity_id
            INNER JOIN t_store_settlement tss ON ppml.product_price_model_id = tss.product_price_model_id
            INNER JOIN t_xda_commodity_app_status cas ON cas.commodity_id = ppml.commodity_id 
                AND (
                    CASE 
                        WHEN #{isPfsStore} = true THEN cas.pf_app_status = 0
                        ELSE cas.app_status = 0
                    END
                )
            INNER JOIN t_product_price_model ppm ON ppm.id = ppml.product_price_model_id AND ppm.price_model_state = 1
            INNER JOIN (
            	SELECT tcs.commodity_id, 1 AS salesStatus
				FROM t_dc_tob_commodity_stock tcs 
				LEFT JOIN t_dc_tob_process_order_commodity_statistics tpocs ON tcs.commodity_id = tpocs.commodity_id AND tpocs.order_time = #{orderTime}
				INNER JOIN t_xda_order_commodity oc ON tcs.commodity_id = oc.commodity_id
				WHERE DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1)
                <if test="null != businessType and businessType == 10 ">
                    AND tcs.logistics_center_id = 1
                    AND (
                    <!-- 1、依据大仓库存 & 有库存 -->
                    (tcs.stock_type = 1 AND tcs.tda_stock_status = 1) OR
                    <!-- 2、不限量订购 & 有库存 -->
                    (tcs.stock_type = 2 AND tcs.tda_stock_status = 1) OR
                    <!-- 3.1、限量供应 & 限总量 & 有库存 -->
                    (tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.tda_stock_status = 1) OR
                    <!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
                    (tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
                    )
                </if>
                <if test="null != businessType and businessType == 15 ">
                    AND (
                    <!-- 1、依据大仓库存 & 有库存 -->
                    (tcs.stock_type = 1 AND tcs.national_stock_status = 1) OR
                    <!-- 2、不限量订购 & 有库存 -->
                    (tcs.stock_type = 2 AND tcs.national_stock_status = 1) OR
                    <!-- 3.1、限量供应 & 限总量 & 有库存 -->
                    (tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.national_stock_status = 1) OR
                    <!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
                    (tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
                    )
                </if>
                <if test="null != businessType and businessType == 0 ">
                    AND tcs.logistics_center_id = 1
                    AND (
                    <!-- 1、依据大仓库存 & 有库存 -->
                    (tcs.stock_type = 1 AND tcs.stock_status = 1) OR
                    <!-- 2、不限量订购 & 有库存 -->
                    (tcs.stock_type = 2 AND tcs.stock_status = 1) OR
                    <!-- 3.1、限量供应 & 限总量 & 有库存 -->
                    (tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.stock_status = 1) OR
                    <!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
                    (tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
                    )
                </if>

		    ) xoc ON pc.commodity_id = xoc.commodity_id
        WHERE
            pc.favor_x_position_id = #{favorXPositionId} AND tss.store_id = #{storeId}
        UNION ALL
        SELECT
            pc.favor_x_position_id AS positionInfoId,
            pc.commodity_id,
            pc.sort_num
            ,xoc.salesStatus
        FROM
            t_xda_favor_x_position_commodity pc
            INNER JOIN t_product_price_model_list ppml ON ppml.commodity_id = pc.commodity_id
            INNER JOIN t_store_settlement tss ON ppml.product_price_model_id = tss.product_price_model_id
            INNER JOIN t_xda_commodity_app_status cas ON cas.commodity_id = ppml.commodity_id 
                AND (
                    CASE 
                        WHEN #{isPfsStore} = true THEN cas.pf_app_status = 0
                        ELSE cas.app_status = 0
                    END
                )
            INNER JOIN t_product_price_model ppm ON ppm.id = ppml.product_price_model_id AND ppm.price_model_state = 1
            INNER JOIN (
            	SELECT tcs.commodity_id, 2 AS salesStatus
				FROM t_dc_tob_commodity_stock tcs 
				LEFT JOIN t_dc_tob_process_order_commodity_statistics tpocs ON tcs.commodity_id = tpocs.commodity_id AND tpocs.order_time = #{orderTime}
				INNER JOIN t_xda_order_commodity oc ON tcs.commodity_id = oc.commodity_id
				WHERE DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1)
                <if test="null != businessType and businessType == 10 ">
                    AND tcs.logistics_center_id = 1
                    AND !(
                    <!-- 1、依据大仓库存 & 有库存 -->
                    (tcs.stock_type = 1 AND tcs.tda_stock_status = 1) OR
                    <!-- 2、不限量订购 & 有库存 -->
                    (tcs.stock_type = 2 AND tcs.tda_stock_status = 1) OR
                    <!-- 3.1、限量供应 & 限总量 & 有库存 -->
                    (tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.tda_stock_status = 1) OR
                    <!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
                    (tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
                    )
                </if>
                <if test="null != businessType and businessType == 15 ">
                    AND !(
                    <!-- 1、依据大仓库存 & 有库存 -->
                    (tcs.stock_type = 1 AND tcs.national_stock_status = 1) OR
                    <!-- 2、不限量订购 & 有库存 -->
                    (tcs.stock_type = 2 AND tcs.national_stock_status = 1) OR
                    <!-- 3.1、限量供应 & 限总量 & 有库存 -->
                    (tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.national_stock_status = 1) OR
                    <!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
                    (tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
                    )
                </if>
                <if test="null != businessType and businessType == 0 ">
                    AND tcs.logistics_center_id = 1
                    AND !(
                    <!-- 1、依据大仓库存 & 有库存 -->
                    (tcs.stock_type = 1 AND tcs.stock_status = 1) OR
                    <!-- 2、不限量订购 & 有库存 -->
                    (tcs.stock_type = 2 AND tcs.stock_status = 1) OR
                    <!-- 3.1、限量供应 & 限总量 & 有库存 -->
                    (tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.stock_status = 1) OR
                    <!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
                    (tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
                    )
                </if>

		    ) xoc ON pc.commodity_id = xoc.commodity_id
        WHERE
            pc.favor_x_position_id = #{favorXPositionId} AND tss.store_id = #{storeId}
      	UNION ALL
        SELECT
            pc.favor_x_position_id AS positionInfoId,
            pc.commodity_id,
            pc.sort_num
            ,xoc.salesStatus
        FROM
            t_xda_favor_x_position_commodity pc
            INNER JOIN t_product_price_model_list ppml ON ppml.commodity_id = pc.commodity_id
            INNER JOIN t_store_settlement tss ON ppml.product_price_model_id = tss.product_price_model_id
            INNER JOIN t_xda_commodity_app_status cas ON cas.commodity_id = ppml.commodity_id 
                AND (
                    CASE 
                        WHEN #{isPfsStore} = true THEN cas.pf_app_status = 0
                        ELSE cas.app_status = 0
                    END
                )
            INNER JOIN t_product_price_model ppm ON ppm.id = ppml.product_price_model_id AND ppm.price_model_state = 1
            INNER JOIN (
            	SELECT oc.commodity_id, 3 AS salesStatus
				FROM t_xda_order_commodity oc
				WHERE !(DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1))
		    ) xoc ON pc.commodity_id = xoc.commodity_id
        WHERE
            pc.favor_x_position_id = #{favorXPositionId} AND tss.store_id = #{storeId}
      	) outerT
      	GROUP BY outerT.commodity_id
        ORDER BY outerT.salesStatus ASC, outerT.sort_num ASC
    </select>

</mapper>