<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorXPositionStoreScopeMapper" >


    <select id="selectSettlementList" resultType="com.pinshang.qingyun.xda.cms.dto.storeScope.StoreScopeODTO">
        SELECT
        s.id,
        s.customer_code AS optionCode,
        s.customer_name AS optionName
        FROM t_settlement s
        WHERE s.id IN (<include refid="refObjIdSQL"></include>)
        ORDER BY s.customer_code ASC
    </select>

    <!-- 查询  资源位-产品价格方案  列表 -->
    <select id="selectProductPriceModelList" resultType="com.pinshang.qingyun.xda.cms.dto.storeScope.StoreScopeODTO">
        SELECT
        ppm.id,
        ppm.price_model_code AS optionCode,
        ppm.price_model_name AS optionName
        FROM t_product_price_model ppm
        WHERE ppm.id IN (<include refid="refObjIdSQL"></include>)
        ORDER BY ppm.price_model_code ASC
    </select>

    <!-- 查询  资源位-客户  列表 -->
    <select id="selectStoreList" resultType="com.pinshang.qingyun.xda.cms.dto.storeScope.StoreScopeODTO">
        SELECT
        s.id,
        s.store_code AS optionCode,
        s.store_name AS optionName
        FROM t_store s
        WHERE s.id IN (<include refid="refObjIdSQL"></include>)
        ORDER BY s.store_code ASC
    </select>

    <!-- 查询  资源位-客户类型/渠道等  列表 -->
    <select id="selectDictionaryList" resultType="com.pinshang.qingyun.xda.cms.dto.storeScope.StoreScopeODTO">
        SELECT
        d.id,
        d.option_code AS optionCode,
        d.option_name AS optionName
        FROM t_dictionary d
        WHERE d.id IN (<include refid="refObjIdSQL"></include>)
        ORDER BY d.option_code ASC
    </select>

    <!-- 公共查询条件 -->
    <sql id="refObjIdSQL">
        SELECT xss.ref_obj_id
        FROM t_xda_favor_x_position_store_scope xss
        WHERE xss.favor_x_position_id = #{xPositionId}
    	AND xss.ref_obj_type = #{refObjType}
    </sql>

</mapper>