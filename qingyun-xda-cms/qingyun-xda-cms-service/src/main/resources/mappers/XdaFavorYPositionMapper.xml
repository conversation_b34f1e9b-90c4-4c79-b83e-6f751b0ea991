<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.favorPosition.XdaFavorYPositionMapper" >

    <select id="queryFavorYPositionPage" resultType="com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorYPositionInfoODTO"
            parameterType="com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorYPositionPageIDTO">
        SELECT
            xp.id AS favorPositionId,
            xp.position_id AS positionId,
            xp.term_type AS termType,
            xp.app_channel,
            xp.mini_channel,
            xp.is_all_store,
            xp.target_type,
            xp.target_type_id,
            xp.pic_url,
            xp.begin_time AS beginTime,
            xp.end_time AS endTime,
            xp.update_time AS updateTime,
            xp.status,
            CASE WHEN xp.target_type = 1 AND c.cate_level = 2 THEN c.parent_id
            WHEN xp.target_type = 1 AND c.cate_level = 1 THEN c.id END AS firstLevel,
            CASE WHEN xp.target_type = 1 AND c.cate_level = 2 THEN c.id END AS secondLevel,
            h5.template_name AS h5TemplateName,
            concat(cc.commodity_code,'-',ct.commodity_app_name) AS targetCommodityName
        FROM t_xda_favor_y_position xp
        LEFT JOIN t_xda_category c ON xp.target_type=1 AND c.id = xp.target_type_id
        LEFT JOIN t_xda_h5_template h5 ON xp.target_type=2 AND xp.target_type_id = h5.id
        LEFT JOIN t_xda_commodity_text ct ON xp.target_type=4 AND xp.target_type_id = ct.commodity_id
        LEFT JOIN t_commodity cc ON ct.commodity_id = cc.id
        <where>
            <if test="positionId != null and positionId > 0">
                AND xp.position_id= #{positionId}
            </if>
            <if test="termType != null and termType > 0">
                AND xp.term_type = #{termType}
            </if>
            <if test="storeId != null and storeId > 0">
                <include refid="storeScopeCondition"></include>
            </if>
            <if test="targetType != null">
                AND xp.target_type = #{targetType}
            </if>
            <if test="status != null">
                <if test="status == 1"><!-- 启用 -->
                    <![CDATA[AND (
					(xp.status = 1 AND xp.term_type = 1) OR
					(xp.status = 1 AND xp.term_type = 2 AND xp.end_time > NOW())
				)]]>
                </if>
                <if test="status == 2"><!-- 停用 -->
                    AND xp.status = 2
                </if>
                <if test="status == 3"><!-- 过期 -->
                    <![CDATA[AND (
					xp.status = 3 OR
					(xp.status = 1 AND xp.term_type = 2 AND xp.end_time < NOW())
				)]]>
                </if>
            </if>
            <if test="channel != null">
                <if test="channel == 1"><!-- APP -->
                    AND xp.app_channel = 1
                </if>
                <if test="channel == 2"><!-- 小程序 -->
                    AND xp.mini_channel = 1
                </if>
            </if>
            <if test="beginTime != null and beginTime != ''">
                <![CDATA[
                AND (
                    xp.term_type = 1
                    OR (
                        xp.term_type = 2
                        AND (xp.begin_time >= #{beginTime} OR xp.end_time >= #{beginTime})
                    )
                )
			]]>
            </if>
            <if test="endTime != null and endTime != ''">
                <![CDATA[
                AND (
                    xp.term_type = 1
                    OR (
                        xp.term_type = 2
                        AND (xp.begin_time <= #{endTime} OR xp.end_time <= #{endTime})
                    )
                )
			]]>
            </if>
            <if test="createBeginTime != null and createBeginTime != ''">
                <![CDATA[
                    AND xp.create_time >= #{createBeginTime}
                ]]>
            </if>
            <if test="createEndTime != null and createEndTime != ''">
                <![CDATA[
                    AND xp.create_time <= #{createEndTime}
                ]]>
            </if>
        </where>
        ORDER BY  xp.position_id ASC, xp.update_time DESC
    </select>

    <select id="queryHomeFavorYPositionPage" resultType="com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorYPositionInfoODTO">
        SELECT * FROM(
            SELECT
                DISTINCT xp.id AS favorPositionId,
                xp.position_id AS positionId,
                xp.term_type AS termType,
                xp.app_channel,
                xp.mini_channel,
                xp.is_all_store AS isAllStore,
                xp.target_type,
                xp.target_type_id,
                xp.pic_url,
                xp.begin_time AS beginTime,
                xp.end_time AS endTime,
                xp.update_time AS updateTime,
                xp.status,
                CASE WHEN xp.target_type = 1 AND c.cate_level = 2 THEN c.parent_id
                WHEN xp.target_type = 1 AND c.cate_level = 1 THEN c.id END AS firstLevel,
                CASE WHEN xp.target_type = 1 AND c.cate_level = 2 THEN c.id END AS secondLevel
            FROM t_xda_favor_y_position xp
            LEFT JOIN t_xda_category c ON xp.target_type=1 AND c.id = xp.target_type_id
            WHERE
                xp.`status` = 1
                <include refid="storeScopeCondition"></include>
                AND (xp.term_type =1 OR (xp.begin_time <![CDATA[<]]> now() AND xp.end_time <![CDATA[>=]]> now()))
                ORDER BY  xp.position_id ASC, xp.term_type DESC ,xp.update_time DESC
        ) tmp GROUP BY tmp.positionId
    </select>

    <!-- 查询特定资源位生效时间有交叉的短期记录ID列表 -->
    <select id="selectYPositionWithTimeCrossList" resultType="Long">
        SELECT api.id
        FROM t_xda_favor_y_position api
        WHERE api.term_type = 2
        AND (
        (api.begin_time BETWEEN #{beginTime} AND #{endTime}) OR
        (api.end_time   BETWEEN #{beginTime} AND #{endTime})
        )
        <![CDATA[ AND (api.status = 1 AND api.end_time >= NOW()) ]]>
        AND api.position_id = #{positionId}
    </select>

    <select id="queryFavorYPositionById" resultType="com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorPositionBaseInfoODTO" >
        SELECT
            xp.id AS favorPositionId,
            xp.position_id AS positionId,
            xp.term_type AS termType,
            xp.app_channel,
            xp.mini_channel,
            xp.is_all_store AS isAllStore,
            xp.begin_time AS beginTime,
            xp.end_time AS endTime,
            xp.update_time AS updateTime,
            xp.status
        FROM t_xda_favor_y_position xp
        WHERE xp.id = #{favorPositionId}
    </select>


    <select id="queryFavorYPositionDetail" resultType="com.pinshang.qingyun.xda.cms.dto.favorPosition.XdaFavorYPositionInfoODTO">
        SELECT
            xp.id AS favorPositionId,
            xp.position_id AS positionId,
            xp.term_type AS termType,
            xp.app_channel,
            xp.mini_channel,
            xp.is_all_store AS isAllStore,
            xp.target_type,
            xp.target_type_id,
            xp.pic_url,
            xp.begin_time AS beginTime,
            xp.end_time AS endTime,
            xp.update_time AS updateTime,
            xp.status,
            c.cate_name AS targetCateName,
            h5.template_name AS h5TemplateName,
            concat(cc.commodity_code,'-',cc.commodity_name) AS targetCommodityName
        FROM t_xda_favor_y_position xp
        LEFT JOIN t_xda_category c ON xp.target_type=1 AND c.id = xp.target_type_id
        LEFT JOIN t_xda_h5_template h5 ON xp.target_type=2 AND xp.target_type_id = h5.id
        LEFT JOIN t_xda_commodity_text ct ON xp.target_type=4 AND xp.target_type_id = ct.commodity_id
        LEFT JOIN t_commodity cc ON ct.commodity_id = cc.id
        WHERE xp.id = #{yPositionId}
    </select>

    <sql id = "storeScopeCondition">
        AND (
          xp.is_all_store = 0
          OR
          (xp.is_all_store = 1
            AND (
                EXISTS (
                SELECT 1
                FROM t_xda_favor_y_position_store_scope xss, t_store_settlement ss
                WHERE xp.id = xss.favor_y_position_id AND xss.ref_obj_type = 1 AND xss.ref_obj_id = ss.settlement_customer_id
                AND ss.store_id = #{storeId}
                )
                OR EXISTS(
                SELECT 1
                FROM t_xda_favor_y_position_store_scope xss, t_store_settlement ss
                WHERE xp.id = xss.favor_y_position_id AND xss.ref_obj_type = 2 AND xss.ref_obj_id = ss.product_price_model_id
                AND ss.store_id = #{storeId}
                )
                OR EXISTS(
                SELECT 1
                FROM t_xda_favor_y_position_store_scope xss, t_store s
                WHERE xp.id = xss.favor_y_position_id AND xss.ref_obj_type = 3 AND xss.ref_obj_id = s.store_type_id
                AND s.id = #{storeId}
                )
                OR EXISTS(
                SELECT 1
                FROM t_xda_favor_y_position_store_scope xss, t_store s
                WHERE xp.id = xss.favor_y_position_id AND xss.ref_obj_type = 4 AND xss.ref_obj_id = s.store_channel_id
                AND s.id = #{storeId}
                )
                OR EXISTS(
                SELECT 1
                FROM t_xda_favor_y_position_store_scope xss
                WHERE xp.id = xss.favor_y_position_id AND xss.ref_obj_type = 8 AND xss.ref_obj_id = #{storeId}
                )
            )
          )
        )
    </sql>

    <select id="selectYPositionCommodityList" resultType="Long">
        SELECT
            cr.commodity_id
        FROM
            t_store_settlement ss
            INNER JOIN t_product_price_model_list ppml ON ss.product_price_model_id = ppml.product_price_model_id
            INNER JOIN t_xda_commodity_recommend cr ON ppml.commodity_id = cr.commodity_id
            INNER JOIN t_xda_commodity_app_status cas ON cas.commodity_id = cr.commodity_id AND cas.app_status = 0
            INNER JOIN t_product_price_model ppm ON ppm.id = ppml.product_price_model_id AND ppm.price_model_state = 1
            INNER JOIN (
                SELECT commodity_id,
                CASE WHEN DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1) THEN 1 ELSE 0 END AS isCanOrder
                FROM t_xda_order_commodity
		    ) xoc ON cr.commodity_id = xoc.commodity_id
        WHERE
            ss.store_id = #{storeId}
        ORDER BY xoc.isCanOrder DESC, cr.commodity_id 
    </select>

</mapper>