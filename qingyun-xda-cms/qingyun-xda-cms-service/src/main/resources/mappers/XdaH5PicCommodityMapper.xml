<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5PicCommodityMapper">

    <!--查询H5图片商品信息-->
    <select id="findXdaH5PicCommodityListByH5TemplateId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5PicCommodityODTO">
        SELECT
            hpc.id AS id,
            hpc.pic_url AS picUrl,
            hpc.pic_name AS picName,
            c.id AS commodityId,
            c.commodity_code AS commodityCode,
            c.commodity_name AS commodityName,
            c.commodity_spec AS commoditySpec,
            c.bar_code AS barCode
        FROM
            t_xda_h5_pic_commodity hpc
        LEFT JOIN t_xda_h5_template_list htl ON hpc.id = htl.resource_id
        LEFT JOIN t_commodity c ON c.id = hpc.commodity_id
        WHERE
            htl.template_id = #{h5TemplateId}
            AND htl.resource_type = 12
        ORDER BY
            htl.position_level_sort ASC
    </select>

</mapper>