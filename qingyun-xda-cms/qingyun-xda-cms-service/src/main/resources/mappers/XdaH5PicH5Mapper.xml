<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5PicH5Mapper">

    <!--查询H5图片+h5-->
    <select id="findXdaH5PicH5ByH5TemplateId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5PicH5ODTO">
        SELECT
            hph.id AS id,
            hph.pic_url AS picUrl,
            hph.pic_name AS picName,
            t.id AS templateId,
            t.template_code AS templateCode,
            t.template_name AS templateName
        FROM
            t_xda_h5_pic_h5 hph
            LEFT JOIN t_xda_h5_template_list htl ON hph.id = htl.resource_id
            LEFT JOIN t_xda_h5_template t ON t.id = hph.template_id
        WHERE
            htl.template_id = #{h5TemplateId}
            AND htl.resource_type = 11
    </select>

</mapper>