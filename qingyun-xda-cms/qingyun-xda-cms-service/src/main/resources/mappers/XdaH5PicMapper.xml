<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5PicMapper">

    <!--查询头图详细信息-->
    <select id="findXdaH5PicEntryByH5TemplateId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5PicODTO">
        SELECT
            hp.id AS id,
            hp.pic_url AS picUrl,
            hp.pic_name AS picName
        FROM
            t_xda_h5_pic hp
            LEFT JOIN t_xda_h5_template_list htl ON hp.id = htl.resource_id
        WHERE
            htl.position_level = #{positionLevel}
            AND htl.template_id = #{h5TemplateId}
            AND htl.resource_type = 10
    </select>

</mapper>