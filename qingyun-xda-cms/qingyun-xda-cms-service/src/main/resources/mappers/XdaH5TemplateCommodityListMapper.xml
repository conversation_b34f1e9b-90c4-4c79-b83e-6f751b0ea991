<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5TemplateCommodityListMapper">

    <!--条件查询列表-->
    <select id="findXdaH5TemplateCommodityListByTemplateCommodityId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateCommodityListODTO">
        SELECT
            tcl.id,
            tcl.template_commodity_id,
            tcl.commodity_id,
            tcl.commodity_id as commodityIdStr
        from t_xda_h5_template_commodity_list tcl
        where
             tcl.template_commodity_id = #{templateCommodityId}
    </select>
</mapper>