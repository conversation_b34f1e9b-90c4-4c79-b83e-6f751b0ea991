<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5TemplateCommodityMapper">

	 <select id="getCommodityData" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabCommodityODTO">
		<![CDATA[
        SELECT
        txtp.pic_url as picUrl,
        c.id as commodityIdStr,
        c.id as commodityIdLong,
        c.id as commodityId,
        CONCAT_WS('','商品名称:',c.commodity_name,',规格:',c.commodity_spec,',条码:',c.bar_code) as commodityName,
        txt.commodity_app_name as commodityAppName,
        txt.commodity_sub_name as commoditySubName,
        c.bar_code,
        c.commodity_spec,
        txt.second_category_id AS categoryId,
        c.is_weight AS isWeight,
        c.commodity_package_spec as commodityPackageSpec,
        c.commodity_is_quick_freeze AS isQuickFreeze,
        (CASE WHEN c.commodity_is_quick_freeze=0 THEN '速冻' ELSE '' END) AS quickFreezeName,
        c.sales_box_capacity AS salesBoxCapacity,
        tag.tag_name,
        tag.tag_bg_color,
        txtp.pic_url AS defaultPicUrl,
        coml.template_commodity_id
        ]]>
        FROM t_xda_h5_template_commodity_list coml
        INNER JOIN t_commodity c on c.id =coml.commodity_id
        INNER JOIN t_xda_commodity_app_status xda on xda.commodity_id=c.id
        INNER JOIN t_xda_commodity_text txt on txt.commodity_id = c.id
		INNER JOIN t_xda_commodity_text_pic txtp on txtp.commodity_id = c.id and txtp.is_default = 1
        LEFT JOIN (SELECT id,tag_name,tag_bg_color FROM t_xda_tag WHERE status=1)as tag ON tag.id=txt.tag_id
        WHERE
        (
         CASE
         WHEN #{isPfsStore} = true THEN xda.pf_app_status = 0
         ELSE xda.app_status = 0
         END
        )
        AND coml.template_commodity_id IN
        <foreach collection="templateCommodityIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
	</select>

    <select id="getTemplateCommodityByTemplateId"  resultType="com.pinshang.qingyun.xda.cms.entry.h5.XdaH5TemplateTemplateCommoditysEntry">
        <![CDATA[ SELECT
        ht.id as templateId,
        ht.template_code,
        ht.template_content,
        ht.template_code_id,
        com.show_count,
        com.id as templateCommodityId
        ]]>
        FROM t_xda_h5_template ht
        INNER JOIN t_xda_h5_template_commodity com ON com.template_id=ht.id
        WHERE
        ht.id =#{templateId}
        ORDER BY com.id ASC
    </select>

    <select id="selectStoreSettByStoreId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaStoreSettODTO">
        <![CDATA[     SELECT
                    s.id as storeId,
                    t.settlement_customer_id,
                    t.product_price_model_id,
                    co.company_id
                    ]]>
            FROM
                t_store s
            INNER JOIN t_store_company co ON co.store_id = s.id
            INNER JOIN t_store_settlement t ON t.store_id = s.id AND t.company_id = co.company_id
            WHERE s.id=#{storeId}
             AND  s.store_type = 2
            AND s.store_status IN (0, 1)
    </select>


    <select id="selectStoreProductPriceCommodityById" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaStoreProductPriceODTO">
        SELECT
            l.commodity_id,
            l.commodity_price,
            l.product_price_model_id,
            t.pic_url
        FROM
            t_product_price_model m
        INNER JOIN t_product_price_model_list l ON l.product_price_model_id = m.id
        LEFT JOIN t_xda_commodity_text_pic t ON t.commodity_id=l.commodity_id
        WHERE m.id=#{id}
        GROUP BY l.commodity_id
    </select>


    <select id="selectPromotionProductPriceCommodity" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaPromotionProductPriceODTO" parameterType="com.pinshang.qingyun.xda.cms.dto.h5.XdaPromotionProductPriceIDTO">
       <![CDATA[
       SELECT
        o.id AS promotionId ,
        p.scope_type,
        p.type_id,
        t.limit_number,
        t.price,
        t.product_code,
        c.id AS commodityId,
        o.company_id
        ]]>
        FROM
        t_promotion o
        INNER JOIN t_promotion_scope p ON p.promotion_id = o.id
        INNER JOIN t_promotion_product t ON t.promotion_id=o.id
        INNER JOIN t_commodity c ON c.commodity_code=t.product_code
        INNER JOIN t_xda_commodity_app_status xda on xda.commodity_id=c.id
        WHERE
        o. STATUS = 0
        AND xda.app_status=0
        AND p.scope_type IN (7, 8, 9)
        AND (
                p.type_id = #{storeId}
                OR p.type_id = #{settlementCustomerId}
                OR p.type_id = #{productPriceModelId}
            )
       <!-- AND o.company_id=#{companyId} -->
        AND (
        <![CDATA[
        o.start_time <= #{nowTime}
        AND o.end_time >= #{nowTime}
        ]]>
        )
        ORDER BY p.scope_type,o.create_time
    </select>


    <select id="selectGiftProductCommodity" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaGiftProductODTO" parameterType="com.pinshang.qingyun.xda.cms.dto.h5.XdaPromotionProductPriceIDTO">
        <![CDATA[
        SELECT
        m.id AS giftModelId,
        p.scope_type,
        p.type_id,
        t.commodity_id,
        m.company_id
        ]]>
        FROM
        t_gift_model m
        INNER JOIN t_gift_model_scope p ON p.gift_model_id = m.id
        INNER JOIN t_gift_model_condition c ON c.gift_model_id=m.id
        INNER JOIN t_gift_product t ON t.gift_model_condition_id=c.id
        WHERE
        m.STATUS = 1
        AND p.scope_type IN (1, 2, 3)
        AND (
        p.type_id = #{storeId}
        OR p.type_id = #{settlementCustomerId}
        OR p.type_id = #{productPriceModelId}
        )
        AND m.company_id= #{companyId}
        AND (
        <![CDATA[
        m.begin_date <= #{nowTime}
        AND m.end_date >= #{nowTime}
        ]]>
        )
        ORDER BY p.scope_type,m.create_time
    </select>

    <select id="selectStoreDataByCodeOrName" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaStoreDataODTO" parameterType="com.pinshang.qingyun.xda.cms.dto.h5.XdaStoreDataIDTO">
        <![CDATA[     SELECT
                    s.id as storeId,
                    m.shop_name,
                    m.shop_code,
                    s.store_status,
                    (
                        CASE
                        WHEN s.store_status = 0 THEN
                            '启用'
                        WHEN s.store_status = 1 THEN
                            '停用'
                        ELSE
                            ''
                        END
                    ) AS storeStatusName,
                    s.store_code,
                    s.store_name,
                    CONCAT_WS(
                        '_',
                        s.store_code,
                        s.store_name
                    )as concatCodeAndName,
                    co.company_id
                    ]]>
            FROM
                t_store s
            INNER JOIN t_store_company co ON co.store_id = s.id
            LEFT JOIN t_md_shop m ON m.store_id = s.id
            WHERE
                s.store_type = 2
            AND s.store_status IN (0, 1)
            AND (
                s.store_code LIKE CONCAT('%', #{content}, '%')
                OR s.store_name LIKE CONCAT('%', #{content}, '%')
            )
    </select>

</mapper>