<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5TemplateListMapper">

    <select id="findXdH5PicListByTemplateId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateListODTO">
        SELECT
        htl.id as resourceId,
        hp.pic_url as picUrl,
        hp.pic_name as picName ,
        htl.position_level_sort as positionLevelSort,
        htl.resource_type as resourceType
        FROM t_xda_h5_template_list htl
        LEFT JOIN t_xda_h5_template ht ON ht.id = htl.template_id
        LEFT JOIN t_xda_h5_pic hp ON hp.id = htl.resource_id
        <where>
            <if test="templateId !=null and templateId!=''">
                and ht.id =#{templateId}
            </if>
            <if test="resourceType !=null">
                and htl.resource_type =#{resourceType}
            </if>
        </where>
        ORDER BY htl.position_level_sort
    </select>
    <select id="findXdH5PicListByH5TemplateId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateListODTO">
        SELECT
        htl.id as resourceId,
        hp.pic_url as picUrl,
        hp.pic_name as picName ,
        htl.position_level_sort as positionLevelSort,
        htl.resource_type as resourceType
        FROM t_xda_h5_template_list htl
        LEFT JOIN t_xda_h5_template ht ON ht.id = htl.template_id
        LEFT JOIN t_xda_h5_pic hp ON hp.id = htl.resource_id
        <where>
            <if test="templateId !=null and templateId!=''">
                and ht.id =#{templateId}
            </if>
            <if test="resourceType !=null">
                and htl.resource_type =#{resourceType}
            </if>
            <if test="positionLevelSort !=null">
                and htl.position_level_sort =#{positionLevelSort}
            </if>
        </where>
        ORDER BY htl.position_level_sort
    </select>

    <select id="findXdH5PicCommodityListByTemplateId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateListODTO">
        SELECT
        DISTINCT htl.id as resourceId,
        hpc.pic_url as picUrl,
        hpc.pic_name as picName ,
        c.id as commodityId,
        c.id as commodityIdStr,
        CONCAT_WS('','商品名称:',c.commodity_name,',规格:',c.commodity_spec,',条码:',c.bar_code) as commodityName,
        htl.position_level_sort as positionLevelSort,
        htl.resource_type as resourceType,
        txt.commodity_app_name as commodityAppName,
        txt.commodity_sub_name as commoditySubName,
        tag.tag_bg_color tagBgColor,
        tag.tag_name as tagName,
        c.commodity_is_quick_freeze AS isQuickFreeze,
        (CASE WHEN c.commodity_is_quick_freeze=0 THEN '速冻' ELSE '' END) AS quickFreezeName,
        c.sales_box_capacity AS salesBoxCapacity
        FROM t_xda_h5_template_list htl
        LEFT JOIN t_xda_h5_template ht ON ht.id = htl.template_id
        LEFT JOIN t_xda_h5_pic_commodity hpc ON hpc.id = htl.resource_id
        LEFT JOIN t_commodity c on c.id = hpc.commodity_id
        LEFT JOIN t_xda_commodity_text txt on txt.commodity_id = c.id
        LEFT JOIN t_xda_tag tag ON tag.id = txt.tag_id
        INNER JOIN t_xda_commodity_app_status xda on xda.commodity_id=c.id
        <where>
            xda.app_status=0
            <if test="templateId !=null and templateId!=''">
                and ht.id =#{templateId}
            </if>
            <if test="resourceType !=null">
                and htl.resource_type =#{resourceType}
            </if>

        </where>
        ORDER BY htl.position_level_sort
    </select>

    <select id="findXdH5PicH5ListByTemplateId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateListODTO">
        SELECT
        htl.id as resourceId,
        hph.pic_url as picUrl,
        hph.pic_name as picName ,
        hph.template_id as templateId,
        htl.position_level_sort as positionLevelSort,
        htl.resource_type as resourceType
        FROM t_xda_h5_template_list htl
        LEFT JOIN t_xda_h5_template ht ON ht.id = htl.template_id
        LEFT JOIN t_xda_h5_pic_h5 hph ON hph.id = htl.resource_id
        <where>
            <if test="templateId !=null and templateId!=''">
                and ht.id =#{templateId}
            </if>
            <if test="resourceType !=null">
                and htl.resource_type =#{resourceType}
            </if>
        </where>
        ORDER BY htl.position_level_sort
    </select>

    <select id="findXdH5TemplateTabListByTemplateId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabODTO">
        SELECT
        htl.resource_id as id,
        htt.tab_name as tabName,
        htt.app_show_num as appShowNum,
        htt.pic_url as picUrl,
        htl.resource_type as resourceType
        FROM t_xda_h5_template_list htl
        LEFT JOIN t_xda_h5_template ht ON ht.id = htl.template_id
        LEFT JOIN t_xda_h5_template_tab htt ON htt.id = htl.resource_id
        <where>
            <if test="templateId !=null and templateId!=''">
                and ht.id =#{templateId}
            </if>
            <if test="resourceType !=null">
                and htl.resource_type =#{resourceType}
            </if>
        </where>
        ORDER BY htl.position_level_sort
    </select>

</mapper>