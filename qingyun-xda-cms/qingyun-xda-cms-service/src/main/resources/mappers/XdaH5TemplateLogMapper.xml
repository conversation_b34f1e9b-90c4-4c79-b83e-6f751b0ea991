<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5TemplateLogMapper">

    <!--鲜达-H5模板日志列表-->
    <select id="findXdaH5TemplateLogListByParams" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateLogODTO">
        SELECT
            h5tl.id AS id,
            h5t.template_code_id AS templateCodeId,
            h5t.template_code AS templateCode,
            h5t.template_name AS templateName,
            h5tl.operate_type AS operateType,
            h5tl.create_time AS createTime,
            u.employee_name AS createName
        FROM
            t_xda_h5_template_log h5tl
        LEFT JOIN t_xda_h5_template h5t ON h5t.id = h5tl.template_id
        LEFT JOIN t_employee_user u ON u.user_id = h5tl.create_id
        <where>
            <if test="templateName != null and templateName != ''">
                and h5t.template_name like concat('%',#{templateName},'%')
            </if>
            <if test="templateCode != null and templateCode != ''">
                and h5t.template_code like concat('%',#{templateCode},'%')
            </if>
            <if test="operateType != null">
                and h5tl.operate_type = #{operateType}
            </if>
            <if test="userId != null">
                and h5tl.create_id = #{userId}
            </if>
            <if test="beginTime != null and beginTime != '' and endTime != null and endTime != ''">
                AND h5tl.create_time BETWEEN #{beginTime} AND #{endTime}
            </if>
        </where>
        order by h5tl.create_time desc
    </select>

</mapper>
