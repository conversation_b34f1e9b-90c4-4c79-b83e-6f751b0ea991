<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5TemplateMapper">

    <!--h5 列表-->
    <select id="findXdaH5TemplateListByParams" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateODTO">
        SELECT
            h5t.id AS id,
            h5t.template_code_id AS templateCodeId,
            h5t.template_code AS templateCode,
            h5t.template_name AS templateName,
            h5t.create_time AS createTime,
            (CASE h5t.`status` WHEN 0 THEN '停用' ELSE '启用' END) AS statusName,
            h5t.status AS status,
            u.employee_name AS createName
        FROM
            t_xda_h5_template h5t
            LEFT JOIN t_employee_user u ON u.user_id = h5t.create_id
        <where>
            <if test="templateName != null and templateName != ''">
                and h5t.template_name like concat('%',#{templateName},'%')
            </if>
            <if test="templateCode != null and templateCode != ''">
                and h5t.template_code like concat('%',#{templateCode},'%')
            </if>
            <if test="status != null">
                and h5t.status = #{status}
            </if>
            <if test="templateType != null">
                <choose>
                    <when test="templateType == 1">
                        AND h5t.template_code_id != 7
                    </when>
                    <when test="templateType == 2">
                        AND h5t.template_code_id = 7
                    </when>
                    <otherwise>
                        AND 1 = 2
                    </otherwise>
                </choose>
            </if>
        </where>
        order by h5t.update_time desc
    </select>

    <!--h5 模板 详情-->
    <select id="findXdaH5TemplateDetailsById" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateDetailsODTO">
        SELECT
            t.id AS id,
            t.template_code AS templateCode,
            t.template_name AS templateName,
            t.template_code_id AS templateCodeId,
            t.template_content AS templateContent,
            tsc.template_name AS templateStyleName,
            tsc.url AS url
        FROM
            t_xda_h5_template t
            LEFT JOIN t_xda_h5_template_style_code tsc ON tsc.id = t.template_code_id
        where t.id = #{id};
    </select>

    <!--添加H5 选择商品,条件查询商品列表  前联前置仓体系-->
    <select id="addXdaH5SelectCommodityByParams" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5AddSelectCommodityODTO">
        SELECT
            c.id as commodityId,
            c.commodity_code,
            c.commodity_name,
            c.commodity_spec,
            (CASE c.commodity_state WHEN 0 THEN '停用' ELSE '启用' END) AS statusName,
            c.bar_code,
            c.commodity_code_siss
        FROM  t_commodity c
        <where>
            <if test="str != null and str != ''">
                and (c.commodity_code like concat('%',#{str},'%')
                or c.commodity_spec like concat('%',#{str},'%')
                OR c.commodity_name like concat('%',#{str},'%'))
            </if>
            <if test="barCode != null and barCode != ''">
                and c.bar_code like concat('%',#{barCode},'%')
            </if>
        </where>
    </select>

    <!--根据商品编码查询商品信息-->
    <select id="addXdaH5QueryCommodityByCommodityCode" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5AddSelectCommodityODTO">
        select
            c.id as commodityId,
            c.commodity_code,
            c.commodity_name,
            c.commodity_spec
        from
            t_commodity c
        where c.commodity_code = #{commodityCode};
    </select>


    <!--添加H5 根据商品编码集合查询商品信息-->
    <select id="addXdaH5QueryCommodityDetailsByCommodityCodeList" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5AddSelectCommodityODTO">
        select
            c.id as commodityId,
            c.commodity_code,
            c.commodity_name,
            c.commodity_spec
        from
            t_commodity c
        where
            c.commodity_code in
            <foreach collection="commodityCodeList" item="commodityCode" open="(" close=")" separator=",">
                #{commodityCode}
            </foreach>
        order by
            field(commodity_code,
            <foreach collection="commodityCodeList" item="commodityCode" separator=",">
                #{commodityCode}
            </foreach>
        )
    </select>
    <select id="addPfH5QueryCommodityDetailsByCommodityCodeList" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5AddSelectCommodityODTO">
        select
        c.id as commodityId,
        c.commodity_code,
        c.commodity_name,
        c.commodity_spec
        from
        t_commodity c
        where
        c.product_type != 2
        and c.commodity_code in
        <foreach collection="commodityCodeList" item="commodityCode" open="(" close=")" separator=",">
            #{commodityCode}
        </foreach>
        order by
        field(commodity_code,
        <foreach collection="commodityCodeList" item="commodityCode" separator=",">
            #{commodityCode}
        </foreach>
        )
    </select>

</mapper>