<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5TemplateStyleCodeMapper">

    <!--模板list-->
    <select id="findXdaH5TemplateStyleCodeList" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateStyleCodeODTO">
        select
            tsc.id as id,
            tsc.id AS value,
            tsc.template_name as label,
            tsc.template_name AS templateName,
            tsc.url AS url
        from t_xda_h5_template_style_code tsc
        where tsc.id != 7;
    </select>
</mapper>