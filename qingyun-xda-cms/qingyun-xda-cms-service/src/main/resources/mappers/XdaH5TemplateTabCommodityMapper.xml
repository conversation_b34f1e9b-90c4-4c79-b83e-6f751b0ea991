<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5TemplateTabCommodityMapper">

    <!--通过商品codes 获取 商品信息-->
    <select id="findCommodityIdByCommodityCodeList" resultType="com.pinshang.qingyun.xda.cms.model.h5.XdaH5TemplateTabCommodity">
        select
            c.id as commodityId
        from
            t_commodity c
        where c.commodity_code in
            <foreach collection="commodityCodeList" item="commodityCode" open="(" close=")" separator=",">
                #{commodityCode}
            </foreach>
        order by
            field(commodity_code,
            <foreach collection="commodityCodeList" item="commodityCode" separator=",">
                #{commodityCode}
            </foreach>
            )
    </select>

    <select id="findCommodityListByCommodityCodeAndShopId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabCommodityODTO">
        select
        c.id as commodityId,
        c.id AS commodityIdLong,
        c.commodity_code as commodityCode,
        c.commodity_name as commodityName,
        ct.commodity_app_name as commodityAppName,
        ct.commodity_sub_name as commoditySubName,
        c.commodity_spec as commoditySpec,
        ct.second_category_id as categoryId,
        c.is_weight as isWeight,
        c.commodity_package_spec as commodityPackageSpec,
        tag.tag_bg_color tagBgColor,
        tag.tag_name as tagName,
        c.commodity_is_quick_freeze AS isQuickFreeze,
        (CASE WHEN c.commodity_is_quick_freeze=0 THEN '速冻' ELSE '' END) AS quickFreezeName,
        c.sales_box_capacity AS salesBoxCapacity
        from t_commodity c
        LEFT JOIN t_xda_commodity_text ct on ct.commodity_id = c.id
        LEFT JOIN t_xda_tag tag ON tag.id = ct.tag_id
        INNER JOIN t_xda_commodity_app_status xda on xda.commodity_id=c.id
        WHERE xda.app_status=0
        and c.commodity_code in
        <foreach collection="commodityCodeList" item="commodityCode" open="(" close=")" separator=",">
            #{commodityCode}
        </foreach>
        order by field(commodity_code,
        <foreach collection="commodityCodeList" item="commodityCode" separator=",">
            #{commodityCode}
        </foreach>
        )
        <if test="showNum !=null">
            limit #{showNum}
        </if>
    </select>

    <!--通过商品ids 获取 商品信息-->
    <select id="findCommodityIdByCommodityIdList" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabCommodityODTO">
        select
        c.id as commodityId,
        c.id AS commodityIdLong,
        c.commodity_code as commodityCode,
        c.commodity_name as commodityName,
        ct.commodity_app_name as commodityAppName,
        ct.commodity_sub_name as commoditySubName,
        c.commodity_spec as commoditySpec,
        ct.second_category_id as categoryId,
        c.is_weight as isWeight,
        c.commodity_package_spec as commodityPackageSpec,
        tag.tag_bg_color tagBgColor,
        tag.tag_name as tagName,
        c.commodity_is_quick_freeze AS isQuickFreeze,
        (CASE WHEN c.commodity_is_quick_freeze=0 THEN '速冻' ELSE '' END) AS quickFreezeName,
        c.sales_box_capacity AS salesBoxCapacity
        from t_commodity c
        LEFT JOIN t_xda_commodity_text ct on ct.commodity_id = c.id
        LEFT JOIN t_xda_tag tag ON tag.id = ct.tag_id
        INNER JOIN t_xda_commodity_app_status xda on xda.commodity_id=c.id
        where xda.app_status=0
        and c.id in
        <foreach collection="commodityIdList" item="commodityId" open="(" close=")" separator=",">
            #{commodityId}
        </foreach>
        order by field(c.id ,
        <foreach collection="commodityIdList" item="commodityId"  separator=",">
            #{commodityId}
        </foreach>
        )
    </select>

    <!--查询Tab列表-->
    <select id="findXdaH5TemplateTabCommodityListByH5TemplateId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabCommodityODTO">
        SELECT
            httc.id AS id,
            httc.tab_id AS tabId,
            c.id AS commodityId,
            c.id AS commodityIdLong,
            c.id AS commodityIdStr,
            c.commodity_code AS commodityCode,
            c.commodity_name AS commodityName,
            c.commodity_spec AS commoditySpec,
            c.bar_code AS barCode
        FROM
            t_xda_h5_template_tab_commodity httc
            LEFT JOIN t_xda_h5_template_tab htt ON htt.id = httc.tab_id
            LEFT JOIN t_xda_h5_template_list htl ON htl.resource_id = htt.id
            LEFT JOIN t_commodity c ON c.id = httc.commodity_id
        WHERE
            htl.template_id = #{h5TemplateId}
            and htl.resource_type = 22
    </select>

    <!--tabId 查询tab 下 商品明细-->
    <select id="findXdaH5TemplateTabCommodityListByTabId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabCommodityODTO">
        SELECT
            c.id AS commodityId,
            c.id AS commodityIdLong,
            c.id AS commodityIdStr,
            c.commodity_code AS commodityCode,
            c.commodity_name AS commodityName,
            c.commodity_spec AS commoditySpec,
            c.bar_code AS barCode
        FROM
            t_xda_h5_template_tab_commodity httc
            LEFT JOIN t_commodity c ON c.id = httc.commodity_id
        WHERE
            httc.tab_id = #{tabId}
    </select>

    <select id="findXdH5TemplateTabCommodityListByTabIdAndShowNum" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabCommodityODTO">
        SELECT
            DISTINCT httc.id AS id,
            htt.id AS tabId,
            c.id AS commodityId,
            c.id AS commodityIdLong,
            c.id AS commodityIdStr,
            c.commodity_code AS commodityCode,
            c.commodity_name AS commodityName,
            c.commodity_spec AS commoditySpec,
            c.bar_code AS barCode ,
            ct.second_category_id AS categoryId,
            c.is_weight AS isWeight,
            pic.pic_url AS defaultPicUrl,
            ct.commodity_app_name as commodityAppName,
            ct.commodity_sub_name as commoditySubName,
            ct.tag_id  as tagId,
            tag.tag_bg_color tagBgColor,
            tag.tag_name as tagName,
            c.commodity_package_spec as commodityPackageSpec,
            c.commodity_is_quick_freeze AS isQuickFreeze,
            (CASE WHEN c.commodity_is_quick_freeze=0 THEN '速冻' ELSE '' END) AS quickFreezeName,
            c.sales_box_capacity AS salesBoxCapacity
        FROM
        t_xda_h5_template_list htl
            LEFT JOIN t_xda_h5_template ht ON ht.id = htl.template_id
            LEFT JOIN t_xda_h5_template_tab htt ON htt.id = htl.resource_id
            LEFT JOIN t_xda_h5_template_tab_commodity httc ON httc.tab_id = htt.id
            LEFT JOIN t_commodity c ON c.id = httc.commodity_id
            LEFT JOIN t_xda_commodity_text_pic pic on pic.commodity_id = c.id and pic.is_default = 1
            LEFT JOIN t_xda_commodity_text ct on ct.commodity_id = c.id
            LEFT JOIN t_xda_tag tag on tag.id = ct.tag_id
            INNER JOIN t_xda_commodity_app_status xda on xda.commodity_id=c.id
        where
        xda.app_status=0
        <if test="templateId !=null ">
            and ht.id =#{templateId}
        </if>
        <if test="tabId !=null">
            and htt.id = #{tabId}
        </if>
        <if test="resourceType !=null">
            and htl.resource_type = #{resourceType}
        </if>
        order by httc.id
        <if test="showNum !=null">
            limit #{showNum}
        </if>

    </select>

    <!--查询Tab明细-->
    <select id="findXdH5TemplateCommodityListByH5TemplateId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabCommodityODTO">

        SELECT
            DISTINCT httc.id AS id,
            htt.id AS tabId,
            c.id AS commodityId,
            c.id AS commodityIdStr,
            c.id AS commodityIdLong,
            c.commodity_code AS commodityCode,
            c.commodity_name AS commodityName,
            c.commodity_spec AS commoditySpec,
            c.bar_code AS barCode ,
            ct.second_category_id AS categoryId,
            c.is_weight AS isWeight,
            pic.pic_url AS defaultPicUrl,
            ct.commodity_app_name as commodityAppName,
            ct.commodity_sub_name as commoditySubName,
            ct.tag_id AS tagId,
            tag.tag_bg_color tagBgColor,
            tag.tag_name as tagName,
            c.commodity_package_spec as commodityPackageSpec,
            c.commodity_is_quick_freeze AS isQuickFreeze,
            (CASE WHEN c.commodity_is_quick_freeze=0 THEN '速冻' ELSE '' END) AS quickFreezeName,
            c.sales_box_capacity AS salesBoxCapacity
        FROM
        t_xda_h5_template_list htl
            LEFT JOIN t_xda_h5_template ht ON ht.id = htl.template_id
            LEFT JOIN t_xda_h5_template_tab htt ON htt.id = htl.resource_id
            LEFT JOIN t_xda_h5_template_tab_commodity httc ON httc.tab_id = htt.id
            LEFT JOIN t_commodity c ON c.id = httc.commodity_id
            LEFT JOIN t_xda_commodity_text_pic pic on pic.commodity_id = c.id and pic.is_default = 1
            LEFT JOIN t_xda_commodity_text ct on ct.commodity_id = c.id
            LEFT JOIN t_xda_tag tag ON tag.id = ct.tag_id
            INNER JOIN t_xda_commodity_app_status xda on xda.commodity_id=c.id
        where
        xda.app_status=0
        <if test="templateId  !=null">
            and ht.id =#{templateId}
        </if>
        <if test="resourceType !=null">
            and htl.resource_type = #{resourceType}
        </if>
        <if test="positionLevelSort !=null">
            and htl.position_level_sort = #{positionLevelSort}
        </if>
        ORDER BY httc.id
    </select>

    <!--查询Tab明细-->
    <select id="findH5TemplateTabCommodityListByTabId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabCommodityODTO">

        SELECT
            DISTINCT httc.id AS id,
            htt.id AS tabId,
            c.id AS commodityId,
            c.id AS commodityIdStr,
            c.id AS commodityIdLong,
            c.commodity_code AS commodityCode,
            c.commodity_name AS commodityName,
            c.commodity_spec AS commoditySpec,
            c.bar_code AS barCode ,
            ct.second_category_id AS categoryId,
            c.is_weight AS isWeight,
            pic.pic_url AS defaultPicUrl,
            ct.commodity_app_name as commodityAppName,
            ct.commodity_sub_name as commoditySubName,
            ct.tag_id AS tagId,
            tag.tag_bg_color as tagBgColor,
            tag.tag_name as tagName,
            c.commodity_package_spec as commodityPackageSpec,
            c.commodity_is_quick_freeze AS isQuickFreeze,
            (CASE WHEN c.commodity_is_quick_freeze=0 THEN '速冻' ELSE '' END) AS quickFreezeName,
            c.sales_box_capacity AS salesBoxCapacity
        FROM
        t_xda_h5_template_list htl
            LEFT JOIN t_xda_h5_template ht ON ht.id = htl.template_id
            LEFT JOIN t_xda_h5_template_tab htt ON htt.id = htl.resource_id
            LEFT JOIN t_xda_h5_template_tab_commodity httc ON httc.tab_id = htt.id
            LEFT JOIN t_commodity c ON c.id = httc.commodity_id
            LEFT JOIN t_xda_commodity_text_pic pic on pic.commodity_id = c.id and pic.is_default = 1
            LEFT JOIN t_xda_commodity_text ct on ct.commodity_id = c.id
            LEFT JOIN t_xda_tag tag ON tag.id = ct.tag_id
            INNER JOIN t_xda_commodity_app_status xda on xda.commodity_id=c.id
        where
        xda.app_status=0
        <if test="templateId !=null and templateId!=''">
            and ht.id =#{templateId}
        </if>
        <if test="tabId !=null and tabId!=''">
            and htt.id = #{tabId}
        </if>
        <if test="resourceType !=null">
            and htl.resource_type = #{resourceType}
        </if>
        order by httc.id

    </select>



    <select id="findCommodityListByCommodityIds" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabCommodityODTO">
        select
            c.id as commodityId,
            c.id AS commodityIdLong,
            c.is_weight as isWeight,
            ct.second_category_id AS secondCategoryId
        from t_xda_commodity_text ct
        LEFT JOIN  t_commodity c on ct.commodity_id = c.id
        WHERE 1=1
        and ct.commodity_id in
        <foreach collection="commodityIdList" item="commodityId" open="(" close=")" separator=",">
            #{commodityId}
        </foreach>
    </select>

</mapper>