<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5TemplateTabMapper">

    <!--查询Tab列表-->
    <select id="findXdaH5TemplateTabListByByH5TemplateId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateTabODTO">
        SELECT
            htt.id AS id,
            htt.tab_name AS tabName,
            htt.app_show_num AS appShowNum,
            htt.commodity_num AS commodityNum,
            htt.commodity_num AS goodsCount,
            htt.pic_url AS picUrl,
            htt.tab_type AS tabType
        FROM
            t_xda_h5_template_tab htt
            LEFT JOIN t_xda_h5_template_list htl ON htt.id = htl.resource_id
        WHERE
            htl.template_id = #{h5TemplateId}
            AND htl.resource_type in(20,21)
    </select>
</mapper>