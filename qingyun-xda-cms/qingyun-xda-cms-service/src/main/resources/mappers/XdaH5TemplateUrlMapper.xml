<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.h5.XdaH5TemplateUrlMapper">

    <!--查询Tab列表-->
    <select id="findXdaH5TemplateUrlByTemplateId" resultType="com.pinshang.qingyun.xda.cms.dto.h5.XdaH5TemplateUrlODTO">
        SELECT
            h5tu.id AS id,
            h5tu.template_id AS templateId,
            h5tu.page_url AS pageUrl
        FROM
            t_xda_h5_template_url h5tu
        <where>
            <if test="templateId != null">
                and h5tu.template_id = #{templateId}
            </if>
        </where>
    </select>
</mapper>