<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.popup.XdaPopupMsgLogMapper">

    <!--弹框通知id 查询日志-->
    <select id="findXdaPopupMsgLogListByPopupMsgId" resultType="com.pinshang.qingyun.xda.cms.dto.popup.XdaPopupMsgLogODTO">
        SELECT
            pml.id,
            pml.popup_msg_id,
            pml.operate_type,
            pml.create_id,
            pml.create_time as createTimeDate,
            eu.employee_name as createName
        FROM
        t_xda_popup_msg_log pml
        LEFT JOIN t_employee_user eu ON eu.user_id = pml.create_id
        where pml.popup_msg_id = #{popupMsgId}
        order by pml.create_time desc
    </select>

</mapper>