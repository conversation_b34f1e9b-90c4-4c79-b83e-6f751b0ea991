<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.popup.XdaPopupMsgMapper">

    <select id="findXdPopupMsgListByParams" resultType="com.pinshang.qingyun.xda.cms.dto.popup.XdaPopupMsgODTO">
        SELECT
            pm.id,
            pm.msg_no,
            pm.msg_summary,
            pm.msg_way,
            pm.begin_time,
            pm.end_time,
            pm.is_all_store,
            pm.status
        FROM
            t_xda_popup_msg pm
        <where>
            <if test=" null != msgNo and msgNo != '' ">
                and pm.msg_no LIKE CONCAT('%', #{msgNo}, '%')
            </if>
            <if test=" null != msgSummary and msgSummary != '' ">
                and pm.msg_summary LIKE CONCAT('%', #{msgSummary}, '%')
            </if>
            <if test="beginTime != null and beginTime != ''">
                <![CDATA[
                    AND (
                        DATE_FORMAT(pm.begin_time, '%Y-%m-%d') >= DATE_FORMAT(#{beginTime}, '%Y-%m-%d') OR
                        DATE_FORMAT(pm.end_time, '%Y-%m-%d') >= DATE_FORMAT(#{beginTime}, '%Y-%m-%d')
                    )
                ]]>
            </if>
            <if test="endTime != null and endTime != ''">
                <![CDATA[
                    AND (
                        DATE_FORMAT(pm.begin_time, '%Y-%m-%d') <= DATE_FORMAT(#{endTime}, '%Y-%m-%d') OR
                        DATE_FORMAT(pm.end_time, '%Y-%m-%d') <= DATE_FORMAT(#{endTime}, '%Y-%m-%d')
                    )
                ]]>
            </if>
            <if test="null != msgWay">
                and pm.msg_way = #{msgWay}
            </if>
            <if test="null != status">
                and pm.status = #{status}
            </if>
            <if test="storeId != null and storeId > 0">
                AND ( pm.is_all_store = 0
                    OR ( pm.is_all_store = 1
                        AND (
                            (pm.id in(SELECT pmss.popup_msg_id FROM t_xda_popup_msg_store_scope pmss WHERE pmss.popup_msg_id = pm.id AND pmss.ref_obj_type = 1 AND pmss.ref_obj_id in
                                    <foreach collection="settlementIdList" item="settlementId" index="index" open="(" close=")" separator=","> #{settlementId} </foreach>
                            ))
                            or (pm.id in(SELECT pmss.popup_msg_id FROM t_xda_popup_msg_store_scope pmss WHERE pmss.popup_msg_id = pm.id AND pmss.ref_obj_type = 2 AND pmss.ref_obj_id in
                                    <foreach collection="productPriceModelIdList" item="productPriceModelId" index="index" open="(" close=")" separator=","> #{productPriceModelId} </foreach>
                            ))
                            or (pm.id in (SELECT pmss.popup_msg_id FROM t_xda_popup_msg_store_scope pmss WHERE pmss.popup_msg_id = pm.id AND pmss.ref_obj_type = 3 AND pmss.ref_obj_id = #{storeTypeId}
                            ))
                            or (pm.id in(SELECT pmss.popup_msg_id FROM t_xda_popup_msg_store_scope pmss WHERE pmss.popup_msg_id = pm.id AND pmss.ref_obj_type = 4 AND pmss.ref_obj_id = #{storeChannelId}
                            ))
                            or (pm.id in(SELECT pmss.popup_msg_id FROM t_xda_popup_msg_store_scope pmss WHERE pmss.popup_msg_id = pm.id AND pmss.ref_obj_type = 8 AND pmss.ref_obj_id = #{storeId}
                            ))
                        )
                    )
                )
            </if>
        </where>
        ORDER BY pm.update_time desc
    </select>

    <select id="selectStoreTypeIdAndStoreChannelIdInfoById" resultType="com.pinshang.qingyun.xda.cms.dto.popup.XdaPopupMsgStoreScopeIDTO">
        SELECT
            s.store_type_id as storeTypeId,
            s.store_channel_id as storeChannelId
        FROM
            t_store s
        WHERE
            s.id = #{storeId}
    </select>

    <!--根据客户id查询结账客户id和产品价格方案id-->
    <select id="selectSettlementIdAndProductPriceModelIdById" resultType="com.pinshang.qingyun.xda.cms.dto.popup.XdaPopupMsgStoreScopeIDTO">
        SELECT
            ss.store_id as storeId,
            ss.product_price_model_id as productPriceModelId,
            ss.settlement_customer_id as settlementId
        FROM
            t_store_settlement ss
        WHERE
            s.id = #{storeId}
    </select>

    <!-- APP弹框通知列表 -->
    <select id="queryXdPopupMsgForApp" resultType="com.pinshang.qingyun.xda.cms.dto.home.XdaPopupMsgAppODTO">
        SELECT
            pi.id AS msgId,
            pi.frequency AS msgPopupRate,
            pi.msg_way,
            pi.app_msg_title AS msgTitle,
            pi.app_msg_details AS msgDetails,
            pi.pic_url AS msgPicUrl,
            pi.is_jump AS jumpStatus,
            pi.h5_id AS msgTargetTypeId
        FROM
            t_xda_popup_msg pi
        WHERE
            #{nowTime}  BETWEEN pi.begin_time AND pi.end_time
            AND pi.`status` = 1
            <if test="storeId != null">
                AND (
                    pi.is_all_store = 0
                    OR (
                        pi.is_all_store = 1
                        AND (
                            EXISTS (
                                SELECT 1
                                FROM t_xda_popup_msg_store_scope pmss, t_store_settlement ss
                                WHERE pmss.popup_msg_id = pi.id AND pmss.ref_obj_type = 1 AND pmss.ref_obj_id = ss.settlement_customer_id
                                AND ss.store_id = #{storeId}
                            )
                            OR EXISTS(
                                SELECT 1
                                FROM t_xda_popup_msg_store_scope pmss, t_store_settlement ss
                                WHERE pmss.popup_msg_id = pi.id AND pmss.ref_obj_type = 2 AND pmss.ref_obj_id = ss.product_price_model_id
                                AND ss.store_id = #{storeId}
                            )
                            OR EXISTS(
                                SELECT 1
                                FROM t_xda_popup_msg_store_scope pmss, t_store s
                                WHERE pmss.popup_msg_id = pi.id AND pmss.ref_obj_type = 3 AND pmss.ref_obj_id = s.store_type_id
                                AND s.id = #{storeId}
                            )
                            OR EXISTS(
                                SELECT 1
                                FROM t_xda_popup_msg_store_scope pmss, t_store s
                                WHERE pmss.popup_msg_id = pi.id AND pmss.ref_obj_type = 4 AND pmss.ref_obj_id = s.store_channel_id
                                AND s.id = #{storeId}
                            )
                            OR EXISTS(
                                SELECT 1
                                FROM t_xda_popup_msg_store_scope pmss
                                WHERE pmss.popup_msg_id = pi.id AND pmss.ref_obj_type = 8 AND pmss.ref_obj_id = #{storeId}
                            )
                            OR EXISTS(
                                SELECT 1
                                FROM t_xda_popup_msg_store_scope pmss, t_store s
                                WHERE pmss.popup_msg_id = pi.id AND pmss.ref_obj_type = 9 AND pmss.ref_obj_id = s.store_company_id
                                AND s.id = #{storeId}
                            )
                        )
                    )
                )
            </if>
        ORDER BY pi.update_time DESC
        LIMIT 1
    </select>
</mapper>