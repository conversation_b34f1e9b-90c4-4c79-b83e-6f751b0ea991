<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.popup.XdaPopupMsgStoreScopeMapper">

    <!--根据弹框通知id 查询结账客户-->
    <select id="findSettlementByPopupMsgIdAndRefObjType" resultType="com.pinshang.qingyun.xda.cms.dto.storeScope.SettlementODTO">
        SELECT
            s.id,
            s.customer_code as settlementCode,
            s.customer_name as settlementName
        FROM
            t_settlement s
        WHERE
            s.id IN (<include refid="searchConditionByPopupMsgIdAndRefObjType"></include>)
    </select>

    <!--根据消息id 查询产品价格方案-->
    <select id="findProductPriceModelByPopupMsgIdAndRefObjType" resultType="com.pinshang.qingyun.xda.cms.dto.storeScope.ProductPriceModelODTO">
        SELECT
        ppm.id,
        ppm.price_model_code as productPriceModelCode,
        ppm.price_model_name as productPriceModelName
        FROM
        t_product_price_model ppm
        WHERE
        ppm.id IN (<include refid="searchConditionByPopupMsgIdAndRefObjType"></include>)
    </select>

    <!--根据消息id 查询客户-->
    <select id="findStoreByPopupMsgIdAndRefObjType" resultType="com.pinshang.qingyun.xda.cms.dto.storeScope.StoreODTO">
        SELECT
        s.id,
        s.store_code as storeCode,
        s.store_name as storeName
        FROM
        t_store s
        where
        s.id IN (<include refid="searchConditionByPopupMsgIdAndRefObjType"></include>)
    </select>

    <select id="findStoreById" resultType="com.pinshang.qingyun.xda.cms.dto.storeScope.StoreODTO">
        SELECT
        s.id,
        s.store_code as storeCode,
        s.store_name as storeName,
        s.business_type
        FROM
        t_store s
        where s.id = #{storeId}
    </select>

    <!--根据消息id 查询字典表(客户类型,渠道,线路组)-->
    <select id="findDictionaryByPopupMsgIdAndRefObjType" resultType="com.pinshang.qingyun.xda.cms.dto.storeScope.DictionaryODTO">
        SELECT
        d.id,
        d.option_code as optionCode,
        d.option_name as optionName
        FROM
        t_dictionary d
        where
        d.id IN (<include refid="searchConditionByPopupMsgIdAndRefObjType"></include>)
    </select>

    <!--公共查询条件-->
    <sql id="searchConditionByPopupMsgIdAndRefObjType">
        SELECT
            pmss.ref_obj_id
        FROM
            t_xda_popup_msg_store_scope pmss
        WHERE
            pmss.popup_msg_id = #{popupMsgId}
            AND pmss.ref_obj_type = #{refObjType}
    </sql>
</mapper>