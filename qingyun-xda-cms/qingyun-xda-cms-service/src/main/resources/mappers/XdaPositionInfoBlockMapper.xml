<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.position.XdaPositionInfoBlockMapper">

    <select id="selectSubBlockList" resultType="com.pinshang.qingyun.xda.cms.dto.home.XdaBlockODTO">
        SELECT
            pib.position_info_id,
            pib.position_id,
            pib.position_type,
            pib.pic_url,
            pib.target_type,
            pib.target_type_id,
            CASE WHEN pib.target_type = 1 AND c.cate_level = 2 THEN c.parent_id
            WHEN pib.target_type = 1 AND c.cate_level = 1 THEN c.id END AS firstLevel,
            CASE WHEN pib.target_type = 1 AND c.cate_level = 2 THEN c.id END AS secondLevel
        FROM
            t_xda_position_info_block pib LEFT JOIN t_xda_category c ON (pib.target_type=1 AND pib.target_type_id=c.id)
        WHERE
            pib.position_info_id IN
            <foreach collection="positionInfoIds" index="index" item="positionInfoId" open="(" separator="," close=")">
                #{positionInfoId}
            </foreach>
        ORDER BY pib.position_id ASC
    </select>

</mapper>