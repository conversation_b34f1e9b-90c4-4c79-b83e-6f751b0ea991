<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.position.XdaPositionInfoCommodityMapper">

	<!-- 查询  商品信息  列表 -->
	<select id="selectCommodityInfoList"
		resultType="com.pinshang.qingyun.xda.cms.dto.position.PositionCommodityInfoODTO" parameterType="map">
        SELECT
        	c.id AS commodityId,
        	c.commodity_code AS commodityCode,
        	c.commodity_name AS commodityName,
        	c.commodity_spec AS commoditySpec
		FROM t_commodity c
		<where>
		<if test="commodityId != null and commodityId > 0">
			AND c.id = #{commodityId}
		</if>
		<if test="commodityCode != null and commodityCode != ''">
			AND c.commodity_code = #{commodityCode}
		</if>
		</where>
		ORDER BY c.commodity_code ASC
    </select>
    
    <!-- 查询  资源位商品信息  列表 -->
    <select id="selectPositionCommodityInfoList" 
    	resultType="com.pinshang.qingyun.xda.cms.dto.position.PositionCommodityInfoODTO" parameterType="long">
        SELECT
			c.id AS commodityId,
			c.commodity_code AS commodityCode,
			c.commodity_name AS commodityName,
			c.commodity_spec AS commoditySpec,
			pic.sort_num AS sortNum
		FROM t_xda_position_info_commodity pic
		LEFT JOIN t_commodity c ON c.id = pic.commodity_id
		WHERE pic.position_info_id = #{positionInfoId}
		ORDER BY pic.sort_num ASC, c.commodity_code ASC
    </select>

    <select id="selectPositionCommodityList" resultType="com.pinshang.qingyun.xda.cms.dto.home.XdaPositionInfoCommodityODTO">
        SELECT
            pic.position_info_id,
            pic.commodity_id,
            pic.sort_num
        FROM
            t_xda_position_info_commodity pic
        WHERE
            pic.position_info_id IN
            <foreach collection="positionInfoIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        ORDER BY pic.sort_num
        <if test=" limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <select id="queryCommodityStock" resultType="com.pinshang.qingyun.xda.cms.vo.XdaFlashSaleCommodityStockVO">
        SELECT
            commodity_id,
            IFNULL(stock_number - freeze_number,0) AS soldOut
        FROM
            t_shop_commodity_stock
        WHERE
            shop_id = #{shopId}
    </select>
</mapper>