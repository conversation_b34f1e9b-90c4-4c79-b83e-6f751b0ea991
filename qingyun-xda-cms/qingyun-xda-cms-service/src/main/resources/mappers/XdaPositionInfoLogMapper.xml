<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.position.XdaPositionInfoLogMapper">

	<!-- 查询  资源位信息-日志信息  列表 -->
	<select id="selectPositionInfoLogInfoList" 
		resultType="com.pinshang.qingyun.xda.cms.dto.position.PositionInfoLogInfoODTO" parameterType="Long">
        SELECT
			pil.operate_type AS operateType,
			eu.employee_name AS createName,
			pil.create_time AS createTime
		FROM t_xda_position_info_log pil
		LEFT JOIN t_employee_user eu ON eu.user_id = pil.create_id
		WHERE pil.position_info_id = #{positionInfoId}
		ORDER BY pil.id DESC
    </select>
    
</mapper>