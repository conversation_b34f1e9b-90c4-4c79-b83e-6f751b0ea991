<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.position.XdaPositionInfoMapper">

	<!-- 查询  资源位信息  列表 -->
	<select id="selectPositionInfoList" 
		resultType="com.pinshang.qingyun.xda.cms.dto.position.PositionInfoODTO" 
		parameterType="com.pinshang.qingyun.xda.cms.dto.position.SelectPositionInfoPageIDTO">
        SELECT
			pi.id,
			pi.position_code AS positionCode,
			pi.position_type AS positionType,
			pi.position_id AS positionId,
			pi.term_type AS termType,
			pi.target_type AS targetType,
			pi.label,
			pi.begin_time AS beginTime,
			pi.end_time AS endTime,
			pi.is_all_store AS isAllStore,
			pi.update_time AS updateTime,
			pi.status
		FROM t_xda_position_info pi
		<where>
		<if test="positionCode != null and positionCode != ''">
			AND pi.position_code LIKE CONCAT('%', #{positionCode}, '%')
		</if>
		<if test="positionType != null">
			AND pi.position_type = #{positionType}
		</if>
		<if test="termType != null">
			AND pi.term_type = #{termType}
		</if>
		
		<if test="storeId != null">
			AND (
				pi.is_all_store = 0
				OR (
					pi.is_all_store = 1 
					AND (
						EXISTS (
							SELECT 1 
							FROM t_xda_position_info_store_scope piss, t_store_settlement ss 
							WHERE pi.id = piss.position_info_id AND piss.ref_obj_type = 1 AND piss.ref_obj_id = ss.settlement_customer_id
							AND ss.store_id = #{storeId}
						)
						OR EXISTS(
							SELECT 1
							FROM t_xda_position_info_store_scope piss, t_store_settlement ss 
							WHERE pi.id = piss.position_info_id AND piss.ref_obj_type = 2 AND piss.ref_obj_id = ss.product_price_model_id
							AND ss.store_id = #{storeId}
						)
						OR EXISTS(
							SELECT 1
							FROM t_xda_position_info_store_scope piss, t_store s 
							WHERE pi.id = piss.position_info_id AND piss.ref_obj_type = 3 AND piss.ref_obj_id = s.store_type_id
							AND s.id = #{storeId}
						)
						OR EXISTS(
							SELECT 1
							FROM t_xda_position_info_store_scope piss, t_store s 
							WHERE pi.id = piss.position_info_id AND piss.ref_obj_type = 4 AND piss.ref_obj_id = s.store_channel_id
							AND s.id = #{storeId}
						)
						OR EXISTS(
							SELECT 1
							FROM t_xda_position_info_store_scope piss
							WHERE pi.id = piss.position_info_id AND piss.ref_obj_type = 8 AND piss.ref_obj_id = #{storeId}
						)
					)

				)
			)
		</if>
		<if test="targetType != null">
			AND pi.target_type = #{targetType}
		</if>
		<if test="label != null and label != ''">
			AND pi.label LIKE CONCAT('%', TRIM(#{label}), '%')
		</if>
		
		<if test="beginTime != null and beginTime != ''">
			<![CDATA[
			AND (
				pi.term_type = 1
				OR (
					pi.term_type = 2
					AND (pi.begin_time >= #{beginTime} OR pi.end_time >= #{beginTime})
				)
			)
			]]>
		</if>
		<if test="endTime != null and endTime != ''">
			<![CDATA[
			AND (
				pi.term_type = 1
				OR (
					pi.term_type = 2
					AND (pi.begin_time <= #{endTime} OR pi.end_time <= #{endTime})
				)
			)
			]]>
		</if>
		<if test="createBeginTime != null and createBeginTime != ''">
			<![CDATA[ 
				AND pi.create_time >= #{createBeginTime}
			 ]]>
		</if>
		<if test="createEndTime != null and createEndTime != ''">
			<![CDATA[ 
				AND pi.create_time <= #{createEndTime}
			 ]]>
		</if>
		
		<if test="status != null">
			<if test="status == 1"><!-- 启用 -->
				<![CDATA[ 
					AND (pi.status = 1 AND (pi.term_type = 1 OR (pi.term_type = 2 AND pi.end_time > NOW())))
				 ]]>
			</if>
			<if test="status == 2"><!-- 停用 -->
				AND pi.status = 2
			</if>
			<if test="status == 3"><!-- 过期 -->
				<![CDATA[ 
					AND (pi.status = 3 OR (pi.status = 1 AND pi.term_type = 2 AND pi.end_time < NOW()))
				 ]]>
			</if>
		</if>
		</where>
		ORDER BY pi.position_type ASC, pi.position_id ASC, pi.update_time DESC
    </select>


    <select id="selectShopPositionList" resultType="com.pinshang.qingyun.xda.cms.dto.home.XdaPositionInfoItemODTO">
        SELECT
            pi.id AS positionInfoId,
            pi.position_id,
            pi.position_type,
            pi.pic_url,
            CASE WHEN pi.position_type = 15 THEN pi.pic_target_type ELSE pi.target_type END AS target_type,
            CASE WHEN pi.position_type = 15 THEN pi.pic_target_type_id ELSE pi.target_type_id END AS target_type_id,
            CASE WHEN (pi.target_type = 1 OR pi.pic_target_type =1) AND c.cate_level = 2 THEN c.parent_id
            WHEN (pi.target_type = 1 OR pi.pic_target_type =1) AND c.cate_level = 1 THEN c.id END AS firstLevel,
            CASE WHEN (pi.target_type = 1 OR pi.pic_target_type =1) AND c.cate_level = 2 THEN c.id END AS secondLevel,
            pi.label,
            pi.min_interval,
            pi.update_time
        FROM
            t_xda_position_info pi
            LEFT JOIN t_xda_category c ON CASE WHEN pi.position_type=15 THEN (pi.pic_target_type=1 AND pi.pic_target_type_id=c.id)
            ELSE (pi.target_type=1 AND pi.target_type_id=c.id) END
        WHERE
            pi.`status` = 1
            AND (pi.term_type = 1 OR (pi.begin_time <![CDATA[ <= ]]> NOW() AND pi.end_time <![CDATA[ >= ]]> NOW()) )
            <if test="storeId != null">
                AND (
                    pi.is_all_store = 0
                    OR (
                        pi.is_all_store = 1
                        AND (
                            EXISTS (
                                SELECT 1
                                FROM t_xda_position_info_store_scope piss, t_store_settlement ss
                                WHERE piss.position_info_id = pi.id and piss.ref_obj_type = 1 AND piss.ref_obj_id = ss.settlement_customer_id
                                AND ss.store_id = #{storeId}
                            )
                            OR EXISTS(
                                SELECT 1
                                FROM t_xda_position_info_store_scope piss, t_store_settlement ss
                                WHERE piss.position_info_id = pi.id and piss.ref_obj_type = 2 AND piss.ref_obj_id = ss.product_price_model_id
                                AND ss.store_id = #{storeId}
                            )
                            OR EXISTS(
                                SELECT 1
                                FROM t_xda_position_info_store_scope piss, t_store s
                                WHERE piss.position_info_id = pi.id and piss.ref_obj_type = 3 AND piss.ref_obj_id = s.store_type_id
                                AND s.id = #{storeId}
                            )
                            OR EXISTS(
                                SELECT 1
                                FROM t_xda_position_info_store_scope piss, t_store s
                                WHERE piss.position_info_id = pi.id and piss.ref_obj_type = 4 AND piss.ref_obj_id = s.store_channel_id
                                AND s.id = #{storeId}
                            )
                            OR EXISTS(
                                SELECT 1
                                FROM t_xda_position_info_store_scope piss
                                WHERE piss.position_info_id = pi.id and piss.ref_obj_type = 8 AND piss.ref_obj_id = #{storeId}
                            )
                        )
                    )
                )
            </if>
    </select>
    
</mapper>
