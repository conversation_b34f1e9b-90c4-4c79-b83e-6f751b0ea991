<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.position.XdaPositionInfoStoreScopeMapper">

	<!-- 查询  资源位-结账客户  列表 -->
    <select id="selectSettlementList" resultType="com.pinshang.qingyun.xda.cms.dto.storeScope.StoreScopeODTO">
        SELECT
            s.id,
            s.customer_code AS optionCode,
            s.customer_name AS optionName
        FROM t_settlement s
        WHERE s.id IN (<include refid="refObjIdSQL"></include>)
        ORDER BY s.customer_code ASC
    </select>

    <!-- 查询  资源位-产品价格方案  列表 -->
    <select id="selectProductPriceModelList" resultType="com.pinshang.qingyun.xda.cms.dto.storeScope.StoreScopeODTO">
        SELECT
	        ppm.id,
	        ppm.price_model_code AS optionCode,
	        ppm.price_model_name AS optionName
        FROM t_product_price_model ppm
        WHERE ppm.id IN (<include refid="refObjIdSQL"></include>)
        ORDER BY ppm.price_model_code ASC
    </select>

    <!-- 查询  资源位-客户  列表 -->
    <select id="selectStoreList" resultType="com.pinshang.qingyun.xda.cms.dto.storeScope.StoreScopeODTO">
        SELECT
	        s.id,
	        s.store_code AS optionCode,
	        s.store_name AS optionName
        FROM t_store s
        WHERE s.id IN (<include refid="refObjIdSQL"></include>)
        ORDER BY s.store_code ASC
    </select>

    <!-- 查询  资源位-客户类型/渠道等  列表 -->
    <select id="selectDictionaryList" resultType="com.pinshang.qingyun.xda.cms.dto.storeScope.StoreScopeODTO">
        SELECT
	        d.id,
	        d.option_code AS optionCode,
	        d.option_name AS optionName
        FROM t_dictionary d
        WHERE d.id IN (<include refid="refObjIdSQL"></include>)
        ORDER BY d.option_code ASC
    </select>

    <!-- 公共查询条件 -->
    <sql id="refObjIdSQL">
        SELECT piss.ref_obj_id
        FROM t_xda_position_info_store_scope piss
        WHERE piss.position_info_id = #{positionInfoId}
    	AND piss.ref_obj_type = #{refObjType}
    </sql>

    <select id="selectOrderTimeList" resultType="java.lang.String">
        SELECT DISTINCT
            CASE 
                WHEN #{isPfsStore} = true THEN oc.pf_delivery_date_range_code
                ELSE oc.delivery_date_range_code
            END as delivery_date_range_code
        FROM
            t_product_price_model_list ppml
            INNER JOIN t_store_settlement tss ON ppml.product_price_model_id = tss.product_price_model_id
            INNER JOIN t_xda_commodity_app_status cas ON cas.commodity_id = ppml.commodity_id 
                AND (
                    CASE 
                        WHEN #{isPfsStore} = true THEN cas.pf_app_status = 0
                        ELSE cas.app_status = 0
                    END
                )
            INNER JOIN t_product_price_model ppm ON ppm.id = ppml.product_price_model_id AND ppm.price_model_state = 1
            INNER JOIN t_xda_order_commodity oc ON oc.commodity_id = ppml.commodity_id
        WHERE
            tss.store_id = #{storeId}
    </select>

    <select id="selectCommodityPriceModelByStore" resultType="java.lang.Long">
        SELECT
	        DISTINCT ppml.commodity_id
        FROM
            t_product_price_model_list ppml
            INNER JOIN t_store_settlement tss ON ppml.product_price_model_id = tss.product_price_model_id
            INNER JOIN t_xda_commodity_app_status cas ON cas.commodity_id = ppml.commodity_id
                AND (
                     CASE
                         WHEN #{isPfsStore} = true THEN cas.pf_app_status = 0
                         ELSE cas.app_status = 0
                     END
                )
            INNER JOIN t_product_price_model ppm ON ppm.id = ppml.product_price_model_id AND ppm.price_model_state = 1
        WHERE
            tss.store_id = #{storeId}
    </select>
</mapper>