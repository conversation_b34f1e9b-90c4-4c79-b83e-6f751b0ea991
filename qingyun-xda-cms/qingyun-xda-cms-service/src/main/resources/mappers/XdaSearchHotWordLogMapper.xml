<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.search.XdaSearchHotWordLogMapper" >

    <select id="queryHotWordLogPage" resultType="com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordLogODTO">
        SELECT
          l.oper_type,l.old_value,l.new_value,u.employee_name AS createName,l.create_time
        FROM t_xda_search_hot_word_log l
        LEFT JOIN t_employee_user u ON l.create_id = u.user_id
        <where>
            <if test="vo.operType !=null ">
                AND l.oper_type = #{vo.operType}
            </if>
            <if test="vo.createId !=null ">
                AND l.create_id = #{vo.createId}
            </if>
            <if test="vo.startTime != null and vo.endTime != null">
                AND l.create_time BETWEEN #{vo.startTime} AND #{vo.endTime}
            </if>
        </where>
        ORDER BY l.create_time DESC
    </select>

</mapper>