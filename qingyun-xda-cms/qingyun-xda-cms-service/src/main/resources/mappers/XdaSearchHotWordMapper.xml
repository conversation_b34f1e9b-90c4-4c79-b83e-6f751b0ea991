<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.cms.mapper.search.XdaSearchHotWordMapper" >

    <select id="queryCount" resultType="Integer">
        SELECT count(1) FROM t_xda_search_hot_word
        <where>
            <if test="hotWord != null and hotWord !='' ">
                AND hot_word = #{hotWord}
            </if>
        </where>
    </select>

    <select id="getMaxSortNum" resultType="Integer">
        SELECT (IFNULL(max(sort_num), 0)) AS sort_num FROM t_xda_search_hot_word
    </select>

    <select id="queryHotWordList" resultType="com.pinshang.qingyun.xda.cms.dto.search.XdaSearchHotWordODTO">
        SELECT id,hot_word,effect_status,sort_num
        FROM t_xda_search_hot_word
        ORDER BY sort_num ASC,update_time DESC
    </select>
</mapper>