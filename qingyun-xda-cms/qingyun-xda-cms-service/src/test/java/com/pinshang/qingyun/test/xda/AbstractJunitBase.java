package com.pinshang.qingyun.test.xda;

import org.junit.BeforeClass;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.transaction.annotation.Transactional;

import com.pinshang.qingyun.ApplicationXdaCmsService;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = ApplicationXdaCmsService.class,properties = {
        "spring.profiles.active=test",
        "application.name.switch=dc-test-"
})
@WebAppConfiguration
@Transactional
public  abstract class AbstractJunitBase {
    @BeforeClass
    public static void beforeClass(){
        //System.setProperty("application.name.switch","weican-");
    }
}