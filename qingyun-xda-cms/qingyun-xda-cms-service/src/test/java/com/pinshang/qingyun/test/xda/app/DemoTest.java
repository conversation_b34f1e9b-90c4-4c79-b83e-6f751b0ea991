package com.pinshang.qingyun.test.xda.app;

//import com.pinshang.qingyun.gee.remote.api.rpc.RpcRecipeService;
import com.pinshang.qingyun.test.xda.AbstractJunitBase;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @ClassName DemoTest
 * @Description TODO
 * <AUTHOR>
 * @Date 2019/11/14 11:16
 **/
public class DemoTest extends AbstractJunitBase {
//    @Autowired
//    private RpcRecipeService rpcRecipeService;
    /*@Test
    public void getId() throws Exception {
        ApiPageEduct<Collection<CommodityItemDTO>> list =  rpcRecipeService.recommComm(16L,1,1,null,1,20);
        System.out.println(list);
    }*/

}
