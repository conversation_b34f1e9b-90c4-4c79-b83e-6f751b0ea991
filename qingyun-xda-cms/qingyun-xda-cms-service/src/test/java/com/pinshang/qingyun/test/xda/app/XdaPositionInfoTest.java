package com.pinshang.qingyun.test.xda.app;

import com.pinshang.qingyun.test.xda.AbstractJunitBase;
import com.pinshang.qingyun.xda.cms.service.position.XdaPositionInfoService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

/**
 * @Description 退货流程测试
 * <AUTHOR>
 * @Date 2019/12/18 18:00
 **/
public class XdaPositionInfoTest extends AbstractJunitBase {
    @Autowired
    private XdaPositionInfoService positionInfoService;

    /**
     * 取货中
     * 1、修改退货单状态为“取货中”
     * */
    @Test
    @Rollback(false)
    public void deliveringTest(){
        
    }
}
