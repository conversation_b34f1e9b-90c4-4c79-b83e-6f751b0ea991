<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.pinshang.qingyun</groupId>
        <artifactId>qingyun-parent</artifactId>
        <version>5.0.0-UP-SNAPSHOT</version>
        <relativePath/>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>qingyun-xda-parent</artifactId>
    <version>1.2.1-UP-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
 <!--        <module>../qingyun-xda-product</module>
        <module>../qingyun-xda-cms</module>
       <module>../qingyun-xda-cms/qingyun-xda-cms-client</module>-->
<!--        <module>../qingyun-xda-product/qingyun-xda-product-client</module>-->
    </modules>
    <properties>
        <!-- 解决文件拷贝时的编码 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <!-- 解决编译时中文乱码-->
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>

        <java.version>1.8</java.version>
        <kotlin.version>1.7.0</kotlin.version>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <org.spring.version>5.3.18</org.spring.version>
        <spring-boot.version>2.6.6</spring-boot.version>
        <springfox-swagger.version>3.0.0</springfox-swagger.version>
        <druid.version>1.0.29</druid.version>
        <wxpay.version>0.0.3</wxpay.version>
        <alipay.version>20170725114550</alipay.version>

        <!-- base infrastructure -->
        <qingyun.infrastructure.version>2.2.6-SNAPSHOT</qingyun.infrastructure.version>
        <qingyun.box.version>3.0.6-UP-SNAPSHOT</qingyun.box.version>
        <qingyun.base.db.version>1.0.3-SNAPSHOT</qingyun.base.db.version>
        <qingyun.basic.version>1.6.2-SNAPSHOT</qingyun.basic.version>
        <qingyun.base.mvc.version>3.5.1-SNAPSHOT</qingyun.base.mvc.version>
        <qingyun.cache.version>3.1.3-UP-SNAPSHOT</qingyun.cache.version>
        <qingyun.mq.version>3.9.6-UP-SNAPSHOT</qingyun.mq.version>

        <!-- service -->
        <qingyun.xs.user.version>3.1.0-SNAPSHOT</qingyun.xs.user.version>
        <qingyun.common.version>3.6.9-UP-SNAPSHOT</qingyun.common.version>
        <qingyun.storage.version>3.5.4-UP-SNAPSHOT</qingyun.storage.version>
        <qingyun.price.version>1.5.2-UP-SNAPSHOT</qingyun.price.version>
        <qingyun.product.version>3.7.0-UP-SNAPSHOT</qingyun.product.version>
        <qingyun.order.version>3.6.6-UP-SNAPSHOT</qingyun.order.version>
        <qingyun.shop.version>4.1.4-UP-SNAPSHOT</qingyun.shop.version>
        <qingyun.msg.version>3.0.4-UP-SNAPSHOT</qingyun.msg.version>
        <easyexcel.version>2.1.7</easyexcel.version>
        <qingyun.marketing.version>1.2.3-UP-SNAPSHOT</qingyun.marketing.version>
        <kafka.version>3.0.1</kafka.version>
        <spring.kafka.version>2.8.4</spring.kafka.version>
        <qingyun.xda.product.version>1.1.4-UP-SNAPSHOT</qingyun.xda.product.version>
        <qingyun.store.version>1.5.2-UP-SNAPSHOT</qingyun.store.version>
        <qingyun.upload.version>3.1.8-UP-SNAPSHOT</qingyun.upload.version>
        <qingyun.weixin.version>1.1.4-UP-SNAPSHOT</qingyun.weixin.version>
        <qingyun.tms.version>1.0.5-UP-SNAPSHOT</qingyun.tms.version>
        <qingyun.pf.version>1.0.4-UP-SNAPSHOT</qingyun.pf.version>
        <qingyun.base.search.version>1.0.2-UP-SNAPSHOT</qingyun.base.search.version>
    </properties>

    <dependencies>
        <!--使用 lombok 简化 Java 代码-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>

        <!--kotlin-->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
            <version>${kotlin.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-core</artifactId>
            <version>1.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.pinshang.qingyun</groupId>
            <artifactId>qingyun-basic</artifactId>
            <version>${qingyun.basic.version}</version>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-msg-client</artifactId>
                <version>${qingyun.msg.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-product-client</artifactId>
                <version>${qingyun.product.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-order-client</artifactId>
                <version>${qingyun.order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-order-manage-client</artifactId>
                <version>${qingyun.order.version}</version>
            </dependency>
            <!--  project version start   -->
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-box</artifactId>
                <version>${qingyun.box.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-db</artifactId>
                <version>${qingyun.base.db.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-mvc</artifactId>
                <version>${qingyun.base.mvc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-mq</artifactId>
                <version>${qingyun.mq.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-cache</artifactId>
                <version>${qingyun.cache.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-storage-client</artifactId>
                <version>${qingyun.storage.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-common-client</artifactId>
                <version>${qingyun.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-shop-client</artifactId>
                <version>${qingyun.shop.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-base-search-client</artifactId>
                <version>${qingyun.base.search.version}</version>
            </dependency>

            <!--  project version end   -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-json</artifactId>
                <version>${spring-boot.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-openfeign</artifactId>
                <version>${spring.cloud.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-starter-config</artifactId>
                <version>${spring.cloud.version}</version>
            </dependency>

            <!-- core -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>3.8.1</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-core</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-beans</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-tx</artifactId>
                <version>${org.spring.version}</version>
            </dependency>


            <!--spring web相关包-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${org.spring.version}</version>
            </dependency>
            <!--fastjson-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.6</version>
            </dependency>

            <!--mybatis依赖包-->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>3.4.0</version>
            </dependency>
            <!--tk.mybatis-->
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper-spring-boot-starter</artifactId>
                <version>2.0.0</version>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>1.3.2</version>
            </dependency>
            <dependency>
                <groupId>tk.mybatis</groupId>
                <artifactId>mapper</artifactId>
                <version>4.0.1</version>
            </dependency>
            <!-- pagehelper -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.2.5</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper</artifactId>
                <version>5.1.8</version>
            </dependency>

            <!--开发相关-->
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>4.0.1</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <scope>test</scope>
                <version>${spring-boot.version}</version>
            </dependency>

            <!--数据库-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.41</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid</artifactId>
                <version>1.1.3</version>
            </dependency>

            <!-- redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>3.11.1</version>
            </dependency>
            <!--kafka -->
	        <dependency>
	            <groupId>org.springframework.kafka</groupId>
	            <artifactId>spring-kafka</artifactId>
	            <version>${spring.kafka.version}</version>
	        </dependency>
	        <dependency>
	            <groupId>org.apache.kafka</groupId>
	            <artifactId>kafka-clients</artifactId>
	            <version>${kafka.version}</version>
	        </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-marketing-client</artifactId>
                <version>${qingyun.marketing.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-price-client</artifactId>
                <version>${qingyun.price.version}</version>
            </dependency>

            <dependency>
                <groupId>com.pinshang.qingyun</groupId>
                <artifactId>qingyun-pf-product-client</artifactId>
                <version>${qingyun.pf.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>2.2.0.RELEASE</version>
                </plugin>
            </plugins>
        </pluginManagement>
        <plugins>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <version>${kotlin.version}</version>
                <configuration>
                    <compilerPlugins>
                        <!-- Or "spring" for the Spring support -->
                        <plugin>spring</plugin>
                        <plugin>no-arg</plugin>
                    </compilerPlugins>

                    <pluginOptions>
                        <!-- Each annotation is placed on its own line -->
                        <!--<option>all-open:annotation=cn.apier.yim.common.com.yanjunhua.base.common.annotation.NeedNoArgConstructor</option>-->
                        <option>no-arg:annotation=com.yanjunhua.base.common.annotation.NeedNoArgConstructor</option>
                    </pluginOptions>
                    <jvmTarget>1.8</jvmTarget>
                </configuration>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/main/kotlin</sourceDir>
                                <sourceDir>${project.basedir}/src/main/java</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <goals> <goal>test-compile</goal> </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
                                <sourceDir>${project.basedir}/src/test/java</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-noarg</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
<!--                <executions>-->
<!--                    &lt;!&ndash; 替换会被 maven 特别处理的 default-compile &ndash;&gt;-->
<!--                    <execution>-->
<!--                        <id>default-compile</id>-->
<!--                        <phase>none</phase>-->
<!--                    </execution>-->
<!--                    &lt;!&ndash; 替换会被 maven 特别处理的 default-testCompile &ndash;&gt;-->
<!--                    <execution>-->
<!--                        <id>default-testCompile</id>-->
<!--                        <phase>none</phase>-->
<!--                    </execution>-->

<!--                    <execution>-->
<!--                        <id>java-compile</id>-->
<!--                        <phase>compile</phase>-->
<!--                        <goals> <goal>compile</goal> </goals>-->
<!--                    </execution>-->
<!--                    <execution>-->
<!--                        <id>java-test-compile</id>-->
<!--                        <phase>test-compile</phase>-->
<!--                        <goals> <goal>testCompile</goal> </goals>-->
<!--                    </execution>-->
<!--                </executions>-->
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>

    </build>


</project>
