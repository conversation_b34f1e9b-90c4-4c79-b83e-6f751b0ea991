package com.pinshang.qingyun.xda.product.dto.category;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * 鲜达APP 分类
 */
@Data
@NoArgsConstructor
public class FeignXdaCategoryResODTO {

    @ApiModelProperty(value = "分类列表", position = 1)
    private List<FeignXdaCategoryODTO> categoryList;

    @ApiModelProperty(value = "有推荐二级类目的 所有一级类目id集合", position = 2)
    private Set<Long> recommondFirstCategoryIdSet;
}
