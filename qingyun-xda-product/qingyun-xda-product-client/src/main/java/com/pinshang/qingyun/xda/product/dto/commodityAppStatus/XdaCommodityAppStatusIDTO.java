package com.pinshang.qingyun.xda.product.dto.commodityAppStatus;

import com.pinshang.qingyun.xda.product.dto.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @time: 2020/12/22 14:07
 */
@Data
public class XdaCommodityAppStatusIDTO extends Pagination {

    @ApiModelProperty(position = 1,value = "商品id")
    private Long commodityId;

    @ApiModelProperty(position = 2,value = "是否称重:0-非称重,1-称重")
    private Integer isWeight;

    @ApiModelProperty(position = 3,value = "前台品类")
    private String appCategoryId;

    @ApiModelProperty(position = 4,value = "前台品名")
    private String commodityAppId;

    @ApiModelProperty(position = 5,value = "是否速冻产品 0-否,1-是")
    private Integer commodityIsQuickFreeze;

    @ApiModelProperty(position = 6,value = "条码")
    private String barCode;

    @ApiModelProperty(position = 7,value = "上下架状态:0-上架,1-下架")
    private String commodityAppState;

    @ApiModelProperty(position = 8,value = "客户id")
    private Long storeId;

    @ApiModelProperty(position = 9,value = "产品价格方案id")
    private Long productPriceModelId;
}
