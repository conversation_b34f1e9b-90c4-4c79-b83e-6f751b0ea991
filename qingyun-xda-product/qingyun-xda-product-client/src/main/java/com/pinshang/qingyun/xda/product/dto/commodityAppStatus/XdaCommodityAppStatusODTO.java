package com.pinshang.qingyun.xda.product.dto.commodityAppStatus;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @time: 2020/12/22 13:59
 */
@Data
@NoArgsConstructor
public class XdaCommodityAppStatusODTO {
    @ApiModelProperty(position = 1,value = "上下架状态")
    private String commodityAppState;

    @ApiModelProperty(position = 2,value = "总部可售")
    private String commodityState;

    @ApiModelProperty(position = 3,value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(position = 4,value = "商品名称")
    private String commodityName;

    @ApiModelProperty(position = 5,value = "规格")
    private String commoditySpec;

    @ApiModelProperty(position = 6,value = "前台品名")
    private String commodityAppName;

    @ApiModelProperty(position = 7,value = "前台品类")
    private String appCategoryName;

    @ApiModelProperty(position = 8, value = "图片")
    private String picStatus;

    @ApiModelProperty(position = 9,value = "送货时间范围")
    private String deliveryDateRangeValue;

    @ApiModelProperty(position = 10, value = "长图")
    private String longPicStatus;

    @ApiModelProperty(position = 11, value = "副标题")
    private String commoditySubName;

    @ApiModelProperty(position = 12,value = "自定义标签")
    private String commodityTag;

    @ApiModelProperty(position = 13,value = "销售箱规")
    private BigDecimal salesBoxCapacity;

    @ApiModelProperty(position = 14,value = "单位")
    private String commodityUnitName;

    @ApiModelProperty(position = 15,value = "是否称重")
    private String isWeight;

    @ApiModelProperty(position = 16,value = "净含量")
    private String commodityWeight;

    @ApiModelProperty(position = 17,  value = "贮存条件")
    private String storageCondition;

    @ApiModelProperty(position = 18,value = "保质期(天)")
    private Integer qualityDays;

    @ApiModelProperty(position = 19,value = "是否速冻")
    private String commodityIsQuickFreeze;

    @ApiModelProperty(position = 20,value = "后台品类")
    private String commodityCategory;

    @ApiModelProperty(position = 21,value = "条码")
    private String barCode;

    @ApiModelProperty(position = 22,value = "商品id")
    private Long commodityId;

    @ApiModelProperty(position = 1,value = "上下架状态")
    private Long appState;

    @ApiModelProperty("批发上下架状态")
    private Long pfAppState;
}
