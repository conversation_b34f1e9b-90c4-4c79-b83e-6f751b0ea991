package com.pinshang.qingyun.xda.product.dto.commodityText;

import com.pinshang.qingyun.xda.product.dto.BaseEnterpriseUserIDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: hhf
 * @date: 2024/7/8/008 10:56
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchUpdateCommodityQualityStatusIDTO extends BaseEnterpriseUserIDTO {

    @ApiModelProperty(position = 10, required = true, value = "商品是否显示保质期信息集合")
    private List<CommodityQualityStatusIDTO> commodityQualityStatusList;
}
