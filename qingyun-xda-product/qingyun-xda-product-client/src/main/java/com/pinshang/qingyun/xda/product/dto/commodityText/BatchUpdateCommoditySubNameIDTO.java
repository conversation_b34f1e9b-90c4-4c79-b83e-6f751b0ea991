package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.xda.product.dto.BaseEnterpriseUserIDTO;

/**
 * 批量更新  商品文描-副标题
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@NoArgsConstructor
public class BatchUpdateCommoditySubNameIDTO extends BaseEnterpriseUserIDTO {
	@ApiModelProperty(position = 10, required = true, value = "商品副标题信息集合")
	private List<CommoditySubNameIDTO> commoditySubNameList;
}
