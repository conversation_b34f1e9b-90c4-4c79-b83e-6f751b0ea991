package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品文描-前台品名
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@NoArgsConstructor
public class CommodityAppNameIDTO {
	@ApiModelProperty(position = 10, required = true, value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(position = 11, required = true, value = "前台品名")
    private String commodityAppName;
	
	public CommodityAppNameIDTO(String commodityCode, String commodityAppName) {
		this.commodityCode = commodityCode;
		this.commodityAppName = commodityAppName;
	}
	
}
