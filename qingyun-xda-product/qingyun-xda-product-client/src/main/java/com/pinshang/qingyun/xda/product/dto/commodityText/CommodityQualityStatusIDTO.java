package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 商品文描-是否显示保质期
 * @author: hhf
 * @date: 2024/7/8/008 10:51
 */
@Data
@NoArgsConstructor
public class CommodityQualityStatusIDTO {

    @ApiModelProperty(position = 10, required = true, value = "商品编码")
    private String commodityCode;
    @ApiModelProperty(position = 11, required = true, value = "是否显示保质期：1-显示，0-不显示")
    private String qualityStatusName;

    public CommodityQualityStatusIDTO(String commodityCode, String qualityStatusName) {
        this.commodityCode = commodityCode;
        this.qualityStatusName = qualityStatusName;
    }
}
