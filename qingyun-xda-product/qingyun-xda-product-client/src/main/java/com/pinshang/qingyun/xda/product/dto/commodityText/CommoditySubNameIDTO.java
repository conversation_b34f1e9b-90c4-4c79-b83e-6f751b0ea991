package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品文描-副标题
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@NoArgsConstructor
public class CommoditySubNameIDTO {
	@ApiModelProperty(position = 10, required = true, value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(position = 11, required = true, value = "商品副标题")
    private String commoditySubName;
	
	public CommoditySubNameIDTO(String commodityCode, String commoditySubName) {
		this.commodityCode = commodityCode;
		this.commoditySubName = commoditySubName;
	}
	
}
