package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品文描信息
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@ApiModel
@NoArgsConstructor
public class CommodityTextInfoODTO {
	@ApiModelProperty(position = 0, required = true, value = "商品ID", hidden = true)
	private Long id;
	@ApiModelProperty(position = 10, required = true, value = "商品ID")
	private String commodityId;
	
	@ApiModelProperty(position = 11, required = true, value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(position = 12, required = true, value = "商品名称")
	private String commodityName;
	@ApiModelProperty(position = 13, required = true, value = "商品规格")
	private String commoditySpec;
	@ApiModelProperty(position = 14, required = true, value = "商品单位ID", hidden = true)
	private Long commodityUnitId;
	@ApiModelProperty(position = 14, required = true, value = "商品单位名称")
	private String commodityUnitName;
	
	@ApiModelProperty(position = 21, required = true, value = "前台一级品类ID")
    private Long xdaFirstCategoryId;
	@ApiModelProperty(position = 21, required = true, value = "前台二级品类ID")
	private Long xdaSecondCategoryId;
	@ApiModelProperty(position = 21, required = true, value = "前台一级品类")
    private String xdaFirstCategoryName;
	@ApiModelProperty(position = 21, required = true, value = "前台二级品类")
	private String xdaSecondCategoryName;
	@ApiModelProperty(position = 21, required = true, value = "前台品类-显示")
	private String xdaCategoryStatusName;
	@ApiModelProperty(position = 22, required = true, value = "前台品名")
	private String commodityAppName;
	
	@ApiModelProperty(position = 23, required = true, value = "系列品-主品ID", hidden = true)
	private String serialCommodityId;
	@ApiModelProperty(position = 23, required = true, value = "系列品-编码")
	private String serialCommodityCode;
	@ApiModelProperty(position = 23, required = true, value = "系列品-是否主品状态")
	private String serialCommodityStatusName;
	
	@ApiModelProperty(position = 31, required = true, value = "图片集合")
    private List<CommodityTextPicODTO> picList;
	@ApiModelProperty(position = 31, required = true, value = "图片-展示文案")
	private String picStatusName;
	@ApiModelProperty(position = 32, required = true, value = "长图Url")
    private String longPicUrl;
	@ApiModelProperty(position = 32, required = true, value = "长图Url-访问")
    private String visitLongPicUrl;
	@ApiModelProperty(position = 32, required = true, value = "长图-展示文案")
	private String longPicStatusName;
	
	@ApiModelProperty(position = 33, required = true, value = "副标题")
    private String commoditySubName;
	
	@ApiModelProperty(position = 34, required = true, value = "标签ID", hidden = true)
	private Long tagId;
	@ApiModelProperty(position = 34, required = true, value = "标签名称")
	private String tagName;
	@ApiModelProperty(position = 34, required = true, value = "标签背景色值")
	private String tagBgColor;

	@ApiModelProperty(position = 35, required = true, value = "保质期(天)")
	private BigDecimal qualityDays;
	@ApiModelProperty(position = 18, required = true, value = "是否显示保质期-展示文案")
	private String qualityStatusName;
	
	@ApiModelProperty(position = 41, required = true, value = "称重状态", hidden = true)
	private Integer isWeight;
	@ApiModelProperty(position = 41, required = true, value = "称重状态名称")
	private String isWeightName;
	
	@ApiModelProperty(position = 42, required = true, value = "可售状态", hidden = true)
    private Integer commodityState;
	@ApiModelProperty(position = 42, required = true, value = "可售状态名称")
	private String commodityStateName;
	
	@ApiModelProperty(position = 51, required = true, value = "后台一级品类ID", hidden = true)
	private Long firstCategoryId;
	@ApiModelProperty(position = 51, required = true, value = "后台二级品类ID", hidden = true)
	private Long secondCategoryId;
	@ApiModelProperty(position = 51, required = true, value = "后台三级品类ID", hidden = true)
	private Long thirdCategoryId;
	@ApiModelProperty(position = 51, required = true, value = "后台一级品类名称")
	private String firstCategoryName;
	@ApiModelProperty(position = 51, required = true, value = "后台二级品类名称")
	private String secondCategoryName;
	@ApiModelProperty(position = 51, required = true, value = "后台三级品类名称")
	private String thirdCategoryName;
	
	@ApiModelProperty(position = 61, required = true, value = "条码")
	private String barCode;
	@ApiModelProperty(position = 61, required = true, value = "副条码")
	private String subBarCodes;

	/** 上下架状态：0-上架，1-下架 */
	private Integer appStatus;

	/**
	 * 2-8
	 */
	private String deliveryDateRangeCode;

	/** 1有库存 0售罄 */
	private Integer soldOut;
	private Integer soldOut1;
	private Integer soldOut2;
	private Integer soldOut3;
	private Integer soldOut4;
	private Integer soldOut5;
	private Integer soldOut6;
	private Integer soldOut7;

	public void setUnLimitAndStockSoldOut(Integer soldOut){
		this.soldOut = soldOut;
		this.soldOut1 = soldOut;
		this.soldOut2 = soldOut;
		this.soldOut3 = soldOut;
		this.soldOut4 = soldOut;
		this.soldOut5 = soldOut;
		this.soldOut6 = soldOut;
		this.soldOut7 = soldOut;

	}
}
