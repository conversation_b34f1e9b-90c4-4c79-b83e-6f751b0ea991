package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品文描-图片
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@NoArgsConstructor
public class CommodityTextPicODTO {
	@ApiModelProperty(position = 11, required = true, value = "图片URL")
	private String picUrl;
	@ApiModelProperty(position = 12, required = true, value = "默认状态：0-否、1-是")
	private Integer isDefault;
	
	@ApiModelProperty(position = 21, required = true, value = "访问图片URL")
	private String visitPicUrl;
	
	public CommodityTextPicODTO(String picUrl, Integer isDefault, String visitPicUrl) {
		this.picUrl = picUrl;
		this.isDefault = isDefault;
		this.visitPicUrl = visitPicUrl;
	}
}
