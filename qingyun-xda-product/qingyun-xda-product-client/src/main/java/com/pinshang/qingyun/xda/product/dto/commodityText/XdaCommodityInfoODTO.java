package com.pinshang.qingyun.xda.product.dto.commodityText;

import com.pinshang.qingyun.xda.product.dto.processGroup.CommodityProcessInfoODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 鲜达商品信息
 */
@Data
@NoArgsConstructor
public class XdaCommodityInfoODTO {
	@ApiModelProperty(position = 11, required = true, value = "商品ID")
	private Long commodityId;
	@ApiModelProperty(position = 11, required = true, value = "前台品名")
	private String commodityAppName;
	@ApiModelProperty(position = 11, required = true, value = "副标题")
	private String commoditySubName;
	@ApiModelProperty(position = 11, required = true, value = "规格")
	private String commoditySpec;
	@ApiModelProperty(position = 11, required = true, value = "商品单位")
	private String commodityUnitName;
	@ApiModelProperty(position = 11, required = true, value = "是否称重：0-否、1-是")
	private Integer isWeight;
	@ApiModelProperty(position = 11, required = true, value = "包装规格")
	private BigDecimal commodityPackageSpec;
	@ApiModelProperty(position = 11, required = true,value ="箱规")
	private BigDecimal salesBoxCapacity;
	@ApiModelProperty(position = 11, required = true, value = "处理方式List")
	private List<CommodityProcessInfoODTO> processList;
	
	@ApiModelProperty(position = 11, required = true, value = "默认图片URL")
	private String defaultPicUrl;
	@ApiModelProperty(position = 21, required = true, value = "前台一级品类ID")
	private Long xdaFirstCategoryId;
	@ApiModelProperty(position = 21, required = true, value = "前台二级品类ID")
	private Long xdaSecondCategoryId;
}
