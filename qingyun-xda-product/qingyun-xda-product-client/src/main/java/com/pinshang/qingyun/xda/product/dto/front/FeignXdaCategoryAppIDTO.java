package com.pinshang.qingyun.xda.product.dto.front;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class FeignXdaCategoryAppIDTO extends XdaCategoryBaseIDTO {

    @ApiModelProperty(value = "一级分类ID",position = 2)
    private Long xdaFirstCategoryId;
    @ApiModelProperty(value = "二级分类ID",position = 3)
    private Long xdaSecondCategoryId;

    public static FeignXdaCategoryAppIDTO init(Date orderTime, Long storeId, Long firstId, Long secondId){
        FeignXdaCategoryAppIDTO idto = new FeignXdaCategoryAppIDTO();
        idto.setOrderTime(orderTime);
        idto.setStoreId(storeId);
        idto.setXdaFirstCategoryId(firstId);
        idto.setXdaSecondCategoryId(secondId);
        return idto;
    }
}
