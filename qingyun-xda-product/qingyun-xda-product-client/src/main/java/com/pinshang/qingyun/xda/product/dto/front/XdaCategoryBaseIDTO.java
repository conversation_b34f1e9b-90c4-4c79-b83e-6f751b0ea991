package com.pinshang.qingyun.xda.product.dto.front;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdaCategoryBaseIDTO {

    @ApiModelProperty(required = true,value = "送货日期",position = 1)
    private Date orderTime;
    @ApiModelProperty(value = "客户ID",hidden = true)
    private Long storeId;
    @ApiModelProperty(value = "销量排序：0=默认不按销量排序，1=按销量排序",position = 4)
    private Integer saleSort;
    @ApiModelProperty(value = "价格排序：0=默认不按价格排序，1=按价格升序，2=按价格降序",position = 5)
    private Integer priceSort;


    public Integer getSaleSort() {
        return saleSort==null?0:saleSort;
    }

    public Integer getPriceSort() {
        return priceSort==null?0:priceSort;
    }

}
