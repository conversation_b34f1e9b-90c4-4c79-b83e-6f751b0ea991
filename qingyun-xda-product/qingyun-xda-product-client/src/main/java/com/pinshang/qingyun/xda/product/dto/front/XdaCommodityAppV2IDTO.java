package com.pinshang.qingyun.xda.product.dto.front;

import com.pinshang.qingyun.base.enums.pic.PicSizeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 23/6/14/014 14:11
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class XdaCommodityAppV2IDTO {
    @ApiModelProperty(position = 1,required = true,value = "送货日期")
    private Date orderTime;
    @ApiModelProperty(position = 2,required = true,value = "商品ID集合")
    private List<Long> commodityIdList;
    @ApiModelProperty(position = 3,required = true,value = "客户ID")
    private Long storeId;
    @ApiModelProperty(value = "商品默认图片尺寸。不传时返回原图",position = 4)
    private PicSizeEnums defaultImageSize;
    @ApiModelProperty(value = "请求来源(预留)：1=App，3=小程序，不传默认为1=APP",hidden = true)
    private Integer sourceType;
    @ApiModelProperty(value = "是否需要返回下架商品，购物车使用，默认不返回，不传默认为false",position = 5)
    private Boolean needAppDown;
    @ApiModelProperty("是否需要自定义标签，不传默认为true；购物车不需要展示标签")
    private Boolean needTag;
}
