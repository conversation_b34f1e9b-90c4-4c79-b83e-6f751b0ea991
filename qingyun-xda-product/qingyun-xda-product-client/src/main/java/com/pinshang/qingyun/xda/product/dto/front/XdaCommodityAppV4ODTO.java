package com.pinshang.qingyun.xda.product.dto.front;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class XdaCommodityAppV4ODTO {
    @ApiModelProperty(value = "商品ID",position = 1)
    private Long commodityId;
    @ApiModelProperty(value ="商品ID字符串",position = 1)
    private String commodityIdStr;
    @ApiModelProperty(value = "商品编码",position = 1)
    private String commodityCode;
    @ApiModelProperty(value ="商品前台名称",position = 2)
    private String commodityName;
    @ApiModelProperty(value ="规格",position = 3)
    private String commoditySpec;
    @ApiModelProperty(value ="副标题",position = 4)
    private String commoditySubName;
    @ApiModelProperty(value ="箱规",position = 5)
    private BigDecimal salesBoxCapacity;
    @ApiModelProperty(value ="计量单位",position = 6)
    private String commodityUnitName;
    @ApiModelProperty(value ="是否速冻：0-否、1-是",position = 7)
    private Integer isQuickFreeze;
//    @ApiModelProperty("是否称重：0-否、1-是")
//    private Integer isWeight;
//    @ApiModelProperty("包装规格")
//    private BigDecimal commodityPackageSpec;
    @ApiModelProperty(value ="APP上架状态：0-上架，1-下架；购物车使用，其他场景只返回上架商品",position = 8)
    private Integer appStatus;
    @ApiModelProperty(value ="默认图片URL,拼接后的url",position = 9)
    private String imageUrl;

    @ApiModelProperty(value ="商品单价，取客户价格方案",position = 10)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;

    //特价
    @ApiModelProperty(value ="是否有特价：0=无，1=有",position = 11)
    private Integer isSpecialPrice;
    @ApiModelProperty(value ="原始特价，取产品特价方案价格",position = 11)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal specialPrice;

    //促销
    @ApiModelProperty(value ="是否有赠品：0=无，1=有",position = 12)
    private Integer isPromotion;
    @ApiModelProperty(value ="赠品方案详细内容",hidden = true)
    private List<XdaStorePromotionODTO> promotionList;

    //自定义标签
    @ApiModelProperty(value ="自定义标签列表",position = 13)
    private List<CommodityTextTagInfoODTO> tagList;

    //是否可订货
    @ApiModelProperty(value ="是否可订货",position = 14)
    private Boolean isCanOrder;
    @ApiModelProperty(value ="最早可订货时间,订单使用",hidden = true)
    private Date beginDeliveryTime;
    @ApiModelProperty(value ="最晚可订货时间,订单使用",hidden = true)
    private Date endDeliveryTime;

    //限量
    @ApiModelProperty(value ="是否有限量：0=无，1=有",position = 15)
    private Integer isLimit;
    @ApiModelProperty(value ="商品限量值",position = 16)
    private BigDecimal limitNumber;

    @ApiModelProperty(value ="库存限量开始时间",position = 17)
    private Date limitStartTime;

    @ApiModelProperty(value ="库存限量结束时间",position = 18)
    private Date limitEndTime;
    
    @ApiModelProperty(value = "销售状态：1-正常可订货的商品、2-已抢光的商品、3-当前送货日期不支持订货的商品	 —— 参见枚举 SalesStatusEnums", position = 30)
	private Integer salesStatus;
    // 标签
    @ApiModelProperty(value ="标签合集-列表页：1-自定义标签、2-特价、3-促销（、4-凑整、5-速冻）、6-限量",position = 13)
    private List<CommodityTextTagInfoODTO> listTagList;
    @ApiModelProperty(value ="标签集合-详情页：2-特价、4-凑整、5-速冻、6-限量",position = 13)
    private List<CommodityTextTagInfoODTO> tagV2List;
}
