package com.pinshang.qingyun.xda.product.dto.front.search;

import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.xda.product.dto.front.XdaSerialCommodityDetailODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class XdaCategoryCommodityODTO extends XdaCommodityAppODTO {
    @ApiModelProperty(value = "系列品主品规格拼接,如：1箱|1瓶|1筐；当isSerial=1时规格展示此值",position = 20)
    private String serialCommoditySpec;
    @ApiModelProperty(value = "系列品主品价格拼接,如：16.38-1062.39；当isSerial=1时价格展示此值",position = 20)
    private String serialCommodityPrice;
    @ApiModelProperty(value="系列品展开列表，当isSerial=1时选规格展开列表",position = 20)
    private List<XdaSerialCommodityDetailODTO> serialCommodityDetailList;

    public static XdaCategoryCommodityODTO convert(XdaCommodityAppODTO commodityInfoAppODTO){
        XdaCategoryCommodityODTO resultODTO = BeanCloneUtils.copyTo(commodityInfoAppODTO, XdaCategoryCommodityODTO.class);
        return resultODTO;
    }
    
    @ApiModelProperty(position = 30, required = true, value = "销售状态：1-正常可订货的商品、2-已抢光的商品、3-当前送货日期不支持订货的商品	 —— 参见枚举 SalesStatusEnums")
	private Integer salesStatus;
    
}
