package com.pinshang.qingyun.xda.product.dto.processGroup;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品加更方式信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommodityProcessInfoODTO {
	/**
	 * t_xda_process_group_item 表主键
	 */
	@ApiModelProperty(position = 11, required = true, value = "加工方式ID")
	private Long processId;

	@ApiModelProperty( required = true, value = "处理方式id字符串形式，字符串")
	private String processIdSeq;
	/**
	 * t_xda_process_group_item 表 name
	 */
	@ApiModelProperty(position = 12, required = true, value = "加工方式名称")
	private String processName;
	/**
	 * t_xda_process_group_item 表 sortNum
	 */
	@ApiModelProperty(position = 12, required = true, value = "加工方式排序号")
	private Integer sortNum;

	private Long commodityId;

	public String getProcessIdSeq() {
		return this.processId == null ? "" : this.processId.toString() ;
	}
}
