package com.pinshang.qingyun.xda.product.hystrix;

import com.pinshang.qingyun.xda.product.dto.category.*;
import com.pinshang.qingyun.xda.product.dto.front.FeignXdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.FeignXdaCategoryCommodityResODTO;
import com.pinshang.qingyun.xda.product.dto.front.FeignXdaRecommondCommodityIDTO;
import com.pinshang.qingyun.xda.product.dto.front.FeignXdaRecommondCommodityODTO;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import com.pinshang.qingyun.xda.product.service.XdaCategoryClient;

import java.util.List;

/**
 * 鲜达前台品类
 */
@Component
public class XdaCategoryClientHystrix implements FallbackFactory<XdaCategoryClient> {

    @Override
    public XdaCategoryClient create(Throwable throwable) {
        return new XdaCategoryClient(){

        	@Override
			public XdaCategoryODTO selectXdaCategory(Long id) {
				return null;
			}

			@Override
			public List<XdaCategoryListODTO> findAllSecondXdaCategoryListToExport() {
				return null;
			}

			@Override
			public FeignXdaCategoryCommodityResODTO queryXdaCategoryCommodityList(FeignXdaCategoryAppIDTO appIDTO) {
				return null;
			}

			@Override
			public FeignXdaRecommondCommodityODTO queryRecommondCommodity(FeignXdaRecommondCommodityIDTO req) {
				return null;
			}

			@Override
			public FeignXdaCategoryResODTO queryExtraCategory(String orderTime, FeignXdaExtraCategoryIDTO req) {
				return null;
			}

			@Override
			public List<XdaCategoryODTO> findAllXdaCategory() {
				return null;
			}
			@Override
			public List<XdaCategoryODTO> selectXdaCategoryList(XdaCategoryListIDTO idto) {
				return null;
			}

			@Override
			public List<FeignCategoryCommodityFieldODTO> queryCategoryCommodityField(FeignCategoryCommodityFieldIDTO req) {
				return null;
			}

			@Override
			public List<Long> queryXdaCategoryCommodityIdList(FeignXdaCategoryAppIDTO appIDTO) {
				return null;
			}
		};
    }
    
}
