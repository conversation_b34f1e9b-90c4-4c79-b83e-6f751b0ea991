package com.pinshang.qingyun.xda.product.hystrix;

import com.pinshang.qingyun.xda.product.dto.category.*;
import com.pinshang.qingyun.xda.product.dto.front.*;
import com.pinshang.qingyun.xda.product.service.XdaCategoryWebClient;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


@Component
public class XdaCategoryWebClientHystrix implements FallbackFactory<XdaCategoryWebClient> {
    @Override
    public XdaCategoryWebClient create(Throwable cause) {
        return new XdaCategoryWebClient() {

            @Override
            public FeignXdaCategoryCommodityResODTO queryXdaCategoryCommodityList(FeignXdaCategoryAppIDTO appIDTO) {
                return null;
            }

            @Override
            public FeignXdaCategoryResODTO queryExtraCategoryV2(FeignQueryExtraCategoryIDTO req) {
                return null;
            }

            @Override
            public FeignXdaRecommondCommodityODTO queryRecommondCommodity(FeignXdaRecommondCommodityWebIDTO req) {
                return null;
            }
        };
    }
}
