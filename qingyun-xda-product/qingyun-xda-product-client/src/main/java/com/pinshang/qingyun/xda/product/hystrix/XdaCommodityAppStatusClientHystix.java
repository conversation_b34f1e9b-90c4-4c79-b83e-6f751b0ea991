package com.pinshang.qingyun.xda.product.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.xda.product.dto.commodityAppStatus.XdaCommodityAppStatusIDTO;
import com.pinshang.qingyun.xda.product.dto.commodityAppStatus.XdaCommodityAppStatusODTO;
import com.pinshang.qingyun.xda.product.dto.commodityAppStatus.XdaCommodityAppStatusToDownIDTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityAppStatusClient;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: chenqiang
 * @time: 2020/12/22 13:56
 */
@Component
public class XdaCommodityAppStatusClientHystix implements FallbackFactory<XdaCommodityAppStatusClient> {
    @Override
    public XdaCommodityAppStatusClient create(Throwable cause) {
        return new XdaCommodityAppStatusClient(){

            @Override
            public PageInfo<XdaCommodityAppStatusODTO> selectCommodityAppStatusList(XdaCommodityAppStatusIDTO xdaCommodityAppStateIDTO) {
                return null;
            }

            @Override
            public Long updateXdaCommodityAppStatusToDown(XdaCommodityAppStatusToDownIDTO idto) {
                return null;
            }

            @Override
            public List<XdaCommodityAppStatusODTO> batchSelectCommodityAppStatus(List<Long> commodityIdList) {
                return null;
            }
        };
    }
}
