package com.pinshang.qingyun.xda.product.hystrix;

import com.pinshang.qingyun.xda.product.dto.*;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import com.pinshang.qingyun.xda.product.dto.front.*;
import com.pinshang.qingyun.xda.product.service.XdaCommodityFrontClient;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


@Component
public class XdaCommodityFrontClientHystrix implements FallbackFactory<XdaCommodityFrontClient> {

    @Override
    public XdaCommodityFrontClient create(Throwable throwable) {
        return new XdaCommodityFrontClient(){

            @Override
            public List<XdaCommodityAppODTO> queryXdaCommodityListForApp(XdaCommodityAppIDTO appIDTO) {
                return null;
            }

            @Override
            public List<XdaCommodityAppV2ODTO> queryXdaCommodityListForAppV2(XdaCommodityAppV2IDTO appIDTO) {
                return null;
            }
            
            @Override
            public List<XdaCommodityAppV4ODTO> queryXdaCommodityListForAppV4(XdaCommodityAppV4IDTO appIDTO) {
                return null;
            }

            @Override
            public Map<Long, Integer> queryXdaCommodityAppStatus(List<Long> commodityIdList) {
                return null;
            }

            @Override
            public Map<Long, XdaCommodityDeliveryTimeODTO> queryXdaCommodityDeliveryTime(XdaCommodityDeliveryTimeIDTO appIDTO) {
                return null;
            }

            @Override
            public BigDecimal findOrderTargetByStoreIdAndOrderTime(XdaShoppingCartV2IDTO xdaShoppingCartV2IDTO) {
                return null;
            }

            @Override
            public XdaOrderTargetSetV2ODTO findXdaOrderTargetIdByStoreIdAndOrderTime(XdaShoppingCartV2IDTO xdaShoppingCartV2IDTO) {
                return null;
            }

            @Override
            public List<Long> selectXdaSpecialsCommoditySet(List<Long> commodityIdList) {
                return null;
            }

            @Override
            public Map<Long, XdaCommodityLimitODTO> queryXdaCommodityLimit(XdaCommodityLimitIDTO appIDTO) {
                return null;
            }
            @Override
            public List<CommodityTextTagInfoODTO> selectCommodityTextTagInfoList(List<Long> commodityIdList) {
                return null;
            }

            @Override
            public List<XdaCommodityTextPicODTO> selectCommodityTextPicList(List<Long> commodityIdList) {
                return null;
            }

            @Override
            public List<XdaShoppingCartODTO> queryXdaShoppingCartQuantityV2(XdaShoppingCartIDTO idto) {
                return null;
            }

            @Override
            public List<XdaCommodityAppV4ODTO> queryXdaCommodityDetailsForApp(XdaCommodityAppV4IDTO idto) {
                return null;
            }

            @Override
            public List<XdaCommodityAppV4ODTO> queryXdaCommodityListForApp(XdaCommodityAppV4IDTO idto) {
                return null;
            }

            @Override
            public List<XdaSerialCommodityODTO> querySerialCommodityListFront(XdaSerialCommodityIDTO detailAppODTO) {
                return null;
            }

            @Override
            public List<XdaCommodityCollectODTO> getXdaCommodityCollects(XdaCommodityCollectIDTO idto) {
                return null;
            }

            @Override
            public List<CommodityFreezeGroupODTO> getCommodityFreezeGroups(List<Long> commodityIdList) {
                return null;
            }

            @Override
            public List<StoreCommodityPriceODTO> getStoreCommodityPrice(Long storeId) {
                return null;
            }

            @Override
            public List<XdaSpecialsCommoditySetODTO> selectXdaSpecialsCommoditySetListByCommodityIdList(List<Long> commodityIdList) {
                return null;
            }
        };
    }
}
