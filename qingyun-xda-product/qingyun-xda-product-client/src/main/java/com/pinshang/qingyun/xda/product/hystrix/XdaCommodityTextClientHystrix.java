package com.pinshang.qingyun.xda.product.hystrix;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.xda.product.dto.SpecialResultODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.*;
import com.pinshang.qingyun.xda.product.service.XdaCommodityTextClient;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 鲜达商品文描管理
 */
@Component
public class XdaCommodityTextClientHystrix implements FallbackFactory<XdaCommodityTextClient> {

    @Override
    public XdaCommodityTextClient create(Throwable throwable) {
        return new XdaCommodityTextClient(){
        	
        	@Override
			public PageInfo<CommodityTextInfoODTO> selectCommodityTextInfoPage(SelectCommodityTextInfoPageIDTO idto) {
				return null;
			}

			@Override
			public PageInfo<CommoditySortNumInfoODTO> selectCommoditySortNumInfoPage(SelectCommoditySortNumInfoPageIDTO idto) {
				return null;
			}

			@Override
			public SpecialResultODTO batchUpdateCommodityAppName(BatchUpdateCommodityAppNameIDTO idto) {
				return null;
			}

			@Override
			public SpecialResultODTO batchUpdateCommoditySubName(BatchUpdateCommoditySubNameIDTO idto) {
				return null;
			}

			@Override
			public SpecialResultODTO batchUpdateCommodityXdaCategory(BatchUpdateCommodityXdaCategoryIDTO idto) {
				return null;
			}

			@Override
			public SpecialResultODTO batchUpdateCommodityTag(BatchUpdateCommodityTagIDTO idto) {
				return null;
			}

			@Override
			public SpecialResultODTO batchUpdateCommoditySortNum(BatchUpdateCommoditySortNumIDTO idto) {
				return null;
			}

			@Override
			public SpecialResultODTO batchUpdateCommodityQualityStatus(BatchUpdateCommodityQualityStatusIDTO idto) {
				return null;
			}


			@Override
			public List<XdaCommodityInfoODTO> selectXdaCommodityInfoList(SelectXdaCommodityInfoListIDTO idto) {
				return null;
			}

			@Override
			public List<XdaCommodityDropdownInfoODTO> selectXdaCommodityDropdownInfoList(SelectXdaCommodityDropdownInfoListIDTO idto) {
				return null;
			}

			@Override
			public List<CommodityTextInfoODTO> queryAllXdaCommodityText(SelectXdaCommodityInfoListIDTO idto) {
				return null;
			}

			@Override
			public List<CommodityTextEsODTO> selectCommodityTextEsList(SelectXdaCommodityInfoListIDTO dto) {
				return null;
			}

			@Override
			public List<XdaCommodityTextODTO> selectCommodityByCateId(Long cateId) {
				return null;
			}

		};
    }
    
}
