package com.pinshang.qingyun.xda.product.hystrix;

import com.pinshang.qingyun.xda.product.dto.shoppingCart.XdaShoppingCartV3IDTO;
import com.pinshang.qingyun.xda.product.dto.shoppingCart.XdaShoppingCartV3ODTO;
import com.pinshang.qingyun.xda.product.service.XdaShoppingCartController;
import org.springframework.cloud.openfeign.FallbackFactory;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


public class XdaShoppingCartControllerHystrix implements FallbackFactory<XdaShoppingCartController> {
    @Override
    public XdaShoppingCartController create(Throwable throwable) {
        return new XdaShoppingCartController(){

            @Override
            public XdaShoppingCartV3ODTO getXdaCommoditySalesBoxCapacity(XdaShoppingCartV3IDTO appIDTO) {
                return null;
            }

            @Override
            public XdaShoppingCartV3ODTO getXdaCommodityLimit(XdaShoppingCartV3IDTO appIDTO) {
                return null;
            }

            @Override
            public List<XdaShoppingCartV3ODTO> getVarietySum(XdaShoppingCartV3IDTO appIDTO) {
                return null;
            }

            @Override
            public List<XdaShoppingCartV3ODTO> getShopCartList(XdaShoppingCartV3IDTO appIDTO) {
                return null;
            }

            @Override
            public List<XdaShoppingCartV3ODTO> getXdaCommodityTextList(XdaShoppingCartV3IDTO appIDTO) {
                return null;
            }

            @Override
            public Map<Long, BigDecimal> selectCommodityPackageSpecByCommodityIdList(List<Long> commodityIdList) {
                return null;
            }

            @Override
            public List<XdaShoppingCartV3ODTO> getValidCommodityGift(XdaShoppingCartV3IDTO appIDTO) {
                return null;
            }

            @Override
            public XdaShoppingCartV3ODTO getXdaCommodityTextListIsCanOrder(XdaShoppingCartV3IDTO appIDTO) {
                return null;
            }
        };
    }

}
