package com.pinshang.qingyun.xda.product.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.xda.product.dto.category.*;
import com.pinshang.qingyun.xda.product.dto.front.FeignXdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.FeignXdaCategoryCommodityResODTO;
import com.pinshang.qingyun.xda.product.dto.front.FeignXdaRecommondCommodityIDTO;
import com.pinshang.qingyun.xda.product.dto.front.FeignXdaRecommondCommodityODTO;
import com.pinshang.qingyun.xda.product.hystrix.XdaCategoryClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 鲜达前台品类
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_XDA_PRODUCT_SERVICE, fallbackFactory = XdaCategoryClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XdaCategoryClient {

	/**
	 * 查询  鲜达前台品类
	 *
	 * @param id
	 * @return
	 */
	@PostMapping(value = "/xdaCategory/selectXdaCategory")
	public XdaCategoryODTO selectXdaCategory(@RequestParam(value = "id",required = false) Long id);


	@PostMapping("/xdaCategory/findAllSecondXdaCategoryListToExport")
	public List<XdaCategoryListODTO> findAllSecondXdaCategoryListToExport();

	@PostMapping("/xdaCategoryFrontV4/queryXdaCategoryCommodityList")
	FeignXdaCategoryCommodityResODTO queryXdaCategoryCommodityList(@RequestBody FeignXdaCategoryAppIDTO appIDTO);

	@PostMapping("/xdaCategoryFrontV4/queryRecommondCommodity")
	FeignXdaRecommondCommodityODTO queryRecommondCommodity(@RequestBody FeignXdaRecommondCommodityIDTO req);

	@PostMapping(value = "/xdaCategoryFrontV4/queryExtraCategory")
	FeignXdaCategoryResODTO queryExtraCategory(@RequestParam(value = "orderTime", required = false) String orderTime
			, @RequestBody(required = false) FeignXdaExtraCategoryIDTO req);

	@GetMapping("/xdaCategory/findNormalList")
	List<XdaCategoryODTO> findAllXdaCategory();

	/**
	 * 查询鲜达前台品类list
	 *
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/xdaCategory/selectXdaCategoryList",method = RequestMethod.POST)
	List<XdaCategoryODTO> selectXdaCategoryList(@RequestBody XdaCategoryListIDTO idto);

	@RequestMapping(value = "/xdaCategoryFrontV4/queryCategoryCommodityField",method = RequestMethod.POST)
	List<FeignCategoryCommodityFieldODTO> queryCategoryCommodityField(@RequestBody FeignCategoryCommodityFieldIDTO req);

	@RequestMapping(value = "/xdaCategoryFrontV4/queryXdaCategoryCommodityIdList", method = RequestMethod.POST)
	List<Long> queryXdaCategoryCommodityIdList(@RequestBody FeignXdaCategoryAppIDTO appIDTO);
}
