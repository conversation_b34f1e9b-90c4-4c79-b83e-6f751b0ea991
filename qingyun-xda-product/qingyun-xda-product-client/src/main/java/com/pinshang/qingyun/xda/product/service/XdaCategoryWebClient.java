package com.pinshang.qingyun.xda.product.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.xda.product.dto.category.*;
import com.pinshang.qingyun.xda.product.dto.front.*;
import com.pinshang.qingyun.xda.product.hystrix.XdaCategoryWebClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 鲜达前台品类
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_XDA_PRODUCT_SERVICE, fallbackFactory = XdaCategoryWebClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XdaCategoryWebClient {


    @PostMapping("/xdaCategoryFrontWeb/queryXdaCategoryCommodityList")
    FeignXdaCategoryCommodityResODTO queryXdaCategoryCommodityList(@RequestBody FeignXdaCategoryAppIDTO appIDTO);

    @PostMapping(value = "/xdaCategoryFrontWeb/queryExtraCategory")
    FeignXdaCategoryResODTO queryExtraCategoryV2(@RequestBody FeignQueryExtraCategoryIDTO req);

    @PostMapping(value = "/xdaCategoryFrontWeb/queryRecommondCommodity")
    FeignXdaRecommondCommodityODTO queryRecommondCommodity(@RequestBody FeignXdaRecommondCommodityWebIDTO req);


}
