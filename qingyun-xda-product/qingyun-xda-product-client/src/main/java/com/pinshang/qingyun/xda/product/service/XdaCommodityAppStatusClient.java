package com.pinshang.qingyun.xda.product.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.xda.product.dto.commodityAppStatus.XdaCommodityAppStatusIDTO;
import com.pinshang.qingyun.xda.product.dto.commodityAppStatus.XdaCommodityAppStatusODTO;
import com.pinshang.qingyun.xda.product.dto.commodityAppStatus.XdaCommodityAppStatusToDownIDTO;
import com.pinshang.qingyun.xda.product.hystrix.XdaCommodityAppStatusClientHystix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @time: 2020/12/15 10:50
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_XDA_PRODUCT_SERVICE, fallbackFactory = XdaCommodityAppStatusClientHystix.class, configuration = FeignClientConfiguration.class)
public interface XdaCommodityAppStatusClient {

    /**
     * 商品上下架 列表导出
     * @param xdaCommodityAppStateIDTO
     * @return
     */
    @RequestMapping(value = "/xdaCommodityAppStatus/selectCommodityAppStatusList")
    public PageInfo<XdaCommodityAppStatusODTO> selectCommodityAppStatusList(@RequestBody XdaCommodityAppStatusIDTO xdaCommodityAppStateIDTO);

    /**
     * 总部商品不可售-操作鲜达商品下架
     * @param idto
     * @return
     */
    @RequestMapping(value = "/xdaCommodityAppStatus/updateXdaCommodityAppStatusToDown")
    Long updateXdaCommodityAppStatusToDown(@RequestBody XdaCommodityAppStatusToDownIDTO idto);

    /**
     * 批量查询商品上下架状态(marketing调用，创建买赠商品判断)
     */
    @RequestMapping(value = "/xdaCommodityAppStatus/batchSelectCommodityAppStatus")
    List<XdaCommodityAppStatusODTO> batchSelectCommodityAppStatus(@RequestBody List<Long> commodityIdList);

}
