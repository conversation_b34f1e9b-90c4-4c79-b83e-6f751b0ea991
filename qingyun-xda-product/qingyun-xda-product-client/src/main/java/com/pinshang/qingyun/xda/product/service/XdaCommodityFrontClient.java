package com.pinshang.qingyun.xda.product.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.xda.product.dto.*;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import com.pinshang.qingyun.xda.product.dto.front.*;
import com.pinshang.qingyun.xda.product.hystrix.XdaCommodityFrontClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@FeignClient(value = ApplicationNameConstant.QINGYUN_XDA_PRODUCT_SERVICE, fallbackFactory = XdaCommodityFrontClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XdaCommodityFrontClient {

    @RequestMapping(value = "/xdaCommodityFront/queryXdaCommodityListForApp",method = RequestMethod.POST)
    List<XdaCommodityAppODTO> queryXdaCommodityListForApp(@RequestBody XdaCommodityAppIDTO appIDTO);

    @RequestMapping(value = "/xdaCommodityFrontV2/queryXdaCommodityListForAppV2",method = RequestMethod.POST)
    List<XdaCommodityAppV2ODTO> queryXdaCommodityListForAppV2(@RequestBody XdaCommodityAppV2IDTO appIDTO);

    @RequestMapping(value = "/xdaCommodityFrontV4/queryXdaCommodityListForAppV4",method = RequestMethod.POST)
    List<XdaCommodityAppV4ODTO> queryXdaCommodityListForAppV4(@RequestBody XdaCommodityAppV4IDTO appIDTO);
    
    @RequestMapping(value = "/xdaCommodityFront/queryXdaCommodityAppStatus",method = RequestMethod.POST)
    Map<Long, Integer> queryXdaCommodityAppStatus(@RequestBody List<Long> commodityIdList);

    @RequestMapping(value = "/xdaCommodityFront/queryXdaCommodityDeliveryTime",method = RequestMethod.POST)
    Map<Long, XdaCommodityDeliveryTimeODTO> queryXdaCommodityDeliveryTime(@RequestBody XdaCommodityDeliveryTimeIDTO appIDTO);

    @RequestMapping(value = "/xdaCommodityFrontV2/findOrderTargetByStoreIdAndOrderTime",method = RequestMethod.POST)
    BigDecimal findOrderTargetByStoreIdAndOrderTime(@RequestBody XdaShoppingCartV2IDTO xdaShoppingCartV2IDTO);

    @RequestMapping(value = "/xdaCommodityFrontV2/findXdaOrderTargetIdByStoreIdAndOrderTime",method = RequestMethod.POST)
    XdaOrderTargetSetV2ODTO findXdaOrderTargetIdByStoreIdAndOrderTime(@RequestBody XdaShoppingCartV2IDTO xdaShoppingCartV2IDTO);

    @RequestMapping(value = "/xdaCommodityFrontV2/selectXdaSpecialsCommoditySet", method = RequestMethod.POST)
    List<Long> selectXdaSpecialsCommoditySet(@RequestBody List<Long> commodityIdList);

    @RequestMapping(value = "/xdaCommodityFront/queryXdaCommodityLimit", method = RequestMethod.POST)
    Map<Long, XdaCommodityLimitODTO> queryXdaCommodityLimit(@RequestBody XdaCommodityLimitIDTO appIDTO);


    @PostMapping("/xda/commodity/selectCommodityTextTagInfoList")
    List<CommodityTextTagInfoODTO> selectCommodityTextTagInfoList(@RequestBody List<Long> commodityIdList);

    @PostMapping("/xda/commodity/selectCommodityTextPicList")
    List<XdaCommodityTextPicODTO> selectCommodityTextPicList(@RequestBody List<Long> commodityIdList);

    @PostMapping("/xda/commodity/queryXdaShoppingCartQuantityV2")
    List<XdaShoppingCartODTO> queryXdaShoppingCartQuantityV2(@RequestBody XdaShoppingCartIDTO idto);

    @PostMapping("/xda/commodity/queryXdaCommodityDetailsForApp")
    public List<XdaCommodityAppV4ODTO> queryXdaCommodityDetailsForApp(@RequestBody XdaCommodityAppV4IDTO idto);

    @PostMapping("/xda/commodity/queryXdaCommodityListForApp")
    List<XdaCommodityAppV4ODTO> queryXdaCommodityListForApp(@RequestBody XdaCommodityAppV4IDTO idto);

    @PostMapping("/xda/commodity/querySerialCommodityListFront")
    List<XdaSerialCommodityODTO> querySerialCommodityListFront(@RequestBody XdaSerialCommodityIDTO idto);

    @PostMapping("/xda/commodity/getXdaCommodityCollects")
    List<XdaCommodityCollectODTO> getXdaCommodityCollects(@RequestBody XdaCommodityCollectIDTO idto);

    @PostMapping("/xda/commodity/getCommodityFreezeGroups")
    List<CommodityFreezeGroupODTO> getCommodityFreezeGroups(@RequestBody List<Long> commodityIdList);

    /**
     * 根据storeId查询客户的产品价格方案下面的商品及价格(鲜达分类页、搜索页面渲染使用)
     * @param storeId
     * @return
     */
    @PostMapping("/xda/commodity/getStoreCommodityPrice")
    List<StoreCommodityPriceODTO> getStoreCommodityPrice(@RequestParam("storeId") Long storeId);

    @RequestMapping(value = "/xdaCommodityFrontV2/selectXdaSpecialsCommoditySetListByCommodityIdList", method = RequestMethod.POST)
    List<XdaSpecialsCommoditySetODTO> selectXdaSpecialsCommoditySetListByCommodityIdList(@RequestBody List<Long> commodityIdList);
}
