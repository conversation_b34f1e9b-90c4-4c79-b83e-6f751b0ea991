package com.pinshang.qingyun.xda.product.service;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.xda.product.dto.SpecialResultODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.*;
import com.pinshang.qingyun.xda.product.hystrix.XdaCommodityTextClientHystrix;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 鲜达商品文描管理
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_XDA_PRODUCT_SERVICE, fallbackFactory = XdaCommodityTextClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface XdaCommodityTextClient {
	
	/**
	 * 分页查询  商品文描信息  列表
	 * 
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/xdaCommodityText/selectCommodityTextInfoPage", method = RequestMethod.POST)
	public PageInfo<CommodityTextInfoODTO> selectCommodityTextInfoPage(@RequestBody SelectCommodityTextInfoPageIDTO idto);
	
	/**
	 * 分页查询  商品排序信息  列表
	 *
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/xdaCommodityText/selectCommoditySortNumInfoPage", method = RequestMethod.POST)
	public PageInfo<CommoditySortNumInfoODTO> selectCommoditySortNumInfoPage(@RequestBody SelectCommoditySortNumInfoPageIDTO idto);

	/**
	 * 批量更新  商品文描-前台品名
	 * 
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/xdaCommodityText/batchUpdateCommodityAppName", method = RequestMethod.POST)
	public SpecialResultODTO batchUpdateCommodityAppName(@RequestBody BatchUpdateCommodityAppNameIDTO idto);
	
	/**
	 * 批量更新  商品文描-副标题
	 * 
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/xdaCommodityText/batchUpdateCommoditySubName", method = RequestMethod.POST)
	public SpecialResultODTO batchUpdateCommoditySubName(@RequestBody BatchUpdateCommoditySubNameIDTO idto);
	
	/**
	 * 批量更新  商品文描-前台品类
	 * 
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/xdaCommodityText/batchUpdateCommodityXdaCategory", method = RequestMethod.POST)
	public SpecialResultODTO batchUpdateCommodityXdaCategory(@RequestBody BatchUpdateCommodityXdaCategoryIDTO idto);
	
	/**
	 * 批量更新  商品文描-标签
	 * 
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/xdaCommodityText/batchUpdateCommodityTag", method = RequestMethod.POST)
	public SpecialResultODTO batchUpdateCommodityTag(@RequestBody BatchUpdateCommodityTagIDTO idto);

	/**
	 * 批量更新  商品文描-排序
	 *
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/xdaCommodityText/batchUpdateCommoditySortNum", method = RequestMethod.POST)
	public SpecialResultODTO batchUpdateCommoditySortNum(@RequestBody BatchUpdateCommoditySortNumIDTO idto);

	/**
	 * 批量更新  商品文描-是否显示保质期
	 *
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/xdaCommodityText/batchUpdateCommodityQualityStatus",method = RequestMethod.POST)
	SpecialResultODTO batchUpdateCommodityQualityStatus(@RequestBody BatchUpdateCommodityQualityStatusIDTO idto);

	
	
    /**
     * 查询  门店商品信息 列表
     * 
     * @param idto
     * @return
     */
    @RequestMapping(value = "/xdaCommodityText/selectXdaCommodityInfoList", method = RequestMethod.POST)
    public List<XdaCommodityInfoODTO> selectXdaCommodityInfoList(@RequestBody SelectXdaCommodityInfoListIDTO idto);
    
    /**
     * 查询   鲜达商品下拉信息  列表
     * 
     * @param idto
     * @return
     */
    @RequestMapping(value = "/xdaCommodityText/selectXdaCommodityDropdownInfoList", method = RequestMethod.POST)
    public List<XdaCommodityDropdownInfoODTO> selectXdaCommodityDropdownInfoList(@RequestBody SelectXdaCommodityDropdownInfoListIDTO idto);


	/**
	 * 查询所有文描信息(前台品名，1级2级分类，是否上下架)
	 * @param idto
	 * @return
	 */
	@RequestMapping(value = "/xdaCommodityText/queryAllXdaCommodityText", method = RequestMethod.POST)
	List<CommodityTextInfoODTO> queryAllXdaCommodityText(@RequestBody SelectXdaCommodityInfoListIDTO idto);

	/**
	 * 查询鲜达商品(xda-search手动初始化)
	 * @param dto
	 * @return
	 */
	@RequestMapping(value = "/xdaCommodityText/selectCommodityTextEsList", method = RequestMethod.POST)
	List<CommodityTextEsODTO> selectCommodityTextEsList(@RequestBody SelectXdaCommodityInfoListIDTO dto);

	@ApiOperation("根据分类id查询所有商品id")
	@RequestMapping(value  = "/xdaCommodityText/selectCommodityByCateId/{cateId}", method = RequestMethod.GET)
	List<XdaCommodityTextODTO> selectCommodityByCateId(@PathVariable("cateId") Long cateId);

}
