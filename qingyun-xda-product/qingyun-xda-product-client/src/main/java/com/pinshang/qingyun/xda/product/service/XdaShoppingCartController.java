package com.pinshang.qingyun.xda.product.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppV2IDTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppV2ODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDeliveryTimeODTO;
import com.pinshang.qingyun.xda.product.dto.shoppingCart.XdaShoppingCartV3IDTO;
import com.pinshang.qingyun.xda.product.dto.shoppingCart.XdaShoppingCartV3ODTO;
import com.pinshang.qingyun.xda.product.hystrix.XdaShoppingCartControllerHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@FeignClient(value = ApplicationNameConstant.QINGYUN_XDA_PRODUCT_SERVICE, fallbackFactory = XdaShoppingCartControllerHystrix.class, configuration = FeignClientConfiguration.class)
public interface XdaShoppingCartController {

    @PostMapping(value = "/xdaShoppingCart/getXdaCommoditySalesBoxCapacity")
    XdaShoppingCartV3ODTO getXdaCommoditySalesBoxCapacity(@RequestBody XdaShoppingCartV3IDTO appIDTO);

    @PostMapping(value = "/xdaShoppingCart/getXdaCommodityLimit")
    XdaShoppingCartV3ODTO getXdaCommodityLimit(@RequestBody XdaShoppingCartV3IDTO appIDTO);

    @PostMapping(value = "/xdaShoppingCart/getVarietySum")
    List<XdaShoppingCartV3ODTO> getVarietySum(@RequestBody XdaShoppingCartV3IDTO appIDTO);

    @PostMapping(value = "/xdaShoppingCart/getShopCartList")
    List<XdaShoppingCartV3ODTO> getShopCartList(@RequestBody XdaShoppingCartV3IDTO appIDTO);

    @PostMapping(value = "/xdaShoppingCart/getXdaCommodityTextList")
    List<XdaShoppingCartV3ODTO> getXdaCommodityTextList(@RequestBody XdaShoppingCartV3IDTO appIDTO);

    @PostMapping(value = "/xdaShoppingCart/selectCommodityPackageSpecByCommodityIdList")
    Map<Long, BigDecimal> selectCommodityPackageSpecByCommodityIdList(@RequestBody List<Long> commodityIdList);

    @PostMapping(value = "/xdaShoppingCart/getValidCommodityGift")
    List<XdaShoppingCartV3ODTO> getValidCommodityGift(@RequestBody XdaShoppingCartV3IDTO appIDTO);

    @PostMapping(value = "/xdaShoppingCart/getXdaCommodityTextListIsCanOrder")
    XdaShoppingCartV3ODTO getXdaCommodityTextListIsCanOrder(@RequestBody XdaShoppingCartV3IDTO appIDTO);
}
