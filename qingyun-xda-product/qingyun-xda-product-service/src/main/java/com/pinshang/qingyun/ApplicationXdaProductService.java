package com.pinshang.qingyun;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.pinshang.qingyun.base.spring.MainArgsPreHandler;
import com.pinshang.qingyun.infrastructure.apm.cat.springboot.EnableCatMetrics;
import com.pinshang.qingyun.infrastructure.cache.starter.EnableCacheComponent;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheConfiguration;
import com.pinshang.qingyun.infrastructure.loadBalancer.starter.EnableQyLoadBalancer;
import com.pinshang.qingyun.infrastructure.mq.starter.EnableMqComponent;
import com.pinshang.qingyun.infrastructure.switcher.starter.EnableOnlineSwitchComponent;
import com.pinshang.qinyun.cache.service.RedisServiceDefinition;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;

import tk.mybatis.spring.annotation.MapperScan;

@Controller
//@EnableWebMvc
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients
@MapperScan(basePackages = { "com.pinshang.qingyun.xda.*.mapper"})
@EnableAsync
@EnableAspectJAutoProxy(exposeProxy = true)
@Import(value = {RedisServiceDefinition.class, FileCacheConfiguration.class})
@EnableMqComponent
@EnableApolloConfig
@EnableOnlineSwitchComponent
@EnableCatMetrics
@EnableQyLoadBalancer
@EnableCacheComponent
public class ApplicationXdaProductService extends WebMvcConfigurerAdapter {

    public static void main(String[] args) {
        SpringApplication.run(ApplicationXdaProductService.class, MainArgsPreHandler.argsHandle(args));
    }

    @GetMapping(value={"","/"})
    public String index(){
        return "redirect:/chk.html";
    }

}
