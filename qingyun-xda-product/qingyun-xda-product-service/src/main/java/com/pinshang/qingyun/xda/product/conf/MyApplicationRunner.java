package com.pinshang.qingyun.xda.product.conf;

import com.pinshang.qingyun.xda.product.helper.BizCodeGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class MyApplicationRunner implements ApplicationRunner {
    @Resource
    BizCodeGenerator bizCodeGenerator;


    @Override
    public void run(ApplicationArguments args) throws Exception {
//        bizCodeGenerator.syncCommodityDate();
        log.info("日期装备完成");
    }
}
