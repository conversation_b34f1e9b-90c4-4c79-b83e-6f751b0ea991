package com.pinshang.qingyun.xda.product.conf;

import com.pinshang.qingyun.base.interceptor.QYServiceInterceptor;
import com.pinshang.qingyun.base.interceptor.XdaServiceInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

/**
 * <AUTHOR> weican on 2018-11-14.
 */
@Configuration
public class XdaProductConfiguration {
    @Bean("xdaServiceInterceptor")
    public HandlerInterceptorAdapter xdaServiceInterceptor(){
        return new XdaServiceInterceptor();
    }

    @Bean("qyServiceInterceptor")
    public HandlerInterceptorAdapter qyServiceInterceptor(){
        return new QYServiceInterceptor();
    }
}
