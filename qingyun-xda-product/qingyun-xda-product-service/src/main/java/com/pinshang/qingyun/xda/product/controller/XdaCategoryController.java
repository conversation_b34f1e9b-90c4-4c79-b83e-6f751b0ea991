package com.pinshang.qingyun.xda.product.controller;

import com.pinshang.qingyun.xda.product.dto.xdaCategory.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.xda.product.model.XdaCategory;
import com.pinshang.qingyun.xda.product.service.XdaCategoryService;

/**
 * 鲜达前台品类
 *
 * <AUTHOR>
 *
 * @date 2019年11月21日
 */
@RestController
@RequestMapping("/xdaCategory")
@Api(value = "鲜达-前台品类", tags = "XdaCategoryController", description ="鲜达-前台品类" )
public class XdaCategoryController {
	
	@Autowired
	private XdaCategoryService categoryService;

	@Value("${pinshang.img-server-url}")
	private String imgServerUrl;
	
	/**
	 * 查询鲜达前台品类
	 * 
	 * @param id
	 * @return
	 */
	@PostMapping("/selectXdaCategory")
	public XdaCategory selectXdaCategory(@RequestParam(value = "id", defaultValue = "0",required = false)Long id) {
    	return categoryService.selectXdaCategory(id);
    }

	/**
	 * 查询鲜达前台品类list
	 *
	 * @param idto
	 * @return
	 */
	@PostMapping("/selectXdaCategoryList")
	public List<XdaCategory> selectXdaCategoryList(@RequestBody XdaCategoryListIDTO idto) {
    	return categoryService.selectXdaCategoryList(idto.getIdList());
    }

	/**
	 * 查询品类树
	 * @return
	 */
	@ApiOperation(value = "查询品类树", notes = "查询品类树")
	@PostMapping("/selectXdaCategoryTree")
	public XdaCategoryTreeODTO selectXdaCategoryTree(){
		List<XdaCategory> allXdaCategory = categoryService.findAllXdaCategory();
		return this.convertListToTree(allXdaCategory);
	}

	/**
	 * list 转换 为 Tree
	 * @param categoryList
	 * @return
	 */
	private XdaCategoryTreeODTO convertListToTree(List<XdaCategory> categoryList) {
		List<XdaCategoryTreeODTO> categoryInfoTreeList = new ArrayList<>();

		categoryList.forEach(i ->{
			String picUrl = i.getPicUrl();
			String visitPicUrl = StringUtil.isNullOrEmpty(picUrl)? null: (imgServerUrl + "/" + picUrl);
			categoryInfoTreeList.add(new XdaCategoryTreeODTO(i.getId().toString(), i.getParentId().toString(), i.getCateName(), i.getCateLevel(), picUrl, i.getSortNum(),visitPicUrl, null));
		});

		List<XdaCategoryTreeODTO> result = new ArrayList<>();
		for(XdaCategoryTreeODTO dto: categoryInfoTreeList) {
			if (dto.getParentId().equals("0")) {
				result.add(dto);
			}
			for (XdaCategoryTreeODTO dto2: categoryInfoTreeList) {
				if (dto2.getParentId().equals(dto.getId())) {
					if (dto.getChildren() == null) {
						List<XdaCategoryTreeODTO> myChildrens = new ArrayList<>();
						myChildrens.add(dto2);
						dto.setChildren(myChildrens);
					} else {
						dto.getChildren().add(dto2);
					}
				}
			}
		}
		return new XdaCategoryTreeODTO("0", "-1", "前台品类", 0, null, 1,null, result);
	}


    /**
     * 添加品类
     * @param vo
     * @return
     */
	@ApiOperation(value = "添加品类", notes = "添加品类")
	@PostMapping(value = "/insertXdaCategory")
	public int insertXdaCategory(@RequestBody XdaCategorySaveVo vo){
		Long userId = FastThreadLocalUtil.getQY().getUserId();
		vo.setUserId(userId);
		return categoryService.insertXdaCategory(vo);
	}

	/**
	 * 删除品类
	 * @param vo
	 * @return
	 */
	@ApiOperation(value = "删除品类", notes = "删除品类")
	@PostMapping(value = "/deleteXdaCategory")
	public int deleteXdaCategory(@RequestBody XdaCategoryEditVo vo){
		Long userId = FastThreadLocalUtil.getQY().getUserId();
		vo.setUserId(userId);
		return categoryService.deleteXdaCategory(vo);
	}

	/**
	 * 修改品类
	 * @param vo
	 * @return
	 */
	@ApiOperation(value = "修改品类", notes = "修改品类")
	@PostMapping("/editXdaCategory")
	public int editXdaCategory(@RequestBody XdaCategoryEditVo vo){
		Long userId = FastThreadLocalUtil.getQY().getUserId();
		vo.setUserId(userId);
		return categoryService.editXdaCategory(vo);
	}

	/**
	 * 修改品类图片
	 * @param vo
	 * @return
	 */
	@ApiOperation(value = "修改品类图片", notes = "修改品类图片")
	@PostMapping("/editXdaCategoryPicUrl")
	public int editXdaCategoryPicUrl(@RequestBody XdaCategoryEditVo vo){
		Long userId = FastThreadLocalUtil.getQY().getUserId();
		vo.setUserId(userId);
		return categoryService.editXdaCategoryPicUrl(vo);
	}


	/**
	 * 查询所有二级分类 to 导出
	 * @return
	 */
	@ApiOperation(value = "查询所有二级分类 to 导出", notes = "查询所有二级分类 to 导出")
	@PostMapping("/findAllSecondXdaCategoryListToExport")
	public List<XdaCategoryListODTO> findAllSecondXdaCategoryListToExport(){
		return categoryService.findAllSecondXdaCategoryListToExport();
	}

	/**
	 * 查询所有一级分类
	 * @return
	 */
	@ApiOperation(value = "查询所有一级分类", notes = "查询所有一级分类")
	@PostMapping("/findAllFirstXdaCategoryList")
	public List<XdaCategoryListODTO> findAllFirstXdaCategoryList(){
		return categoryService.findAllFirstXdaCategoryList();
	}

	/**
     * 查询  品类和父级信息  集合
     *
     * @param categoryIdList
     * @return
     */
	@PostMapping("/selectXdaCategoryAndParentInfoList")
    public List<XdaCategoryAndParentInfoODTO> selectXdaCategoryAndParentInfoList(@RequestBody List<Long> categoryIdList) {
    	return categoryService.selectXdaCategoryAndParentInfoList(categoryIdList);
    }

    /**
     * 查询  一、二级品类Id  集合
     * @return
     */
    @PostMapping("/selectXdaCuponList")
    public List<XdaCuponODTO> selectXdaCuponList() {
        return categoryService.selectXdaCuponList();
    }


	/**
	 * 查看所有正常状态的分类
	 * @return
	 */
	@GetMapping("/findNormalList")
	public List<XdaCategory> findNormalList(){
		return categoryService.findAllXdaCategory();

	}
}
