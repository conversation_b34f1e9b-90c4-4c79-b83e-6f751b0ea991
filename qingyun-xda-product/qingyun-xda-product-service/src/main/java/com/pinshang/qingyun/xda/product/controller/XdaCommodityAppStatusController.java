package com.pinshang.qingyun.xda.product.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.*;
import com.pinshang.qingyun.xda.product.service.XdaCommodityAppStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @time: 2020/12/15 10:50
 */
@RestController
@RequestMapping("/xdaCommodityAppStatus")
@Api(value = "鲜达-商品上下架", tags = "XdaCommodityAppStatusController",description ="鲜达-商品上下架")
public class XdaCommodityAppStatusController {

    @Autowired
    private XdaCommodityAppStatusService xdaCommodityAppStatusService;

    @ApiOperation(value = "查询鲜达上下架列表",httpMethod ="POST",notes = "查询鲜达上下架列表")
    @PostMapping("/selectCommodityAppStatusList")
    public PageInfo<XdaCommodityAppStatusODTO> selectCommodityAppStatusList(@RequestBody XdaCommodityAppStatusIDTO xdaCommodityAppStateIDTO){
        return xdaCommodityAppStatusService.selectCommodityAppStatusList(xdaCommodityAppStateIDTO);
    }
    @ApiOperation(value = "鲜达商品上下架",httpMethod ="POST",notes = "鲜达商品上下架")
    @PostMapping("/modifyXdaCommodityAppStatus")
    public Object modifyXdaCommodityAppStatus(@RequestBody XdaCommodityAppStatusUpIDTO idto){
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        idto.setUserId(userId);
        return xdaCommodityAppStatusService.modifyXdaCommodityAppStatus(idto);
    }
    @ApiOperation(value = "查询鲜达上下架日志列表",httpMethod ="POST",notes = "查询鲜达上下架日志列表")
    @PostMapping("/selectCommodityAppStatusLogList")
    public PageInfo<XdaCommodityAppStatusLogODTO> selectCommodityAppStatusLogList(@RequestBody XdaCommodityAppStatusLogIDTO xdaCommodityAppStateLogIDTO){
        return xdaCommodityAppStatusService.selectCommodityAppStatusLogList(xdaCommodityAppStateLogIDTO);
    }

    @ApiOperation(value = "鲜达商品一键上架",httpMethod ="POST",notes = "鲜达商品一键上架")
    @PostMapping("/upCommodity")
    public void upCommodity(@RequestBody XdaCommodityAppStatusLogIDTO dto){
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        xdaCommodityAppStatusService.upCommodity(userId,dto.getAppType());
    }

    @ApiOperation(value = "总部商品不可售-操作鲜达商品下架",httpMethod ="POST",notes = "总部商品不可售-操作鲜达商品下架")
    @PostMapping("/updateXdaCommodityAppStatusToDown")
    public Long updateXdaCommodityAppStatusToDown(@RequestBody XdaCommodityAppStatusToDownIDTO idto){
        return xdaCommodityAppStatusService.updateXdaCommodityAppStatusToDown(idto);
    }

    /**
     * 批量查询商品上下架状态
     */
    @ApiOperation(value = "批量查询商品上下架状态", httpMethod = "POST", notes = "批量查询商品上下架状态")
    @PostMapping("/batchSelectCommodityAppStatus")
    public List<XdaCommodityAppStatusODTO> batchSelectCommodityAppStatus(@RequestBody List<Long> commodityIdList) {
        return xdaCommodityAppStatusService.batchSelectCommodityAppStatus(commodityIdList);
    }
}
