package com.pinshang.qingyun.xda.product.controller;

import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.xda.product.dto.CommodityFreezeGroupODTO;
import com.pinshang.qingyun.xda.product.dto.StoreCommodityPriceODTO;
import com.pinshang.qingyun.xda.product.dto.XdaCommodityCollectODTO;
import com.pinshang.qingyun.xda.product.dto.XdaCommodityTextPicODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import com.pinshang.qingyun.xda.product.dto.front.*;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityAppV4IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityAppV4ODTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityTextService;
import com.pinshang.qingyun.xda.product.service.XdaSerialCommodityService;
import com.pinshang.qingyun.xda.product.service.front.XdaCommodityFrontService;
import com.pinshang.qingyun.xda.product.service.front.v4.XdaCommodityFrontV4Service;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @author: chenqiang
 * @time: 2020/12/22 14:27
 */
@RestController
@RequestMapping("/xda/commodity")
public class XdaCommodityController {
    @Autowired
    private XdaCommodityTextService xdaCommodityTextService;
    @Autowired
    private XdaCommodityFrontV4Service xdaCommodityFrontV4Service;
    @Autowired
    private XdaSerialCommodityService xdaSerialCommodityService;
    @Autowired
    private XdaCommodityFrontService xdaCommodityFrontService;
    @PostMapping("/selectCommodityTextTagInfoList")
    public List<CommodityTextTagInfoODTO> selectCommodityTextTagInfoList(@RequestBody List<Long> commodityIdList) {
        return xdaCommodityTextService.selectCommodityTextTagInfoList(commodityIdList);
    }

    @PostMapping("/selectCommodityTextPicList")
    public List<XdaCommodityTextPicODTO> selectCommodityTextPicList(@RequestBody List<Long> commodityIdList) {
        return BeanCloneUtils.copyTo(xdaCommodityTextService.selectCommodityTextPicList(commodityIdList), XdaCommodityTextPicODTO.class);
    }

    @PostMapping("/queryXdaShoppingCartQuantityV2")
    public List<XdaShoppingCartODTO> queryXdaShoppingCartQuantityV2(@RequestBody XdaShoppingCartIDTO idto) {
        return xdaCommodityFrontV4Service.queryXdaShoppingCartQuantityV2(idto.getStoreId(), idto.getCommodityIdList(), idto.getCommodityType());
    }

    @PostMapping("/queryXdaCommodityDetailsForApp")
    public List<XdaCommodityAppV4ODTO> queryXdaCommodityDetailsForApp(@RequestBody XdaCommodityAppV4IDTO idto) {
        return xdaCommodityFrontV4Service.queryXdaCommodityDetailsForApp(idto);
    }

    @PostMapping("/queryXdaCommodityListForApp")
    public List<XdaCommodityAppV4ODTO> queryXdaCommodityListForApp(@RequestBody XdaCommodityAppV4IDTO idto) {
        return xdaCommodityFrontV4Service.queryXdaCommodityListForApp(idto);
    }

    @PostMapping("/querySerialCommodityListFront")
    public List<XdaSerialCommodityODTO> querySerialCommodityListFront(@RequestBody XdaSerialCommodityIDTO idto) {
        return xdaSerialCommodityService.querySerialCommodityListFront(idto.getCommodityId(),idto.getXdaSecondCategoryId());
    }

    @PostMapping("/getXdaCommodityCollects")
    public List<XdaCommodityCollectODTO> getXdaCommodityCollects(@RequestBody XdaCommodityCollectIDTO idto) {
        return BeanCloneUtils.copyTo(xdaCommodityFrontV4Service.getXdaCommodityCollects(idto.getCommodityId(), idto.getStoreId()), XdaCommodityCollectODTO.class);
    }

    @PostMapping("/getCommodityFreezeGroups")
    public List<CommodityFreezeGroupODTO> getCommodityFreezeGroups(@RequestBody List<Long> commodityIdList) {
        return BeanCloneUtils.copyTo(xdaCommodityFrontV4Service.getCommodityFreezeGroups(commodityIdList), CommodityFreezeGroupODTO.class);
    }


    /**
     * 根据storeId查询客户的产品价格方案下面的商品及价格
     * @param storeId
     * @return
     */
    @PostMapping("/getStoreCommodityPrice")
    public List<StoreCommodityPriceODTO> getStoreCommodityPrice(@RequestParam("storeId") Long storeId) {
        return xdaCommodityFrontV4Service.getStoreCommodityPrice(storeId);
    }

}

