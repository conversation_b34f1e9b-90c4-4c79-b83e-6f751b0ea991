package com.pinshang.qingyun.xda.product.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.product.dto.recommend.*;
import com.pinshang.qingyun.xda.product.service.XdaCommodityRecommendService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 推荐商品设置
 */
@RestController
@RequestMapping("/xdaCommodityRecommend")
@Api(value = "推荐商品设置", tags = "XdaCommodityRecommendController",description ="推荐商品设置")
public class XdaCommodityRecommendController {
	
	@Autowired
	private XdaCommodityRecommendService xdCommodityRecommendService;


	/**
	 * 推荐方式列表
	 * @param recommendIDTO
	 * @return
	 */
	@PostMapping("/findList")
	@ApiOperation(value = "推荐方式列表")
	@ApiImplicitParam(name = "recommendIDTO", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaCommodityRecommendIDTO")
	public PageInfo<XdaCommodityRecommendODTO> findList(@RequestBody XdaCommodityRecommendIDTO recommendIDTO){
		return xdCommodityRecommendService.findList(recommendIDTO);
	}

	/**
	 * 推荐方式列表 log
	 * @param recommendLogIDTO
	 * @return
	 */
	@PostMapping("/findLogList")
	@ApiOperation(value = "推荐方式列表 log")
	@ApiImplicitParam(name = "recommendLogIDTO", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaCommodityRecommendLogIDTO")
	public PageInfo<XdaCommodityRecommendLogODTO> findLogList(@RequestBody XdaCommodityRecommendLogIDTO recommendLogIDTO){
		return xdCommodityRecommendService.findLogList(recommendLogIDTO);
	}

	/**
	 * 保存推荐方式
	 * @param xdCommodityRecommendIDTO
	 */
	@PostMapping("/save")
	@ApiOperation(value = "保存推荐方式")
	@ApiImplicitParam(name = "xdCommodityRecommendIDTO", value = "请求IDTO", required = true, paramType = "body", dataType = "XdaCommodityRecommendSaveIDTO")
	public Integer save(@RequestBody XdaCommodityRecommendSaveIDTO xdCommodityRecommendIDTO){
		Long userId = FastThreadLocalUtil.getQY().getUserId();
		xdCommodityRecommendIDTO.setUserId(userId);
		xdCommodityRecommendService.save(xdCommodityRecommendIDTO);
		return 1;
	}


	/**
	 * 删除推荐方式
	 */
	@PostMapping("/delete")
	@ApiOperation(value = "删除推荐方式")
	@ApiImplicitParam(name = "id", value = "请求IDTO", required = true, paramType = "query", dataType = "Long")
	public Integer delete(@RequestParam(value = "id",required = false) Long id){
		QYAssert.isTrue(id!=null,"删除推荐商品设置id不能为空");
		TokenInfo tokenInfo= FastThreadLocalUtil.getQY();
		xdCommodityRecommendService.delete(id,tokenInfo.getUserId());
		return 1;
	}
	
}
