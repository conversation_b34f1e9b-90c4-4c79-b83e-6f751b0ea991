package com.pinshang.qingyun.xda.product.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.product.dto.SpecialResultODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.*;
import com.pinshang.qingyun.xda.product.model.XdaCommodityText;
import com.pinshang.qingyun.xda.product.service.XdaCommodityTextService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 鲜达商品文描
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@RestController
@RequestMapping("/xdaCommodityText")
@Api(value = "鲜达-商品文描", tags = "XdaCommodityTextController", description ="鲜达-商品文描" )
public class XdaCommodityTextController {
	
	@Autowired
	private XdaCommodityTextService xdaCommodityTextService;
	
	@ApiOperation(value = "分页查询  商品文描信息  列表", notes = "分页查询  商品文描信息  列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "SelectCommodityTextInfoPageIDTO")
	@PostMapping("/selectCommodityTextInfoPage")
	public PageInfo<CommodityTextInfoODTO> selectCommodityTextInfoPage(@RequestBody SelectCommodityTextInfoPageIDTO idto) {
		return xdaCommodityTextService.selectCommodityTextInfoPage(idto);
	}
	
	@ApiOperation(value = "分页查询  商品排序信息  列表", notes = "分页查询  商品排序信息  列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "SelectCommoditySortNumInfoPageIDTO")
	@PostMapping("/selectCommoditySortNumInfoPage")
	public PageInfo<CommoditySortNumInfoODTO> selectCommoditySortNumInfoPage(@RequestBody SelectCommoditySortNumInfoPageIDTO idto) {
    	return xdaCommodityTextService.selectCommoditySortNumInfoPage(idto);
    }
	
	@ApiOperation(value = "分页查询  商品文描日志信息列表", notes = "分页查询  商品文描日志信息列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "SelectCommodityTextLogInfoPageIDTO")
	@PostMapping("/selectCommodityTextLogInfoPage")
	public PageInfo<CommodityTextLogInfoODTO> selectCommodityTextLogInfoPage(@RequestBody SelectCommodityTextLogInfoPageIDTO idto) {
		return xdaCommodityTextService.selectCommodityTextLogInfoPage(idto);
	}
	
	@ApiOperation(value = "批量更新  商品文描-前台品名", notes = "批量更新  商品文描-前台品名", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "BatchUpdateCommodityAppNameIDTO")
	@PostMapping("/batchUpdateCommodityAppName")
	public SpecialResultODTO batchUpdateCommodityAppName(@RequestBody BatchUpdateCommodityAppNameIDTO idto) {
		return xdaCommodityTextService.batchUpdateCommodityAppName(idto);
	}
	
	@ApiOperation(value = "批量更新  商品文描-副标题", notes = "批量更新  商品文描-副标题", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "BatchUpdateCommoditySubNameIDTO")
	@PostMapping("/batchUpdateCommoditySubName")
	public SpecialResultODTO batchUpdateCommoditySubName(@RequestBody BatchUpdateCommoditySubNameIDTO idto) {
		return xdaCommodityTextService.batchUpdateCommoditySubName(idto);
	}
	
	@ApiOperation(value = "批量更新  商品文描-前台品类", notes = "批量更新  商品文描-前台品类", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "BatchUpdateCommodityXdaCategoryIDTO")
	@PostMapping("/batchUpdateCommodityXdaCategory")
	public SpecialResultODTO batchUpdateCommodityXdaCategory(@RequestBody BatchUpdateCommodityXdaCategoryIDTO idto) {
		return xdaCommodityTextService.batchUpdateCommodityXdaCategory(idto);
	}

	@ApiOperation(value = "批量更新  商品文描-标签", notes = "批量更新  商品文描-标签", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "BatchUpdateCommodityTagIDTO")
	@PostMapping("/batchUpdateCommodityTag")
	public SpecialResultODTO batchUpdateCommodityTag(@RequestBody BatchUpdateCommodityTagIDTO idto) {
		return xdaCommodityTextService.batchUpdateCommodityTag(idto);
	}
	
	@ApiOperation(value = "批量更新  商品文描-排序", notes = "批量更新  商品文描-排序", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "BatchUpdateCommoditySortNumIDTO")
	@PostMapping("/batchUpdateCommoditySortNum")
	public SpecialResultODTO batchUpdateCommoditySortNum(@RequestBody BatchUpdateCommoditySortNumIDTO idto) {
		return xdaCommodityTextService.batchUpdateCommoditySortNum(idto);
	}

	@ApiOperation(value = "批量更新  商品文描-是否显示保质期", notes = "批量更新  商品文描-是否显示保质期", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataTypeClass = BatchUpdateCommodityQualityStatusIDTO.class)
	@PostMapping("/batchUpdateCommodityQualityStatus")
	public SpecialResultODTO batchUpdateCommodityQualityStatus(@RequestBody BatchUpdateCommodityQualityStatusIDTO idto) {
		return xdaCommodityTextService.batchUpdateCommodityQualityStatus(idto);
	}
	
	@ApiOperation(value = "更新  商品文描-前台品名", notes = "更新  商品文描-前台品名", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "UpdateCommodityAppNameIDTO")
	@PostMapping("/updateCommodityAppName")
	public int updateCommodityAppName(@RequestBody UpdateCommodityAppNameIDTO idto) {
		idto.setUserId(FastThreadLocalUtil.getQY().getUserId());
		return xdaCommodityTextService.updateCommodityAppName(idto);
	}
	
	@ApiOperation(value = "更新  商品文描-副标题", notes = "更新  商品文描-副标题", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "UpdateCommoditySubNameIDTO")
	@PostMapping("/updateCommoditySubName")
	public int updateCommoditySubName(@RequestBody UpdateCommoditySubNameIDTO idto) {
		idto.setUserId(FastThreadLocalUtil.getQY().getUserId());
		return xdaCommodityTextService.updateCommoditySubName(idto);
	}
	
	@ApiOperation(value = "更新  商品文描-前台品类", notes = "更新  商品文描-前台品类", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "UpdateCommodityXdaCategoryIDTO")
	@PostMapping("/updateCommodityXdaCategory")
	public int updateCommodityXdaCategory(@RequestBody UpdateCommodityXdaCategoryIDTO idto) {
		idto.setUserId(FastThreadLocalUtil.getQY().getUserId());
		return xdaCommodityTextService.updateCommodityXdaCategory(idto);
	}
	
	@ApiOperation(value = "更新  商品文描-排序", notes = "更新  商品文描-排序", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "UpdateCommoditySortNumIDTO")
	@PostMapping("/updateCommoditySortNum")
	public int updateCommoditySortNum(@RequestBody UpdateCommoditySortNumIDTO idto) {
		idto.setUserId(FastThreadLocalUtil.getQY().getUserId());
		return xdaCommodityTextService.updateCommoditySortNum(idto);
	}
	
	@ApiOperation(value = "查询   鲜达商品下拉信息  列表", notes = "查询   鲜达商品下拉信息  列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "SelectXdaCommodityDropdownInfoListIDTO")
	@PostMapping("/selectXdaCommodityDropdownInfoList")
	public List<XdaCommodityDropdownInfoODTO> selectXdaCommodityDropdownInfoList(@RequestBody SelectXdaCommodityDropdownInfoListIDTO idto){
		 return xdaCommodityTextService.selectXdaCommodityDropdownInfoList(idto);
	}
	
	@ApiOperation(value = "查询  鲜达商品信息 列表", notes = "查询  鲜达商品信息 列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "SelectXdaCommodityInfoListIDTO")
	@PostMapping("/selectXdaCommodityInfoList")
	public List<XdaCommodityInfoODTO> selectXdaCommodityInfoList(@RequestBody SelectXdaCommodityInfoListIDTO idto) {
    	return xdaCommodityTextService.selectXdaCommodityInfoList(idto);
    }

	/**
	 * 查询所有文描信息(前台品名，1级2级分类，是否上下架)
	 * @param idto
	 * @return
	 */
	@ApiOperation(value = "查询所有文描信息(1级2级分类，是否上下架)", notes = "查询所有文描信息(1级2级分类，是否上下架)", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
	@PostMapping("/queryAllXdaCommodityText")
	public List<CommodityTextInfoODTO> queryAllXdaCommodityText(@RequestBody SelectXdaCommodityInfoListIDTO idto) {
		return xdaCommodityTextService.queryAllXdaCommodityText(idto);
	}

	/**
	 * 查询鲜达商品(xda-search手动初始化)
	 * @param dto
	 * @return
	 */
	@ApiOperation("查询所有的鲜达商品")
	@PostMapping("/selectCommodityTextEsList")
	public List<CommodityTextEsODTO> selectCommodityTextEsList(@RequestBody SelectXdaCommodityInfoListIDTO dto) {
		return xdaCommodityTextService.selectCommodityTextEsList(dto);
	}

	@ApiOperation("根据分类id查询所有商品id")
	@GetMapping("/selectCommodityByCateId/{cateId}")
	public List<XdaCommodityText> selectCommodityByCateId(@PathVariable("cateId") Long cateId){
		return xdaCommodityTextService.selectCommodityByCateId(cateId);
	}

}
