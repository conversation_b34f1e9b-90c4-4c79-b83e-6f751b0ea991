package com.pinshang.qingyun.xda.product.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.product.dto.orderTargetSet.*;
import com.pinshang.qingyun.xda.product.dto.XdaImportExcelResultBasicEntry;
import com.pinshang.qingyun.xda.product.service.XdaOrderTargetSetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:37
 */
@RestController
@RequestMapping(value = "/orderTargetSet")
@Api(tags = "XdaOrderTargetSetController",description = "鲜达订货目标设置相关接口")
public class XdaOrderTargetSetController {

    @Autowired
    private XdaOrderTargetSetService xdaOrderTargetSetService;

    /***
     * 新增订货目标设置
     * @param vo
     * @return
     */
    @PostMapping(value = "/add")
    @ApiOperation(value = "新增订货目标设置")
    @ApiImplicitParam(name = "vo", paramType = "body", dataTypeClass = XdaOrderTargetSetAddIDTO.class)
    public Integer addOrderTargetSet(@RequestBody XdaOrderTargetSetAddIDTO vo) throws Exception {
        QYAssert.isTrue(vo.getStoreId() != null, "客户不能为空，请选择！");
        QYAssert.isTrue(vo.getOrderTargetToDay() != null, "单日订货目标不能为空，请输入！");
        Pattern patternLimit = Pattern.compile("^([1-9]{1}\\d{0,6})$");
        QYAssert.isTrue(patternLimit.matcher(vo.getOrderTargetToDay().toString()).matches(), "单日订货目标输入有误，范围为：正整数，0＜订货目标＜9999999");

        QYAssert.isTrue(vo.getDeliveryStartDate() != null, "送货日期范围不能为空，请选择！");
        QYAssert.isTrue(vo.getDeliveryEndDate() != null, "送货日期范围不能为空，请选择！");

        QYAssert.isTrue(vo.getDeliveryDateRangeType() != null, "是否常规/循环设置类型不能为空，请输入！");
        QYAssert.isTrue(vo.getDeliveryDateRangeType() == 1 || vo.getDeliveryDateRangeType() == 2, "是否常规/循环设置，类型不符！");
        if (vo.getDeliveryDateRangeType() == 2) {
            QYAssert.isTrue(StringUtils.isNotBlank(vo.getLoopSet()), "循环设置至少勾选1个！");
            String[] loopSetArray= vo.getLoopSet().split(";");
            Map<Integer,String> loopSetMap = new TreeMap<>();
            for(String s:loopSetArray) {
                s = s.trim();
                Integer val = XdaOrderTargetSetService.loopSetMap.get(s);
                QYAssert.isTrue(val != null, "循环设置有误，只允许输入一~七，以英文分号分割，请确认！");
                loopSetMap.put(val, s);
            }
            vo.setLoopSet(loopSetMap.values().stream().collect(Collectors.joining(";")));
        }else {
            vo.setLoopSet(null);
        }
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        vo.setUserId(tokenInfo.getUserId());
        vo.setUserName(tokenInfo.getRealName());
        return xdaOrderTargetSetService.addOrderTargetSet(vo);
    }

    /***
     * 停用订货目标设置
     * @param vo
     * @return
     */
    @PostMapping(value = "/disable")
    @ApiOperation(value = "停用订货目标设置")
    @ApiImplicitParam(name = "vo", paramType = "body", dataTypeClass = XdaOrderTargetSetDisableIDTO.class)
    public Integer disableOrderTargetSet(@RequestBody XdaOrderTargetSetDisableIDTO vo) {
        QYAssert.isTrue(vo.getId() != null, "停用订货目标设置失败，订货目标设置信息不能为空", "订货目标设置ID不能为空");
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        vo.setUserId(tokenInfo.getUserId());
        vo.setUserName(tokenInfo.getRealName());
        return xdaOrderTargetSetService.disableOrderTargetSet(vo);
    }

    /***
     * 获取订货目标设置列表
     * @param vo
     * @return
     */
    @PostMapping(value = "/list")
    @ApiOperation(value = "获取订货目标设置列表")
    @ApiImplicitParam(name = "vo", paramType = "body", dataTypeClass = XdaOrderTargetSetQueryIDTO.class)
    public PageInfo<XdaOrderTargetSetQueryODTO> findXdaOrderTargetSetList(@RequestBody XdaOrderTargetSetQueryIDTO vo) {
        QYAssert.isTrue(StringUtils.isNotBlank(vo.getDeliveryDate()), "送货日期不能为空，请选择！");
        if(vo.getIsExpire()!=null){
            QYAssert.isTrue(vo.getIsExpire()==0 || vo.getIsExpire()==1, "是否过期类型不符，请确认！");
        }
        return xdaOrderTargetSetService.findXdaOrderTargetSetList(vo);
    }


    /***
     * 获取订货目标设置日志列表
     * @param vo
     * @return
     */
    @PostMapping(value = "/log/list")
    @ApiOperation(value = "获取订货目标设置日志列表")
    @ApiImplicitParam(name = "vo", paramType = "body", dataTypeClass = XdaOrderTargetSetLogQueryIDTO.class)
    public List<XdaOrderTargetSetLogQueryODTO> findXdaOrderTargetSetLogList(@RequestBody XdaOrderTargetSetLogQueryIDTO vo) {
        QYAssert.isTrue(vo.getStoreId()!=null, "客户不能为空，请输入！");
        return xdaOrderTargetSetService.findXdaOrderTargetSetLogList(vo);
    }


    /***
     * 获取订货目标设置详情
     * @param id
     * @return
     */
    @GetMapping(value = "/detail")
    @ApiOperation(value = "获取订货目标设置详情")
    public XdaOrderTargetSetDetailODTO findXdaOrderTargetSetDetailById(@RequestParam("id") Long id) {
        return xdaOrderTargetSetService.findXdaOrderTargetSetDetailById(id);
    }

    /***
     * 订货目标设置导入
     * @param file
     * @return
     */
    @PostMapping(value = "/import")
    @ApiOperation(value = "订货目标设置导入")
    public XdaImportExcelResultBasicEntry importXdaOrderTargetSet(@RequestParam("file") MultipartFile file) throws Exception {
        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        String[] header = {"客户编码", "送货日期范围", "单日订货目标(元)", "循环设置"};
        return xdaOrderTargetSetService.importOrderTargetSet(checkExcelTemplate(workbook, header), header, FastThreadLocalUtil.getQY());
    }

    /***
     * 检验excel数据、行数 是否空 、行数是否超过限制
     * @param wb
     */
    private Sheet checkExcelTemplate(Workbook wb, String[] header) {
        if (wb == null) {
            QYAssert.isFalse("导入模板不对，请重新下载模板！");
        }
        Sheet sheet = wb.getSheetAt(0);
        if (sheet == null) {
            QYAssert.isFalse("导入模板不对，请重新下载模板！");
        }
        if (sheet.getPhysicalNumberOfRows() == 0) {
            QYAssert.isFalse("导入数据不能为空！");
        }
        if (sheet.getLastRowNum() < 1) {
            QYAssert.isFalse("导入的数据不能为空！");
        }
        if (sheet.getPhysicalNumberOfRows() > 5001) {
            QYAssert.isFalse("最多只能导入5000条数据！");
        }

        Row row = sheet.getRow(0);
        for (int i = 0; i < header.length; i++) {
            Cell cell = row.getCell(i);
            QYAssert.isTrue(cell != null, "导入模板不对，请重新下载模板！");
            cell.setCellType(CellType.STRING);
            QYAssert.isTrue(StringUtils.isNotBlank(cell.getStringCellValue()), "导入模板不对，请重新下载模板！");
            QYAssert.isTrue(header[i].equals(cell.getStringCellValue().trim()), "导入模板不对，请重新下载模板！");
        }
        return sheet;
    }
}
