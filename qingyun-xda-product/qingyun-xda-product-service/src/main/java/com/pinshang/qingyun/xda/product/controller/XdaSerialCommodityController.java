package com.pinshang.qingyun.xda.product.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.BatchInsertSerialCommodityIDTO;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.DeleteSerialCommodityIDTO;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.InsertSerialCommodityIDTO;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.MasterSerialCommodityIDTO;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.SelectCommodityDropdownInfoList4SerialCommodityIDTO;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.SelectSerialCommodityInfoListIDTO;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.SelectSerialCommodityLogInfoPageIDTO;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.SerialCommodityInfoODTO;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.SerialCommodityLogInfoODTO;
import com.pinshang.qingyun.xda.product.service.XdaSerialCommodityService;

/**
 * 系列品
 * 
 * <AUTHOR>
 *
 * @date 2020年12月22日
 */
@RestController
@RequestMapping("/xdaSerialCommodity")
@Api(value = "鲜达-系列品", tags = "XdaSerialCommodityController", description ="鲜达-系列品" )
public class XdaSerialCommodityController {
	
	@Autowired
	private XdaSerialCommodityService xdaSerialCommodityService;
	
	@ApiOperation(value = "分页查询  系列品日志信息  列表", notes = "分页查询  系列品日志信息  列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "SelectSerialCommodityLogInfoPageIDTO")
	@PostMapping("/selectSerialCommodityLogInfoPage")
	public PageInfo<SerialCommodityLogInfoODTO> selectSerialCommodityLogInfoPage(@RequestBody SelectSerialCommodityLogInfoPageIDTO idto) {
		return xdaSerialCommodityService.selectSerialCommodityLogInfoPage(idto);
	}
	
	@ApiOperation(value = "查询  系列品信息  列表", notes = "查询  系列品信息  列表", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "SelectSerialCommodityInfoListIDTO")
	@PostMapping("/selectSerialCommodityInfoList")
	public List<SerialCommodityInfoODTO> selectSerialCommodityInfoList(@RequestBody SelectSerialCommodityInfoListIDTO idto) {
		return xdaSerialCommodityService.selectSerialCommodityInfoList(idto);
	}
	
	@ApiOperation(value = "查询  商品下拉信息  列表 （for系列品）", notes = "查询  商品下拉信息  列表 （for系列品）", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "SelectCommodityDropdownInfoList4SerialCommodityIDTO")
	@PostMapping("/selectCommodityDropdownInfoList4SerialCommodity")
	public List<SerialCommodityInfoODTO> selectCommodityDropdownInfoList4SerialCommodity(@RequestBody SelectCommodityDropdownInfoList4SerialCommodityIDTO idto) {
		return xdaSerialCommodityService.selectCommodityDropdownInfoList4SerialCommodity(idto);
	}
	
	@ApiOperation(value = "批量添加  系列品", notes = "批量添加  系列品", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "BatchInsertSerialCommodityIDTO")
	@PostMapping("/batchInsertSerialCommodity")
	public int batchInsertSerialCommodity(@RequestBody BatchInsertSerialCommodityIDTO idto) {
		idto.setUserId(FastThreadLocalUtil.getQY().getUserId());
		return xdaSerialCommodityService.batchInsertSerialCommodity(idto);
	}
	
	@ApiOperation(value = "新增  系列品", notes = "新增  系列品", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "InsertSerialCommodityIDTO")
	@PostMapping("/insertSerialCommodity")
	public int insertSerialCommodity(@RequestBody InsertSerialCommodityIDTO idto) {
		idto.setUserId(FastThreadLocalUtil.getQY().getUserId());
    	return xdaSerialCommodityService.insertSerialCommodity(idto);
    }
	
	@ApiOperation(value = "删除  系列品", notes = "删除  系列品", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "DeleteSerialCommodityIDTO")
	@PostMapping("/deleteSerialCommodity")
	public int deleteSerialCommodity(@RequestBody DeleteSerialCommodityIDTO idto) {
		idto.setUserId(FastThreadLocalUtil.getQY().getUserId());
    	return xdaSerialCommodityService.deleteSerialCommodity(idto);
    }
	
	@ApiOperation(value = "设置主品  系列品", notes = "设置主品  系列品", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiImplicitParam(name = "idto", value = "请求IDTO", required = true, paramType = "body", dataType = "MasterSerialCommodityIDTO")
	@PostMapping("/masterSerialCommodity")
	public int masterSerialCommodity(@RequestBody MasterSerialCommodityIDTO idto) {
		idto.setUserId(FastThreadLocalUtil.getQY().getUserId());
    	return xdaSerialCommodityService.masterSerialCommodity(idto);
    }

}
