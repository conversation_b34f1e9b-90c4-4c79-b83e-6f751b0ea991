package com.pinshang.qingyun.xda.product.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.product.dto.XdaImportExcelResultBasicEntry;
import com.pinshang.qingyun.xda.product.dto.specialsCommoditySet.*;
import com.pinshang.qingyun.xda.product.service.XdaSpecialsCommoditySetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:40
 */
@RestController
@RequestMapping(value = "/specialsCommoditySet")
@Api(tags = "XdaSpecialsCommoditySetController",description = "鲜达特惠商品设置相关接口")
public class XdaSpecialsCommoditySetController {

    @Autowired
    private XdaSpecialsCommoditySetService xdaSpecialsCommoditySetService;


    /***
     * 添加特惠商品设置
     * @param vo
     * @return
     */
    @PostMapping(value = "/add")
    @ApiOperation(value = "添加特惠商品设置")
    @ApiImplicitParam(name = "vo", paramType = "body", dataTypeClass = XdaSpecialsCommoditySetAddIDTO.class)
    public Integer addSpecialsCommoditySet(@RequestBody XdaSpecialsCommoditySetAddIDTO vo) {
        QYAssert.isTrue(vo.getCommodityId() != null, "特惠商品不能为空，请选择！");
        this.checkRequestParam(vo);
        return xdaSpecialsCommoditySetService.addSpecialsCommoditySet(vo);

    }

    /***
     * 修改特惠商品设置
     * @param vo
     * @return
     */
    @PostMapping(value = "/modify")
    @ApiOperation(value = "修改特惠商品设置")
    @ApiImplicitParam(name = "vo", paramType = "body", dataTypeClass = XdaSpecialsCommoditySetModifyIDTO.class)
    public Integer updateSpecialsCommoditySet(@RequestBody XdaSpecialsCommoditySetModifyIDTO vo) {
        QYAssert.isTrue(vo.getId() != null, "需修改特惠商品信息不能为空", "需修改特惠商品信ID不能为空");
        this.checkRequestParam(vo);
        return xdaSpecialsCommoditySetService.updateSpecialsCommoditySet(vo);
    }


    /***
     * 删除特惠商品设置
     * @return
     */
    @PostMapping(value = "/remove")
    @ApiOperation(value = "删除特惠商品设置")
    @ApiImplicitParam(name = "vo", paramType = "body", dataTypeClass = XdaSpecialsCommoditySetRemoveIDTO.class)
    public Integer deleteSpecialsCommoditySetById(@RequestBody XdaSpecialsCommoditySetRemoveIDTO vo) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        vo.setUserId(tokenInfo.getUserId());
        vo.setUserName(tokenInfo.getRealName());
        return xdaSpecialsCommoditySetService.deleteSpecialsCommoditySetById(vo);
    }

    /***
     * 特惠商品设置列表
     * @param vo
     * @return
     */
    @PostMapping(value = "/list")
    @ApiOperation(value = "特惠商品设置列表")
    @ApiImplicitParam(name = "vo", paramType = "body", dataTypeClass = XdaSpecialsCommoditySetQueryIDTO.class)
    public PageInfo<XdaSpecialsCommoditySetQueryODTO> findSpecialsCommoditySetList(@RequestBody XdaSpecialsCommoditySetQueryIDTO vo) {
        return xdaSpecialsCommoditySetService.findSpecialsCommoditySetList(vo);
    }

    /***
     * 特惠商品设置日志列表
     * @param vo
     * @return
     */
    @PostMapping(value = "/log/list")
    @ApiOperation(value = "特惠商品设置日志列表")
    @ApiImplicitParam(name = "vo", paramType = "body", dataTypeClass = XdaSpecialsCommoditySetLogQueryIDTO.class)
    public PageInfo<XdaSpecialsCommoditySetLogQueryODTO> findSpecialsCommoditySetLogList(@RequestBody XdaSpecialsCommoditySetLogQueryIDTO vo) {
        return xdaSpecialsCommoditySetService.findSpecialsCommoditySetLogList(vo);
    }

    /***
     * 特惠商品设置列表导出
     * @param vo
     * @return
     */
    @GetMapping(value = "/export")
    @ApiOperation(value = "特惠商品设置列表导出")
    public void exportSpecialsCommoditySetList(XdaSpecialsCommoditySetExportIDTO vo, HttpServletResponse response) throws IOException {
        xdaSpecialsCommoditySetService.exportSpecialsCommoditySetList(vo,response);
    }

    /***
     * 特惠商品设置导入
     * @param file
     * @return
     */
    @PostMapping(value = "/import")
    @ApiOperation(value = "特惠商品设置导入")
    public XdaImportExcelResultBasicEntry importSpecialsCommoditySet(@RequestParam("file") MultipartFile file) throws Exception {
        Workbook workbook = WorkbookFactory.create(file.getInputStream());
        String[] header = {"商品编码", "特惠单价", "限量(订单级)"};
        return xdaSpecialsCommoditySetService.importSpecialsCommoditySet(checkExcelTemplate(workbook, header),header,FastThreadLocalUtil.getQY());
    }

    /***
     * 检验excel数据、行数 是否空 、行数是否超过限制
     * @param wb
     */
    private Sheet checkExcelTemplate(Workbook wb, String[] header) {
        if (wb == null) {
            QYAssert.isFalse("导入模板不对，请重新下载模板！");
        }
        Sheet sheet = wb.getSheetAt(0);
        if (sheet == null) {
            QYAssert.isFalse("导入模板不对，请重新下载模板！");
        }
        if (sheet.getPhysicalNumberOfRows() == 0) {
            QYAssert.isFalse("导入数据不能为空！");
        }
        if (sheet.getLastRowNum() < 1) {
            QYAssert.isFalse("导入的数据不能为空！");
        }
        if (sheet.getPhysicalNumberOfRows() > 501) {
            QYAssert.isFalse("最多只能导入500条数据！");
        }

        Row row = sheet.getRow(0);
        for (int i = 0; i < header.length; i++) {
            Cell cell = row.getCell(i);
            QYAssert.isTrue(cell!=null, "导入模板不对，请重新下载模板！");
            cell.setCellType(CellType.STRING);
            QYAssert.isTrue(StringUtils.isNotBlank(cell.getStringCellValue()), "导入模板不对，请重新下载模板！");
            QYAssert.isTrue(header[i].equals(cell.getStringCellValue().trim()), "导入模板不对，请重新下载模板！");
        }
        return sheet;
    }


    /***
     * 获取特惠商品详情
     * @param id
     * @return
     */
    @GetMapping(value = "/detail")
    @ApiOperation(value = "获取特惠商品详情")
    public XdaSpecialsCommoditySetDetailODTO findSpecialsCommoditySetDetailsById(@RequestParam(value = "id") Long id) {
        return xdaSpecialsCommoditySetService.findSpecialsCommoditySetDetailsById(id);
    }

    /***
     * 商品下拉列表
     * @param vo
     * @return
     */
    @PostMapping(value = "/commodity/dropDown/list")
    @ApiOperation(value = "商品下拉列表")
    @ApiImplicitParam(name = "vo", paramType = "body", dataTypeClass = XdaSpecialsCommoditydropDownIDTO.class)
    public List<XdaSpecialsCommoditydropDownODTO> selectCommodityDropDownList(@RequestBody XdaSpecialsCommoditydropDownIDTO vo) {
        return xdaSpecialsCommoditySetService.selectCommodityDropDownList(vo);
    }


    private void checkRequestParam(XdaSpecialsCommoditySetBasicIDTO vo) {
        QYAssert.isTrue(vo.getCommoditySpecialsPrice() != null, "特惠单价不能为空，请输入！");
        Pattern patternPrice = Pattern.compile("^(([1-9]{1}\\d{0,4})(\\.(\\d){1,2})?)|(0\\.([1-9]{1,2}|0[1-9]{1}))$");
        QYAssert.isTrue(patternPrice.matcher(vo.getCommoditySpecialsPrice().toString()).matches(), "特惠单价输入有误，范围为：0.00<特惠单价≤99999.99！");
        QYAssert.isTrue(vo.getCommodityLimit() != null, "限量数量不能为空，请输入！");
        Pattern patternLimit = Pattern.compile("^([1-9]{1}\\d{0,4})$");
        QYAssert.isTrue(patternLimit.matcher(vo.getCommodityLimit().toString()).matches(), "限量数量输入有误，范围为：1≤限量≤99999！");

        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        vo.setUserId(tokenInfo.getUserId());
        vo.setUserName(tokenInfo.getRealName());
    }
}
