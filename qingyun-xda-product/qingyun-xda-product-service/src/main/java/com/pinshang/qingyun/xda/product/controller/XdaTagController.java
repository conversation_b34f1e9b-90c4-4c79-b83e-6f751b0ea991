package com.pinshang.qingyun.xda.product.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.xda.product.dto.commodityTag.*;
import com.pinshang.qingyun.xda.product.service.XdaTagService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: chenqiang
 * @time: 2020/12/22 14:27
 */
@RestController
@RequestMapping("/xdaTag")
@Api(value = "鲜达-标签", tags = "XdaTagController",description ="鲜达-标签")
public class XdaTagController {
    @Autowired
    private XdaTagService xdaTagService;

    @PostMapping("/selectXdaCommodityTagList")
    @ApiOperation(value = "查询鲜达商品自定义标签列表")
    public PageInfo<XdaCommodityTagODTO> selectXdaCommodityTagList(@RequestBody XdaCommodityTagIDTO commodityTagIDTO){
        return xdaTagService.selectXdaCommodityTagList(commodityTagIDTO);
    }

    @PostMapping("/saveXdaCommodityTag")
    @ApiOperation(value = "新增鲜达商品自定义标签")
    public Integer saveXdaCommodityTag(@RequestBody XdaCommodityTagSaveIDTO commodityTagSaveIDTO){
        return xdaTagService.saveXdaCommodityTag(commodityTagSaveIDTO);
    }

    @PostMapping("/updateXdaCommodityTag")
    @ApiOperation(value = "修改鲜达商品自定义标签")
    public Integer updateXdaCommodityTag(@RequestBody XdaCommodityTagUpdateIDTO commodityTagUpdateIDTO){
        return xdaTagService.updateXdaCommodityTag(commodityTagUpdateIDTO);
    }
    @PostMapping("/updateXdaCommodityTagStatus")
    @ApiOperation(value = "修改鲜达商品自定义标签状态")
    public Integer updateXdaCommodityTagStatus(@RequestBody XdaCommodityTagUpdateStatusIDTO commodityTagUpdateStatusIDTO){
        return xdaTagService.updateXdaCommodityTagStatus(commodityTagUpdateStatusIDTO);
    }

    @PostMapping("/selectXdaCommodityTagLogList")
    @ApiOperation(value = "查询鲜达商品自定义标签日志")
    public PageInfo<XdaCommodityTagLogODTO> selectXdaCommodityTagLogList(@RequestBody XdaCommodityTagLogIDTO commodityTagLogIDTO){
        return xdaTagService.selectXdaCommodityTagLogList(commodityTagLogIDTO);
    }

}
