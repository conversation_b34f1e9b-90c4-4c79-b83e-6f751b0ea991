package com.pinshang.qingyun.xda.product.controller.deliveryDate;


import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityAddVO;
import com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityBatchAddVO;
import com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityDTO;
import com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityVO;
import com.pinshang.qingyun.xda.product.service.deliveryDate.XdaOrderCommodityService;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/xdaOrderCommodity")
@Api(value = "鲜达-订货日期", tags = "xdaOrderCommodityController", description ="鲜达-订货日期" )
public class XdaOrderCommodityController {
    
    
    @Resource
    XdaOrderCommodityService xdaOrderCommodityService;



    /**
     * 查询鲜达订货商品列表
     */
    @PostMapping("/list")
    public PageInfo<XdaOrderCommodityDTO> list(@RequestBody XdaOrderCommodityVO xdaOrderCommodityVO) {
        return xdaOrderCommodityService.selectOrderCommodityList(xdaOrderCommodityVO);
    }



    /**
     * 设置订货商品日期
     */
    @PostMapping("/set")
    public void set(@RequestBody XdaOrderCommodityAddVO xdaOrderCommodityAddVO) {
        QYAssert.isTrue(StringUtils.isNotEmpty(xdaOrderCommodityAddVO.getCommodityDateCode()),"送货日期范围不能空");
        QYAssert.isTrue(null != xdaOrderCommodityAddVO.getAppType(),"app类型不能为空!");
        xdaOrderCommodityService.addOrderCommodityDate(xdaOrderCommodityAddVO);
    }

    

    /**
     * 批量设置订货商品日期
     */
    @PostMapping("/batchSet")
    public void batchSet(@RequestBody XdaOrderCommodityBatchAddVO xdaOrderCommodityAddVO) {
        QYAssert.isTrue(StringUtils.isNotEmpty(xdaOrderCommodityAddVO.getCommodityDateCode()),"送货日期范围不能空");
        QYAssert.isTrue(null != xdaOrderCommodityAddVO.getAppType(),"app类型不能为空!");
        xdaOrderCommodityService.batchAddOrderCommodityDate(xdaOrderCommodityAddVO);
    }
    
}
