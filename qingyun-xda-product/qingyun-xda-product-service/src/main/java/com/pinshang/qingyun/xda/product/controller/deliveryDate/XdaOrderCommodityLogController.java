package com.pinshang.qingyun.xda.product.controller.deliveryDate;


import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityLogDTO;
import com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityLogVO;
import com.pinshang.qingyun.xda.product.service.deliveryDate.XdaOrderCommodityLogService;
import com.pinshang.qingyun.xda.product.service.deliveryDate.XdaOrderCommodityService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping("/xdaOrderCommodityLog")
@Api(value = "鲜达-订货日期日志", tags = "xdaOrderCommodityLogController", description ="鲜达-订货日期日志" )
public class XdaOrderCommodityLogController {
    @Resource
    XdaOrderCommodityService xdaOrderCommodityService;

    @Resource
    XdaOrderCommodityLogService xdaOrderCommodityLogService;

    /**
     * 查询鲜达前台品类
     */
    @PostMapping("/list")
    public PageInfo<XdaOrderCommodityLogDTO> list(@RequestBody XdaOrderCommodityLogVO xdaOrderCommodityLogVO) {
         return xdaOrderCommodityLogService.getXdaOrderCommodityLog(xdaOrderCommodityLogVO);
    }
    
}
