package com.pinshang.qingyun.xda.product.controller.front;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.xda.XdaAppCodeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaCategoryCommodityResODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaCategoryResODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaSearchAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityAppV3ODTO;
import com.pinshang.qingyun.xda.product.service.front.XdaCategoryFrontService;
import com.pinshang.qingyun.xda.product.service.front.v3.XdaCategoryFrontV3Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 鲜达分类商品
 */
@RequestMapping("/xdaCategoryFront")
@RestController
@Api(value = "鲜达APP分类", tags = "xdaCategoryFront", description ="鲜达APP分类")
@Slf4j
public class XdaCategoryFrontController {
    @Autowired
    private XdaCategoryFrontService xdaCategoryCommodityFrontService;
    @Autowired
    private XdaCategoryFrontV3Service xdaCategoryFrontV3Service;
    @Deprecated
    @ApiOperation(value = "APP查询分类")
    @ApiImplicitParam(name = "orderTime", value = "送货日期", required = true, paramType = "query")
    @RequestMapping(value = "/queryXdaCategoryList", method = RequestMethod.GET)
    public XdaCategoryResODTO queryXdaCategoryList(@RequestParam(value = "orderTime",required = false) String orderTime) {
        if(StringUtils.isEmpty(orderTime)){
            return new XdaCategoryResODTO();
        }
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        XdaCategoryResODTO result = xdaCategoryCommodityFrontService.queryXdaCategoryList(DateUtil.parseDate(orderTime,"yyyy-MM-dd"),xdaTokenInfo.getStoreId());
        if (null == result) {
        	result = new XdaCategoryResODTO();
        }
        return result;
    }

    @Deprecated
    @ApiOperation(value = "APP查询分类商品")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataType = "XdaCategoryAppIDTO")
    @RequestMapping(value = "/queryXdaCategoryCommodityList", method = RequestMethod.POST)
    public XdaCategoryCommodityResODTO queryXdaCategoryCommodityList(@RequestBody XdaCategoryAppIDTO appIDTO) {
        if(appIDTO.getXdaFirstCategoryId()==null || appIDTO.getXdaSecondCategoryId()==null){
            log.error("查询分类商品，一级分类和二级分类都必传");
            return new XdaCategoryCommodityResODTO();
        }
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        appIDTO.setStoreId(xdaTokenInfo.getStoreId());
        XdaCategoryCommodityResODTO result = xdaCategoryCommodityFrontService.queryXdaCategoryCommodityList(appIDTO);
        if (null == result) {
        	result = new XdaCategoryCommodityResODTO();
        }
        return result;
    }

    @ApiOperation(value = "APP搜索商品")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataType = "XdaSearchAppIDTO")
    @RequestMapping(value = "/queryXdaCommoditySearch", method = RequestMethod.POST)
    public ApiResponse<XdaCommodityAppV3ODTO> queryXdaCommoditySearch(@RequestBody XdaSearchAppIDTO appIDTO, HttpServletResponse response) {
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG,QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        appIDTO.setStoreId(xdaTokenInfo.getStoreId());
        if((xdaTokenInfo.getAppCode().equals(XdaAppCodeEnum.IOS.getAppCode()) && xdaTokenInfo.getAppVersion().compareToIgnoreCase("1.4.0") >= 0)||
                (xdaTokenInfo.getAppCode().equals(XdaAppCodeEnum.ANDROID.getAppCode()) && xdaTokenInfo.getAppVersion().compareToIgnoreCase("1.4.1") >= 0)){
            PageInfo<XdaCommodityAppV3ODTO> xdaCommodityAppV3ODTOPageInfo = xdaCategoryFrontV3Service.queryXdaCommodityPageInfo(appIDTO);
            return  ApiResponse.convert( xdaCommodityAppV3ODTOPageInfo);
        }
        PageInfo<XdaCommodityAppODTO> pageInfo = xdaCategoryCommodityFrontService.queryXdaCommoditySearch(appIDTO);
        return  ApiResponse.convert( pageInfo);
    }
}
