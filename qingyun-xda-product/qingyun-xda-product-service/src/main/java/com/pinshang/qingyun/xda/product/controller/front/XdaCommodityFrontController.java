package com.pinshang.qingyun.xda.product.controller.front;

import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.product.dto.front.*;
import com.pinshang.qingyun.xda.product.service.front.XdaCommodityFrontService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 鲜达商品Controller
 */
@RestController
@RequestMapping("/xdaCommodityFront")
@Api(value = "鲜达APP商品", tags = "xdaCommodityFront",description ="鲜达APP商品")
public class XdaCommodityFrontController {

    @Autowired
    private XdaCommodityFrontService xdaCommodityFrontService;

    @Deprecated
    @ApiOperation(value = "APP查询商品列表信息")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataType = "XdaCommodityAppIDTO")
    @RequestMapping(value = "/queryXdaCommodityListForApp", method = RequestMethod.POST)
    public List<XdaCommodityAppODTO> queryXdaCommodityListForApp(@RequestBody XdaCommodityAppIDTO appIDTO) {
        return xdaCommodityFrontService.queryXdaCommodityListForApp(appIDTO);
    }

    @Deprecated
    @ApiOperation(value = "APP查询商品详情信息")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataType = "XdaCommodityDetailAppIDTO")
    @RequestMapping(value = "/queryXdaCommodityDetailForApp", method = RequestMethod.POST)
    public XdaCommodityDetailAppODTO queryXdaCommodityDetailForApp(@RequestBody XdaCommodityDetailAppIDTO appIDTO) {
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        if(xdaTokenInfo.getStoreId()==null){
            return null;
        }
        appIDTO.setStoreId(xdaTokenInfo.getStoreId());
        return xdaCommodityFrontService.queryXdaCommodityDetailForApp(appIDTO);
    }

    @ApiOperation(value = "商品收藏/取消收藏")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "commodityId", value = "商品ID", required = true, paramType = "query"),
            @ApiImplicitParam(name="collectStatus", value="1=收藏，0=取消收藏", required = true, paramType = "query")
    })
    @RequestMapping(value = "/updateXdaCommodityCollect", method = RequestMethod.POST)
    public Integer updateXdaCommodityCollect(@RequestParam(value = "commodityId",required = false)Long cookbookId, @RequestParam(value = "collectStatus",required = false)Integer collectStatus) {
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        return xdaCommodityFrontService.updateXdaCommodityCollect(cookbookId,collectStatus,xdaTokenInfo.getStoreId());
    }

    /**
     * 查询鲜达商品状态
     * @return
     */
    @PostMapping("/queryXdaCommodityAppStatus")
    public Map<Long, Integer> queryXdaCommodityAppStatus(@RequestBody List<Long> commodityIdList) {
        return xdaCommodityFrontService.queryXdaCommodityAppStatus(commodityIdList);
    }

    @RequestMapping(value = "/queryXdaCommodityDeliveryTime", method = RequestMethod.POST)
    public Map<Long, XdaCommodityDeliveryTimeODTO> queryXdaCommodityDeliveryTime(@RequestBody XdaCommodityDeliveryTimeIDTO appIDTO) {
        return xdaCommodityFrontService.queryXdaCommodityDeliveryTime(appIDTO.getOrderTime(),appIDTO.getStoreId(),appIDTO.getCommodityIdList());
    }

    @RequestMapping(value = "/queryXdaCommodityLimit", method = RequestMethod.POST)
    public Map<Long,XdaCommodityLimitODTO> queryXdaCommodityLimit(@RequestBody XdaCommodityLimitIDTO appIDTO) {
        return xdaCommodityFrontService.queryXdaCommodityLimit(appIDTO.getOrderTime(),appIDTO.getCommodityIdList());
    }

}
