package com.pinshang.qingyun.xda.product.controller.front.shoppingCart;

import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDeliveryTimeODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3ODTO;
import com.pinshang.qingyun.xda.product.service.front.XdaCommodityFrontService;
import com.pinshang.qingyun.xda.product.service.front.shoppingCart.XdaShoppingCartService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@RequestMapping("/xdaShoppingCart")
@RestController
@Api(value = "鲜达购物车相关接口", tags = "xdaShoppingCartController", description ="鲜达购物车相关接口")
@Slf4j
public class XdaShoppingCartController {


    @Autowired
    private XdaShoppingCartService xdaShoppingCartService;

    @ApiOperation(value = "鲜达购物车获取商品销售箱规 -- 加车使用")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaShoppingCartV3IDTO.class)
    @RequestMapping(value = "/getXdaCommoditySalesBoxCapacity", method = RequestMethod.POST)
    public XdaShoppingCartV3ODTO getXdaCommoditySalesBoxCapacity(@RequestBody XdaShoppingCartV3IDTO appIDTO) {
        return xdaShoppingCartService.getXdaCommoditySalesBoxCapacity(appIDTO);
    }
    @ApiOperation(value = "鲜达购物车获取商品限量 -- 加车使用")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaShoppingCartV3IDTO.class)
    @RequestMapping(value = "/getXdaCommodityLimit", method = RequestMethod.POST)
    public XdaShoppingCartV3ODTO getXdaCommodityLimit(@RequestBody XdaShoppingCartV3IDTO appIDTO) {
        return xdaShoppingCartService.getXdaCommodityLimit(appIDTO);
    }

    @ApiOperation(value = "鲜达购物车商品是否正常 -- 统计品类数量使用")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaShoppingCartV3IDTO.class)
    @RequestMapping(value = "/getVarietySum", method = RequestMethod.POST)
    public List<XdaShoppingCartV3ODTO> getVarietySum(@RequestBody XdaShoppingCartV3IDTO appIDTO) {
        return xdaShoppingCartService.getVarietySum(appIDTO);
    }

    @ApiOperation(value = "鲜达购物车商品基本信息 -- 刷新购物车使用")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaShoppingCartV3IDTO.class)
    @RequestMapping(value = "/getShopCartList", method = RequestMethod.POST)
    public List<XdaShoppingCartV3ODTO> getShopCartList(@RequestBody XdaShoppingCartV3IDTO appIDTO) {
        return xdaShoppingCartService.getShopCartList(appIDTO);
    }

    @ApiOperation(value = "鲜达购物车获取商品销售箱规 -- 加车使用")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaShoppingCartV3IDTO.class)
    @RequestMapping(value = "/getXdaCommodityTextList", method = RequestMethod.POST)
    public List<XdaShoppingCartV3ODTO> getXdaCommodityTextList(@RequestBody XdaShoppingCartV3IDTO appIDTO) {
        return xdaShoppingCartService.getXdaCommodityTextList(appIDTO);
    }

    @ApiOperation(value = "鲜达购物车获取是否可订货 -- 加车使用")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaShoppingCartV3IDTO.class)
    @RequestMapping(value = "/getXdaCommodityTextListIsCanOrder", method = RequestMethod.POST)
    public XdaShoppingCartV3ODTO getXdaCommodityTextListIsCanOrder(@RequestBody XdaShoppingCartV3IDTO appIDTO) {
        return xdaShoppingCartService.getXdaCommodityTextListIsCanOrder(appIDTO);
    }

    @PostMapping(value = "/selectCommodityPackageSpecByCommodityIdList")
    public Map<Long, BigDecimal> selectCommodityPackageSpecByCommodityIdList(@RequestBody List<Long> commodityIdList){
        return xdaShoppingCartService.selectCommodityPackageSpecByCommodityIdList(commodityIdList);
    }
    @PostMapping(value = "/getValidCommodityGift")
    public List<XdaShoppingCartV3ODTO> getValidCommodityGift(@RequestBody XdaShoppingCartV3IDTO appIDTO){
       return xdaShoppingCartService.getValidCommodityGift(appIDTO);
    }
}
