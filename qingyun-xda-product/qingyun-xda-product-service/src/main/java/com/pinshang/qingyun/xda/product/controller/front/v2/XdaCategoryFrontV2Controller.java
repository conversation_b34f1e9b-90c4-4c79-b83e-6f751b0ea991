package com.pinshang.qingyun.xda.product.controller.front.v2;

import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v2.XdaCategoryCommodityResV2ODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v2.XdaCategoryResV2ODTO;
import com.pinshang.qingyun.xda.product.service.front.v2.XdaCategoryFrontV2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description:
 * @author: hhf
 * @date: 2023/6/14/014 10:10
 */
@RequestMapping("/xdaCategoryFrontV2")
@RestController
@Api(value = "鲜达APP分类-V2", tags = "xdaCategoryFrontV2", description ="鲜达APP分类-V2")
@Slf4j
public class XdaCategoryFrontV2Controller {

    @Autowired
    private XdaCategoryFrontV2Service xdaCategoryFrontV2Service;


    @Deprecated
    @ApiOperation(value = "APP查询分类-v2")
    @ApiImplicitParam(name = "orderTime", value = "送货日期", required = true, paramType = "query")
    @RequestMapping(value = "/queryXdaCategoryListV2", method = RequestMethod.GET)
    public XdaCategoryResV2ODTO queryXdaCategoryListV2(@RequestParam(value = "orderTime",required = false) String orderTime) {
        if(StringUtils.isEmpty(orderTime)){
            return new XdaCategoryResV2ODTO();
        }
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        XdaCategoryResV2ODTO result = xdaCategoryFrontV2Service.queryXdaCategoryList(DateUtil.parseDate(orderTime,"yyyy-MM-dd"),xdaTokenInfo.getStoreId());
        if (null == result) {
            result = new XdaCategoryResV2ODTO();
        }
        return result;
    }

    @Deprecated
    @ApiOperation(value = "APP查询分类商品-v2")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaCategoryAppIDTO.class)
    @RequestMapping(value = "/queryXdaCategoryCommodityListV2", method = RequestMethod.POST)
    public XdaCategoryCommodityResV2ODTO queryXdaCategoryCommodityListV2(@RequestBody XdaCategoryAppIDTO appIDTO) {
        if(appIDTO.getXdaFirstCategoryId()==null || appIDTO.getXdaSecondCategoryId()==null){
            log.error("查询分类商品，一级分类和二级分类都必传");
            return new XdaCategoryCommodityResV2ODTO();
        }
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        appIDTO.setStoreId(xdaTokenInfo.getStoreId());
        XdaCategoryCommodityResV2ODTO result = xdaCategoryFrontV2Service.queryXdaCategoryCommodityList(appIDTO);
        if (null == result) {
            result = new XdaCategoryCommodityResV2ODTO();
        }
        return result;
    }
}
