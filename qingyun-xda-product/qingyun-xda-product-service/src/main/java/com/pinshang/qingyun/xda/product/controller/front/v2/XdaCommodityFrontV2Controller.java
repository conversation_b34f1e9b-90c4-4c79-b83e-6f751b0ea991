package com.pinshang.qingyun.xda.product.controller.front.v2;

/**
 * <AUTHOR>
 * @date 23/6/14/014 15:42
 */

import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.product.dto.front.XdaOrderTargetSetV2ODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.*;
import com.pinshang.qingyun.xda.product.model.specialsCommoditySet.XdaSpecialsCommoditySet;
import com.pinshang.qingyun.xda.product.service.XdaSpecialsCommoditySetService;
import com.pinshang.qingyun.xda.product.service.front.v2.XdaCommodityFrontV2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;


@RestController
@RequestMapping("/xdaCommodityFrontV2")
@Api(value = "鲜达APP商品", tags = "xdaCommodityFrontV2",description ="鲜达APP商品")
public class XdaCommodityFrontV2Controller {

    @Autowired
    private XdaCommodityFrontV2Service xdaCommodityFrontV2Service;

    @Autowired
    private XdaSpecialsCommoditySetService xdaSpecialsCommoditySetService;



    @ApiOperation(value = "APP查询商品列表信息")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaCommodityAppV2IDTO.class)
    @RequestMapping(value = "/queryXdaCommodityListForAppV2", method = RequestMethod.POST)
    public List<XdaCommodityAppV2ODTO> queryXdaCommodityListForV2App(@RequestBody XdaCommodityAppV2IDTO appIDTO) {
        return xdaCommodityFrontV2Service.queryXdaCommodityListForV2App(appIDTO);
    }

    @ApiOperation(value = "根据客户信息查询特惠满减价格")
    @ApiImplicitParam(name = "xdaShoppingCartV2IDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaShoppingCartV2IDTO.class)
    @RequestMapping(value = "/findOrderTargetByStoreIdAndOrderTime", method = RequestMethod.POST)
    public BigDecimal findOrderTargetByStoreIdAndOrderTime(@RequestBody XdaShoppingCartV2IDTO xdaShoppingCartV2IDTO){
        return xdaCommodityFrontV2Service.findOrderTargetByStoreIdAndOrderTime(xdaShoppingCartV2IDTO);
    }

    @ApiOperation(value = "根据客户信息查询特惠方案")
    @ApiImplicitParam(name = "xdaShoppingCartV2IDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaShoppingCartV2IDTO.class)
    @RequestMapping(value = "/findXdaOrderTargetIdByStoreIdAndOrderTime", method = RequestMethod.POST)
    public XdaOrderTargetSetV2ODTO findXdaOrderTargetIdByStoreIdAndOrderTime(@RequestBody XdaShoppingCartV2IDTO xdaShoppingCartV2IDTO){
        return xdaCommodityFrontV2Service.findXdaOrderTargetIdByStoreIdAndOrderTime(xdaShoppingCartV2IDTO);
    }

    @ApiOperation(value = "根据商品id 查询特惠商品")
    @RequestMapping(value = "/selectXdaSpecialsCommoditySet", method = RequestMethod.POST)
    public List<Long> selectXdaSpecialsCommoditySet(@RequestBody List<Long> commodityIdList){
        return xdaSpecialsCommoditySetService.selectXdaSpecialsCommoditySet(commodityIdList);
    }

    @Deprecated
    @ApiOperation(value = "APP查询商品详情信息")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaCommodityDetailAppV2IDTO.class)
    @RequestMapping(value = "/queryXdaCommodityDetailForAppV2", method = RequestMethod.POST)
    public XdaCommodityDetailAppV2ODTO queryXdaCommodityDetailForAppV2(@RequestBody XdaCommodityDetailAppV2IDTO appIDTO) {
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        if(xdaTokenInfo.getStoreId()==null){
            return null;
        }
        appIDTO.setStoreId(xdaTokenInfo.getStoreId());
        return xdaCommodityFrontV2Service.queryXdaCommodityDetailForAppV2(appIDTO);
    }

    @ApiOperation(value = "根据商品id 批量查询特惠商品")
    @RequestMapping(value = "/selectXdaSpecialsCommoditySetListByCommodityIdList", method = RequestMethod.POST)
    public List<XdaSpecialsCommoditySet> selectXdaSpecialsCommoditySetListByCommodityIdList(@RequestBody List<Long> commodityIdList){
        return xdaSpecialsCommoditySetService.selectXdaSpecialsCommoditySetListByCommodityIdList(commodityIdList);
    }
}
