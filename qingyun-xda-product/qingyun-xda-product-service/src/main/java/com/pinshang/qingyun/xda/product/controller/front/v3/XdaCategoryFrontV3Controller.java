package com.pinshang.qingyun.xda.product.controller.front.v3;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.pic.PicSizeEnums;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaSearchAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v3.XdaCategoryV3ODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v3.XdaThTipsV3ODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityAppV3ODTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v3.XdaCategoryCommodityResV3ODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v3.XdaCategoryResV3ODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.FromPageEnums;
import com.pinshang.qingyun.xda.product.service.front.v3.XdaCategoryFrontV3Service;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/xdaCategoryFrontV3")
@Api(value = "鲜达APP分类-V3", tags = "XdaCategoryFrontV3Controller")
public class XdaCategoryFrontV3Controller {

    @Autowired
    private XdaCategoryFrontV3Service xdaCategoryFrontV3Service;
    
    @ApiOperation(value = "APP查询分类-V3")
    @ApiImplicitParam(name = "orderTime", value = "送货日期", required = true, paramType = "query")
    @RequestMapping(value = "/queryXdaCategoryList", method = RequestMethod.GET)
    public XdaCategoryResV3ODTO queryXdaCategoryList(@RequestParam(value = "orderTime", required = false)String orderTime) {
        if(StringUtils.isEmpty(orderTime)){
            return new XdaCategoryResV3ODTO();
        }
        
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        XdaCategoryResV3ODTO result = xdaCategoryFrontV3Service.queryXdaCategoryList(DateUtil.parseDate(orderTime, "yyyy-MM-dd"), xdaTokenInfo.getStoreId());
        if (null == result) {
            result = new XdaCategoryResV3ODTO();
        }
        
        return result;
    }

    @ApiOperation(value = "APP查询分类商品-V3")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaCategoryAppIDTO.class)
    @RequestMapping(value = "/queryXdaCategoryCommodityList", method = RequestMethod.POST)
    public XdaCategoryCommodityResV3ODTO queryXdaCategoryCommodityList(@RequestBody XdaCategoryAppIDTO appIDTO) {
        if(null == appIDTO.getXdaFirstCategoryId() || null == appIDTO.getXdaSecondCategoryId()){
            log.error("查询分类商品，一级分类和二级分类都必传");
            return new XdaCategoryCommodityResV3ODTO();
        }
        
        appIDTO.setStoreId(FastThreadLocalUtil.getXDA().getStoreId());
        XdaCategoryCommodityResV3ODTO result = xdaCategoryFrontV3Service.queryXdaCategoryCommodityList(appIDTO, FromPageEnums.XDA_APP_CATEGORY_COMMODITY_LIST);
        
        if (null == result) {
            result = new XdaCategoryCommodityResV3ODTO();
        }
        
        return result;
    }
    @ApiOperation(value = "APP查询特惠提示信息-V3")
    @ApiImplicitParam(name = "orderTime", value = "送货日期", required = true, paramType = "query")
    @RequestMapping(value = "/getXdaThTipsV3", method = RequestMethod.GET)
    public XdaThTipsV3ODTO getXdaThTipsV3(@RequestParam(value = "orderTime", required = false)String orderTime) {
        Long storeId = FastThreadLocalUtil.getXDA().getStoreId();
        return xdaCategoryFrontV3Service.getXdaThTipsV3ODTO(orderTime,storeId);
    }

//    @ApiOperation(value = "APP搜索商品-V3")
//    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaSearchAppIDTO.class)
//    @RequestMapping(value = "/queryXdaCommoditySearchV3", method = RequestMethod.POST)
//    public ApiResponse<XdaCommodityAppV3ODTO> queryXdaCommoditySearchV3(@RequestBody XdaSearchAppIDTO appIDTO, HttpServletResponse response) {
//        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG,QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
//        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
//        appIDTO.setStoreId(xdaTokenInfo.getStoreId());
//        PageInfo<XdaCommodityAppV3ODTO> xdaCommodityAppV3ODTOPageInfo = xdaCategoryFrontV3Service.queryXdaCommodityPageInfo(appIDTO);
//        return  ApiResponse.convert(xdaCommodityAppV3ODTOPageInfo);
//    }
}
