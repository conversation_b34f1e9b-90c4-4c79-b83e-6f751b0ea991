package com.pinshang.qingyun.xda.product.controller.front.v3;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityDetailAppV3IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityDetailAppV3ODTO;
import com.pinshang.qingyun.xda.product.service.front.v3.XdaCommodityFrontV3Service;

@RestController
@RequestMapping("/xdaCommodityFrontV3")
@Api(value = "鲜达APP商品", tags = "XdaCommodityFrontV3Controller")
public class XdaCommodityFrontV3Controller {

	@Autowired
	private XdaCommodityFrontV3Service xdaCommodityFrontV3Service;

	@ApiOperation(value = "APP查询商品详情信息")
	@ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaCommodityDetailAppV3IDTO.class)
	@RequestMapping(value = "/queryXdaCommodityDetailForApp", method = RequestMethod.POST)
	public XdaCommodityDetailAppV3ODTO queryXdaCommodityDetailForApp(@RequestBody XdaCommodityDetailAppV3IDTO appIDTO) {
		XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
		if (null == xdaTokenInfo.getStoreId()) {
			return null;
		}
		appIDTO.setStoreId(xdaTokenInfo.getStoreId());
		return xdaCommodityFrontV3Service.queryXdaCommodityDetailForApp(appIDTO);
	}
	
}
