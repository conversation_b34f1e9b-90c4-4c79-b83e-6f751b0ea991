package com.pinshang.qingyun.xda.product.controller.front.v4;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.xda.product.dto.XdaRecommondCommodityIDTO;
import com.pinshang.qingyun.xda.product.dto.XdaRecommondCommodityODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.CategoryCommodityFieldIDTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.CategoryCommodityFieldODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaSearchAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v3.XdaThTipsV3ODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v4.XdaCategoryCommodityResV4ODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v4.XdaCategoryResV4ODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v4.XdaExtraCategoryIDTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v4.XdaExtraCategoryODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.FromPageEnums;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityAppV4ODTO;
import com.pinshang.qingyun.xda.product.service.front.v4.XdaCategoryFrontV4Service;
import com.pinshang.qingyun.xda.product.utils.ThreadLocalUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/xdaCategoryFrontV4")
@Api(value = "鲜达APP分类-V4", tags = "XdaCategoryFrontV4Controller")
public class XdaCategoryFrontV4Controller {

    @Autowired
    private XdaCategoryFrontV4Service xdaCategoryFrontV4Service;
    
    @ApiOperation(value = "APP查询分类-V4")
    @ApiImplicitParam(name = "orderTime", value = "送货日期", required = true, paramType = "query")
    @RequestMapping(value = "/queryXdaCategoryList", method = RequestMethod.GET)
    public XdaCategoryResV4ODTO queryXdaCategoryList(@RequestParam(value = "orderTime", required = false)String orderTime,
                                                     @RequestHeader(value = "logisticsCenterId", required = false) Long logisticsCenterId) {
        try {
            if(StringUtils.isEmpty(orderTime)){
                return new XdaCategoryResV4ODTO();
            }
            ThreadLocalUtils.setLogisticsCenterId(logisticsCenterId);
            XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
            XdaCategoryResV4ODTO result = xdaCategoryFrontV4Service.queryXdaCategoryList(DateUtil.parseDate(orderTime, "yyyy-MM-dd"), xdaTokenInfo.getStoreId());
            if (null == result) {
                result = new XdaCategoryResV4ODTO();
            }

            return result;
        } finally {
            ThreadLocalUtils.remove();
        }
    }

    @ApiOperation(value = "根据商品id集合获取商品属性")
    @PostMapping(value = "/queryCategoryCommodityField")
    public List<CategoryCommodityFieldODTO> queryCategoryCommodityField(@RequestBody CategoryCommodityFieldIDTO req) {
        return xdaCategoryFrontV4Service.queryCategoryCommodityField(req);
    }

    @ApiOperation(value = "额外类目")
    @RequestMapping(value = "/queryExtraCategory", method = {RequestMethod.POST, RequestMethod.GET})
    public XdaExtraCategoryODTO queryExtraCategory(@RequestParam(value = "orderTime", required = false) String orderTimeStr
            , @RequestBody(required = false) XdaExtraCategoryIDTO req) {
        if (StringUtils.isBlank(orderTimeStr)) {
            return new XdaExtraCategoryODTO();
        }

        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        Date orderTime = DateUtil.parseDate(orderTimeStr, "yyyy-MM-dd");
        XdaExtraCategoryODTO result = xdaCategoryFrontV4Service.queryExtraCategory(orderTime,req, xdaTokenInfo);

        return Objects.nonNull(result) ? result : new XdaExtraCategoryODTO();
    }

    @ApiOperation(value = "推荐二级类目查询")
    @RequestMapping(value = "/queryRecommondCommodity", method = RequestMethod.POST)
    public XdaRecommondCommodityODTO queryRecommondCommodity(@RequestBody XdaRecommondCommodityIDTO req) {
        return xdaCategoryFrontV4Service.queryRecommondCommodity(req);
    }

    @ApiOperation(value = "APP查询分类商品-V4")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaCategoryAppIDTO.class)
    @RequestMapping(value = "/queryXdaCategoryCommodityList", method = RequestMethod.POST)
    public XdaCategoryCommodityResV4ODTO queryXdaCategoryCommodityList(@RequestBody XdaCategoryAppIDTO appIDTO) {
        if(null == appIDTO.getXdaFirstCategoryId() || null == appIDTO.getXdaSecondCategoryId()){
            log.error("查询分类商品，一级分类和二级分类都必传");
            return new XdaCategoryCommodityResV4ODTO();
        }
        
        appIDTO.setStoreId(FastThreadLocalUtil.getXDA().getStoreId());
        XdaCategoryCommodityResV4ODTO result = xdaCategoryFrontV4Service.queryXdaCategoryCommodityList(appIDTO, FromPageEnums.XDA_APP_CATEGORY_COMMODITY_LIST);
        
        if (null == result) {
            result = new XdaCategoryCommodityResV4ODTO();
        }
        
        return result;
    }
    
    @ApiOperation(value = "APP查询特惠提示信息-V4")
    @ApiImplicitParam(name = "orderTime", value = "送货日期", required = true, paramType = "query")
    @RequestMapping(value = "/getXdaThTips", method = RequestMethod.GET)
    public XdaThTipsV3ODTO getXdaThTips(@RequestParam(value = "orderTime", required = false)String orderTime,
                                        @RequestHeader(value = "logisticsCenterId", required = false) Long logisticsCenterId) {
        try {
            ThreadLocalUtils.setLogisticsCenterId(logisticsCenterId);
            Long storeId = FastThreadLocalUtil.getXDA().getStoreId();
            return xdaCategoryFrontV4Service.getXdaThTipsV4(orderTime,storeId);
        } finally {
            ThreadLocalUtils.remove();
        }
    }
    
    @ApiOperation(value = "APP搜索商品")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaSearchAppIDTO.class)
    @RequestMapping(value = "/queryXdaCommoditySearch", method = RequestMethod.POST)
    public ApiResponse<XdaCommodityAppV4ODTO> queryXdaCommoditySearch(@RequestBody XdaSearchAppIDTO appIDTO, HttpServletResponse response) {
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG,QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        appIDTO.setStoreId(xdaTokenInfo.getStoreId());
        PageInfo<XdaCommodityAppV4ODTO> pageInfo = xdaCategoryFrontV4Service.queryXdaCommodityPageInfo(appIDTO);
        return  ApiResponse.convert( pageInfo);
    }

    @ApiOperation(value = "APP查询分类下的商品Id")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaCategoryAppIDTO.class)
    @RequestMapping(value = "/queryXdaCategoryCommodityIdList", method = RequestMethod.POST)
    public List<Long> queryXdaCategoryCommodityIdList(@RequestBody XdaCategoryAppIDTO appIDTO) {
        if (null == appIDTO.getXdaFirstCategoryId() || null == appIDTO.getXdaSecondCategoryId()) {
            log.error("查询分类商品，一级分类和二级分类都必传");
            return Collections.emptyList();
        }
        appIDTO.setStoreId(FastThreadLocalUtil.getXDA().getStoreId());
        return xdaCategoryFrontV4Service.queryXdaCategoryCommodityIdList(appIDTO);
    }

}
