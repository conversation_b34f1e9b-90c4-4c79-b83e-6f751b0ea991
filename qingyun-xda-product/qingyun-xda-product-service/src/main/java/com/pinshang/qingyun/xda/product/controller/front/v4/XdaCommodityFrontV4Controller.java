package com.pinshang.qingyun.xda.product.controller.front.v4;

import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.product.aspect.RequestBodyAndHeader;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityAppV4IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityAppV4ODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityDetailAppV4IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityDetailAppV4ODTO;
import com.pinshang.qingyun.xda.product.service.front.v4.XdaCommodityFrontV4Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/xdaCommodityFrontV4")
@Api(value = "鲜达APP商品", tags = "XdaCommodityFrontV4Controller")
public class XdaCommodityFrontV4Controller {

	@Autowired
	private XdaCommodityFrontV4Service xdaCommodityFrontV4Service;
	
    @ApiOperation(value = "APP查询商品列表信息")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaCommodityAppV4IDTO.class)
    @RequestMapping(value = "/queryXdaCommodityListForAppV4", method = RequestMethod.POST)
    public List<XdaCommodityAppV4ODTO> queryXdaCommodityListForAppV4(@RequestBodyAndHeader XdaCommodityAppV4IDTO appIDTO) {
        return xdaCommodityFrontV4Service.queryXdaCommodityListForAppV4(appIDTO);
    }

	@ApiOperation(value = "APP查询商品详情信息")
	@ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaCommodityDetailAppV4IDTO.class)
	@RequestMapping(value = "/queryXdaCommodityDetailForApp", method = RequestMethod.POST)
	public XdaCommodityDetailAppV4ODTO queryXdaCommodityDetailForApp(@RequestBodyAndHeader XdaCommodityDetailAppV4IDTO appIDTO) {
		XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
		if (null == xdaTokenInfo.getStoreId()) {
			return null;
		}
		appIDTO.setStoreId(xdaTokenInfo.getStoreId());
		return xdaCommodityFrontV4Service.queryXdaCommodityDetailForApp(appIDTO);
	}
	
}
