package com.pinshang.qingyun.xda.product.controller.web;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.enums.xda.XdaAppCodeEnum;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.xda.product.dto.XdaRecommondCommodityIDTO;
import com.pinshang.qingyun.xda.product.dto.XdaRecommondCommodityODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.QueryExtraCategoryIDTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v4.XdaCategoryCommodityResV4ODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v4.XdaCategoryResV4ODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v4.XdaExtraCategoryIDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.FromPageEnums;
import com.pinshang.qingyun.xda.product.dto.web.XdaRecommondCommodityWebIDTO;
import com.pinshang.qingyun.xda.product.dto.xdaCategory.XdaCategoryListODTO;
import com.pinshang.qingyun.xda.product.service.XdaCategoryService;
import com.pinshang.qingyun.xda.product.service.front.v4.XdaCategoryFrontV4Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/xdaCategoryFrontWeb")
@Api(value = "鲜达APP分类-WEB", tags = "XdaCategoryWebController")
public class XdaCategoryWebController {

    @Autowired
    private XdaCategoryFrontV4Service xdaCategoryFrontV4Service;
    @Autowired
    private XdaCategoryService xdaCategoryService;

    @ApiOperation(value = "额外类目V2")
    @PostMapping(value = "/queryExtraCategory")
    public XdaCategoryResV4ODTO queryExtraCategory(@RequestBody QueryExtraCategoryIDTO req) {
        Date orderTime = req.getOrderTime();
        if (Objects.isNull(orderTime)) {
            return new XdaCategoryResV4ODTO();
        }
        Long storeId = req.getStoreId();

        XdaTokenInfo xdaTokenInfo = buildDefaultXdaToken(storeId);

        XdaExtraCategoryIDTO xdaExtraCategoryIDTO = BeanCloneUtils.copyTo(req, XdaExtraCategoryIDTO.class);

        XdaCategoryResV4ODTO result = xdaCategoryFrontV4Service.queryExtraCategory(orderTime, xdaExtraCategoryIDTO, xdaTokenInfo);

        return Objects.nonNull(result) ? result : new XdaCategoryResV4ODTO();
    }

    @ApiOperation(value = "APP查询分类商品")
    @ApiImplicitParam(name = "appIDTO", value = "请求参数", required = true, paramType = "body", dataTypeClass = XdaCategoryAppIDTO.class)
    @RequestMapping(value = "/queryXdaCategoryCommodityList", method = RequestMethod.POST)
    public XdaCategoryCommodityResV4ODTO queryXdaCategoryCommodityList(@RequestBody XdaCategoryAppIDTO appIDTO) {
        if(null == appIDTO.getXdaFirstCategoryId() || null == appIDTO.getXdaSecondCategoryId()){
            log.error("查询分类商品，一级分类和二级分类都必传");
            return new XdaCategoryCommodityResV4ODTO();
        }
        Long storeId = appIDTO.getStoreId();
        buildDefaultXdaToken(storeId);
        XdaCategoryCommodityResV4ODTO result = xdaCategoryFrontV4Service.queryXdaCategoryCommodityList(appIDTO, FromPageEnums.XDA_APP_CATEGORY_COMMODITY_LIST);

        if (null == result) {
            result = new XdaCategoryCommodityResV4ODTO();
        }

        return result;
    }

    private static XdaTokenInfo buildDefaultXdaToken(Long storeId) {
        XdaTokenInfo xdaTokenInfo = new XdaTokenInfo();
        xdaTokenInfo.setStoreId(storeId);
        xdaTokenInfo.setIsTouristStore(Boolean.FALSE);
        xdaTokenInfo.setAppCode(XdaAppCodeEnum.ANDROID.getAppCode());
        FastThreadLocalUtil.setXDA(xdaTokenInfo);
        return xdaTokenInfo;
    }

    @ApiOperation(value = "推荐二级类目查询")
    @RequestMapping(value = "/queryRecommondCommodity", method = RequestMethod.POST)
    public XdaRecommondCommodityODTO queryRecommondCommodity(@RequestBody XdaRecommondCommodityWebIDTO req) {

        buildDefaultXdaToken(req.getStoreId());
        req.setXdaTokenInfo(FastThreadLocalUtil.getXDA());
        return xdaCategoryFrontV4Service.queryRecommondCommodity(BeanCloneUtils.copyTo(req, XdaRecommondCommodityIDTO.class));
    }

    @ApiOperation(value = "导出  鲜达-品类信息  列表", notes = "导出  鲜达-品类信息  列表")
    @RequestMapping(value = "/exportXdaCategoryList", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "XDA_CATEGORY_LIST")
    public void exportXdaCategoryList(HttpServletResponse response) throws IOException {
        long beginTime = System.currentTimeMillis();
        List<XdaCategoryListODTO> list = xdaCategoryService.findAllSecondXdaCategoryListToExport();
        long middleTime = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "鲜达品类信息"+"_"+ sdf.format(new Date());
        try {
            ExcelUtil.setFileNameAndHead(response, filename);
            EasyExcel.write(response.getOutputStream(), XdaCategoryListODTO.class).autoCloseStream(Boolean.FALSE).sheet("鲜达品类信息列表").doWrite(list);
        }   catch (Exception e) {
            log.error("\n鲜达-品类信息-导出-异常：", e);
            ExcelUtil.setExceptionResponse(response);
        }
        long endTime = System.currentTimeMillis();
        log.info("\n鲜达-品类信息-导出-开始时间：{},中间时间：{},结束时间：{}",beginTime,middleTime,endTime);
    }
}
