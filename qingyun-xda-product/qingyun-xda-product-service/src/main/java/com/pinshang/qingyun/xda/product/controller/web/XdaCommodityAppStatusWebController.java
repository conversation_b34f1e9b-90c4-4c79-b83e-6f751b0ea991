package com.pinshang.qingyun.xda.product.controller.web;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQueryParameter;
import com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.PfAppStatusODTO;
import com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.XdaAppStatusODTO;
import com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.XdaCommodityAppStatusIDTO;
import com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.XdaCommodityAppStatusODTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityAppStatusService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/xdaCommodityAppStatus/web")
@Api(value = "鲜达-商品上下架WEB", tags = "XdaCommodityAppStatusWebController",description ="鲜达-商品上下架Web")
public class XdaCommodityAppStatusWebController {

    @Autowired
    private XdaCommodityAppStatusService xdaCommodityAppStatusService;

	@ApiOperation(value = "鲜达商品上下架列表-导出")
	@RequestMapping(value = "/exportXdaCommodityAppStatusList", method = RequestMethod.GET)
	@FileCacheQuery(bizCode = "XDA_COMMODITY_APP_STATUS_LIST")
	public void exportXdaCommodityAppStatusList(@FileCacheQueryParameter XdaCommodityAppStatusIDTO idto, HttpServletResponse response) throws Exception {
		long beginTime = System.currentTimeMillis();
		
		idto.initExportPage();
	 	List<XdaCommodityAppStatusODTO> list = xdaCommodityAppStatusService.selectCommodityAppStatusList(idto).getList();
         if (CollectionUtils.isEmpty(list)){
             return;
         }
	 	
	 	long middleTime = System.currentTimeMillis();
	 	
	 	SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String fileNamePrefix = (Objects.equals(idto.getAppType(), 1)) ? "鲜达商品上下架" : "批发商品上下架";
        String filename = fileNamePrefix + "_" + sdf.format(new Date());
        try {
            ExcelUtil.setFileNameAndHead(response, filename);
            if (Objects.equals(idto.getAppType(), 1)) {
                List<XdaAppStatusODTO> xdaAppStatusList = BeanCloneUtils.copyTo(list, XdaAppStatusODTO.class);
                EasyExcel.write(response.getOutputStream(), XdaAppStatusODTO.class).autoCloseStream(Boolean.FALSE).sheet("鲜达商品上下架").doWrite(xdaAppStatusList);
            } else {
                List<PfAppStatusODTO> pfAppStatusList = BeanCloneUtils.copyTo(list, PfAppStatusODTO.class);
                EasyExcel.write(response.getOutputStream(), PfAppStatusODTO.class).autoCloseStream(Boolean.FALSE).sheet("批发商品上下架").doWrite(pfAppStatusList);
            }
        } catch (Exception e) {
            log.error("\n鲜达商品上下架列表-导出-异常：", e);
            ExcelUtil.setExceptionResponse(response);
        }
        long endTime = System.currentTimeMillis();
        
        log.info("\n鲜达商品上下架列表-导出- 完成，查询耗时{}秒、组装Excel耗时{}秒，总耗时{}秒", ((middleTime - beginTime) / 1000), ((endTime - middleTime) / 1000), ((endTime - beginTime) / 1000));
	}
    
}
