package com.pinshang.qingyun.xda.product.controller.web;

import com.alibaba.excel.EasyExcel;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQuery;
import com.pinshang.qingyun.infrastructure.exportfile.cache.starter.FileCacheQueryParameter;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommoditySortNumInfoODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextInfoODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommoditySortNumInfoPageIDTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextInfoPageIDTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityTextService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: hhf
 * @date: 2025/4/8/008 10:27
 */
@Slf4j
@RestController
@RequestMapping("/xdaCommodityText/web")
@Api(value = "鲜达-商品文描WEB", tags = "XdaCommodityTextWebController",description ="鲜达-商品文描WEB")
public class XdaCommodityTextWebController {

    @Autowired
    private XdaCommodityTextService xdaCommodityTextService;

    @ApiOperation(value = "导出  鲜达-商品排序信息  列表", notes = "导出  鲜达-商品排序信息  列表")
    @RequestMapping(value = "/exportXdaCommoditySortNumInfoList", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "XDA_COMMODITY_SORT_NUM_INFO_LIST")
    public void exportXdaCommoditySortNumInfoList(@FileCacheQueryParameter SelectCommoditySortNumInfoPageIDTO idto, HttpServletResponse response) throws IOException {
        long beginTime = System.currentTimeMillis();

        idto.initExportPage();
        List<CommoditySortNumInfoODTO> list = xdaCommodityTextService.selectCommoditySortNumInfoPage(idto).getList();

        long middleTime = System.currentTimeMillis();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "鲜达-商品排序信息列表_" + sdf.format(new Date());
        try {
            ExcelUtil.setFileNameAndHead(response, filename);
            EasyExcel.write(response.getOutputStream(), CommoditySortNumInfoODTO.class).autoCloseStream(Boolean.FALSE).sheet("鲜达-商品排序信息列表").doWrite(list);
        } catch (Exception e) {
            log.error("\n鲜达-商品排序信息列表-导出-异常：", e);
            ExcelUtil.setExceptionResponse(response);
        }
        long endTime = System.currentTimeMillis();

        log.info("\n鲜达-商品排序信息列表-导出- 完成，查询耗时{}秒、组装Excel耗时{}秒，总耗时{}秒", ((middleTime - beginTime) / 1000), ((endTime - middleTime) / 1000), ((endTime - beginTime) / 1000));
    }

    @ApiOperation(value = "导出  鲜达-商品信息  列表", notes = "导出  鲜达-商品信息  列表")
    @RequestMapping(value = "/exportXdaCommodityTextInfoList", method = RequestMethod.GET)
    @FileCacheQuery(bizCode = "XDA_COMMODITY_TEXT_INFO_LIST")
    public void exportXdaCommodityTextInfoList(@FileCacheQueryParameter SelectCommodityTextInfoPageIDTO idto, HttpServletResponse response) throws IOException {
        long beginTime = System.currentTimeMillis();
        idto.initExportPage();
        List<CommodityTextInfoODTO> list = xdaCommodityTextService.selectCommodityTextInfoPage(idto).getList();
        long middleTime = System.currentTimeMillis();

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String filename = "鲜达-商品文描信息列表_" + sdf.format(new Date());
        try {
            ExcelUtil.setFileNameAndHead(response, filename);
            EasyExcel.write(response.getOutputStream(), CommodityTextInfoODTO.class).autoCloseStream(Boolean.FALSE).sheet("鲜达-商品文描信息列表").doWrite(list);
        } catch (Exception e) {
            log.error("\n鲜达-商品文描信息列表-导出-异常：", e);
            ExcelUtil.setExceptionResponse(response);
        }

        long endTime = System.currentTimeMillis();

        log.info("\n鲜达-商品文描信息列表-导出- 完成，查询耗时{}秒、组装Excel耗时{}秒，总耗时{}秒", ((middleTime - beginTime) / 1000), ((endTime - middleTime) / 1000), ((endTime - beginTime) / 1000));

    }
}
