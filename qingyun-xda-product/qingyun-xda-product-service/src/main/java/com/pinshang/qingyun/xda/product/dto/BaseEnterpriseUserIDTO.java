package com.pinshang.qingyun.xda.product.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@ApiModel
@NoArgsConstructor
public abstract class BaseEnterpriseUserIDTO  {
	@ApiModelProperty(position = 91, required = true, value = "企业ID，由后端设置，无需前端设置", hidden = true)
	@Deprecated private Long enterpriseId;
	@ApiModelProperty(position = 92, required = true, value = "用户ID，由后端设置，无需前端设置", hidden = true)
	private Long userId;
}
