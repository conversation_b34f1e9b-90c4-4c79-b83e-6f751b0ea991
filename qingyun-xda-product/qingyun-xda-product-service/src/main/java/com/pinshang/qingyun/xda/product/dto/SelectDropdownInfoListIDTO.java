package com.pinshang.qingyun.xda.product.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询下拉信息列表 —— 一般作为基类，需要其他条件，可以在子类中扩充
 *
 * <AUTHOR>
 *
 * @date 2020年4月10日
 */
@Data
public class SelectDropdownInfoListIDTO {
	protected static final Integer DEFAULT_LIMIT_QUANTITY = 50;
	
	@ApiModelProperty(position = 1, value = "关键词")
	private String keyword;
	@ApiModelProperty(position = 2,  value = "最大匹配数量")
	private Integer limitQuantity;
	
	public Integer getLimitQuantity() {
		if (null == this.limitQuantity || this.limitQuantity.intValue() < 1) {
			return DEFAULT_LIMIT_QUANTITY;
		}
		return this.limitQuantity;
	}
	
}
