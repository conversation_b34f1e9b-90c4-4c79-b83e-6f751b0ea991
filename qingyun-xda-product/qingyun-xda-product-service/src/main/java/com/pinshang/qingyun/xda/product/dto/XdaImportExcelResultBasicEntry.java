package com.pinshang.qingyun.xda.product.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/23 17:56
 */
@Data
@NoArgsConstructor
public class XdaImportExcelResultBasicEntry {
    @ApiModelProperty(value = "true成功、false失败时msg集合有错误信息 展示页面")
    private Boolean success;
    private String batchNo;
    @ApiModelProperty(value = "错误信息List集合对象String")
    private List<String> errMsgList;


    public XdaImportExcelResultBasicEntry(String batchNo, Boolean success, List<String> errMsgList) {
        this.batchNo = batchNo;
        this.success = success;
        this.errMsgList = errMsgList;
    }
}
