package com.pinshang.qingyun.xda.product.dto.commodityTag;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: chen<PERSON><PERSON>
 * @time: 2020/12/22 14:45
 */
@Data
public class XdaCommodityTagIDTO extends Pagination {
    @ApiModelProperty(position = 1,value = "标签名称")
    private String commodityTagName;

    @ApiModelProperty(position = 2,value = "状态")
    private Integer status;
}
