package com.pinshang.qingyun.xda.product.dto.commodityTag;

import com.pinshang.qingyun.base.enums.settlement.OperateTypeNewEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @time: 2020/12/22 14:47
 */
@Data
public class XdaCommodityTagLogODTO {
    @ApiModelProperty(position = 1,value = "操作类型")
    private Integer operateType;

    private String operateTypeName;

    @ApiModelProperty(position = 2,value = "标签名称")
    private String commodityTagName;

    @ApiModelProperty(position = 3,value = "标签底色")
    private String tagBgColor;

    @ApiModelProperty(position = 4,value = "操作人")
    private String employeeName;

    @ApiModelProperty(position = 5,value = "操作时间")
    private String createTime;


    public String getOperateTypeName() {
        String str = "";
        if(null != this.operateType){
            OperateTypeNewEnum op =  OperateTypeNewEnum.fromCode(this.operateType);
            str = op.getName();
        }
        return str;
    }
}
