package com.pinshang.qingyun.xda.product.dto.commodityTag;

import com.pinshang.qingyun.base.enums.XSCouponPublishRuleStatusEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @time: 2020/12/22 14:45
 */
@Data
public class XdaCommodityTagODTO {
    private Long id;

    @ApiModelProperty(position = 1,value = "标签名称")
    private String commodityTagName;

    @ApiModelProperty(position = 2,value = "状态 0-停用、1-启用 XSCouponPublishRuleStatusEnums")
    private Integer status;

    @ApiModelProperty(position = 3,value = "状态")
    private String statusName;

    @ApiModelProperty(position = 4,value = "颜色")
    private String tagBgColor;

    public String getStatusName(){
        String name = "";
        if(null != status){
            if(XSCouponPublishRuleStatusEnums.ENABLE.getCode().equals(status)){
                name = XSCouponPublishRuleStatusEnums.ENABLE.getName();
            }else if(XSCouponPublishRuleStatusEnums.DISABLE.getCode().equals(status)){
                name = XSCouponPublishRuleStatusEnums.DISABLE.getName();
            }
        }
        return name;
    }
}
