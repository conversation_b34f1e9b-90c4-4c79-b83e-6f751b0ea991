package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.xda.product.dto.BaseEnterpriseUserIDTO;

/**
 * 批量更新  商品文描-排序号
 *
 * <AUTHOR>
 *
 * @date 2020年3月6日
 */
@Data
@NoArgsConstructor
public class BatchUpdateCommoditySortNumIDTO extends BaseEnterpriseUserIDTO {
	@ApiModelProperty(position = 10, required = true, value = "商品排序信息集合")
	private List<CommoditySortNumIDTO> commoditySortNumList;
}
