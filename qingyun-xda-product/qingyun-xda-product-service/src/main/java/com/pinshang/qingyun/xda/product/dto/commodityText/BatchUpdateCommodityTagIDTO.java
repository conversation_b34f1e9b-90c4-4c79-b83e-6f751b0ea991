package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.xda.product.dto.BaseEnterpriseUserIDTO;

/**
 * 批量更新  商品文描-标签
 *
 * <AUTHOR>
 *
 * @date 2019年12月26日
 */
@Data
@NoArgsConstructor
public class BatchUpdateCommodityTagIDTO extends BaseEnterpriseUserIDTO {
	@ApiModelProperty(position = 10, required = true, value = "商品前台品类信息集合")
	private List<CommodityTagIDTO> commodityTagList;
}
