package com.pinshang.qingyun.xda.product.dto.commodityText;

import com.pinshang.qingyun.xda.product.dto.BaseEnterpriseUserIDTO;

import lombok.Data;
import lombok.NoArgsConstructor;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 批量更新  商品文描-前台品类
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@NoArgsConstructor
public class BatchUpdateCommodityXdaCategoryIDTO extends BaseEnterpriseUserIDTO {
	@ApiModelProperty(position = 10, required = true, value = "商品前台品类信息集合")
	private List<CommodityXdaCategoryIDTO> commodityXdaCategoryList;
}
