package com.pinshang.qingyun.xda.product.dto.commodityText;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.box.utils.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品排序信息
 *
 * <AUTHOR>
 *
 * @date 2020年3月6日
 */
@Data
@ApiModel
@NoArgsConstructor
public class CommoditySortNumInfoODTO {
	@ExcelIgnore
	@ApiModelProperty(position = 10, required = true, value = "商品ID")
	private String commodityId;


	@ApiModelProperty(position = 11, required = true, value = "商品编码")
	@ExcelProperty(value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(position = 12, required = true, value = "排序")
	@ExcelProperty(value = "排序")
	private Integer sortNum;
	@ApiModelProperty(position = 13, required = true, value = "商品名称")
	@ExcelProperty(value = "商品名称")
	private String commodityName;
	@ApiModelProperty(position = 14, required = true, value = "商品规格")
	@ExcelProperty(value = "商品规格")
	private String commoditySpec;
	@ApiModelProperty(position = 15, required = true, value = "条码")
	@ExcelProperty(value = "条码")
	private String barCode;
	@ApiModelProperty(position = 15, required = true, value = "副条码")
	@ExcelIgnore
	private String subBarCodes;
	@ApiModelProperty(position = 16, required = true, value = "前台品名")
	@ExcelProperty(value = "前台品名")
	private String commodityAppName;
	
	@ApiModelProperty(position = 17, required = true, value = "前台一级品类名称", hidden = true)
	@ExcelIgnore
	private String xdaFirstCategoryName;
	@ApiModelProperty(position = 17, required = true, value = "前台一级品类名称", hidden = true)
	@ExcelIgnore
	private String xdaSecondCategoryName;
	@ApiModelProperty(position = 17, required = true, value = "前台品类")
	@ExcelProperty(value = "前台品类")
	private String xdaCategoryName;

	@ExcelIgnore
	@ApiModelProperty(position = 18, required = true, value = "后台一级品类ID", hidden = true)
	private Long firstCategoryId;
	@ExcelIgnore
	@ApiModelProperty(position = 18, required = true, value = "后台二级品类ID", hidden = true)
	private Long secondCategoryId;
	@ExcelIgnore
	@ApiModelProperty(position = 18, required = true, value = "后台三级品类ID", hidden = true)
	private Long thirdCategoryId;

	@ApiModelProperty(position = 18, required = true, value = "后台品类")
	@ExcelProperty(value = "后台品类")
	private String categoryName;
	
	public String getCommodityAppName() {
		return StringUtil.isNullOrEmpty(this.commodityAppName)? "": this.commodityAppName.trim();
	}
	
	public String getXdaCategoryName() {
		if (!StringUtil.isNullOrEmpty(this.xdaFirstCategoryName) && !StringUtil.isNullOrEmpty(this.xdaSecondCategoryName)){
			return this.xdaFirstCategoryName.trim() + "/" + this.xdaSecondCategoryName.trim();
		}
		return "";
	}
	
}
