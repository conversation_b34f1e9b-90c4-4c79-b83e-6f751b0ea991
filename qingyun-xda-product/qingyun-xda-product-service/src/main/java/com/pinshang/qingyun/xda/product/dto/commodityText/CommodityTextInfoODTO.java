package com.pinshang.qingyun.xda.product.dto.commodityText;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.base.enums.CommodityStatusEnum;
import com.pinshang.qingyun.base.enums.IsWeightEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品文描信息
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@ApiModel
@NoArgsConstructor
public class CommodityTextInfoODTO {

	@ExcelIgnore
	@ApiModelProperty(position = 0, required = true, value = "商品ID", hidden = true)
	private Long id;
	@ExcelIgnore
	@ApiModelProperty(position = 10, required = true, value = "商品ID")
	private String commodityId;
	
	@ApiModelProperty(position = 11, required = true, value = "商品编码")
	@ExcelProperty(value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(position = 12, required = true, value = "商品名称")
	@ExcelProperty(value = "商品名称")
	private String commodityName;
	@ApiModelProperty(position = 13, required = true, value = "商品规格")
	@ExcelProperty(value = "规格")
	private String commoditySpec;
	@ApiModelProperty(position = 14, required = true, value = "商品单位ID", hidden = true)
	@ExcelIgnore
	private Long commodityUnitId;
	@ApiModelProperty(position = 14, required = true, value = "商品单位名称")
	@ExcelProperty(value = "单位")
	private String commodityUnitName;
	
	@ApiModelProperty(position = 21, required = true, value = "前台一级品类ID")
	@ExcelIgnore
    private Long xdaFirstCategoryId;
	@ApiModelProperty(position = 21, required = true, value = "前台二级品类ID")
	@ExcelIgnore
	private Long xdaSecondCategoryId;
	@ApiModelProperty(position = 21, required = true, value = "前台一级品类")
	@ExcelIgnore
    private String xdaFirstCategoryName;
	@ApiModelProperty(position = 21, required = true, value = "前台二级品类")
	@ExcelIgnore
	private String xdaSecondCategoryName;
	@ApiModelProperty(position = 21, required = true, value = "前台品类-显示")
	@ExcelProperty(value = "前台品类")
	private String xdaCategoryStatusName;
	@ApiModelProperty(position = 22, required = true, value = "前台品名")
	@ExcelProperty(value = "前台品名")
	private String commodityAppName;

	@ExcelIgnore
	@ApiModelProperty(position = 23, required = true, value = "系列品-主品ID", hidden = true)
	private String serialCommodityId;
	@ApiModelProperty(position = 23, required = true, value = "系列品-编码")
	@ExcelProperty(value = "系列品编码")
	private String serialCommodityCode;
	@ApiModelProperty(position = 23, required = true, value = "系列品-是否主品状态")
	@ExcelProperty(value = "系列品主品")
	private String serialCommodityStatusName;

	@ExcelIgnore
	@ApiModelProperty(position = 31, required = true, value = "图片集合")
    private List<CommodityTextPicODTO> picList;
	@ApiModelProperty(position = 31, required = true, value = "图片-展示文案")
	@ExcelProperty(value = "图片")
	private String picStatusName;
	@ApiModelProperty(position = 32, required = true, value = "长图Url")
	@ExcelIgnore
    private String longPicUrl;
	@ApiModelProperty(position = 32, required = true, value = "长图Url-访问")
	@ExcelIgnore
    private String visitLongPicUrl;
	@ApiModelProperty(position = 32, required = true, value = "长图-展示文案")
	@ExcelProperty(value = "长图")
	private String longPicStatusName;
	
	@ApiModelProperty(position = 33, required = true, value = "副标题")
	@ExcelProperty(value = "副标题")
    private String commoditySubName;

	@ExcelIgnore
	@ApiModelProperty(position = 34, required = true, value = "标签ID", hidden = true)
	private Long tagId;
	@ApiModelProperty(position = 34, required = true, value = "标签名称")
	@ExcelProperty(value = "自定义标签")
	private String tagName;
	@ApiModelProperty(position = 34, required = true, value = "标签背景色值")
	@ExcelIgnore
	private String tagBgColor;

	@ApiModelProperty(position = 35, required = true, value = "保质期(天)")
	@ExcelProperty(value = "保质期(天)")
	private BigDecimal qualityDays;
	@ApiModelProperty(position = 36, required = true, value = "是否显示保质期：1-显示，0-不显示")
	@ExcelIgnore
	private Integer qualityStatus;
	@ApiModelProperty(position = 18, required = true, value = "是否显示保质期-展示文案")
	@ExcelProperty(value = "是否显示保质期")
	private String qualityStatusName;
	
	@ApiModelProperty(position = 41, required = true, value = "称重状态", hidden = true)
	@ExcelIgnore
	private Integer isWeight;
	@ApiModelProperty(position = 41, required = true, value = "称重状态名称")
	@ExcelProperty(value = "是否称重")
	private String isWeightName;
	
	@ApiModelProperty(position = 42, required = true, value = "可售状态", hidden = true)
	@ExcelIgnore
    private Integer commodityState;
	@ApiModelProperty(position = 42, required = true, value = "可售状态名称")
	@ExcelProperty(value = "总部可售")
	private String commodityStateName;
	
	@ApiModelProperty(position = 51, required = true, value = "后台一级品类ID", hidden = true)
	@ExcelIgnore
	private Long firstCategoryId;
	@ApiModelProperty(position = 51, required = true, value = "后台二级品类ID", hidden = true)
	@ExcelIgnore
	private Long secondCategoryId;
	@ApiModelProperty(position = 51, required = true, value = "后台三级品类ID", hidden = true)
	@ExcelIgnore
	private Long thirdCategoryId;
	@ApiModelProperty(position = 51, required = true, value = "后台一级品类名称")
	@ExcelProperty(value = "后台品类1")
	private String firstCategoryName;
	@ApiModelProperty(position = 51, required = true, value = "后台二级品类名称")
	@ExcelProperty(value = "后台品类2")
	private String secondCategoryName;
	@ApiModelProperty(position = 51, required = true, value = "后台三级品类名称")
	@ExcelProperty(value = "后台品类3")
	private String thirdCategoryName;
	
	@ApiModelProperty(position = 61, required = true, value = "条码")
	@ExcelProperty(value = "条码")
	private String barCode;
	@ApiModelProperty(position = 61, required = true, value = "副条码")
	@ExcelIgnore
	private String subBarCodes;

	/** 上下架状态：0-上架，1-下架 */
	@ExcelIgnore
	private Integer appStatus;

	/**
	 * 2-8
	 */
	@ExcelIgnore
	private String deliveryDateRangeCode;

	public String getCommodityAppName() {
		return StringUtil.isNullOrEmpty(this.commodityAppName)? "无": this.commodityAppName.trim();
	}
	
	public String getXdaCategoryStatusName() {
		return StringUtil.isNullOrEmpty(this.xdaFirstCategoryName) || StringUtil.isNullOrEmpty(this.xdaSecondCategoryName)? "无": this.xdaFirstCategoryName.trim() + " / " + this.xdaSecondCategoryName.trim();
	}
	
	public String getSerialCommodityStatusName() {
		return null == this.serialCommodityId? "": (this.commodityId.equals(this.serialCommodityId)? "是": "否");
	}
	
	public List<CommodityTextPicODTO> getPicList() {
		return null == picList? new ArrayList<>(): picList;
	}
	
	public String getPicStatusName() {
		return SpringUtil.isEmpty(this.picList)? "无": "有";
	}
	
	public String getLongPicStatusName() {
		return StringUtil.isNullOrEmpty(this.longPicUrl)? "无": "有";
	}
	
	public String getCommoditySubName() {
		return StringUtil.isNullOrEmpty(this.commoditySubName)? "无": this.commoditySubName.trim();
	}

	public String getQualityStatusName() {
		return (null != this.qualityStatus && this.qualityStatus == 1) ? "显示": "不显示";
	}
	
	public String getIsWeightName() {
		return IsWeightEnums.getName(this.isWeight);
	}
	
	public String getCommodityStateName() {
		if (CommodityStatusEnum.START.getCode().equals(this.commodityState)) {
			return "可售";
		} else if (CommodityStatusEnum.STOP.getCode().equals(this.commodityState)) {
			return "停售";
		} 
		return "";
	}
	
}
