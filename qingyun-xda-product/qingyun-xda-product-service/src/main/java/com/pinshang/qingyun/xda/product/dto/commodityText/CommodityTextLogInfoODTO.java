package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.enums.commodity.CommodityTextOperateTypeEnum;

/**
 * 商品文描日志信息
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@ApiModel
@NoArgsConstructor
public class CommodityTextLogInfoODTO {
	@ApiModelProperty(position = 11, required = true, value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(position = 12, required = true, value = "商品名称")
	private String commodityName;
	@ApiModelProperty(position = 13, required = true, value = "商品规格")
	private String commoditySpec;
	@ApiModelProperty(position = 14, required = true, value = "操作类型：")
	private Integer operateType;
	@ApiModelProperty(position = 14, required = true, value = "操作类型名称")
	private String operateTypeName;
	@ApiModelProperty(position = 15, required = true, value = "旧值")
	private String oldValue;
	@ApiModelProperty(position = 16, required = true, value = "新值")
	private String newValue;
	@ApiModelProperty(position = 17, required = true, value = "操作人名称")
	private String createName;
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@ApiModelProperty(position = 18, required = true, value = "操作时间：yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
	public String getOperateTypeName() {
		return CommodityTextOperateTypeEnum.getName(this.operateType);
	}
	
}
