package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品文描标签信息
 */
@Data
@NoArgsConstructor
public class CommodityTextTagInfoODTO {
	@ApiModelProperty(position = 11, required = true, value = "标签名称")
	private String tagName;
	@ApiModelProperty(position = 12, required = true, value = "标签背景色值")
	private String tagBgColor;
	
	@ApiModelProperty(position = 10, required = true, value = "商品ID", hidden = true)
	private Long commodityId;

	public CommodityTextTagInfoODTO(String tagName, String tagBgColor, Long commodityId) {
		this.tagName = tagName;
		this.tagBgColor = tagBgColor;
		this.commodityId = commodityId;
	}
}
