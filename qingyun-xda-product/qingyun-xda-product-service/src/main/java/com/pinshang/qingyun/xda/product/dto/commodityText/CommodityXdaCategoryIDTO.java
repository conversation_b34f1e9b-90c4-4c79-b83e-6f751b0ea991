package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品文描-前台品类
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@NoArgsConstructor
public class CommodityXdaCategoryIDTO {
	@ApiModelProperty(position = 10, required = true, value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(position = 11, required = true, value = "前台一级品类名称")
    private String xdaFirstCategoryName;
	@ApiModelProperty(position = 12, required = true, value = "前台二级品类名称")
    private String xdaSecondCategoryName;
}
