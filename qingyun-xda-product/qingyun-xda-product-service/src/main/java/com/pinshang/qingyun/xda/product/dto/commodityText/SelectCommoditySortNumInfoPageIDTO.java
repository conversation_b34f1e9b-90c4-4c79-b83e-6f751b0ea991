package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.page.Pagination;

/**
 * 分页查询  商品排序信息  列表
 *
 * <AUTHOR>
 *
 * @date 2020年3月6日
 */
@Data
@NoArgsConstructor
public class SelectCommoditySortNumInfoPageIDTO extends Pagination {
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(position = 11, value = "商品ID")
	private Long commodityId;
	@ApiModelProperty(position = 12, value = "前台品类ID")
	private Long xdaCategoryId;
	@ApiModelProperty(position = 13, value = "前台品名")
	private String commodityAppName;
	@ApiModelProperty(position = 14, value = "条码")
	private String barCode;
	@ApiModelProperty(position = 15, value = "仅显示未排序商品：1-是")
	private Integer onlySortNumNull;
}
