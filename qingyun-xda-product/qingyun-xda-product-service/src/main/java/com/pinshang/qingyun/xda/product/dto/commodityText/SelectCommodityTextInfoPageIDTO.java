package com.pinshang.qingyun.xda.product.dto.commodityText;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.page.Pagination;

/**
 * 分页查询  商品文描信息列表
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@NoArgsConstructor
public class SelectCommodityTextInfoPageIDTO extends Pagination {
	private static final long serialVersionUID = 1L;
	
	@ApiModelProperty(position = 11, value = "商品名称")
	private String commodityName;
	@ApiModelProperty(position = 11, value = "前台品名")
	private String commodityAppName;
	@ApiModelProperty(position = 12, value = "条码")
	private String barCode;
	@ApiModelProperty(position = 13, value = "可售状态：0-否、1-是")
	private Integer commodityState;
	@ApiModelProperty(position = 14, value = "称重状态：0-否、1-是")
	private Integer isWeight;
	
	@ApiModelProperty(position = 21, value = "图片状态：0-无、1-有")
	private Integer picStatus;
	@ApiModelProperty(position = 22, value = "长图状态：0-无、1-有")
	private Integer longPicStatus;
	@ApiModelProperty(position = 23, value = "客户ID")
	private Long storeId;
	@ApiModelProperty(position = 24, value = "产品价格方案ID")
	private Long productPriceModelId;
	
	@ApiModelProperty(position = 31, value = "副标题状态：0-无、1-有")
	private Integer commoditySubNameStatus;
	@ApiModelProperty(position = 32, value = "前台品名状态：0-无、1-有")
	private Integer commodityAppNameStatus;
	@ApiModelProperty(position = 33, value = "前台品类状态：0-无、1-有")
	private Integer xdaCategoryStatus;
	@ApiModelProperty(position = 34, value = "系列品状态：0-否、1-是")
	private Integer serialCommodityStatus;
	
	@ApiModelProperty(position = 41, value = "前台品类ID")
	private Long xdaCategoryId;
	
	@ApiModelProperty(position = 91, value = "商品ID集合，由storeId查询而来，无需前端设置", hidden = true)
	private List<Long> commodityIdListByStoreId;

	@ApiModelProperty(position = 44, required = true, value = "是否显示保质期：1-显示，0-不显示")
	private Integer qualityStatus;
}
