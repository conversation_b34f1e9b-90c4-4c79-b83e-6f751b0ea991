package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.page.Pagination;
import com.pinshang.qingyun.box.utils.StringUtil;

/**
 * 分页查询  商品文描日志信息列表
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@ApiModel
@NoArgsConstructor
public class SelectCommodityTextLogInfoPageIDTO extends Pagination {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(position = 11, value = "商品")
	private String commodityName;
	@ApiModelProperty(position = 12, value = "操作人")
	private String createName;
	@ApiModelProperty(position = 13, value = "操作时间-起：yyyy-MM-dd")
	private String beginCreateDate;
	@ApiModelProperty(position = 14, value = "操作时间-讫：yyyy-MM-dd")
	private String endCreateDate;
	@ApiModelProperty(position = 15, value = "操作类型：")
	private Integer operateType;
	
	public String getBeginCreateDate() {
		return StringUtil.isNullOrEmpty(beginCreateDate)? null: beginCreateDate + " 00:00:00";
	}
	public String getEndCreateDate() {
		return StringUtil.isNullOrEmpty(endCreateDate)? null: endCreateDate + " 23:59:59";
	}
	
	
}
