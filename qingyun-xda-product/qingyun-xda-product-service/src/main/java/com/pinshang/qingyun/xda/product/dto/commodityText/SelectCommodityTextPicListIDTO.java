package com.pinshang.qingyun.xda.product.dto.commodityText;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查询  商品文描图片  列表
 */
@Data
@NoArgsConstructor
public class SelectCommodityTextPicListIDTO {
	private Long commodityId;
	private List<Long> commodityIdList;
	private Integer picType;
	private Integer isDefault;
	
	public SelectCommodityTextPicListIDTO(Long commodityId) {
		this.commodityId = commodityId;
	}
	
	public SelectCommodityTextPicListIDTO(List<Long> commodityIdList) {
		this.commodityIdList = commodityIdList;
	}

	public SelectCommodityTextPicListIDTO(List<Long> commodityIdList, Integer picType) {
		this.commodityIdList = commodityIdList;
		this.picType = picType;
	}

	public SelectCommodityTextPicListIDTO(List<Long> commodityIdList, Integer picType, Integer isDefault) {
		this.commodityIdList = commodityIdList;
		this.picType = picType;
		this.isDefault = isDefault;
	}
}
