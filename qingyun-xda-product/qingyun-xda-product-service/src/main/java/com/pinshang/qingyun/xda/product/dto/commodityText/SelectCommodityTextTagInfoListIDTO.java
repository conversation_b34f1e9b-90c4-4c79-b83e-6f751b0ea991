package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 查询  商品文描标签信息  列表
 */
@Data
@NoArgsConstructor
public class SelectCommodityTextTagInfoListIDTO {
	@ApiModelProperty(position = 11, value = "商品ID")
	private Long commodityId;
	@ApiModelProperty(position = 12, value = "商品ID集合")
	private List<Long> commodityIdList;
	
	public SelectCommodityTextTagInfoListIDTO(Long commodityId) {
		this.commodityId = commodityId;
	}
	public SelectCommodityTextTagInfoListIDTO(List<Long> commodityIdList) {
		this.commodityIdList = commodityIdList;
	}
}
