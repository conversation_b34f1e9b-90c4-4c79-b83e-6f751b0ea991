package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 查询   鲜达商品下拉信息  列表
 *
 * <AUTHOR>
 *
 * @date 2020年3月9日
 */
@Data
public class SelectXdaCommodityDropdownInfoListIDTO {
	public static final Integer DEFAULT_QUANTITY = 50;
	
	@ApiModelProperty(position = 21, value = "关键字：商品编码/商品名称  模糊查询")
	private String keyword;
	
	@ApiModelProperty(position = 21, value = "关键字：商品编码/商品前台名称  模糊查询")
	private String commodityAppName;
	
	@ApiModelProperty(position = 22, value = "商品ID")
	private Long commodityId;
	
	@ApiModelProperty(position = 51, value = "最大匹配数量")
	private Integer limitQuantity;

	public Integer getLimitQuantity() {
		if (null == this.limitQuantity || this.limitQuantity.intValue() < 1) {
			return DEFAULT_QUANTITY;
		}
		return this.limitQuantity;
	}
	
}
