package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询  鲜达-商品文描-前台品类信息
 */
@Data
@NoArgsConstructor
public class SelectXdaCommodityTextCategoryInfoIDTO {
	@ApiModelProperty(position = 11, required = true, value = "前台一级品类名称")
    private String xdaFirstCategoryName;
	@ApiModelProperty(position = 12, required = true, value = "前台二级品类名称")
	private String xdaSecondCategoryName;
	
	public SelectXdaCommodityTextCategoryInfoIDTO(String xdaFirstCategoryName, String xdaSecondCategoryName) {
		this.xdaFirstCategoryName = xdaFirstCategoryName;
		this.xdaSecondCategoryName = xdaSecondCategoryName;
	}
	
}
