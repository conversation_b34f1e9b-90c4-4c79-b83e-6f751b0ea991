package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询  鲜达-商品文描信息  列表
 */
@Data
@NoArgsConstructor
public class SelectXdaPriceStructureCommodityTextInfoListIDTO {
	@ApiModelProperty(position = 11, value = "商品编码集合")
	private List<String> commodityCodeList;
	
	public SelectXdaPriceStructureCommodityTextInfoListIDTO(List<String> commodityCodeList) {
		this.commodityCodeList = commodityCodeList;
	}
	
}
