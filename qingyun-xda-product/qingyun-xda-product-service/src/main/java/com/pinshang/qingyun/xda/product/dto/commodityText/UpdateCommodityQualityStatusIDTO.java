package com.pinshang.qingyun.xda.product.dto.commodityText;

import com.pinshang.qingyun.xda.product.dto.BaseEnterpriseUserIDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: hhf
 * @date: 2024/7/8/008 12:45
 */
@Data
@NoArgsConstructor
public class UpdateCommodityQualityStatusIDTO extends BaseEnterpriseUserIDTO {

    @ApiModelProperty(position = 10, required = true, value = "商品ID")
    private Long commodityId;
    @ApiModelProperty(position = 11, required = true, value = "是否显示保质期： 1-显示， 0-不显示")
    private Integer qualityStatus;

    public UpdateCommodityQualityStatusIDTO(Long commodityId, Integer qualityStatus) {
        this.commodityId = commodityId;
        this.qualityStatus = qualityStatus;
    }
}
