package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.xda.product.dto.BaseEnterpriseUserIDTO;

/**
 * 更新  商品文描-排序
 *
 * <AUTHOR>
 *
 * @date 2020年3月6日
 */
@Data
@NoArgsConstructor
public class UpdateCommoditySortNumIDTO extends BaseEnterpriseUserIDTO {
	@ApiModelProperty(position = 10, required = true, value = "商品ID")
	private Long commodityId;
	@ApiModelProperty(position = 11, required = true, value = "排序号")
    private Integer sortNum;
    
    public UpdateCommoditySortNumIDTO(Long commodityId, Integer sortNum) {
    	this.commodityId = commodityId;
    	this.sortNum = sortNum;
    }
}
