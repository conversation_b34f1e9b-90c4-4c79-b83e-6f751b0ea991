package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;

import com.pinshang.qingyun.xda.product.dto.BaseEnterpriseUserIDTO;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新  商品文描-副标题
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@NoArgsConstructor
public class UpdateCommoditySubNameIDTO extends BaseEnterpriseUserIDTO {
	@ApiModelProperty(position = 10, required = true, value = "商品ID")
	private Long commodityId;
	@ApiModelProperty(position = 11, required = true, value = "商品副标题")
    private String commoditySubName;
    
    public UpdateCommoditySubNameIDTO(Long commodityId, String commoditySubName) {
    	this.commodityId = commodityId;
    	this.commoditySubName = commoditySubName;
    }
}
