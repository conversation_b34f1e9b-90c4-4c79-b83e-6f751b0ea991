package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.xda.product.dto.BaseEnterpriseUserIDTO;

/**
 * 更新  商品文描-标签
 *
 * <AUTHOR>
 *
 * @date 2019年12月26日
 */
@Data
@NoArgsConstructor
public class UpdateCommodityTagIDTO extends BaseEnterpriseUserIDTO {
	@ApiModelProperty(position = 10, required = true, value = "商品ID")
	private Long commodityId;
	@ApiModelProperty(position = 11, required = true, value = "标签ID")
    private Long tagId;
    
    public UpdateCommodityTagIDTO(Long commodityId, Long tagId) {
    	this.commodityId = commodityId;
    	this.tagId = tagId;
    }
}
