package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;

import com.pinshang.qingyun.xda.product.dto.BaseEnterpriseUserIDTO;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新  商品文描-前台品类
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@NoArgsConstructor
public class UpdateCommodityXdaCategoryIDTO extends BaseEnterpriseUserIDTO {
	@ApiModelProperty(position = 10, required = true, value = "商品ID")
	private Long commodityId;
	@ApiModelProperty(position = 11, required = true, value = "前台二级品类ID")
	private Long xdaSecondCategoryId;
    
    public UpdateCommodityXdaCategoryIDTO(Long commodityId, Long xdaSecondCategoryId) {
    	this.commodityId = commodityId;
    	this.xdaSecondCategoryId = xdaSecondCategoryId;
    }
}
