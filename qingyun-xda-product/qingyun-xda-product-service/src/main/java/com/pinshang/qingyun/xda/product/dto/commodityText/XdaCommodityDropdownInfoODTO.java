package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 鲜达商品下拉信息
 *
 * <AUTHOR>
 *
 * @date 2020年3月9日
 */
@Data
public class XdaCommodityDropdownInfoODTO {
	@ApiModelProperty(position = 10, required = true, value = "商品ID")
	private String commodityId;
	@ApiModelProperty(position = 11, required = true, value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(position = 12, required = true, value = "商品名称")
	private String commodityName;
	@ApiModelProperty(position = 13, required = true, value = "商品规格")
	private String commoditySpec;
	@ApiModelProperty(position = 14, required = true, value = "商品前台品名")
	private String commodityAppName;
}
