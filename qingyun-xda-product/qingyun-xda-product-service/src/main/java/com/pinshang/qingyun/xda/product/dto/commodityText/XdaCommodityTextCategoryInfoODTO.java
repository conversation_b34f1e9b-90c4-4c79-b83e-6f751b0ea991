package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 鲜达-商品文描-前台品类信息
 */
@Data
@NoArgsConstructor
public class XdaCommodityTextCategoryInfoODTO {
	@ApiModelProperty(position = 11, required = true, value = "前台一级品类ID")
	private Long xdaFirstCategoryId;
	@ApiModelProperty(position = 11, required = true, value = "前台一级品类名称")
    private String xdaFirstCategoryName;	
	@ApiModelProperty(position = 12, required = true, value = "前台二级品类ID")
	private Long xdaSecondCategoryId;
	@ApiModelProperty(position = 12, required = true, value = "前台二级品类名称")
	private String xdaSecondCategoryName;
}
