package com.pinshang.qingyun.xda.product.dto.commodityText;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 鲜达价格体系-商品文描信息
 */
@Data
@NoArgsConstructor
public class XdaPriceStructureCommodityTextInfoODTO {
	@ApiModelProperty(position = 10, required = true, value = "商品ID")
	private Long commodityId;
	@ApiModelProperty(position = 11, required = true, value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(position = 12, required = true, value = "前台品名")
	private String commodityAppName;
	@ApiModelProperty(position = 12, required = true, value = "外卖规格")
    private String commoditySubName;
	@ApiModelProperty(position = 13, required = true, value = "前台一级品类")
    private String xdaFirstCategoryName;
	@ApiModelProperty(position = 13, required = true, value = "前台二级品类")
	private String xdaSecondCategoryName;
	@ApiModelProperty(position = 14, required = true, value = "标签名称")
	private String tagName;
	@ApiModelProperty(position = 14, required = true, value = "排序")
	private Integer sortNum;
	@ApiModelProperty(position = 17, required = true, value = "是否显示保质期： 1-是 0-否")
	private Integer qualityStatus;
}
