package com.pinshang.qingyun.xda.product.dto.deliveryDate;

public enum LogOperateTypeEnum {
    SINGLE_CHANGE("修改",0),
    MULTI_CHANGE("批量设置",1),
    ;

    private String name;
    private int code;

    LogOperateTypeEnum(String name, int code) {
        this.name = name;
        this.code = code;
    }

    public static String getName(int code) {
        for (LogOperateTypeEnum bot : LogOperateTypeEnum.values()) {
            if (code == bot.getCode()) {
                return bot.name;
            }
        }
        return null;
    }

    public static Integer getCode(String name) {
        for (LogOperateTypeEnum bot : LogOperateTypeEnum.values()) {
            if (name == bot.getName()) {
                return bot.code;
            }
        }
        return null;
    }


    public String getName() {
        return name;
    }

    public int getCode() {
        return code;
    }
}
