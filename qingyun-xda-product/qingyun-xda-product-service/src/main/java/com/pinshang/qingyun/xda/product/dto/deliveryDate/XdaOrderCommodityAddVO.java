package com.pinshang.qingyun.xda.product.dto.deliveryDate;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class XdaOrderCommodityAddVO {

   @ApiModelProperty(value = "商品ID",required = false)
   private Long orderCommodityId;
   @ApiModelProperty(value = "商品名称",required = false)
   private String commodityName;
   @ApiModelProperty(value = "商品编号",required = false)
   private String commodityCode;
   @ApiModelProperty(value = "Code",required = false)
   private String commodityDateCode;
   @ApiModelProperty(value = "Value",required = false)
   private String commodityDateValue;
   @ApiModelProperty(position = 10,value = "app类型:1-鲜达,2-批发")
   private Integer appType;
}
