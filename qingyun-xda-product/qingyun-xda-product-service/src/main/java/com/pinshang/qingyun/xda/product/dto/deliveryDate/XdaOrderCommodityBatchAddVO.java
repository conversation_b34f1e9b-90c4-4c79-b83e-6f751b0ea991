package com.pinshang.qingyun.xda.product.dto.deliveryDate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
public class XdaOrderCommodityBatchAddVO {

    @ApiModelProperty(value = "商品ID列表",required = false)
    List<Long> orderCommodityIds;
    @ApiModelProperty(value = "商品名",required = false)
    String commodityName;
    @ApiModelProperty(value = "商品名",required = false)
    String commodityCode;
    String commodityDateCode;
    String commodityDateValue;
    @ApiModelProperty(position = 10,value = "app类型:1-鲜达,2-批发")
    private Integer appType;
}
