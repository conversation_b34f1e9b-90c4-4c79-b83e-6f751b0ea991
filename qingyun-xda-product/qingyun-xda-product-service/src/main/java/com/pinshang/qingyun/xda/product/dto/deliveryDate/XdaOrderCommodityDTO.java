package com.pinshang.qingyun.xda.product.dto.deliveryDate;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class XdaOrderCommodityDTO {
    @ApiModelProperty(value = "ID",required = false)
    private Long id;
    @ApiModelProperty(value = "修改人ID",required = false)
    private Long updateId;
    @ApiModelProperty(value = "商品编号",required = false)
    private String commodityCode;
    @ApiModelProperty(value = "创建时间",required = false)
    private java.util.Date createTime;
    @ApiModelProperty(value = "修改人",required = false)
    private String updateName;
    @ApiModelProperty(value = "spec",required = false)
    private String spec;
    @ApiModelProperty(value = "修改时间",required = false)
    private java.util.Date updateTime;
    @ApiModelProperty(value = "商品ID",required = false)
    private String commodityId;
    @ApiModelProperty(value = "创建人",required = false)
    private Long createId;
    @ApiModelProperty(value = "举例：【2-8】",required = false)
    private String deliveryDateRangeCode;
    @ApiModelProperty(value = "举例：【T+2~T+8】",required = false)
    private String deliveryDateRangeValue;
    @ApiModelProperty(value = "商品名称",required = false)
    private String commodityName;
    @ApiModelProperty(value = "创建人",required = false)
    private String createName;

}
