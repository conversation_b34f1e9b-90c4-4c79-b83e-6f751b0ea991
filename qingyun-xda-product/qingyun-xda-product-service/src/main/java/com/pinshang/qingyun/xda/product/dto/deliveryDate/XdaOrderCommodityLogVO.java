package com.pinshang.qingyun.xda.product.dto.deliveryDate;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class XdaOrderCommodityLogVO {
    @ApiModelProperty(value = "页码",required = true)
    private Integer pageNo;
    @ApiModelProperty(value = "每页长度",required = true)
    private Integer pageSize;
    @ApiModelProperty(value = "操作类型",required = true)
    private String operateType;
    @ApiModelProperty(value = "商品ID",required = true)
    private String commodityId;
    @ApiModelProperty(position = 10,value = "app类型:1-鲜达,2-批发")
    private Integer appType;
}
