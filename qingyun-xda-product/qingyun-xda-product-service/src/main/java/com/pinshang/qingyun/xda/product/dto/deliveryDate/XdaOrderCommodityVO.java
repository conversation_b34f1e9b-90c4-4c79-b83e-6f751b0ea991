package com.pinshang.qingyun.xda.product.dto.deliveryDate;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class XdaOrderCommodityVO {

    @ApiModelProperty(value = "页码",required = true)
    private Integer pageNo;
    @ApiModelProperty(value = "每页长度",required = true)
    private Integer pageSize;
    private Long id;//主键
    @ApiModelProperty(value = "商品编码",required = false)
    private String commodityCode;
    @ApiModelProperty(value = "创建时间",required = false)
    private java.util.Date createTime;
    @ApiModelProperty(value = "创建人",required = false)
    private Long createId;
    @ApiModelProperty(value = "2-8",required = false)
    private String deliveryDateRangeCode;
    @ApiModelProperty(value = "T+2~T+8",required = false)
    private String deliveryDateRangeValue;
    @ApiModelProperty(value = "操作类型",required = false)
    private String opType;
    @ApiModelProperty(value = "是否可变价",required = false)
    private Integer changePrice;
    @ApiModelProperty(value = "规格",required = false)
    private String spec;
    @ApiModelProperty(value = "商品名称",required = false)
    private String commodityName;
    private String createName;
    private Long supplierId;
    private String prizeModelId;
    private String categoryId;
    private List<Long> commodityIds;
    private String setDate;
    @ApiModelProperty(position = 10,value = "app类型:1-鲜达,2-批发")
    private Integer appType;
}
