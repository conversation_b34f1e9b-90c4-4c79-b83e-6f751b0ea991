package com.pinshang.qingyun.xda.product.dto.front;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class XdaCommodityAppODTO {
    @ApiModelProperty(value = "商品ID",position = 1)
    private Long commodityId;
    @ApiModelProperty(value ="商品ID字符串",position = 1)
    private String commodityIdStr;
    @ApiModelProperty(value = "商品编码",position = 2)
    private String commodityCode;
    @ApiModelProperty(value ="商品前台名称",position = 3)
    private String commodityName;
    @ApiModelProperty(value ="副标题",position = 4)
    private String commoditySubName;
    @ApiModelProperty(value ="规格",position = 5)
    private String commoditySpec;
    @ApiModelProperty(value ="计量单位",position = 6)
    private String commodityUnitName;
    @ApiModelProperty(value ="箱规",position = 7)
    private BigDecimal salesBoxCapacity;
    @ApiModelProperty(value ="是否速冻：0-否、1-是",position = 8)
    private Integer isQuickFreeze;
    @ApiModelProperty(value = "净含量",position = 8)
    private String commodityWeight;
    @ApiModelProperty(value = "贮存条件",position = 8)
    private String storageCondition;
    @ApiModelProperty(value = "保质期天数",position = 8)
    private Integer qualityDays;
    @ApiModelProperty("是否显示保质期： 1-是 0-否")
    private Integer qualityStatus;
    @ApiModelProperty(value = "是否称重：0-否、1-是",hidden = true)
    private Integer isWeight;
    @ApiModelProperty(value = "包装规格",hidden = true)
    private BigDecimal commodityPackageSpec;
    @ApiModelProperty(value ="APP上架状态：0-上架，1-下架；购物车使用",hidden = true)
    private Integer appStatus;
    //默认图片： imageXdServer+imageUrl+defaultImageSize(入参)
    @ApiModelProperty(value ="默认图片URL,拼接后的url",position = 9)
    private String imageUrl;

    @ApiModelProperty(value ="商品单价，取客户价格方案",position = 10)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;

    @ApiModelProperty(value ="前台一级品类ID",position = 11)
    private Long xdaFirstCategoryId;
    @ApiModelProperty(value ="前台二级品类ID",position = 12)
    private Long xdaSecondCategoryId;
    @ApiModelProperty(value ="商品分类页排序号",hidden = true)
    private Integer sortNum;

    //自定义标签
    @ApiModelProperty(value ="自定义标签列表",position = 13)
    private List<CommodityTextTagInfoODTO> tagList;

    //特价
    @ApiModelProperty(value ="是否有特价：0=无特价，1=普通特价",position = 14)
    private Integer isSpecialPrice;
    @ApiModelProperty(value ="原始特价，产品特价方案价格",position = 14)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal specialPrice;

    //促销
    @ApiModelProperty(value ="是否有促销/赠品：0=无，1=有",position = 15)
    private Integer isPromotion;
    @ApiModelProperty(value ="赠品方案详细内容",hidden = true)
    private List<XdaStorePromotionODTO> promotionList;


    //限量
    @ApiModelProperty(value ="是否有限量：0=无，1=有",position = 16)
    private Integer isLimit;
    @ApiModelProperty(value ="商品限量值",position = 16)
    private BigDecimal limitNumber;
    @ApiModelProperty(value ="库存限量开始时间",position = 16)
    private Date limitStartTime;

    @ApiModelProperty(value ="库存限量结束时间",position = 16)
    private Date limitEndTime;
    //是否可订货
    @ApiModelProperty(value ="是否可订货",position = 17)
    private Boolean isCanOrder;
    @ApiModelProperty(value ="送货日期范围包含的详细信息",hidden = true)
    private XdaCommodityDeliveryTimeODTO deliveryTimeODTO;
    @ApiModelProperty(value ="最早可订货时间,订单使用",hidden = true)
    private Date beginDeliveryTime;
    @ApiModelProperty(value ="最晚可订货时间,订单使用",hidden = true)
    private Date endDeliveryTime;

    @ApiModelProperty(value = "已加入购物车数量",position = 19)
    private BigDecimal shoppingCartQuantity;

    //系列品
    @ApiModelProperty(value ="是否系列品：0=不是，1=是",position = 20)
    private Integer isSerial;
    @ApiModelProperty(value ="系列品主品ID",hidden = true)
    private Long serialCommodityId;

    @ApiModelProperty(value ="7天销量",hidden = true)
    private BigDecimal saleQuantity;
    @ApiModelProperty(value ="排序价格",hidden = true)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal sortPrice;

    /**
     * 商品类型：1-普通商品，2-组合商品
     */
    @ApiModelProperty(value ="商品类型",hidden = true)
    private Integer productType;
    @ApiModelProperty(value ="后台三级品类ID",position = 12)
    private Long commodityThirdId;



    public String getCommodityIdStr() {
        return this.commodityId==null?"":String.valueOf(this.commodityId);
    }

    public Integer getIsSpecialPrice() {
        if(isSpecialPrice==null){
            return specialPrice!=null && specialPrice.compareTo(BigDecimal.ZERO)>0 ? 1 : 0;
        }
        return this.isSpecialPrice;
    }

    public Integer getIsPromotion() {
        return isPromotion==null?0:isPromotion;
    }

    public Integer getIsLimit() {
        return isLimit==null?0:isLimit;
    }

    public Integer getIsSerial() {
        return isSerial==null?0:isSerial;
    }

    public BigDecimal getSaleQuantity() {
        return saleQuantity==null?BigDecimal.ZERO:saleQuantity;
    }

    public BigDecimal getSortPrice() {
        return specialPrice!=null && specialPrice.compareTo(commodityPrice)<0? specialPrice: commodityPrice;
    }

    public Integer getSortNum() {
        return sortNum==null?Integer.MAX_VALUE:sortNum;
    }

    public Boolean getIsCanOrder() {
        if(this.getAppStatus()!=null && this.getAppStatus()==1){
            return false;
        }
        return isCanOrder==null?false:isCanOrder;
    }

    public Long getSerialCommodityId() {
        return serialCommodityId==null?0:serialCommodityId;
    }
}
