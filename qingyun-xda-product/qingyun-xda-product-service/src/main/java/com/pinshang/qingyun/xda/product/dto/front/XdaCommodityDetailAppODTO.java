package com.pinshang.qingyun.xda.product.dto.front;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@Data
public class XdaCommodityDetailAppODTO extends XdaCommodityAppODTO {
    @ApiModelProperty(value = "收藏状态：0=未收藏，1=已收藏",position = 30)
    private Integer isCollect;
    @ApiModelProperty(value = "图片URL集合",position = 30)
    private List<String> imageUrlList;
    @ApiModelProperty(value = "长图URL集合",position = 31)
    private List<XdaCommodityLongPicODTO> longPicList;

    @ApiModelProperty(value = "商品详情：促销详细信息",position = 32)
    private List<XdaStorePromotionODTO> promotionList;

    //最快送达提示
    @ApiModelProperty(value = "商品详情：最快送达日期提示",position = 33)
    private List<String> distributionTipList;

    //系列品
    @ApiModelProperty(value = "系列品规格列表，当isSerial=1时展示",position = 34)
    private List<XdaSerialCommodityDetailODTO> serialCommodityDetailList;

    //前台品名、自定义标签、副标题、商品规格（净含量、贮存条件、保质期）、商品图片、长图、
    public static XdaCommodityDetailAppODTO serialConvert(XdaCommodityDetailAppODTO detailAppODTO, XdaCommodityAppODTO appODTO){
        detailAppODTO.setCommodityName(appODTO.getCommodityName());
        detailAppODTO.setTagList(appODTO.getTagList());
        detailAppODTO.setCommoditySubName(appODTO.getCommoditySubName());
        detailAppODTO.setCommoditySpec(appODTO.getCommoditySpec());
        detailAppODTO.setCommodityWeight(appODTO.getCommodityWeight());
        detailAppODTO.setStorageCondition(appODTO.getStorageCondition());
        detailAppODTO.setQualityDays(appODTO.getQualityDays());
        return detailAppODTO;
    }

    public List<XdaStorePromotionODTO> getPromotionList() {
        if(CollectionUtils.isNotEmpty(promotionList)){
           for(int i=0;i<promotionList.size();i++){
                promotionList.get(i).setGiftModelName("活动"+(i+1)+"：");
           }
        }
        return promotionList;
    }
}
