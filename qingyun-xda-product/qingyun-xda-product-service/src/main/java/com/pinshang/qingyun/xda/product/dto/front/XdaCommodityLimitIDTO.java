package com.pinshang.qingyun.xda.product.dto.front;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class XdaCommodityLimitIDTO {
    @ApiModelProperty(value = "送货日期",required = true,position = 1)
    private Date orderTime;
    @ApiModelProperty(value = "商品ID集合",position = 3,example = "[2580787591198700,468788174651305600,6772315779888100]")
    private List<Long> commodityIdList;
}
