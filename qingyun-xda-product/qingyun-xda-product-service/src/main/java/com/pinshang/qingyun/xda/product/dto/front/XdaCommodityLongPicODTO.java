package com.pinshang.qingyun.xda.product.dto.front;

import com.pinshang.qingyun.common.dto.ImageLibraryODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class XdaCommodityLongPicODTO {

    @ApiModelProperty("长图url")
    private String longPicUrl;

    @ApiModelProperty("长图宽度")
    private Integer longPicWidth;

    @ApiModelProperty("长图高度")
    private Integer longPicHeight;

    public static XdaCommodityLongPicODTO convert(ImageLibraryODTO imageLibraryODTO){
        XdaCommodityLongPicODTO longPicODTO = new XdaCommodityLongPicODTO();
        longPicODTO.setLongPicUrl(imageLibraryODTO.getImgVisitUrl());
        longPicODTO.setLongPicWidth(imageLibraryODTO.getRealWidth());
        longPicODTO.setLongPicHeight(imageLibraryODTO.getRealHeight());
        return longPicODTO;
    }
}