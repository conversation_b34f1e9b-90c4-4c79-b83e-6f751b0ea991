package com.pinshang.qingyun.xda.product.dto.front;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class XdaSerialCommodityDetailODTO {
    @ApiModelProperty(value = "系列品商品ID",position = 1)
    private Long commodityId;
    @ApiModelProperty(value = "是否当前商品，详情页高亮显示：0=否，1=是",position = 2)
    private Integer isCurrentCommodity;
    @ApiModelProperty(value = "系列品商品规格",position = 3)
    private String commoditySpec;
    @ApiModelProperty(value = "系列品商品价格",position = 3)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;
    @ApiModelProperty(value = "系列品商品单位",position = 4)
    private String commodityUnitName;
    @ApiModelProperty(value = "系列品商品箱规",position = 4)
    private BigDecimal salesBoxCapacity;

    @ApiModelProperty(value ="系列品自定义标签",position = 5)
    private List<CommodityTextTagInfoODTO> tagList;
    @ApiModelProperty(value ="是否有特价：0=无特价，1=普通特价",position = 5)
    private Integer isSpecialPrice;
    @ApiModelProperty(value ="特价",position = 5)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal specialPrice;
    @ApiModelProperty(value ="系列品是否有促销：0=无，1=有",position = 6)
    private Integer isPromotion;
    @ApiModelProperty(value ="系列品是否有限量：0=无，1=有",position = 7)
    private Integer isLimit;
    @ApiModelProperty(value ="系列品限量值",position = 7)
    private BigDecimal limitNumber;
    @ApiModelProperty(value ="是否可订货",position = 8)
    private Boolean isCanOrder;
    @ApiModelProperty(value = "已加入购物车数量",position = 9)
    private BigDecimal shoppingCartQuantity;
    @JsonIgnore
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal sortPrice;


     public static XdaSerialCommodityDetailODTO convert(XdaCommodityAppODTO appODTO){
         XdaSerialCommodityDetailODTO serialCommodityODTO = BeanCloneUtils.copyTo(appODTO, XdaSerialCommodityDetailODTO.class);
         BigDecimal specialPrice = appODTO.getSpecialPrice();
         if(specialPrice!=null && specialPrice.compareTo(BigDecimal.ZERO)>0 && specialPrice.compareTo(appODTO.getCommodityPrice())<0){
             serialCommodityODTO.setIsSpecialPrice(1);
             serialCommodityODTO.setSpecialPrice(appODTO.getSpecialPrice());
         }
         serialCommodityODTO.setTagList(appODTO.getTagList());
         return serialCommodityODTO;
     }

    public BigDecimal getSortPrice() {
        return specialPrice==null?commodityPrice:specialPrice;
    }
}
