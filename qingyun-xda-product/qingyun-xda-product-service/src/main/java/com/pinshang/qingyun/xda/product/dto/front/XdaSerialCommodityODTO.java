package com.pinshang.qingyun.xda.product.dto.front;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdaSerialCommodityODTO {
    @ApiModelProperty("系列品主品ID")
    private Long serialCommodityId;

    @ApiModelProperty("商品ID")
    private Long commodityId;

    @ApiModelProperty(value="是否系列品主品:0=否，1=是",hidden = true)
    private Integer isMain;

    public Integer getIsMain() {
        if(serialCommodityId!=null && commodityId!=null && serialCommodityId.equals(commodityId)){
            return 1;
        }
        return 0;
    }

}
