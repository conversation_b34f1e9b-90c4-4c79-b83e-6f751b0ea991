package com.pinshang.qingyun.xda.product.dto.front.categoryFront;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CategoryCommodityFieldODTO implements Serializable {

    private static final long serialVersionUID = -974547808115765144L;

    /**
     * t_commodity
     */
    private Long commodityId;

    private String commoditySpec;

    private String commodityCode;

    private BigDecimal salesBoxCapacity;

    private Integer isQuickFreeze;

    private Integer isWeight;

    private BigDecimal commodityPackageSpec;

    private String commodityWeight;

    private String storageCondition;

    private Integer qualityDays;

    /**
     * t_dictionary
     */
    private String commodityUnitName;

    /**
     * t_xda_commodity_text_pic
     */
    private String imageUrl;

    /**
     * t_xda_commodity_sale_multi_day_statistics
     */
    private BigDecimal saleQuantity;

    /**
     * t_xda_serial_commodity
     */
    private Long serialCommodityId;

    private Date updateTime;

}
