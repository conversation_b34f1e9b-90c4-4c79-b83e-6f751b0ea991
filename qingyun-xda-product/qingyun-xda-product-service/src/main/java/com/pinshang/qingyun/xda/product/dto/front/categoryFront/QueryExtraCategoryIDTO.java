package com.pinshang.qingyun.xda.product.dto.front.categoryFront;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class QueryExtraCategoryIDTO implements Serializable {

    private static final long serialVersionUID = 8541921751535990597L;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderTime;

    private Long storeId;

    private List<Long> firstCategoryIdList;
}
