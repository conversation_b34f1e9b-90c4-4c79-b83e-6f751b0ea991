package com.pinshang.qingyun.xda.product.dto.front.categoryFront;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdaCategoryAppIDTO extends XdaCategoryBaseIDTO {

    @ApiModelProperty(value = "一级分类ID",position = 2)
    private Long xdaFirstCategoryId;
    @ApiModelProperty(value = "二级分类ID",position = 3)
    private Long xdaSecondCategoryId;

    public static XdaCategoryAppIDTO init(Date orderTime, Long storeId, Long firstId, Long secondId){
        XdaCategoryAppIDTO idto = new XdaCategoryAppIDTO();
        idto.setOrderTime(orderTime);
        idto.setStoreId(storeId);
        idto.setXdaFirstCategoryId(firstId);
        idto.setXdaSecondCategoryId(secondId);
        return idto;
    }
}
