package com.pinshang.qingyun.xda.product.dto.front.categoryFront;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 鲜达APP 分类
 */
@Data
public class XdaCategoryBaseODTO {

    @ApiModelProperty(value = "分类ID",position = 1)
    private Long xdaCategoryId;
    @ApiModelProperty(value = "分类ID字符串",position = 1)
    private String xdaCategoryIdStr;
    @ApiModelProperty(value = "分类名称",position = 2)
    private String xdaCategoryName;
    @ApiModelProperty(value = "分类排序",hidden = true)
    private Integer xdaCategorySort;
    @ApiModelProperty(value = "父节点ID",hidden = true)
    private Long parentId;

    public String getXdaCategoryIdStr() {
        return this.xdaCategoryId==null?"":String.valueOf(this.xdaCategoryId);
    }

    public static XdaCategoryBaseODTO buildRecommendLabel(Long firstCategoryId) {
        XdaCategoryBaseODTO baseODTO = new XdaCategoryBaseODTO();
        baseODTO.setXdaCategoryId(0L);
        baseODTO.setXdaCategoryName("精选");
        baseODTO.setXdaCategorySort(0);
        baseODTO.setParentId(firstCategoryId);
        return baseODTO;
    }
}
