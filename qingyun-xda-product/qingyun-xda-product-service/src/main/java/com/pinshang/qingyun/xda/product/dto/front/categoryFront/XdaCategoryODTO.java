package com.pinshang.qingyun.xda.product.dto.front.categoryFront;

import com.pinshang.qingyun.xda.product.constant.XdaConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 鲜达APP 分类
 */
@Data
public class XdaCategoryODTO extends XdaCategoryBaseODTO {

    @ApiModelProperty(value = "一级分类下的二级分类列表",position = 4)
    private List<XdaCategoryBaseODTO> secondCategoryList;

    public static XdaCategoryODTO buildDefaultLabel(){
        XdaCategoryODTO appODTO = new XdaCategoryODTO();
        appODTO.setXdaCategoryId(XdaConstant.FIRST_CATEGORY_OFTEN_BUY);
        appODTO.setXdaCategoryName("常购清单");
        appODTO.setXdaCategorySort(0);

        List<XdaCategoryBaseODTO> secondList = new ArrayList<>();
        XdaCategoryBaseODTO second1 = new XdaCategoryBaseODTO();
        second1.setXdaCategoryId(XdaConstant.SECOND_CATEGORY_MY_OFTEN_BUY);
        second1.setXdaCategoryName("我的常购");
        second1.setXdaCategorySort(1);
        second1.setParentId(XdaConstant.FIRST_CATEGORY_OFTEN_BUY);
        secondList.add(second1);
        XdaCategoryBaseODTO second2 = new XdaCategoryBaseODTO();
        second2.setXdaCategoryId(XdaConstant.SECOND_CATEGORY_MY_COLLECT);
        second2.setXdaCategoryName("我的收藏");
        second2.setXdaCategorySort(2);
        second2.setParentId(XdaConstant.FIRST_CATEGORY_OFTEN_BUY);
        secondList.add(second2);
        appODTO.setSecondCategoryList(secondList);
        return appODTO;
    }
}
