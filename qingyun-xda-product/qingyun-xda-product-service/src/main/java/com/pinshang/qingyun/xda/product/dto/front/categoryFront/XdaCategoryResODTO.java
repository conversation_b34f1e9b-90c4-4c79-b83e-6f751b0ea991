package com.pinshang.qingyun.xda.product.dto.front.categoryFront;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 鲜达APP 分类
 */
@Data
@NoArgsConstructor
public class XdaCategoryResODTO {
    @ApiModelProperty(value = "分类列表",position = 1)
    private List<XdaCategoryODTO> categoryList;

    @ApiModelProperty(value = "第一个一级分类的第一个二级分类的商品信息，默认展示",position = 2)
    private XdaCategoryCommodityResODTO commodityODTO;

    public XdaCategoryResODTO(List<XdaCategoryODTO> categoryList, XdaCategoryCommodityResODTO commodityODTO) {
        this.categoryList = categoryList;
        this.commodityODTO = commodityODTO;
    }
}
