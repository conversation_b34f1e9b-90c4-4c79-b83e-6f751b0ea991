package com.pinshang.qingyun.xda.product.dto.front.categoryFront;

import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdaSearchAppIDTO extends XdaCategoryBaseIDTO {

    /**
     * 搜索匹配：前台品名+前台二级分类
     */
    @ApiModelProperty(value ="商品搜索关键字",position = 2)
    private String keyword;

    @ApiModelProperty(value = "当前页号, 默认第 1 页",example = "1", position = 10)
    private int pageNo = 1;

    @ApiModelProperty(value = "每页显示记录数, 默认20条",example = "20",position = 10)
    private int pageSize = 10;

    @ApiModelProperty(value = "一级分类ID",hidden = true)
    private Long xdaFirstCategoryId;
    @ApiModelProperty(value = "二级分类ID",hidden = true)
    private Long xdaSecondCategoryId;
    @ApiModelProperty(value = "指定商品ID",hidden = true)
    private List<Long> commodityIdList;

    public static XdaSearchAppIDTO convert(XdaCategoryAppIDTO categoryAppIDTO){
        XdaSearchAppIDTO searchAppIDTO = BeanCloneUtils.copyTo(categoryAppIDTO, XdaSearchAppIDTO.class);
        return searchAppIDTO;
    }

}
