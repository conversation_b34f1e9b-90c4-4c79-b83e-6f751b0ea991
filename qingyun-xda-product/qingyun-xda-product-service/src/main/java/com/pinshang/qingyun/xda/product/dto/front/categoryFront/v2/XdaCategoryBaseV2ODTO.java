package com.pinshang.qingyun.xda.product.dto.front.categoryFront.v2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 鲜达APP 分类
 */
@Data
public class XdaCategoryBaseV2ODTO {

    @ApiModelProperty(value = "分类ID",position = 1)
    private Long xdaCategoryId;
    @ApiModelProperty(value = "分类ID字符串",position = 1)
    private String xdaCategoryIdStr;
    @ApiModelProperty(value = "分类名称",position = 2)
    private String xdaCategoryName;
    @ApiModelProperty(value = "分类排序",hidden = true)
    private Integer xdaCategorySort;
    @ApiModelProperty(value = "父节点ID",hidden = true)
    private Long parentId;

    /**特惠分类提示信息**/
    @ApiModelProperty(value = "特惠分类提示信息")
    private String thCategoryTips;
    @ApiModelProperty(value = "特惠分类规则详细信息")
    private String thCategoryTipsDetails;
    @ApiModelProperty("是否满足特惠")
    private Boolean isThInvalidate = Boolean.FALSE;


    public String getXdaCategoryIdStr() {
        return this.xdaCategoryId==null?"":String.valueOf(this.xdaCategoryId);
    }

    public static XdaCategoryBaseV2ODTO buildRecommendLabel(Long firstCategoryId) {
        XdaCategoryBaseV2ODTO baseODTO = new XdaCategoryBaseV2ODTO();
        baseODTO.setXdaCategoryId(0L);
        baseODTO.setXdaCategoryName("精选");
        baseODTO.setXdaCategorySort(0);
        baseODTO.setParentId(firstCategoryId);
        return baseODTO;
    }
}
