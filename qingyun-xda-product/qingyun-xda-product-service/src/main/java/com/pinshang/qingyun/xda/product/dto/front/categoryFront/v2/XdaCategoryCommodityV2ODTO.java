package com.pinshang.qingyun.xda.product.dto.front.categoryFront.v2;

import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaCommodityAppV2ODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaSerialCommodityDetailV2ODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class XdaCategoryCommodityV2ODTO extends XdaCommodityAppV2ODTO {
    @ApiModelProperty(value = "系列品主品规格拼接,如：1箱|1瓶|1筐；当isSerial=1时规格展示此值",position = 20)
    private String serialCommoditySpec;
    @ApiModelProperty(value = "系列品主品价格拼接,如：16.38-1062.39；当isSerial=1时价格展示此值",position = 20)
    private String serialCommodityPrice;
    @ApiModelProperty(value="系列品展开列表，当isSerial=1时选规格展开列表",position = 20)
    private List<XdaSerialCommodityDetailV2ODTO> serialCommodityDetailList;

    public static XdaCategoryCommodityV2ODTO convert(XdaCommodityAppV2ODTO commodityInfoAppODTO){
        XdaCategoryCommodityV2ODTO resultODTO = BeanCloneUtils.copyTo(commodityInfoAppODTO, XdaCategoryCommodityV2ODTO.class);
        return resultODTO;
    }
}
