package com.pinshang.qingyun.xda.product.dto.front.categoryFront.v2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 鲜达APP 分类
 */
@Data
@NoArgsConstructor
public class XdaCategoryResV2ODTO {
    @ApiModelProperty(value = "分类列表",position = 1)
    private List<XdaCategoryV2ODTO> categoryList;

    @ApiModelProperty(value = "第一个一级分类的第一个二级分类的商品信息，默认展示",position = 2)
    private XdaCategoryCommodityResV2ODTO commodityODTO;

    public XdaCategoryResV2ODTO(List<XdaCategoryV2ODTO> categoryList, XdaCategoryCommodityResV2ODTO commodityODTO) {
        this.categoryList = categoryList;
        this.commodityODTO = commodityODTO;
    }
}
