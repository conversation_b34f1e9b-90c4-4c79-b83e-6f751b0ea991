package com.pinshang.qingyun.xda.product.dto.front.categoryFront.v3;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdaCategoryCommodityResV3ODTO {
    @ApiModelProperty(value = "一级分类ID",position = 1)
    private Long xdaFirstCategoryId;
    @ApiModelProperty(value = "二级分类ID",position = 2)
    private Long xdaSecondCategoryId;
    @ApiModelProperty(value = "二级分类下的商品列表",position = 3)
    private List<XdaCategoryCommodityV3ODTO> commodityList;

}
