package com.pinshang.qingyun.xda.product.dto.front.categoryFront.v3;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityAppV3ODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaSerialCommodityDetailV3ODTO;

@Data
@NoArgsConstructor
public class XdaCategoryCommodityV3ODTO extends XdaCommodityAppV3ODTO {
    @ApiModelProperty(value = "系列品主品规格拼接,如：1箱|1瓶|1筐；当isSerial=1时规格展示此值",position = 20)
    private String serialCommoditySpec;
    @ApiModelProperty(value = "系列品主品价格拼接,如：16.38-1062.39；当isSerial=1时价格展示此值",position = 20)
    private String serialCommodityPrice;
    @ApiModelProperty(value="系列品展开列表，当isSerial=1时选规格展开列表",position = 20)
    private List<XdaSerialCommodityDetailV3ODTO> serialCommodityDetailList;

    public static XdaCategoryCommodityV3ODTO convert(XdaCommodityAppV3ODTO commodityInfoAppODTO){
        XdaCategoryCommodityV3ODTO resultODTO = BeanCloneUtils.copyTo(commodityInfoAppODTO, XdaCategoryCommodityV3ODTO.class);
        return resultODTO;
    }
    
}
