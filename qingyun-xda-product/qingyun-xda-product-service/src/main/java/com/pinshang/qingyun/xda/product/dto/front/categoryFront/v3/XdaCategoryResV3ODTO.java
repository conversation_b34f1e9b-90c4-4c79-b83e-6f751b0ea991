package com.pinshang.qingyun.xda.product.dto.front.categoryFront.v3;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 鲜达APP 分类
 */
@Data
@NoArgsConstructor
public class XdaCategoryResV3ODTO {
    @ApiModelProperty(value = "分类列表",position = 1)
    private List<XdaCategoryV3ODTO> categoryList;

    @ApiModelProperty(value = "第一个一级分类的第一个二级分类的商品信息，默认展示",position = 2)
    private XdaCategoryCommodityResV3ODTO commodityODTO;

    public XdaCategoryResV3ODTO(List<XdaCategoryV3ODTO> categoryList, XdaCategoryCommodityResV3ODTO commodityODTO) {
        this.categoryList = categoryList;
        this.commodityODTO = commodityODTO;
    }
    
}
