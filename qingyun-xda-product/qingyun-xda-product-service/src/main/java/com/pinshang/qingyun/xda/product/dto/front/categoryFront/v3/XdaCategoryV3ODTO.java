package com.pinshang.qingyun.xda.product.dto.front.categoryFront.v3;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.xda.product.constant.XdaConstant;

/**
 * 鲜达APP 分类
 */
@Data
@NoArgsConstructor
public class XdaCategoryV3ODTO extends XdaCategoryBaseV3ODTO {

    @ApiModelProperty(value = "一级分类下的二级分类列表",position = 4)
    private List<XdaCategoryBaseV3ODTO> secondCategoryList;

    public static XdaCategoryV3ODTO buildDefaultLabel(){
        XdaCategoryV3ODTO appODTO = new XdaCategoryV3ODTO();
        appODTO.setXdaCategoryId(XdaConstant.FIRST_CATEGORY_OFTEN_BUY);
        appODTO.setXdaCategoryName("常购清单");
        appODTO.setXdaCategorySort(0);

        List<XdaCategoryBaseV3ODTO> secondList = new ArrayList<>();
        XdaCategoryBaseV3ODTO second1 = new XdaCategoryBaseV3ODTO();
        second1.setXdaCategoryId(XdaConstant.SECOND_CATEGORY_MY_OFTEN_BUY);
        second1.setXdaCategoryName("我的常购");
        second1.setXdaCategorySort(1);
        second1.setParentId(XdaConstant.FIRST_CATEGORY_OFTEN_BUY);
        secondList.add(second1);
        XdaCategoryBaseV3ODTO second2 = new XdaCategoryBaseV3ODTO();
        second2.setXdaCategoryId(XdaConstant.SECOND_CATEGORY_MY_COLLECT);
        second2.setXdaCategoryName("我的收藏");
        second2.setXdaCategorySort(2);
        second2.setParentId(XdaConstant.FIRST_CATEGORY_OFTEN_BUY);
        secondList.add(second2);
        appODTO.setSecondCategoryList(secondList);
        return appODTO;
    }

    public static XdaCategoryV3ODTO buildSpecialLabel(){
        XdaCategoryV3ODTO appODTO = new XdaCategoryV3ODTO();
        appODTO.setXdaCategoryId(XdaConstant.FIRST_CATEGORY_SPECIAL);
        appODTO.setXdaCategoryName("促销商品");
        appODTO.setXdaCategorySort(0);

        List<XdaCategoryBaseV3ODTO> secondList = new ArrayList<>();
        XdaCategoryBaseV3ODTO second1 = new XdaCategoryBaseV3ODTO();
        second1.setXdaCategoryId(XdaConstant.SECOND_CATEGORY_SPECIAL);
        second1.setXdaCategoryName("促销商品");
        second1.setXdaCategorySort(1);
        second1.setParentId(XdaConstant.FIRST_CATEGORY_SPECIAL);
        secondList.add(second1);
        appODTO.setSecondCategoryList(secondList);
        return appODTO;
    }

    public static XdaCategoryV3ODTO buildThLabel(String thCategoryTips,Boolean isThInvalidate){
        XdaCategoryV3ODTO appODTO = new XdaCategoryV3ODTO();
        appODTO.setXdaCategoryId(XdaConstant.FIRST_CATEGORY_TH);
        appODTO.setXdaCategoryName("特惠商品");
        appODTO.setXdaCategorySort(0);

        List<XdaCategoryBaseV3ODTO> secondList = new ArrayList<>();
        XdaCategoryBaseV3ODTO second1 = new XdaCategoryBaseV3ODTO();
        second1.setXdaCategoryId(XdaConstant.SECOND_CATEGORY_TH);
        second1.setXdaCategoryName("特惠商品");
        second1.setXdaCategorySort(1);
        second1.setParentId(XdaConstant.FIRST_CATEGORY_TH);
        //特惠分类特殊字段
        appODTO.setThCategoryTips(thCategoryTips);
        appODTO.setIsThInvalidate(isThInvalidate);
        String thCategoryTipsDetails = "已加购物车的正常商品的实付总金额≥所选送货日期的订货目标，则客户可享受特惠商品，以特惠的价格限量加购指定商品；其中，\n" +
                "1.订货目标由销售部门设置，若当天未设置订货目标，则客户不能享受特惠商品加购，特惠商品分类不可见；\n" +
                "2.正常商品，指特惠商品之外的商品，特惠商品的订货金额不能参与订货目标的计算；\n" +
                "3.实付总金额，指特价、促销（比如满减活动）后实付总金额；\n" +
                "4.特惠商品不参与任何特价活动、促销活动的计算，不能与任何活动叠加优惠；\n" +
                "5.特惠商品不参与结算返利。";
        appODTO.setThCategoryTipsDetails(thCategoryTipsDetails);
        secondList.add(second1);
        appODTO.setSecondCategoryList(secondList);
        return appODTO;
    }
    
}
