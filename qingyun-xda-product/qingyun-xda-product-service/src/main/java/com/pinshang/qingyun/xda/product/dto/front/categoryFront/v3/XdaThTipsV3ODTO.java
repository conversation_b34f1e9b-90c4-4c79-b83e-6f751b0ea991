package com.pinshang.qingyun.xda.product.dto.front.categoryFront.v3;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2024/1/24 19:12
 */
@Data
@NoArgsConstructor
public class XdaThTipsV3ODTO {
    /**特惠分类提示信息**/
    @ApiModelProperty(value = "特惠分类提示信息")
    private String thCategoryTips;
    @ApiModelProperty(value = "特惠分类规则详细信息")
    private String thCategoryTipsDetails;
    @ApiModelProperty("是否满足特惠")
    private Boolean isThInvalidate = Boolean.FALSE;


    public static XdaThTipsV3ODTO buildSpecialLabel() {
        XdaThTipsV3ODTO xdaThTipsV3ODTO = new XdaThTipsV3ODTO();
        xdaThTipsV3ODTO.setThCategoryTips("");
        xdaThTipsV3ODTO.setThCategoryTipsDetails("");
        xdaThTipsV3ODTO.setIsThInvalidate(Boolean.FALSE);
        return xdaThTipsV3ODTO;
    }

    public static XdaThTipsV3ODTO buildSpecialLabelALL(String thCategoryTips,Boolean isThInvalidate) {
        XdaThTipsV3ODTO xdaThTipsV3ODTO = new XdaThTipsV3ODTO();
        xdaThTipsV3ODTO.setThCategoryTips(thCategoryTips);
        xdaThTipsV3ODTO.setIsThInvalidate(isThInvalidate);
        String thCategoryTipsDetails = "已加购物车的正常商品的实付总金额≥所选送货日期的订货目标，则客户可享受特惠商品，以特惠的价格限量加购指定商品；其中，\n" +
                "1.订货目标由销售部门设置，若当天未设置订货目标，则客户不能享受特惠商品加购，特惠商品分类不可见；\n" +
                "2.正常商品，指特惠商品之外的商品，特惠商品的订货金额不能参与订货目标的计算；\n" +
                "3.实付总金额，指特价、促销（比如满减活动）后实付总金额；\n" +
                "4.特惠商品不参与任何特价活动、促销活动的计算，不能与任何活动叠加优惠；\n" +
                "5.特惠商品不参与结算返利。";
        xdaThTipsV3ODTO.setThCategoryTipsDetails(thCategoryTipsDetails);
        xdaThTipsV3ODTO.setIsThInvalidate(isThInvalidate);
        return xdaThTipsV3ODTO;
    }
}
