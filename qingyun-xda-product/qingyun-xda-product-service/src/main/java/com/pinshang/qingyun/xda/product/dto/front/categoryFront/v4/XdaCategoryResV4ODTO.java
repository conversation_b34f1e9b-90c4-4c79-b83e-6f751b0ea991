package com.pinshang.qingyun.xda.product.dto.front.categoryFront.v4;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 鲜达APP 分类
 */
@Data
@NoArgsConstructor
public class XdaCategoryResV4ODTO {
    @ApiModelProperty(value = "分类列表",position = 1)
    private List<XdaCategoryV4ODTO> categoryList;

    @ApiModelProperty(value = "第一个一级分类的第一个二级分类的商品信息，默认展示",position = 2)
    private XdaCategoryCommodityResV4ODTO commodityODTO;

    public XdaCategoryResV4ODTO(List<XdaCategoryV4ODTO> categoryList, XdaCategoryCommodityResV4ODTO commodityODTO) {
        this.categoryList = categoryList;
        this.commodityODTO = commodityODTO;
    }
    
}
