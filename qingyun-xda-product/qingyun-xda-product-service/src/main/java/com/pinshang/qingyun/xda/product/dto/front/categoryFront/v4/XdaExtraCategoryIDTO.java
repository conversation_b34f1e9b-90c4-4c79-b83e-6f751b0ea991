package com.pinshang.qingyun.xda.product.dto.front.categoryFront.v4;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@ToString
public class XdaExtraCategoryIDTO implements Serializable {
    private static final long serialVersionUID = 3000474650306132511L;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date orderTime;

    private List<Long> firstCategoryIdList;

    private Long logisticsCenterId;
}
