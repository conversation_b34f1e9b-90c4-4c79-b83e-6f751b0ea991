package com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class XdaCommodityDetailAppV2IDTO {

    @ApiModelProperty(position = 1,required = true,value = "送货日期")
    private Date orderTime;
    @ApiModelProperty(position = 2,required = true,value = "商品ID",example = "468788174651305600")
    private Long commodityId;
    @ApiModelProperty(hidden = true,value = "客户ID")
    private Long storeId;
    @ApiModelProperty(value ="是否特惠：0=否，1=是",position = 17)
    private Integer isThPrice;
}
