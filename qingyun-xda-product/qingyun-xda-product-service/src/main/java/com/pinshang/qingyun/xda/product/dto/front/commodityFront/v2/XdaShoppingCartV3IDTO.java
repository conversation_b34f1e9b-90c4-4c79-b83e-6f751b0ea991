package com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class XdaShoppingCartV3IDTO {
    @ApiModelProperty(value = "送货日期")
    private Date orderTime;

    @ApiModelProperty(value = "客户ID")
    private Long storeId;

    @ApiModelProperty(value = "商品集合")
    private List<Long> commodityIdList;

    @ApiModelProperty(value = "商品ID")
    private Long commodityId;

    @ApiModelProperty(value = "默认1、普通商品 2、特惠商品")
    private Integer commodityType;

    @ApiModelProperty(value = "1、购物车判断商品状态 2、订单 不判断商品状态")
    private Boolean isShoppingCart = Boolean.TRUE;

    private Boolean isPfsStore;
}
