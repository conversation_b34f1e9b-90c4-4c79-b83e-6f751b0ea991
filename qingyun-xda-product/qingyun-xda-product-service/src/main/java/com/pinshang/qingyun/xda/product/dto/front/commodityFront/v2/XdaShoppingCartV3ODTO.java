package com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class XdaShoppingCartV3ODTO {
    @ApiModelProperty(value = "商品ID")
    private Long commodityId;

    @ApiModelProperty(value = "商品前台名称")
    private String commodityName;

    @ApiModelProperty(value ="默认图片URL,拼接后的url")
    private String imageUrl;

    @ApiModelProperty(value ="前台二级品类ID")
    private Long xdaSecondCategoryId;

    @ApiModelProperty(value ="箱规")
    private BigDecimal salesBoxCapacity;

    @ApiModelProperty(value = "是否可订货")
    private Boolean isCanOrder;

    @ApiModelProperty(value = "是否有限量：0=无，1=有")
    private Integer isLimit;

    @ApiModelProperty(value = "商品限量值")
    private BigDecimal limitNumber;

    @ApiModelProperty(value = "是否特惠：0=无，1=有")
    private Integer isThPrice;

    @ApiModelProperty(value = "特惠商品限量值")
    private BigDecimal thLimitNumber;

    @ApiModelProperty(value ="规格")
    private String commoditySpec;

    @ApiModelProperty(value ="计量单位")
    private String commodityUnitName;

    @ApiModelProperty(value ="商品单价，取客户价格方案")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @Deprecated
    private BigDecimal commodityPrice;

    //特价
    @ApiModelProperty(value ="是否有特价：0=无特价，1=普通特价")
    @Deprecated
    private Integer isSpecialPrice;

    @ApiModelProperty(value ="原始特价，产品特价方案价格")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    @Deprecated
    private BigDecimal specialPrice;

    @ApiModelProperty(value ="库存限量开始时间")
    private Date limitStartTime;

    @ApiModelProperty(value ="库存限量结束时间")
    private Date limitEndTime;

    //凑整
    @ApiModelProperty(value ="是否凑整：0-否、1-是")
    private Integer isFreezeRounding;

    @ApiModelProperty(value ="凑整倍数")
    private Integer isFreezeRoundingMultiple;

    @ApiModelProperty(value ="特惠价格")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal thPrice;

    @ApiModelProperty(value ="特惠满价格")
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal thFullPrice;

    @ApiModelProperty(value ="最早可订货时间,订单使用")
    private Date beginDeliveryTime;

    @ApiModelProperty(value ="最晚可订货时间,订单使用")
    private Date endDeliveryTime;

    /**
     * 商品类型：1-普通商品，2-组合商品
     */
    @ApiModelProperty(value ="商品类型",hidden = true)
    private Integer productType;

    public static XdaShoppingCartV3ODTO forInsert(Long commodityId,Long xdaSecondCategoryId){
        XdaShoppingCartV3ODTO xdaShoppingCartV3ODTO = new XdaShoppingCartV3ODTO();
        xdaShoppingCartV3ODTO.setCommodityId(commodityId);
        xdaShoppingCartV3ODTO.setXdaSecondCategoryId(xdaSecondCategoryId);
        return xdaShoppingCartV3ODTO;
    }
}
