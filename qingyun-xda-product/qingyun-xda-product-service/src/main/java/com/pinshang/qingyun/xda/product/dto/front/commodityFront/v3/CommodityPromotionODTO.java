package com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3;

import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.enums.SalesPromotionStatusEnums;
import com.pinshang.qingyun.marketing.dto.app.CommodityDetailODTO;

/**
 * 促销信息
 */
@Data
@NoArgsConstructor
public class CommodityPromotionODTO {
	@ApiModelProperty(position = 11, required = true, value = "促销类型：34-买赠、40-梯度满折		—— 参见枚举 SalesPromotionStatusEnums")
	private Integer promotionType;
	@ApiModelProperty(position = 11, required = true, value = "促销类型名称")
	private String promotionTypeName;
	@ApiModelProperty(position = 12, required = true, value = "促销名称")
	private String promotionName;
	@ApiModelProperty(position = 13, required = true, value = "促销力度规则")
	private List<String> ruleList;
	@ApiModelProperty(position = 14, required = true, value = "规则说明")
	private List<String> remarkList;
	
	private static List<String> GIFT_REMARK_LIST = new ArrayList<>();
	private static List<String> GRADIENT_DISCOUNT_REMARK_LIST = new ArrayList<>();
	static {
		GIFT_REMARK_LIST.add("按照能满足的最高一个梯度执行促销计算，不累加。");
		GIFT_REMARK_LIST.add("注：特价商品、特惠商品、赠品、配送费不参与促销计算，赠品详见购物车，限量赠品赠完为止，如有疑问请联系客服。");
		
		GRADIENT_DISCOUNT_REMARK_LIST.add("按照能满足的最高一个梯度执行促销计算，不累加。");
		GRADIENT_DISCOUNT_REMARK_LIST.add("注：特价商品、特惠商品、赠品、配送费不参与促销计算。");
	}
	
	public static CommodityPromotionODTO init(CommodityDetailODTO odto) {
		CommodityPromotionODTO promotion = null;
		if (null != odto) {
			Integer promotionType = odto.getPromotionType();
			promotion = new CommodityPromotionODTO();
			if (null != promotionType) {
				promotion.promotionType = odto.getPromotionType();
				if (SalesPromotionStatusEnums.GIFT.getCode() == promotionType.intValue()) {
					promotion.promotionTypeName = "买赠";
					promotion.remarkList = GIFT_REMARK_LIST;
				} else if (SalesPromotionStatusEnums.GRADIENT_DISCOUNT.getCode() == promotionType.intValue()) {
					promotion.promotionTypeName = "梯度满折";
					promotion.remarkList = GRADIENT_DISCOUNT_REMARK_LIST;
				}
			}
			promotion.promotionName = odto.getPromotionName();
			promotion.ruleList = odto.getPromotionRules();
		}
		return promotion;
	}
	
}
