package com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3;

import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppODTO;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDeliveryTimeODTO;
//import com.pinshang.qingyun.xda.product.dto.front.XdaStorePromotionODTO;

@Data
@NoArgsConstructor
public class XdaCommodityAppV3ODTO extends XdaCommodityAppODTO {
    // 标签
    @ApiModelProperty(value ="标签合集-列表页：1-自定义标签、2-特价、3-促销（、4-凑整、5-速冻）、6-限量",position = 13)
    private List<CommodityTextTagInfoODTO> listTagList;
    @ApiModelProperty(value ="标签集合-详情页：2-特价、4-凑整、5-速冻、6-限量",position = 13)
    private List<CommodityTextTagInfoODTO> tagV2List;

    //凑整
    @ApiModelProperty(value ="是否凑整：0-否、1-是",position = 8)
    private Integer isFreezeRounding;
    @ApiModelProperty(value ="凑整倍数",position = 8)
    private Integer isFreezeRoundingMultiple;


    //特惠
    @ApiModelProperty(value ="是否特惠：0=无，1=有",position = 17)
    private Integer isThPrice = 0;
    @ApiModelProperty(value ="特惠商品限量值",position = 18)
    private BigDecimal thLimitNumber;
    @ApiModelProperty(value ="特惠价格",position = 19)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal thPrice;
    @ApiModelProperty(value ="特惠满价格",position = 19)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal thFullPrice;
    
    @ApiModelProperty(value ="价格标签",position = 30, example = "约￥1.23/斤")
    private String priceLabel;
    
    private BigDecimal TWO = new BigDecimal("2");
    public String getPriceLabel() {
        if (null == super.getCommodityUnitName()) {
            return "";
        }

        if (!super.getCommodityUnitName().trim().equalsIgnoreCase("kg")) {
            return "";
        }

        if (null != isThPrice && isThPrice.equals(1)) {
            if (null == thPrice) {
                return "";
            }
            return "约￥" + thPrice.divide(TWO, 2, BigDecimal.ROUND_HALF_UP) + "/斤";
        }

        if (null != this.getIsSpecialPrice() && this.getIsSpecialPrice().equals(1)) {
            if (null == super.getSpecialPrice()) {
                return "";
            }
            return "约￥" + super.getSpecialPrice().divide(TWO, 2, BigDecimal.ROUND_HALF_UP) + "/斤";
        }

        if (null != super.getCommodityPrice()) {
            return "约￥" + super.getCommodityPrice().divide(TWO, 2, BigDecimal.ROUND_HALF_UP) + "/斤";
        }

        return "";
    }

    public BigDecimal getSalesBoxCapacity() {
        if (null != super.getSalesBoxCapacity()) {
//			return salesBoxCapacity.stripTrailingZeros(); // 存在科学计数法的问题
            return new BigDecimal(super.getSalesBoxCapacity().stripTrailingZeros().toPlainString());
        }
        return super.getSalesBoxCapacity();
    }
}
