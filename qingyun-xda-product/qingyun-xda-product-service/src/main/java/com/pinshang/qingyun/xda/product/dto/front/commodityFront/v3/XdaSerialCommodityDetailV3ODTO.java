package com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3;

import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;

@Data
@NoArgsConstructor
public class XdaSerialCommodityDetailV3ODTO {
    @ApiModelProperty(value = "系列品商品ID",position = 1)
    private Long commodityId;
    @ApiModelProperty(value = "是否当前商品，详情页高亮显示：0=否，1=是",position = 2)
    private Integer isCurrentCommodity;
    @ApiModelProperty(value = "系列品商品规格",position = 3)
    private String commoditySpec;
    @ApiModelProperty(value = "系列品商品价格",position = 3)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;
    @ApiModelProperty(value = "系列品商品单位",position = 4)
    private String commodityUnitName;
    @ApiModelProperty(value = "系列品商品箱规",position = 4)
    private BigDecimal salesBoxCapacity;
    
    // 标签
    @ApiModelProperty(value ="标签合集-列表页：1-自定义标签、2-特价、3-促销（、4-凑整、5-速冻）、6-限量",position = 13)
    private List<CommodityTextTagInfoODTO> listTagList;
    @ApiModelProperty(value ="标签集合-详情页：自定义标签",position = 13)
    private List<CommodityTextTagInfoODTO> tagList;
    @ApiModelProperty(value ="标签集合-详情页：2-特价、4-凑整、5-速冻、6-限量",position = 13)
    private List<CommodityTextTagInfoODTO> tagV2List;

    @ApiModelProperty(value ="是否有特价：0=无特价，1=普通特价",position = 5)
    private Integer isSpecialPrice;
    @ApiModelProperty(value ="特价",position = 5)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal specialPrice;
//    @ApiModelProperty(value ="系列品是否有促销：0=无，1=有",position = 6)
//    private Integer isPromotion;
    @ApiModelProperty(value ="系列品是否有限量：0=无，1=有",position = 7)
    private Integer isLimit;
    @ApiModelProperty(value ="系列品限量值",position = 7)
    private BigDecimal limitNumber;
    @ApiModelProperty(value ="是否可订货",position = 8)
    private Boolean isCanOrder;
    @ApiModelProperty(value = "已加入购物车数量",position = 9)
    private BigDecimal shoppingCartQuantity;
    @JsonIgnore
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal sortPrice;
    
    @ApiModelProperty(value ="价格标签",position = 30, example = "约￥1.23/斤")
    private String priceLabel;


     public static XdaSerialCommodityDetailV3ODTO convert(XdaCommodityAppV3ODTO appODTO){
         XdaSerialCommodityDetailV3ODTO serialCommodityODTO = BeanCloneUtils.copyTo(appODTO, XdaSerialCommodityDetailV3ODTO.class);
         BigDecimal specialPrice = appODTO.getSpecialPrice();
         if(specialPrice!=null && specialPrice.compareTo(BigDecimal.ZERO)>0 && specialPrice.compareTo(appODTO.getCommodityPrice())<0){
             serialCommodityODTO.setIsSpecialPrice(1);
             serialCommodityODTO.setSpecialPrice(appODTO.getSpecialPrice());
         }
         serialCommodityODTO.setListTagList(appODTO.getListTagList());
         serialCommodityODTO.setTagList(appODTO.getTagList());
         serialCommodityODTO.setTagV2List(appODTO.getTagV2List());
         return serialCommodityODTO;
     }

    public BigDecimal getSortPrice() {
        return specialPrice==null?commodityPrice:specialPrice;
    }
    
    public BigDecimal getSalesBoxCapacity() {
		if (null != salesBoxCapacity) {
			return salesBoxCapacity.stripTrailingZeros();
		}
		return salesBoxCapacity;
	}
    
}
