package com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4;

import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityLongPicODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.CommodityPromotionODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class XdaCommodityDetailAppV4ODTO extends XdaCommodityAppV4ODTO {
    @ApiModelProperty(value = "收藏状态：0=未收藏，1=已收藏",position = 30)
    private Integer isCollect;
    @ApiModelProperty(value = "图片URL集合",position = 30)
    private List<String> imageUrlList;
    @ApiModelProperty(value = "长图URL集合",position = 31)
    private List<XdaCommodityLongPicODTO> longPicList;

//    @ApiModelProperty(value = "商品详情：促销详细信息",position = 32)
//    private List<XdaStorePromotionODTO> promotionList;
    
    @ApiModelProperty(value = "促销信息",position = 32)
    private CommodityPromotionODTO promotion;

    //最快送达提示
    @ApiModelProperty(value = "商品详情：最快送达日期提示",position = 33)
    private List<String> distributionTipList;

    //系列品
    @ApiModelProperty(value = "系列品规格列表，当isSerial=1时展示",position = 34)
    private List<XdaSerialCommodityDetailV4ODTO> serialCommodityDetailList;

    //特惠商品提示信息
    @ApiModelProperty(value = "特惠商品提示信息",position = 35)
    private String thTipsDetails;
    
    @ApiModelProperty(value = "销售状态：1-正常可订货的商品、2-已抢光的商品、3-当前送货日期不支持订货的商品	 —— 参见枚举 SalesStatusEnums", position = 30)
	private Integer salesStatus;

    @ApiModelProperty("是否可用券 1 是 0 否")
    private Integer enableCoupon;


    //前台品名、自定义标签、副标题、商品规格（净含量、贮存条件、保质期）、商品图片、长图、
    public static XdaCommodityDetailAppV4ODTO serialConvert(XdaCommodityDetailAppV4ODTO detailAppODTO, XdaCommodityAppV4ODTO appODTO){
        detailAppODTO.setCommodityName(appODTO.getCommodityName());
//        detailAppODTO.setTagList(appODTO.getTagList());
        detailAppODTO.setCommoditySubName(appODTO.getCommoditySubName());
        detailAppODTO.setCommoditySpec(appODTO.getCommoditySpec());
        detailAppODTO.setCommodityWeight(appODTO.getCommodityWeight());
        detailAppODTO.setStorageCondition(appODTO.getStorageCondition());
        detailAppODTO.setQualityDays(appODTO.getQualityDays());
        return detailAppODTO;
    }

//    public List<XdaStorePromotionODTO> getPromotionList() {
//        if(CollectionUtils.isNotEmpty(promotionList)){
//           for(int i=0;i<promotionList.size();i++){
//                promotionList.get(i).setGiftModelName("活动"+(i+1)+"：");
//           }
//        }
//        return promotionList;
//    }
    
}
