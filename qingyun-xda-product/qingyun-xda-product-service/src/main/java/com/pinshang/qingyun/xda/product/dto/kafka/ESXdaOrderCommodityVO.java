package com.pinshang.qingyun.xda.product.dto.kafka;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: hhf
 * @date: 2024/4/8/008 15:14
 */
@Data
@NoArgsConstructor
public class ESXdaOrderCommodityVO {
    /**
     * 商品id-必填
     */
    private Long commodityId;
    /**
     * 送货日期范围code 2-8
     */
    private String deliveryDateRangeCode;
    /**
     * 送货日期范围value T+2~T+8
     */
    private String deliveryDateRangeValue;

    /**
     * app类型:1-鲜达,2-批发
     */
    private Integer appType;

    public ESXdaOrderCommodityVO(Long commodityId, String deliveryDateRangeCode, String deliveryDateRangeValue,Integer appType) {
        this.commodityId = commodityId;
        this.deliveryDateRangeCode = deliveryDateRangeCode;
        this.deliveryDateRangeValue = deliveryDateRangeValue;
        this.appType = appType;
    }
}
