package com.pinshang.qingyun.xda.product.dto.kafka;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: hhf
 * @date: 2024/4/8/008 15:14
 */
@Data
@NoArgsConstructor
public class EsXdaCategoryChangeKafkaVO {

    /**
     * 类id
     */
    private Long cateId;

    /**
     * 父id
     */
    private Long parentId;

    /**
     * 品类名称
     */
    private String cateName;

    /**
     * 品类级别
     */
    private Integer cateLevel;

    /**
     * 排序号
     */
    private Integer sortNum;

    /**
     * 状态：1-新增,0-删除, 2-修改
     */
    private Integer status;

    public EsXdaCategoryChangeKafkaVO(Long cateId, Long parentId, String cateName, Integer cateLevel, Integer sortNum, Integer status) {
        this.cateId = cateId;
        this.parentId = parentId;
        this.cateName = cateName;
        this.cateLevel = cateLevel;
        this.sortNum = sortNum;
        this.status = status;
    }
}
