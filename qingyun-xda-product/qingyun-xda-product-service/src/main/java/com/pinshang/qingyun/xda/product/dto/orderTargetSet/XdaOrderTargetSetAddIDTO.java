package com.pinshang.qingyun.xda.product.dto.orderTargetSet;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/6 14:05
 */
@Data
public class XdaOrderTargetSetAddIDTO {
    @ApiModelProperty(value = "客户id")
    private Long storeId;
    @ApiModelProperty(value = "单日订货目标（元）")
    private BigDecimal orderTargetToDay;
    @ApiModelProperty(value = "送货日期开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryStartDate;
    @ApiModelProperty(value = "送货日期结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deliveryEndDate;
    @ApiModelProperty(value = "日期范围类型：1-常规设置 2-循环设置")
    private Integer deliveryDateRangeType;
    @ApiModelProperty(value = "循环设置的值字符串，多个英文分号隔开")
    private String loopSet;
    @ApiModelProperty(hidden = true)
    private Long userId;
    @ApiModelProperty(hidden = true)
    private String userName;
}
