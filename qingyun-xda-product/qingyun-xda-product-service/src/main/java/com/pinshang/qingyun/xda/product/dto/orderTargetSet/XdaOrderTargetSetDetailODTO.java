package com.pinshang.qingyun.xda.product.dto.orderTargetSet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/6 15:29
 */
@Data
public class XdaOrderTargetSetDetailODTO {
    private String id;
    @ApiModelProperty(value = "客户id")
    private String storeId;
    @ApiModelProperty(value = "客户编码_客户名称")
    private String storeCodeName;
    @ApiModelProperty(value = "客户类型名称")
    private String storeTypeName;
    @ApiModelProperty(value = "单日订货目标")
    private String orderTargetToDay;
    @ApiModelProperty(value = "送货日期开始日期")
    private String deliveryStartDate;
    @ApiModelProperty(value = "送货日期结束日期")
    private String deliveryEndDate;
    @ApiModelProperty(value = "循环设置值")
    private String loopSet;
    @ApiModelProperty(value = "日期范围类型：1-常规设置 2-循环设置")
    private Integer deliveryDateRangeType;

    public String getOrderTargetToDay() {
        if(StringUtils.isNotBlank(orderTargetToDay)){
            orderTargetToDay= (new BigDecimal(orderTargetToDay).setScale(0)).toString();
        }
        return orderTargetToDay;
    }
}
