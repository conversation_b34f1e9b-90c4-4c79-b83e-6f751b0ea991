package com.pinshang.qingyun.xda.product.dto.orderTargetSet;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/6 15:29
 */
@Data
public class XdaOrderTargetSetQueryIDTO extends Pagination {
    @ApiModelProperty(value = "送货日期日期")
    private String deliveryDate;
    @ApiModelProperty(value = "是否过期：0-已过期 1-未过期")
    private Integer isExpire;
    @ApiModelProperty(value = "状态：0-停用 1-启用")
    private Integer orderTargetStatus;
    @ApiModelProperty(value = "订货目标编码")
    private String orderTargetCode;
    @ApiModelProperty(value = "客户id")
    private Long storeId;
}
