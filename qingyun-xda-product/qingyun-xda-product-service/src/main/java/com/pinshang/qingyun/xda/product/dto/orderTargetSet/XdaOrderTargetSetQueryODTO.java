package com.pinshang.qingyun.xda.product.dto.orderTargetSet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/6 15:29
 */
@Data
public class XdaOrderTargetSetQueryODTO {
    private String id;
    private String storeId;
    @ApiModelProperty(value = "客户编码")
    private String storeCode;
    @ApiModelProperty(value = "客户名称")
    private String storeName;
    @ApiModelProperty(value = "结账客户编码_名称")
    private String settlementCodeName;
    @ApiModelProperty(value = "客户类型名称")
    private String storeTypeName;
    @ApiModelProperty(value = "送货日期范围")
    private String deliveryDate;
    @ApiModelProperty(value = "单日订货目标")
    private String orderTargetToDay;
    @ApiModelProperty(value = "循环设置值")
    private String loopSet;
    private Integer orderTargetStatus;
    @ApiModelProperty(value = "状态名称")
    private String orderTargetStatusName;
    @ApiModelProperty(value = "编码")
    private String orderTargetCode;
    @ApiModelProperty(value = "操作时间")
    private String operateTime;
    @ApiModelProperty(value = "操作人")
    private String operateUser;

    public String getOrderTargetToDay() {
        if(StringUtils.isNotBlank(orderTargetToDay)){
            orderTargetToDay= (new BigDecimal(orderTargetToDay).setScale(0)).toString();
        }
        return orderTargetToDay;
    }
}
