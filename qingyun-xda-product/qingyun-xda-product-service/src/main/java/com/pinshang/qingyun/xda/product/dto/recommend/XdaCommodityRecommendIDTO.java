package com.pinshang.qingyun.xda.product.dto.recommend;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class XdaCommodityRecommendIDTO extends Pagination {


    @ApiModelProperty(position = 1, value = "商品编码或名称")
    private String commodityParam;
    // 一级分类ids;
    @ApiModelProperty(position = 4, value = "一级分类id")
    private Long cateId1s;
    // 二级分类ids
    @ApiModelProperty(position = 5, value = "二级分类ids")
    private Long cateId2s;
    // 三级分类ids
    @ApiModelProperty(position = 6, value = " 三级分类ids")
    private Long cateId3s;

    @ApiModelProperty(position = 7, value = "前台品名")
    private String commodityAppName;

}