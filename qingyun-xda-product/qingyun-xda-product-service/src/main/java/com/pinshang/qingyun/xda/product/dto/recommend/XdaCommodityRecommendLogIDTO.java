package com.pinshang.qingyun.xda.product.dto.recommend;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class XdaCommodityRecommendLogIDTO extends Pagination {

    @ApiModelProperty(value = "商品编码或名称")
    private String commodityParam;
    /* @ApiModelProperty(position = 2, value = "开始日期")
    private String deliveryTimeStart;
    @ApiModelProperty(position = 3, value = "结束日期")
    private String deliveryTimeEnd;*/
    @ApiModelProperty(value = "操作类型：1-新增，2-删除")
    private Integer operateType;
}