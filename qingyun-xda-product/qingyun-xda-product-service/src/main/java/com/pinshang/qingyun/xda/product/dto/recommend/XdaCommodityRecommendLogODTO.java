package com.pinshang.qingyun.xda.product.dto.recommend;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
public class XdaCommodityRecommendLogODTO {
    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "商品code")
    private String commodityCode;
    @ApiModelProperty(value = "商品名称")
    private String commodityName;
    @ApiModelProperty(value = "规格")
    private String commoditySpec;
    @ApiModelProperty(value = "操作类型数值")
    private Integer operateType;
    @ApiModelProperty(value = "操作类型名称")
    private String operatetypeName;
    @ApiModelProperty(value = "创建人id")
    private Long createId;
    @ApiModelProperty(value = "创建人名称")
    private String createName;
    @ApiModelProperty(value = "创建时间")
    private String createTime;
}