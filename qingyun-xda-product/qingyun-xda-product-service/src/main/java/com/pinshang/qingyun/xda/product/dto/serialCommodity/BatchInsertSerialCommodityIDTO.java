package com.pinshang.qingyun.xda.product.dto.serialCommodity;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.xda.product.dto.BaseEnterpriseUserIDTO;

/**
 * 批量添加  系列品
 *
 * <AUTHOR>
 *
 * @date 2020年12月22日
 */
@Data
@NoArgsConstructor
public class BatchInsertSerialCommodityIDTO extends BaseEnterpriseUserIDTO {
	@ApiModelProperty(position = 11, required = true, value = "商品编码集合")
	private List<String> commodityCodeList;
}
