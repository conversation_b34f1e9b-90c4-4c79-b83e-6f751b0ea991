package com.pinshang.qingyun.xda.product.dto.serialCommodity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.xda.product.dto.BaseEnterpriseUserIDTO;

/**
 * 删除  系列品
 *
 * <AUTHOR>
 *
 * @date 2020年12月22日
 */
@Data
@NoArgsConstructor
public class DeleteSerialCommodityIDTO extends BaseEnterpriseUserIDTO {
	@ApiModelProperty(position = 11, required = true, value = "商品ID")
	private Long commodityId;
    
    public DeleteSerialCommodityIDTO(Long commodityId) {
    	this.commodityId = commodityId;
    }
}
