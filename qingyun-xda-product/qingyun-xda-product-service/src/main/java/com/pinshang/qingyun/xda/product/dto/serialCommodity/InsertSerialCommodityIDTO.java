package com.pinshang.qingyun.xda.product.dto.serialCommodity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.xda.product.dto.BaseEnterpriseUserIDTO;

/**
 * 新增  系列品
 *
 * <AUTHOR>
 *
 * @date 2020年12月22日
 */
@Data
@NoArgsConstructor
public class InsertSerialCommodityIDTO extends BaseEnterpriseUserIDTO {
	@ApiModelProperty(position = 10, required = true, value = "系列品-主品ID")
	private Long serialCommodityId;
	@ApiModelProperty(position = 11, required = true, value = "商品ID")
	private Long commodityId;
    
    public InsertSerialCommodityIDTO(Long serialCommodityId, Long commodityId) {
    	this.serialCommodityId = serialCommodityId;
    	this.commodityId = commodityId;
    }
}
