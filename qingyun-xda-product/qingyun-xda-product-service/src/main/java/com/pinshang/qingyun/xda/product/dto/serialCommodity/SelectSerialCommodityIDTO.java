package com.pinshang.qingyun.xda.product.dto.serialCommodity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 查询  系列品
 *
 * <AUTHOR>
 *
 * @date 2020年12月22日
 */
@Data
@ApiModel
@NoArgsConstructor
public class SelectSerialCommodityIDTO {
	@ApiModelProperty(position = 11, value = "系列品-编码")
	private String serialCommodityCode;
	@ApiModelProperty(position = 12, value = "商品ID")
	private Long commodityId;
	@ApiModelProperty(position = 13, value = "商品条码")
	private String barCode;
}
