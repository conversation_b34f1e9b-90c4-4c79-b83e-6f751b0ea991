package com.pinshang.qingyun.xda.product.dto.serialCommodity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.page.Pagination;

/**
 * 分页查询  系列品日志信息  列表
 *
 * <AUTHOR>
 *
 * @date 2020年12月22日
 */
@Data
@ApiModel
@NoArgsConstructor
public class SelectSerialCommodityLogInfoPageIDTO extends Pagination {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty(position = 11, value = "系列品-编码")
	private String serialCommodityCode;
	@ApiModelProperty(position = 12, value = "商品ID")
	private Long commodityId;
	@ApiModelProperty(position = 13, value = "商品条码")
	private String barCode;
}
