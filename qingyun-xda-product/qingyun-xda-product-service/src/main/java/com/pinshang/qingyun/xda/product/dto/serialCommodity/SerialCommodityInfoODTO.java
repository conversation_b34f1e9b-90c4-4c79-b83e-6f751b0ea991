package com.pinshang.qingyun.xda.product.dto.serialCommodity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 系列品  信息
 * 
 * <AUTHOR>
 *
 * @date 2020年12月22日
 */
@Data
@ApiModel
@NoArgsConstructor
public class SerialCommodityInfoODTO {
	@ApiModelProperty(position = 10, required = true, value = "商品ID")
	private String commodityId;
	@ApiModelProperty(position = 11, required = true, value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(position = 11, required = true, value = "商品名称")
	private String commodityName;
	@ApiModelProperty(position = 11, required = true, value = "商品规格")
	private String commoditySpec;
	
	@ApiModelProperty(position = 21, required = true, value = "前台品名")
	private String commodityAppName;
	@ApiModelProperty(position = 21, required = true, value = "前台一级品类名称")
	private String xdaFirstCategoryName;
	@ApiModelProperty(position = 21, required = true, value = "前台二级品类名称")
	private String xdaSecondCategoryName;
	
	@ApiModelProperty(position = 31, required = true, value = "系列品-主品ID")
	private String serialCommodityId;
	@ApiModelProperty(position = 31, required = true, value = "系列品-编码")
	private String serialCommodityCode;
	@ApiModelProperty(position = 31, required = true, value = "系列品-是否主品")
	private String serialCommodityStatusName;
	
	public String getSerialCommodityStatusName() {
		return commodityId.equals(serialCommodityId)? "是": "否";
	}
	
}
