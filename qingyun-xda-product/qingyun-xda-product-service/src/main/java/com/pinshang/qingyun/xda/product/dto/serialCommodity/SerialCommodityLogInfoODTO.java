package com.pinshang.qingyun.xda.product.dto.serialCommodity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pinshang.qingyun.base.enums.commodity.CommodityTextOperateTypeEnum;

/**
 * 系列品日志  信息
 * 
 * <AUTHOR>
 *
 * @date 2020年12月22日
 */
@Data
@ApiModel
@NoArgsConstructor
public class SerialCommodityLogInfoODTO {
	@ApiModelProperty(position = 10, required = true, value = "操作类型：	—— 参见 CommodityTextOperateTypeEnum", hidden = true)
	private Integer operateType;
	@ApiModelProperty(position = 10, required = true, value = "操作类型名称")
	private String operateTypeName;
	
	@ApiModelProperty(position = 11, required = true, value = "商品ID", hidden = true)
	private String commodityId;
	@ApiModelProperty(position = 11, required = true, value = "商品编码")
	private String commodityCode;
	@ApiModelProperty(position = 11, required = true, value = "商品名称")
	private String commodityName;
	@ApiModelProperty(position = 11, required = true, value = "商品规格")
	private String commoditySpec;
	
	@ApiModelProperty(position = 21, required = true, value = "前台品名")
	private String commodityAppName;
	@ApiModelProperty(position = 21, required = true, value = "前台一级品类ID", hidden = true)
	private Long xdaFirstCategoryId;
	@ApiModelProperty(position = 21, required = true, value = "前台一级品类名称")
	private String xdaFirstCategoryName;
	@ApiModelProperty(position = 21, required = true, value = "前台二级品类ID", hidden = true)
	private Long xdaSecondCategoryId;
	@ApiModelProperty(position = 21, required = true, value = "前台二级品类名称")
	private String xdaSecondCategoryName;
	
	@ApiModelProperty(position = 31, required = true, value = "系列品-主品ID", hidden = true)
	private String serialCommodityId;
	@ApiModelProperty(position = 31, required = true, value = "系列品-编码")
	private String serialCommodityCode;
	@ApiModelProperty(position = 31, required = true, value = "系列品-是否主品")
	private String serialCommodityStatusName;
	
	@ApiModelProperty(position = 41, required = true, value = "操作人名称")
	private String createName;
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
	@ApiModelProperty(position = 41, required = true, value = "操作时间：yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	
	
	public String getOperateTypeName() {
		return CommodityTextOperateTypeEnum.getName(operateType);
	}
	
	public String getSerialCommodityStatusName() {
		return commodityId.equals(serialCommodityId)? "是": "否";
	}
	
}
