package com.pinshang.qingyun.xda.product.dto.specialsCommoditySet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:49
 */
@Data
public class XdaSpecialsCommoditySetBasicIDTO {
    @ApiModelProperty(value = "特惠单价")
    private BigDecimal commoditySpecialsPrice;
    @ApiModelProperty(value = "限量")
    private Integer commodityLimit;
    @ApiModelProperty(hidden = true)
    private Long userId;
    @ApiModelProperty(hidden = true)
    private String userName;

}
