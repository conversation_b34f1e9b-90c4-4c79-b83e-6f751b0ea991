package com.pinshang.qingyun.xda.product.dto.specialsCommoditySet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:49
 */
@Data
public class XdaSpecialsCommoditySetDetailODTO {
    private String id;
    @ApiModelProperty(value = "商品id")
    private String commodityId;
    @ApiModelProperty(value = "商品编码")
    private String commodityCode;
    @ApiModelProperty(value = "商品名称")
    private String commodityName;
    @ApiModelProperty(value = "名称加编码")
    private String commodityCodeName;
    @ApiModelProperty(value = "规格")
    private String commoditySpec;
    @ApiModelProperty(value = "商品计量单位")
    private String commodityUnitName;
    @ApiModelProperty(value = "销售箱规")
    private String salesBoxCapacity;
    @ApiModelProperty(value = "是否称重")
    private String isWeightName;
    @ApiModelProperty(value = "商品特惠单价")
    private String commoditySpecialsPrice;
    @ApiModelProperty(value = "商品限量")
    private String commodityLimit;
}
