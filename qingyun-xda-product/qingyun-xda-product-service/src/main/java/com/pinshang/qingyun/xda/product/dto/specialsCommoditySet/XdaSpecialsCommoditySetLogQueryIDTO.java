package com.pinshang.qingyun.xda.product.dto.specialsCommoditySet;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/7 13:42
 */
@Data
public class XdaSpecialsCommoditySetLogQueryIDTO extends Pagination {
    @ApiModelProperty(value = "开始日期")
    private String operateStartDate;
    @ApiModelProperty(value = "结束日期")
    private String operateEndDate;
    @ApiModelProperty(value = "条码")
    private String barCode;
    @ApiModelProperty(value = "操作人id")
    private Long operateUserId;
    @ApiModelProperty(value = "商品id")
    private Long commodityId;
}
