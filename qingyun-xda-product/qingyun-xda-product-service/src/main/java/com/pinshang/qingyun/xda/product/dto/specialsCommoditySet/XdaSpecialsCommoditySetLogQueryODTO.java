package com.pinshang.qingyun.xda.product.dto.specialsCommoditySet;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:49
 */
@Data
@ColumnWidth(value = 18)
public class XdaSpecialsCommoditySetLogQueryODTO {
    private String id;
    private String operationType;
    @ApiModelProperty(value = "操作类型名称")
    private String operationTypeName;
    @ApiModelProperty(value = "商品id")
    private String commodityId;
    @ApiModelProperty(value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(value = "商品条码")
    private String barCode;

    @ApiModelProperty(value = "商品名称")
    private String commodityName;

    @ApiModelProperty(value = "前台品名")
    private String commodityAppName;

    @ApiModelProperty(value = "商品规格")
    private String commoditySpec;

    @ApiModelProperty(value = "商品计量单位")
    private String commodityUnitName;

    @ApiModelProperty(value = "是否称重")
    private String isWeightName;

    @ApiModelProperty(value = "商品特惠单价")
    private String commoditySpecialsPrice;

    @ApiModelProperty(value = "商品限量")
    private String commodityLimit;
    @ApiModelProperty(value ="操作时间")
    private String operateTime;
    @ApiModelProperty(value ="操作人")
    private String operateUser;


}
