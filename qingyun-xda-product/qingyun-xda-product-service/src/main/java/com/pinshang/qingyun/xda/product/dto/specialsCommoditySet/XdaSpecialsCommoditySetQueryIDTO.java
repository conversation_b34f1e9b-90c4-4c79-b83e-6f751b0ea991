package com.pinshang.qingyun.xda.product.dto.specialsCommoditySet;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:49
 */
@Data
@NoArgsConstructor
public class XdaSpecialsCommoditySetQueryIDTO extends Pagination {

    @ApiModelProperty(value = "商品id")
    private Long commodityId;
    @ApiModelProperty(value = "商品条码")
    private String barCode;
    @ApiModelProperty(value = "前台品名")
    private String commodityAppName;

    public XdaSpecialsCommoditySetQueryIDTO(Long commodityId, String barCode, String commodityAppName) {
        this.commodityId = commodityId;
        this.barCode = barCode;
        this.commodityAppName = commodityAppName;
    }


}
