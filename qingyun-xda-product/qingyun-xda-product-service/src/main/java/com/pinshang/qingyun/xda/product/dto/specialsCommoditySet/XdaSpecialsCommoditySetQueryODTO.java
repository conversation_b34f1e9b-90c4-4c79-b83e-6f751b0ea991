package com.pinshang.qingyun.xda.product.dto.specialsCommoditySet;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:49
 */
@Data
@ColumnWidth(value = 18)
public class XdaSpecialsCommoditySetQueryODTO {
    @ExcelIgnore
    private String id;

    @ExcelIgnore
    @ApiModelProperty(value = "商品id")
    private String commodityId;

    @ApiModelProperty(value = "商品编码")
    @ExcelProperty(value = "商品编码",index = 0)
    private String commodityCode;

    @ApiModelProperty(value = "商品条码")
    @ExcelProperty(value = "条码",index = 1)
    private String barCode;

    @ApiModelProperty(value = "商品名称")
    @ExcelProperty(value = "商品名称",index = 2)
    private String commodityName;

    @ApiModelProperty(value = "前台品名")
    @ExcelProperty(value = "前台品名",index = 3)
    private String commodityAppName;

    @ApiModelProperty(value = "商品规格")
    @ExcelProperty(value = "规格",index = 4)
    private String commoditySpec;

    @ApiModelProperty(value = "商品计量单位")
    @ExcelProperty(value = "单位",index = 5)
    private String commodityUnitName;

    @ApiModelProperty(value = "是否称重")
    @ExcelProperty(value = "是否称重",index = 6)
    private String isWeightName;

    @ApiModelProperty(value = "商品特惠单价")
    @ExcelProperty(value = "特惠单价",index = 7)
    private String commoditySpecialsPrice;

    @ApiModelProperty(value = "商品限量")
    @ExcelProperty(value = "限量（订单级）",index = 8)
    private String commodityLimit;
}
