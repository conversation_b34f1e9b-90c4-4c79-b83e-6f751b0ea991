package com.pinshang.qingyun.xda.product.dto.specialsCommoditySet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/12 14:37
 */
@Data
public class XdaSpecialsCommoditydropDownIDTO {
    @ApiModelProperty(value = "编码或名称查询")
    private String content;
    @ApiModelProperty(value = "查询商品状态：0-不可售,1-可售")
    private Integer status;
    @ApiModelProperty(value = "查询的数量")
    private Integer quantity;
}
