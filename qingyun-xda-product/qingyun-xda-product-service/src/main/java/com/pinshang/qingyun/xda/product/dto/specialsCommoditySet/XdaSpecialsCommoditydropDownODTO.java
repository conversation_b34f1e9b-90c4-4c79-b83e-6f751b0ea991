package com.pinshang.qingyun.xda.product.dto.specialsCommoditySet;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/6/12 14:35
 */
@Data
public class XdaSpecialsCommoditydropDownODTO {
    @ApiModelProperty(value = "商品id")
    private String commodityId;
    @ApiModelProperty(value = "名称加编码")
    private String commodityCodeName;
    @ApiModelProperty(value = "规格")
    private String commoditySpec;
    @ApiModelProperty(value = "商品计量单位")
    private String commodityUnitName;
    @ApiModelProperty(value = "销售箱规")
    private String salesBoxCapacity;
    @ApiModelProperty(value = "是否称重")
    private String isWeightName;
}
