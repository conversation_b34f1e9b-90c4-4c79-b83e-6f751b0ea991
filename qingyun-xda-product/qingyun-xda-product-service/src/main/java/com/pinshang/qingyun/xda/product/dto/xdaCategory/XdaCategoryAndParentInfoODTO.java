package com.pinshang.qingyun.xda.product.dto.xdaCategory;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 品类和父级信息
 */
@Data
public class XdaCategoryAndParentInfoODTO {
	@ApiModelProperty(position = 11, required = true, value = "品类ID")
	private Long id;
	@ApiModelProperty(position = 12, required = true, value = "品类名称")
	private String cateName;
	@ApiModelProperty(position = 13, required = true, value = "品类序号")
	private Integer sortNum;
	@ApiModelProperty(position = 14, required = true, value = "品类级别")
	private Integer cateLevel;
	
	@ApiModelProperty(position = 21, value = "父级品类ID")
    private Long parentId;
	@ApiModelProperty(position = 22, value = "父级品类名称")
    private String parentCateName;
	@ApiModelProperty(position = 23, value = "父级品类序号")
    private Integer parentSortNum;
	@ApiModelProperty(position = 24,  value = "父级品类级别")
	private Integer parentCateLevel;
}
