package com.pinshang.qingyun.xda.product.dto.xdaCategory;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * Created by hhf on 2019/11/22.
 */
@Data
public class XdaCategoryListODTO {

    @ExcelIgnore
    private String id ;
    @ExcelIgnore
    private String parentId;		// 父节点ID
    @ExcelProperty("前台一级品类")
    private String firstCategoryName; //一级分类名称
    @ExcelProperty("前台二级品类")
    private String secondCategoryName;	// 二级分类名称
    @ExcelIgnore
    private Integer cateLevel;	// 品类级别
}
