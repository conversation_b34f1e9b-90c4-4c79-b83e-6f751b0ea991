package com.pinshang.qingyun.xda.product.dto.xdaCategory;

import lombok.Data;

import java.util.List;

/**
 * Created by hhf on 2019/11/21.
 */
@Data
public class XdaCategoryTreeODTO {

    private String id ;
    private String parentId;		// 父节点ID
    private String cateName;	// 品类名称
    private Integer cateLevel;	// 品类级别
    private String picUrl;		// 品类图片
    private Integer sortNum;	// 排序号


    private String visitPicUrl;

    private List<XdaCategoryTreeODTO> children;

    public XdaCategoryTreeODTO(String id, String parentId, String cateName, Integer cateLevel, String picUrl,Integer sortNum,String visitPicUrl, List<XdaCategoryTreeODTO> children) {
        this.id = id;
        this.parentId = parentId;
        this.cateName = cateName;
        this.cateLevel = cateLevel;
        this.picUrl = picUrl;
        this.sortNum = sortNum;
        this.visitPicUrl = visitPicUrl;
        this.children = children;

    }
}
