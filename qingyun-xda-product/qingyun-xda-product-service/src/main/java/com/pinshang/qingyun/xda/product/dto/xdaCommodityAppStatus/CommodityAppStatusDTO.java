package com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: hhf
 * @date: 2024/4/8/008 13:44
 */
@Data
@NoArgsConstructor
public class CommodityAppStatusDTO {

    /**
     * 商品id
     */
    private List<Long> commodityIds;

    /**新上架的商品id集合**/
    private List<Long> newCommodityIdList;

    /**
     * 0上架 1下架
     */
    private Integer appStatus;

    private Integer appType;

    public CommodityAppStatusDTO(List<Long> commodityIds, List<Long> newCommodityIdList,Integer appStatus) {
        this.commodityIds = commodityIds;
        this.newCommodityIdList = newCommodityIdList;
        this.appStatus = appStatus;
    }

    public CommodityAppStatusDTO(List<Long> commodityIds, List<Long> newCommodityIdList,Integer appStatus,Integer appType) {
        this.commodityIds = commodityIds;
        this.newCommodityIdList = newCommodityIdList;
        this.appStatus = appStatus;
        this.appType = appType;
    }
}
