package com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextPicODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: chen<PERSON>ang
 * @time: 2020/12/15 10:50
 */
@Data
@NoArgsConstructor
public class PfAppStatusODTO {

    @ExcelProperty(value = "上下架状态")
    @ApiModelProperty(position = 1,value = "上下架状态")
    private String commodityAppState;

    @ExcelProperty(value = "总部可售")
    @ApiModelProperty(position = 2,value = "总部是否可售")
    private String commodityState;

    @ExcelProperty(value = "商品编码")
    @ApiModelProperty(position = 3,value = "商品编码")
    private String commodityCode;

    @ExcelProperty(value = "条码")
    @ApiModelProperty(position = 21,value = "条码")
    private String barCode;

    @ExcelProperty(value = "商品名称")
    @ApiModelProperty(position = 4,value = "商品名称")
    private String commodityName;

    @ExcelProperty(value = "规格")
    @ApiModelProperty(position = 5,value = "商品规格")
    private String commoditySpec;

    @ExcelProperty(value = "单位")
    @ApiModelProperty(position = 14,value = "单位")
    private String commodityUnitName;

    @ExcelProperty(value = "是否称重")
    @ApiModelProperty(position = 15,value = "是否称重 0 非称重 1称重")
    private String isWeight;


    @ExcelProperty(value = "前台品名")
    @ApiModelProperty(position = 6,value = "前台品名")
    private String commodityAppName;

    @ExcelProperty(value = "前台品类")
    @ApiModelProperty(position = 7,value = "前台品类")
    private String appCategoryName;

    @ExcelProperty(value = "图片")
    @ApiModelProperty(position = 8, value = "图片：0-无、1-有")
    private String picStatus;

    @ExcelProperty(value = "送货日期范围")
    @ApiModelProperty(position = 9,value = "送货时间范围")
    private String deliveryDateRangeValue;

    @ExcelProperty(value = "批发箱规")
    @ApiModelProperty(position = 13,value = "批发销售箱规")
    private BigDecimal pfBoxCapacity;

    @ExcelProperty(value = "长图")
    @ApiModelProperty(position = 10, value = "长图：0-无、1-有")
    private String longPicStatus;

    @ExcelProperty(value = "副标题")
    @ApiModelProperty(position = 11, value = "副标题")
    private String commoditySubName;

    @ExcelProperty(value = "自定义标签")
    @ApiModelProperty(position = 12,value = "自定义标签")
    private String commodityTag;

    @ExcelProperty(value = "净含量")
    @ApiModelProperty(position = 16,value = "净含量")
    private String commodityWeight;

    @ExcelProperty(value = "贮存条件")
    @ApiModelProperty(position = 17,  value = "贮存条件")
    private String storageCondition;

    @ExcelProperty(value = "保质期(天)")
    @ApiModelProperty(position = 18,value = "保质期(天)")
    private Integer qualityDays;

    @ExcelProperty(value = "是否凑整")
    @ApiModelProperty(position = 19,value = "是否凑整产品 0-否,1-是")
    private String commodityFreezeGroup;

    @ExcelProperty(value = "后台品类")
    @ApiModelProperty(position = 20,value = "后台品类")
    private String commodityCategory;

    @ExcelIgnore
    @ApiModelProperty(hidden = true,value = "商品id")
    private Long commodityId;

    @ExcelIgnore
    @ApiModelProperty(position = 22,value = "图片集合")
    private List<CommodityTextPicODTO> picList;

    @ExcelIgnore
    @ApiModelProperty(position = 23, value = "长图Url")
    private String longPicUrl;

    public String getCommodityAppState(){
        String str = "";
        if(null != commodityAppState){
            str = commodityAppState.equals("0")?"上架":"下架";
        }
        return str;
    }

    public String getCommodityState(){
        String str = "";
        if(null != commodityState){
            //0-不可售,1-可售
            str = commodityState.equals("0")?"否":"是";
        }
        return str;
    }
    public String getIsWeight(){
        String str = "";
        if(null != isWeight){
            //0-不可售,1-可售
            str = isWeight.equals("0")?"否":"是";
        }
        return str;
    }
}
