package com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus;

import com.pinshang.qingyun.base.page.Pagination;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @time: 2020/12/15 10:50
 */
@Data
public class XdaCommodityAppStatusLogIDTO extends Pagination {

    @ApiModelProperty(position = 1,value = "开始时间")
    private String bDate;

    @ApiModelProperty(position = 2,value = "结束时间")
    private String eDate;

    @ApiModelProperty(position = 3,value = "商品id")
    private Long commodityId;
    private Long commodityName;

    @ApiModelProperty(position = 4,value = "前台品名")
    private String commodityAppName;

    @ApiModelProperty(position = 5,value = "上下架状态:0-上架,1-下架")
    private String commodityAppState;

    @ApiModelProperty(position = 10,value = "app类型:1-鲜达,2-批发")
    private Integer appType;
}
