package com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: chenqiang
 * @time: 2020/12/15 10:50
 */
@Data
public class XdaCommodityAppStatusLogODTO {

    @ApiModelProperty(position = 1,value = "商品编码")
    private String commodityCode;

    @ApiModelProperty(position = 2,value = "商品名称")
    private String commodityName;

    @ApiModelProperty(position = 3,value = "前台品名")
    private String commodityAppName;

    @ApiModelProperty(position = 4,value = "商品规格")
    private String commoditySpec;

    @ApiModelProperty(position = 5,value = "操作类型:0-上架,1-下架")
    private String commodityAppState;

    @ApiModelProperty(position = 7,value = "操作人")
    private String userName;

    @ApiModelProperty(position = 8,value = "操作时间")
    private String createTime;

    @ApiModelProperty(position = 9,value = "原因")
    private String reason;

    public String getCommodityAppState(){
        String str = "";
        if(null != commodityAppState){
            str =  commodityAppState.equals("1") ?"下架":"上架";
        }
        return str;
    }
}
