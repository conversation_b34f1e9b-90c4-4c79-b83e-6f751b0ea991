package com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: chenqiang
 * @time: 2020/12/16 16:05
 */
@Data
public class XdaCommodityAppStatusUpIDTO {

    @ApiModelProperty(position = 1,value = "商品编码")
    private String commodityCodes;

    @ApiModelProperty(position = 2,value = "操作原因")
    private String reason;

    @ApiModelProperty(position = 3,value = "操作类型 0-上架、1-下架")
    private Integer status;

    @ApiModelProperty(position = 4,value = "是否强制操作 0-否 1-是")
    private Integer forceStatus;

    @ApiModelProperty(hidden = true,value = "操作人")
    private Long userId;

    @ApiModelProperty(position = 5,value = "app类型:1-鲜达,2-批发")
    private Integer appType;
}
