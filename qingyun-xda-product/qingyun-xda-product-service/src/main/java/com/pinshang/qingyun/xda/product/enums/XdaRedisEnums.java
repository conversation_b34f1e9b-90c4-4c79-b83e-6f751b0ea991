package com.pinshang.qingyun.xda.product.enums;

import lombok.Getter;

@Getter
public enum XdaRedisEnums {
    SPECIALPRICE(0,"XDA:SPECIALPRICE:"),
    PROMOTION(1,"XDA:PROMOTION:"),
    DELIVERYTIME(2,"XDA:DELIVERYTIME:"),
    COMMODITYTAG(3,"XDA:COMMODITYTAG:"),
    COMMODITYLIMIT(4,"XDA:COMMODITYLIMIT:"),

    ;

    private Integer value;
    private String name;

    XdaRedisEnums(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public void setName(String name) {
        this.name = name;
    }
}
