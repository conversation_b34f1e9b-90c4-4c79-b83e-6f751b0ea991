package com.pinshang.qingyun.xda.product.helper;


import com.pinshang.qingyun.xda.product.service.deliveryDate.XdaOrderCommodityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 *  根据Redis生成编号
 *
 * <AUTHOR>
 * @date 2020/8/19
 *
 */
@Component
@Slf4j
public class BizCodeGenerator {

    private static final String ORDER_COMMODITY_DATE_SET = "qingyum:xda:commodity:code:set";
    

    @Resource
    private RedissonClient redissonClient;

    @Resource
    XdaOrderCommodityService xdaOrderCommodityService;

    /**
     * 写缓存
     * @return 
     */
    public void syncCommodityDate(){
        RSet<Integer> set = redissonClient.getSet(ORDER_COMMODITY_DATE_SET);
        if(CollectionUtils.isEmpty(set)){
            set.clear();
        }
        List<Integer> rangeCodes = xdaOrderCommodityService.getDeliveryDateRangeCode();
        set.addAll(rangeCodes);
//        if(!CollectionUtils.isEmpty(rangeCodes)){
//            for(Integer code:rangeCodes){
//                set.add(code);
//            }
//        }


        
    }

   

}
