package com.pinshang.qingyun.xda.product.helper;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.product.dto.CategoryODto;
import com.pinshang.qingyun.product.service.CategoryClient;
import com.pinshang.qingyun.xda.product.mapper.XdaCategoryMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaTagMapper;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityBarCodeMapper;
import com.pinshang.qingyun.xda.product.mapper.common.DictionaryMapper;
import com.pinshang.qingyun.xda.product.model.XdaCategory;
import com.pinshang.qingyun.xda.product.model.XdaTag;
import com.pinshang.qingyun.xda.product.model.common.CommodityBarCode;
import com.pinshang.qingyun.xda.product.model.common.Dictionary;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class CommonHelper {
	
	@Autowired
	private XdaTagMapper tagMapper;
	
	@Autowired
	private CategoryClient categoryClient;
	
	@Autowired
    private XdaCategoryMapper categoryMapper;
	
	@Autowired
	private DictionaryMapper dictionaryMapper;
	
	@Autowired
    private CommodityBarCodeMapper commodityBarCodeMapper;
	
	/**
	 * 查询  后台品类名称Map
	 * 
	 * @param categoryIdList
	 * @return
	 */
	public Map<String, String> getCategoryNameMap(List<Long> categoryIdList) {
		if (SpringUtil.isEmpty(categoryIdList)) {
			return Collections.emptyMap();
		}
		categoryIdList = categoryIdList.stream().distinct().collect(Collectors.toList());
		List<CategoryODto> categoryList = categoryClient.findByIds(categoryIdList);
		if (SpringUtil.isEmpty(categoryList)) {
			return Collections.emptyMap();
		}
		return categoryList.stream().collect(Collectors.toMap(CategoryODto::getId, CategoryODto::getCateName));
	}
	
	/**
	 * 查询  前台品类名称Map
	 * 
	 * @param xdaCategoryIdList
	 * @return
	 */
	public Map<Long, String> getXdaCategoryNameMap(List<Long> xdaCategoryIdList) {
		if (SpringUtil.isEmpty(xdaCategoryIdList)) {
			return Collections.emptyMap();
		}
		Example example = new Example(XdaCategory.class);
		example.createCriteria().andIn("id", xdaCategoryIdList);
		example.selectProperties("id", "cateName");
		List<XdaCategory> xdaCategoryList = categoryMapper.selectByExample(example);
		return xdaCategoryList.stream().collect(Collectors.toMap(XdaCategory::getId, XdaCategory::getCateName));
	}
	
	/**
	 * 查询  字典名称Map
	 * 
	 * @param dictionaryIdList
	 * @return
	 */
	public Map<Long, String> getDictionaryNameMap(List<Long> dictionaryIdList) {
		if (SpringUtil.isEmpty(dictionaryIdList)) {
			return Collections.emptyMap();
		}
		Example example = new Example(Dictionary.class);
		example.createCriteria().andIn("id", dictionaryIdList);
		example.selectProperties("id", "optionName");
		List<Dictionary> dictionaryList = dictionaryMapper.selectByExample(example);
		return dictionaryList.stream().collect(Collectors.toMap(Dictionary::getId, Dictionary::getOptionName));
	}
	
	/**
	 * 查询  标签
	 * 
	 * @param tagIdList
	 * @return
	 */
	public Map<Long, XdaTag> getTagMap(List<Long> tagIdList) {
		if (SpringUtil.isEmpty(tagIdList)) {
			return Collections.emptyMap();
		}
		Example example = new Example(XdaTag.class);
		example.createCriteria().andIn("id", tagIdList);
		example.selectProperties("id", "tagName", "tagBgColor");
		List<XdaTag> tagList = tagMapper.selectByExample(example);
		return tagList.stream().collect(Collectors.toMap(XdaTag::getId, t -> t));
	}
	
	/**
	 * 查询  副条码s
	 * 
	 * @param commodityIdList
	 * @return
	 */
	public Map<Long, String> getSubBarCodesMap(List<Long> commodityIdList) {
		if (SpringUtil.isEmpty(commodityIdList)) {
			return Collections.emptyMap();
		}
		
		Example example = new Example(CommodityBarCode.class);
		example.createCriteria().andIn("commodityId", commodityIdList).andEqualTo("defaultState", 0);
		example.selectProperties("commodityId", "barCode");
		List<CommodityBarCode> commodityBarCodeList = commodityBarCodeMapper.selectByExample(example);
		
		if (SpringUtil.isEmpty(commodityBarCodeList)) {
			return Collections.emptyMap();
		}
		
		Map<Long, List<CommodityBarCode>> commodityBarMap = commodityBarCodeList.stream()/*.filter(Objects::nonNull)*/.collect(Collectors.groupingBy(p -> p.getCommodityId()));
		Set<Map.Entry<Long, List<CommodityBarCode>>> entrySet = commodityBarMap.entrySet();
		Map<Long, String> subBarCodesMap = new HashMap<>();
		for (Map.Entry<Long, List<CommodityBarCode>> entry: entrySet) {
			subBarCodesMap.put(entry.getKey(), entry.getValue().stream().map(CommodityBarCode::getBarCode).collect(Collectors.joining(",")));
		}
		return subBarCodesMap;
	}
	
	public static void main(String[] args) {
		CommodityBarCode model = null;
		List<CommodityBarCode> list = new ArrayList<>();
		
		model = new CommodityBarCode();
		model.setCommodityId(1L);
		model.setBarCode("111");
		list.add(model);
		
		model = new CommodityBarCode();
		model.setCommodityId(1L);
		model.setBarCode("222");
		list.add(model);
		
		System.out.println(list);
		System.out.println(list.stream().map(CommodityBarCode::getBarCode).collect(Collectors.joining(",", "(", ")")));
		System.out.println(list.stream().map(CommodityBarCode::getBarCode).collect(Collectors.joining(",")));
		
		Map<Long, List<CommodityBarCode>> commodityBarMap = list.stream()/*.filter(Objects::nonNull)*/.collect(Collectors.groupingBy(p -> p.getCommodityId()));
		Set<Map.Entry<Long, List<CommodityBarCode>>> entrySet = commodityBarMap.entrySet();
		Map<Long, String> subBarCodesMap = new HashMap<>();
		for (Map.Entry<Long, List<CommodityBarCode>> entry: entrySet) {
			subBarCodesMap.put(entry.getKey(), entry.getValue().stream().map(CommodityBarCode::getBarCode).collect(Collectors.joining(",")));
		}
		System.out.println(subBarCodesMap);
	}
	
}
