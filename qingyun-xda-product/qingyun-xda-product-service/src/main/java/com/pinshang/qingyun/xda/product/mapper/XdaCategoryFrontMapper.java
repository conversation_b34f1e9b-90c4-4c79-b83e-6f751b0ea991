package com.pinshang.qingyun.xda.product.mapper;

import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaReCommendAppODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaCategoryBaseODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaSearchAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaCommodityAppV2ODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityAppV3ODTO;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface XdaCategoryFrontMapper {

    List<XdaCategoryBaseODTO> queryXdaCategoryList(@Param("xdaCategoryIdList") List<Long> xdaCategoryIdList, @Param("storeId") Long storeId, @Param("level") Integer level);

    List<Long> queryOftenBuyCommodityIdList(@Param("storeId") Long storeId);

    List<Long> queryCollectCommodityIdList(@Param("storeId") Long storeId);

    /**
     * 查询鲜达分类商品信息
     * @param appIDTO
     * @return
     */
    List<XdaCommodityAppODTO> queryXdaCategoryCommoditySearch(XdaSearchAppIDTO appIDTO);

    List<XdaCommodityAppV3ODTO> queryXdaCategoryCommoditySearchV3(XdaSearchAppIDTO appIDTO);

    List<XdaReCommendAppODTO> queryXdaReCommendFront(@Param("firstIdList") List<Long> firstIdList,@Param("storeId") Long storeId);

    /**
     * 查询鲜达特惠分类信息
     * @param appIDTO
     * @return
     */
    @Deprecated
    List<XdaCommodityAppV2ODTO> queryXdaThCategoryCommodity(XdaSearchAppIDTO appIDTO);
    
    /**
     * 查询鲜达特惠分类信息
     * 
     * @param appIDTO
     * @return
     */
    public List<XdaCommodityAppV3ODTO> queryXdaThCategoryCommodityV3(XdaSearchAppIDTO appIDTO);
    
}
