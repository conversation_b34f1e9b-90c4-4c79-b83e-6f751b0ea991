package com.pinshang.qingyun.xda.product.mapper;

import java.util.List;

import com.pinshang.qingyun.xda.product.dto.front.categoryFront.CategoryCommodityFieldODTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaSearchAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityAppV4ODTO;

@Mapper
@Repository
public interface XdaCategoryFrontMapperV4 {

    /**
     * 查询鲜达分类商品信息
     * @param appIDTO
     * @return
     */
    List<XdaCommodityAppV4ODTO> queryXdaCategoryCommoditySearchV4(XdaSearchAppIDTO appIDTO);

    public List<XdaCommodityAppV4ODTO> queryXdaThCategoryCommodityV4(XdaSearchAppIDTO appIDTO);

    List<CategoryCommodityFieldODTO> queryCategoryCommodityField(@Param("commodityIdList") List<Long> commodityIdList, @Param("isPfsStore") Boolean isPfsStore);
}
