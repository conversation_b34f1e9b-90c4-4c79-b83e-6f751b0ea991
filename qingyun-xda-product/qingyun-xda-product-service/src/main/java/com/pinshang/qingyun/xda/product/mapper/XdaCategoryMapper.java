package com.pinshang.qingyun.xda.product.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.dto.xdaCategory.XdaCategoryAndParentInfoODTO;
import com.pinshang.qingyun.xda.product.dto.xdaCategory.XdaCategoryListODTO;
import com.pinshang.qingyun.xda.product.dto.xdaCategory.XdaCuponODTO;
import com.pinshang.qingyun.xda.product.model.XdaCategory;

/**
 * 鲜达前台品类
 *
 * <AUTHOR>
 *
 * @date 2019年11月21日
 */
@Mapper
@Repository
public interface XdaCategoryMapper extends MyMapper<XdaCategory> {

    /**
     * 查询：同一父节点下，最大排序值
     *
     * @param parentId
     * @return
     */
    Integer selectMaxSortNoInParent(Long parentId);

    /**
     * 批量更新分类排序
     * @param list
     * @return
     */
    int batchEditXdaCategorySortNum(@Param("list")List<XdaCategory> list) ;

    /**
     * 查询所有二级分类 to 导出
     * @return
     */
    List<XdaCategoryListODTO> findAllSecondXdaCategoryListToExport();

    /**
     * 查询所有一级分类
     * @return
     */
    List<XdaCategoryListODTO> findAllFirstXdaCategoryList();
    
    /**
     * 查询  品类和父级信息  集合
     * 
     * @param categoryIdList
     * @return
     */
    public List<XdaCategoryAndParentInfoODTO> selectXdaCategoryAndParentInfoList(@Param("categoryIdList")List<Long> categoryIdList);

    /**
     * 查询  一、二级品类Id  集合
     * @return
     */
    public List<XdaCuponODTO> selectXdaCuponList();
    
}
