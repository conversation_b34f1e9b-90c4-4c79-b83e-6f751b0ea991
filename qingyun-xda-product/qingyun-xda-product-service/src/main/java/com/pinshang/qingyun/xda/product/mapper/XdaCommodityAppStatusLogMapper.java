package com.pinshang.qingyun.xda.product.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.XdaCommodityAppStatusLogIDTO;
import com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.XdaCommodityAppStatusLogODTO;
import com.pinshang.qingyun.xda.product.model.XdaCommodityAppStatusLog;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR>
 * @time: 2020/12/15 10:50
 */
@Mapper
@Repository
public interface XdaCommodityAppStatusLogMapper extends MyMapper<XdaCommodityAppStatusLog>{

    /**
     * 条件查询上下架日志
     * @param xdaCommodityAppStateLogIDTO
     * @return
     */
    List<XdaCommodityAppStatusLogODTO> selectCommodityAppStatusLogList(XdaCommodityAppStatusLogIDTO xdaCommodityAppStateLogIDTO);
}
