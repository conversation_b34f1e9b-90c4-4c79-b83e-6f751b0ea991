package com.pinshang.qingyun.xda.product.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.XdaCommodityAppStatusIDTO;
import com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.XdaCommodityAppStatusODTO;
import com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.XdaCommodityAppStatusUpODTO;
import com.pinshang.qingyun.xda.product.model.XdaCommodityAppStatus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @time: 2020/12/15 10:50
 */
@Mapper
@Repository
public interface XdaCommodityAppStatusMapper extends MyMapper<XdaCommodityAppStatus>{

    /**
     * 根据条件查询 商品上下架列表
     * @param xdaCommodityAppStateIDTO
     * @return
     */
    List<XdaCommodityAppStatusODTO> selectCommodityAppStatusList(XdaCommodityAppStatusIDTO xdaCommodityAppStateIDTO);

    /**
     * 根据商品id 查询
     * 前台品名、前台品类、送货日期、总部是否可售
     * @param commodityList
     * @return
     */
    List<XdaCommodityAppStatusUpODTO> selectCommodityTextByCommodityIdList(@Param("commodityIdList") List<Long> commodityIdList, @Param("appType") Integer appType);

    /**
     * 查询
     * @return
     */
    List<XdaCommodityAppStatusUpODTO> selectUpCommodityList(@Param("appType") Integer appType);

    /**
     * 根据客户查询产品价格方案id
     * @param storeId
     * @return
     */
    Long selectProductPriceModelIdByStoreId(@Param("storeId") Long storeId);

    /**
     * 根据商品id 批量查询商品上下架状态
     */
    List<XdaCommodityAppStatusODTO> batchSelectCommodityAppStatus(@Param("commodityIdList") List<Long> commodityIdList);

    void updateBatchByIds(@Param("list")List<XdaCommodityAppStatus> commodityStatusesToUpdate);
}
