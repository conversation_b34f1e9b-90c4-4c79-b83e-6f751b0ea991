package com.pinshang.qingyun.xda.product.mapper;

import com.pinshang.qingyun.xda.product.dto.XdaCommodityAppStatusODTO;
import com.pinshang.qingyun.xda.product.dto.front.*;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaCommodityAppV2IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaCommodityAppV2ODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3ODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityAppV3IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityAppV3ODTO;
import com.pinshang.qingyun.xda.product.model.StoreDuration;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Mapper
@Repository
public interface XdaCommodityFrontMapper {
    List<XdaCommodityAppODTO> queryXdaCommodityListForApp(XdaCommodityAppIDTO appIDTO);
    List<XdaCommodityAppODTO>  queryXdaCommodityListForAppV3(XdaShoppingCartV3IDTO appIDTO);
    List<XdaStoreSpecialPriceODTO> queryXdaStoreSpecialPriceList(@Param("orderTime") Date orderTime,@Param("storeId")Long storeId, @Param("commodityIdList")List<Long> commodityIdList);

    List<XdaStorePromotionODTO> queryXdaStorePromotionList(@Param("orderTime") Date orderTime, @Param("storeId")Long storeId, @Param("commodityIdList")List<Long> commodityIdList);

    List<XdaCommodityLimitODTO> queryXdaCommodityLimitList(@Param("orderTime") Date orderTime, @Param("commodityIdList")List<Long> commodityIdList);

    List<XdaCommodityDeliveryTimeODTO> queryXdaCommodityDeliveryTime(@Param("commodityIdList")List<Long> commodityIdList, @Param("isPfsStore") Boolean isPfsStore);

    @Select("SELECT store_id,begin_time,end_time FROM t_store_duration WHERE store_id = #{storeId}")
    StoreDuration queryStoreDuration(@Param("storeId")Long storeId);

    List<XdaShoppingCartODTO> queryXdaShoppingCartQuantity(@Param("storeId")Long storeId, @Param("commodityIdList")List<Long> commodityIdList);

    /**
     * 根据类型区分: 特惠商品和普通商品购物车数量
     * @param storeId
     * @param commodityIdList
     * @param commodityType 1-普通, 2-特惠
     * @return
     */
    List<XdaShoppingCartODTO> queryXdaShoppingCartQuantityV2(@Param("storeId")Long storeId, @Param("commodityIdList")List<Long> commodityIdList, @Param("commodityType") Integer commodityType);

    List<XdaCommodityAppStatusODTO> queryXdaCommodityAppStatus(@Param("commodityIdList") List<Long> commodityIdList);

    /**
     * 鲜达商品详情页
     * @param xdaCommodityAppV2IDTO
     * @return
     */
    @Deprecated
    List<XdaCommodityAppV2ODTO> queryXdaCommodityDetailsForApp(XdaCommodityAppV2IDTO xdaCommodityAppV2IDTO);
    
    /**
     * 鲜达商品详情页
     * @param xdaCommodityAppV2IDTO
     * @return
     */
    public List<XdaCommodityAppV3ODTO> queryXdaCommodityDetailsForAppV3(XdaCommodityAppV3IDTO idto);


    XdaShoppingCartV3ODTO getXdaCommoditySalesBoxCapacity(XdaShoppingCartV3IDTO appIDTO);
}
