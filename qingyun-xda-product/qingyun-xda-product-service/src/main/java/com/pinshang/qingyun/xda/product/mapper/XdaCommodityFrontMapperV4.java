package com.pinshang.qingyun.xda.product.mapper;

import java.util.List;

import com.pinshang.qingyun.xda.product.dto.StoreCommodityPriceODTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityAppV4IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityAppV4ODTO;

@Mapper
@Repository
public interface XdaCommodityFrontMapperV4 {
    public List<XdaCommodityAppV4ODTO> queryXdaCommodityDetailsForAppV4(XdaCommodityAppV4IDTO idto);
    
    List<XdaCommodityAppV4ODTO>  queryXdaCommodityListForAppV4(XdaCommodityAppV4IDTO appIDTO);

    List<StoreCommodityPriceODTO> getStoreCommodityPrice(@Param("storeId") Long storeId);
}
