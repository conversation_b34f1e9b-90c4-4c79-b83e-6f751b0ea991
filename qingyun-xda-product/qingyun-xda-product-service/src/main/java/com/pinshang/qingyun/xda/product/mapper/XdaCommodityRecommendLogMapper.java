package com.pinshang.qingyun.xda.product.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.dto.recommend.XdaCommodityRecommendLogIDTO;
import com.pinshang.qingyun.xda.product.dto.recommend.XdaCommodityRecommendLogODTO;
import com.pinshang.qingyun.xda.product.model.recommend.XdaCommodityRecommendLog;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface XdaCommodityRecommendLogMapper extends MyMapper<XdaCommodityRecommendLog> {

    public List<XdaCommodityRecommendLogODTO> findList(XdaCommodityRecommendLogIDTO xdaCommodityRecommendLogIDTO);

}
