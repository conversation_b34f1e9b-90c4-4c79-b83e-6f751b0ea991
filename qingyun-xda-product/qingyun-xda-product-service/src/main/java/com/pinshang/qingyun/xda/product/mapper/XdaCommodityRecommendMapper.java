package com.pinshang.qingyun.xda.product.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.dto.recommend.XdaCommodityRecommendIDTO;
import com.pinshang.qingyun.xda.product.dto.recommend.XdaCommodityRecommendODTO;
import com.pinshang.qingyun.xda.product.model.recommend.XdaCommodityRecommend;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Mapper
@Repository
public interface XdaCommodityRecommendMapper extends MyMapper<XdaCommodityRecommend> {


    public Integer deleteAll();

    public List<XdaCommodityRecommendODTO> findList(XdaCommodityRecommendIDTO xdaCommodityRecommendIDTO);
}
