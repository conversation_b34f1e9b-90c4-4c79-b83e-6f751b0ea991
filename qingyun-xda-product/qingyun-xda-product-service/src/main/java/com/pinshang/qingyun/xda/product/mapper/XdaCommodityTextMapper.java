package com.pinshang.qingyun.xda.product.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.dto.commodityText.*;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3ODTO;
import com.pinshang.qingyun.xda.product.model.XdaCommodityText;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 鲜达商品文描
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Mapper
@Repository
public interface XdaCommodityTextMapper extends MyMapper<XdaCommodityText> {
	
	/**
	 * 查询  客户-客户价格方案-商品ID 集合
	 * 
	 * @param storeId
	 * @return
	 */
	public List<Long> selectCommodityIdListByStoreId(@Param("storeId")Long storeId);
	
	/**
	 * 查询  商品文描信息列表
	 * 
	 * @param idto
	 * @return
	 */
	public List<CommodityTextInfoODTO> selectCommodityTextInfoList(SelectCommodityTextInfoPageIDTO idto);
	
    /**
	 * 查询  商品排序信息  列表
	 * 
	 * @param idto
	 * @return
	 */
	public List<CommoditySortNumInfoODTO> selectCommoditySortNumInfoList(SelectCommoditySortNumInfoPageIDTO idto);
	
	/**
	 * 查询  商品文描日志信息列表
	 * 
	 * @param idto
	 * @return
	 */
	public List<CommodityTextLogInfoODTO> selectCommodityTextLogInfoList(SelectCommodityTextLogInfoPageIDTO idto);
	
	/**
	 *  更新  商品文描-前台品名
	 *  
	 *  @param model
	 * @return
	 */
	public int updateCommodityAppName(XdaCommodityText model);
	
	/**
	 * 更新  商品文描-副标题
	 * 
	 * @param model
	 * @return
	 */
	public int updateCommoditySubName(XdaCommodityText model);
	
	/**
	 * 更新  商品文描-前台品类
	 * 
	 * @param model
	 * @return
	 */
	public int updateCommodityCategory(XdaCommodityText model);
	
	/**
	 * 更新  商品文描-标签
	 * 
	 * @param model
	 * @return
	 */
	public int updateCommodityTag(XdaCommodityText model);

	/**
	 * 更新  商品文描-排序
	 *
	 * @param model
	 * @return
	 */
	public int updateCommoditySortNum(XdaCommodityText model);

	int updateCommodityQualityStatus(XdaCommodityText model);
	
	/**
	 * 更新  商品文描-系列品
	 *
	 * @param model
	 * @return
	 */
	public int updateSerialCommodity(XdaCommodityText model);

	/**
	 * 查询  鲜达价格体系-商品文描信息  列表
	 *
	 * @param idto
	 * @return
	 */
	public List<XdaPriceStructureCommodityTextInfoODTO> selectXdaPriceStructureCommodityTextInfoList(SelectXdaPriceStructureCommodityTextInfoListIDTO idto);

	/**
	 * 查询  鲜达-商品文描-前台品类信息
	 *
	 * @param idto
	 * @return
	 */
	public XdaCommodityTextCategoryInfoODTO selectXdaCommodityTextCategoryInfo(SelectXdaCommodityTextCategoryInfoIDTO idto);
	
	/**
     * 查询  门店商品信息 列表
     * 
     * @param idto
     * @return
     */
    public List<XdaCommodityInfoODTO> selectXdaCommodityInfoList(SelectXdaCommodityInfoListIDTO idto);
    
    /**
     * 查询  商品文描标签信息  列表
     * 
     * @param idto
     * @return
     */
    public List<CommodityTextTagInfoODTO> selectCommodityTextTagInfoList(SelectCommodityTextTagInfoListIDTO idto);
	
	/**
	 * 查询   鲜达商品下拉信息  列表
	 *
	 * @param idto
	 * @return
	 */
	public List<XdaCommodityDropdownInfoODTO> selectXdaCommodityDropdownInfoList(SelectXdaCommodityDropdownInfoListIDTO idto);


	List<XdaShoppingCartV3ODTO> selectCommodityTextByShoppingCartGiftList(XdaShoppingCartV3IDTO appIDTO);

	List<CommodityTextInfoODTO> queryAllXdaCommodityText(@Param("commodityIdList") List<Long> commodityIdList);

	List<CommodityTextEsODTO> selectCommodityTextEsList(@Param("commodityIdList") List<Long> commodityIdList);

	List<XdaCommodityText> selectCommodityByCateId(@Param("cateId") Long cateId);
}
