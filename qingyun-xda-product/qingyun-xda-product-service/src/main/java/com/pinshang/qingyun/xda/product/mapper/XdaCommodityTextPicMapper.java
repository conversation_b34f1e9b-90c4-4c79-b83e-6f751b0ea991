package com.pinshang.qingyun.xda.product.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;
import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextPicListIDTO;
import com.pinshang.qingyun.xda.product.model.XdaCommodityTextPic;

/**
 * 商品文描图片		—— 注：鲜达文描图片 和 鲜到文描图片  统一由鲜到文描管理，这里只查询，不增删改
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Mapper
@Repository
public interface XdaCommodityTextPicMapper extends MyMapper<XdaCommodityTextPic> {
	
	/**
	 * 查询  商品文描图片  列表
	 * 
	 * @return
	 */
	public List<XdaCommodityTextPic> selectCommodityTextPicList(SelectCommodityTextPicListIDTO idto);
}
