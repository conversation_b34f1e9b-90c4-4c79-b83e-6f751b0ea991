package com.pinshang.qingyun.xda.product.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.dto.orderTargetSet.XdaOrderTargetSetLogQueryIDTO;
import com.pinshang.qingyun.xda.product.dto.orderTargetSet.XdaOrderTargetSetLogQueryODTO;
import com.pinshang.qingyun.xda.product.model.orderTargetSet.XdaOrderTargetSetLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 10:12
 */
@Mapper
@Repository
public interface XdaOrderTargetSetLogMapper extends MyMapper<XdaOrderTargetSetLog> {

    List<XdaOrderTargetSetLogQueryODTO> findXdaOrderTargetSetLogList(@Param("vo") XdaOrderTargetSetLogQueryIDTO vo);
}
