package com.pinshang.qingyun.xda.product.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.dto.orderTargetSet.XdaOrderTargetSetDetailODTO;
import com.pinshang.qingyun.xda.product.dto.orderTargetSet.XdaOrderTargetSetQueryIDTO;
import com.pinshang.qingyun.xda.product.dto.orderTargetSet.XdaOrderTargetSetQueryODTO;
import com.pinshang.qingyun.xda.product.model.orderTargetSet.XdaOrderTargetSet;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:36
 */
@Mapper
@Repository
public interface XdaOrderTargetSetMapper extends MyMapper<XdaOrderTargetSet> {

    /**
     * 根据客户id和送货日期查询当天是否有符合的订货目标
     * @param storeId
     * @param orderTime
     * @return
     */
    XdaOrderTargetSet findOrderTargetByStoreIdAndOrderTime(@Param("storeId") Long storeId, @Param("orderTime") Date orderTime);

    /***
     * 获取订货目标设置列表
     * @param vo
     * @return
     */
    List<XdaOrderTargetSetQueryODTO> findXdaOrderTargetSetList(@Param("vo") XdaOrderTargetSetQueryIDTO vo);

    /***
     * 获取订货目标详情
     * @param id
     * @return
     */
    XdaOrderTargetSetDetailODTO findXdaOrderTargetSetDetailById(@Param("id") Long id);
}
