package com.pinshang.qingyun.xda.product.mapper;

import java.util.List;

import com.pinshang.qingyun.xda.product.dto.front.XdaSerialCommodityODTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.SelectCommodityDropdownInfoList4SerialCommodityIDTO;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.SelectSerialCommodityIDTO;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.SelectSerialCommodityLogInfoPageIDTO;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.SerialCommodityInfoODTO;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.SerialCommodityLogInfoODTO;
import com.pinshang.qingyun.xda.product.model.XdaSerialCommodity;

/**
 * 系列品
 * 
 * <AUTHOR>
 *
 * @date 2020年12月22日
 */
@Mapper
@Repository
public interface XdaSerialCommodityMapper extends MyMapper<XdaSerialCommodity> {
	
	/**
	 * 分页查询  系列品日志信息  列表
	 * 
	 * @param idto
	 * @return
	 */
	public List<SerialCommodityLogInfoODTO> selectSerialCommodityLogInfoList(SelectSerialCommodityLogInfoPageIDTO idto);
	
	/**
	 * 查询  系列品
	 * 
	 * @param idto
	 * @return
	 */
	public XdaSerialCommodity selectSerialCommodity(SelectSerialCommodityIDTO idto);
	
	/**
	 * 查询  系列品信息  列表
	 * 
	 * @param serialCommodityId
	 * @return
	 */
	public List<SerialCommodityInfoODTO> selectSerialCommodityInfoList(@Param("serialCommodityId")Long serialCommodityId);
	
	/**
	 * 查询  商品下拉信息  列表 （for系列品）
	 * 
	 * @param idto
	 * @return
	 */
	public List<SerialCommodityInfoODTO> selectCommodityDropdownInfoList4SerialCommodity(SelectCommodityDropdownInfoList4SerialCommodityIDTO idto);

	//APP
	List<XdaSerialCommodityODTO> querySerialCommodityListFront(@Param("commodityIdList")List<Long> commodityIdList, @Param("xdaSecondCategoryId")Long xdaSecondCategoryId);
}
