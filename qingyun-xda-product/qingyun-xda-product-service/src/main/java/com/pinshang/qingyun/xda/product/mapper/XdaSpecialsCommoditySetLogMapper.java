package com.pinshang.qingyun.xda.product.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.dto.specialsCommoditySet.XdaSpecialsCommoditySetLogQueryIDTO;
import com.pinshang.qingyun.xda.product.dto.specialsCommoditySet.XdaSpecialsCommoditySetLogQueryODTO;
import com.pinshang.qingyun.xda.product.model.specialsCommoditySet.XdaSpecialsCommoditySetLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/7 10:56
 */
@Mapper
@Repository
public interface XdaSpecialsCommoditySetLogMapper extends MyMapper<XdaSpecialsCommoditySetLog> {

    List<XdaSpecialsCommoditySetLogQueryODTO> findSpecialsCommoditySetLogList(@Param("vo") XdaSpecialsCommoditySetLogQueryIDTO vo);
}
