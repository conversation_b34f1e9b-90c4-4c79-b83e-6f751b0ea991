package com.pinshang.qingyun.xda.product.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.dto.specialsCommoditySet.*;
import com.pinshang.qingyun.xda.product.model.specialsCommoditySet.XdaSpecialsCommoditySet;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:35
 */
@Mapper
@Repository
public interface XdaSpecialsCommoditySetMapper extends MyMapper<XdaSpecialsCommoditySet> {

     List<XdaSpecialsCommoditySetQueryODTO> findSpecialsCommoditySetList(@Param("vo") XdaSpecialsCommoditySetQueryIDTO vo);

     XdaSpecialsCommoditySetDetailODTO findSpecialsCommoditySetDetails(@Param("id") Long id);

     List<XdaSpecialsCommoditydropDownODTO> selectCommodityDropDownList(@Param("vo") XdaSpecialsCommoditydropDownIDTO vo);

     int batchUpdateSpecialsCommoditySet(@Param("list") List<XdaSpecialsCommoditySet> list);

     /**
      * 客户id和送货日期查询符合条件的商品集合
      *
      * @param storeId
      * @param isPfsStore
      * @return
      */
     List<XdaSpecialsCommoditySet> findCommodityListByStoreAndOrderTime(@Param("storeId") Long storeId,@Param("isPfsStore")  Boolean isPfsStore);
}
