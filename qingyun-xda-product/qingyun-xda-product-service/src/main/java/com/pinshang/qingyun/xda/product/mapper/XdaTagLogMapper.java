package com.pinshang.qingyun.xda.product.mapper;

import com.pinshang.qingyun.xda.product.dto.commodityTag.XdaCommodityTagLogIDTO;
import com.pinshang.qingyun.xda.product.dto.commodityTag.XdaCommodityTagLogODTO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.model.XdaTagLog;

import java.util.List;

/**
 * 标签日志
 */
@Mapper
@Repository
public interface XdaTagLogMapper extends MyMapper<XdaTagLog> {

    /**
     * 商品自定义标签日志
     * @param commodityTagLogIDTO
     * @return
     */
    List<XdaCommodityTagLogODTO> selectXdaCommodityTagLogList(XdaCommodityTagLogIDTO commodityTagLogIDTO);
	
}
