package com.pinshang.qingyun.xda.product.mapper;

import com.pinshang.qingyun.xda.product.dto.commodityTag.XdaCommodityTagIDTO;
import com.pinshang.qingyun.xda.product.dto.commodityTag.XdaCommodityTagODTO;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.model.XdaTag;

import java.util.List;

/**
 * 标签
 */
@Mapper
@Repository
public interface XdaTagMapper extends MyMapper<XdaTag> {

    /**
     * 查询商品个性标签列表
     * @param commodityTagIDTO
     * @return
     */
    List<XdaCommodityTagODTO> selectXdaCommodityTagList(XdaCommodityTagIDTO commodityTagIDTO);
}
