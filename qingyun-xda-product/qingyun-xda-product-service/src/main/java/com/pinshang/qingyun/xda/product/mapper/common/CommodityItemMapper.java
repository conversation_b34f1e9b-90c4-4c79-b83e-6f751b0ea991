package com.pinshang.qingyun.xda.product.mapper.common;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityItemEntry;
import com.pinshang.qingyun.xda.product.model.common.CommodityItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by hhf on 2019/9/3.
 */
@Mapper
@Repository
public interface CommodityItemMapper extends MyMapper<CommodityItem> {

    /**
     * 根据商品id 查询子商品集合
     * @param commodityId
     * @return
     */
    List<CommodityItemEntry> findCommodityItemListByCommodityId(@Param("commodityId")Long commodityId);

    /**
     * 根据商品id批量查询子商品集合
     */
    List<CommodityItemEntry> findCommodityItemListByCommodityIdList(@Param("commodityIdList")List<Long> commodityIdList);
}
