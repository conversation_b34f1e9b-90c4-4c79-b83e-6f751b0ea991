package com.pinshang.qingyun.xda.product.mapper.deliveryDate;


import com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityLogDTO;
import com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityLogVO;
import com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodityLog;

import java.util.List;

public interface PfOrderCommodityLogDao {

	/**
	 * 查询getBomProcessList
	 * @param pfOrderCommodityLogVO 查询参数
	 * @return getBomProcessList
	 */
	List<XdaOrderCommodityLogDTO> getPfOrderCommodityLogList(XdaOrderCommodityLogVO pfOrderCommodityLogVO);


	/**
	 * 插入PfOrderCommodityLog到数据库,包括null值
	 * @param value
	 * @return
	 */
	int insertPfOrderCommodityLog(XdaOrderCommodityLog value);
}