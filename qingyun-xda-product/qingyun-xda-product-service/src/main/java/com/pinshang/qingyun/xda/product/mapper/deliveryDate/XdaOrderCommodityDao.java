package com.pinshang.qingyun.xda.product.mapper.deliveryDate;
import com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityDTO;
import com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityVO;
import com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodity;
import java.util.List;
public interface XdaOrderCommodityDao{

	List<XdaOrderCommodityDTO> getXdaOrderCommodityList(XdaOrderCommodityVO xdaOrderCommodityVO);

	List<XdaOrderCommodity> getDistinctXdaOrderCommodityList();

	/**
	 * 通过XdaOrderCommodity的id获得XdaOrderCommodity对象
	 * @param id
	 * @return
	 */
	XdaOrderCommodity selectXdaOrderCommodityByCommodityId(Long id);

	/**
	 * 获得XdaOrderCommodity数据的总行数
	 * @return
	 */
    long getXdaOrderCommodityRowCount();
	/**
	 * 获得XdaOrderCommodity数据集合
	 * @return
	 */
    List<XdaOrderCommodity> selectXdaOrderCommodity();
	/**
	 * 获得一个XdaOrderCommodity对象,以参数XdaOrderCommodity对象中不为空的属性作为条件进行查询
	 * @param obj
	 * @return
	 */
    XdaOrderCommodity selectXdaOrderCommodityByObj(XdaOrderCommodity obj);
	/**
	 * 通过XdaOrderCommodity的id获得XdaOrderCommodity对象
	 * @param id
	 * @return
	 */
    XdaOrderCommodity selectXdaOrderCommodityById(Long id);

	/**
	 * 通过XdaOrderCommodity的id获得XdaOrderCommodity对象
	 * @param id
	 * @return
	 */
	XdaOrderCommodity selectCommodityById(Long id);
	/**
	 * 插入XdaOrderCommodity到数据库,包括null值
	 * @param value
	 * @return
	 */
    int insertXdaOrderCommodity(XdaOrderCommodity value);
	/**
	 * 插入XdaOrderCommodity中属性值不为null的数据到数据库
	 * @param value
	 * @return
	 */
    int insertNonEmptyXdaOrderCommodity(XdaOrderCommodity value);
	/**
	 * 批量插入XdaOrderCommodity到数据库,包括null值
	 * @param value
	 * @return
	 */
    int insertXdaOrderCommodityByBatch(List<XdaOrderCommodity> value);
	/**
	 * 通过XdaOrderCommodity的id删除XdaOrderCommodity
	 * @param id
	 * @return
	 */
    int deleteXdaOrderCommodityById(Long id);
	/**
	 * 通过XdaOrderCommodity的id更新XdaOrderCommodity中的数据,包括null值
	 * @param enti
	 * @return
	 */
    int updateXdaOrderCommodityById(XdaOrderCommodity enti);
	/**
	 * 通过XdaOrderCommodity的id更新XdaOrderCommodity中属性不为null的数据
	 * @param enti
	 * @return
	 */
    int updateNonEmptyXdaOrderCommodityById(XdaOrderCommodity enti);
}