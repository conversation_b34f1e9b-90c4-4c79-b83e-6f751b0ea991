package com.pinshang.qingyun.xda.product.mapper.deliveryDate;
import com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityLogVO;
import com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodityLog;
import java.util.List;
public interface XdaOrderCommodityLogDao{

	/**
	 * 查询getBomProcessList
	 * @param xdaOrderCommodityLogVO 查询参数
	 * @return getBomProcessList
	 */
	List<XdaOrderCommodityLog> getXdaOrderCommodityLogList(XdaOrderCommodityLogVO xdaOrderCommodityLogVO);

	/**
	 * 获得XdaOrderCommodityLog数据的总行数
	 * @return
	 */
    long getXdaOrderCommodityLogRowCount();
	/**
	 * 获得XdaOrderCommodityLog数据集合
	 * @return
	 */
    List<XdaOrderCommodityLog> selectXdaOrderCommodityLog();
	/**
	 * 获得一个XdaOrderCommodityLog对象,以参数XdaOrderCommodityLog对象中不为空的属性作为条件进行查询
	 * @param obj
	 * @return
	 */
    XdaOrderCommodityLog selectXdaOrderCommodityLogByObj(XdaOrderCommodityLog obj);
	/**
	 * 通过XdaOrderCommodityLog的id获得XdaOrderCommodityLog对象
	 * @param id
	 * @return
	 */
    XdaOrderCommodityLog selectXdaOrderCommodityLogById(Long id);
	/**
	 * 插入XdaOrderCommodityLog到数据库,包括null值
	 * @param value
	 * @return
	 */
    int insertXdaOrderCommodityLog(XdaOrderCommodityLog value);
	/**
	 * 插入XdaOrderCommodityLog中属性值不为null的数据到数据库
	 * @param value
	 * @return
	 */
    int insertNonEmptyXdaOrderCommodityLog(XdaOrderCommodityLog value);
	/**
	 * 批量插入XdaOrderCommodityLog到数据库,包括null值
	 * @param value
	 * @return
	 */
    int insertXdaOrderCommodityLogByBatch(List<XdaOrderCommodityLog> value);
	/**
	 * 通过XdaOrderCommodityLog的id删除XdaOrderCommodityLog
	 * @param id
	 * @return
	 */
    int deleteXdaOrderCommodityLogById(Long id);
	/**
	 * 通过XdaOrderCommodityLog的id更新XdaOrderCommodityLog中的数据,包括null值
	 * @param enti
	 * @return
	 */
    int updateXdaOrderCommodityLogById(XdaOrderCommodityLog enti);
	/**
	 * 通过XdaOrderCommodityLog的id更新XdaOrderCommodityLog中属性不为null的数据
	 * @param enti
	 * @return
	 */
    int updateNonEmptyXdaOrderCommodityLogById(XdaOrderCommodityLog enti);
}