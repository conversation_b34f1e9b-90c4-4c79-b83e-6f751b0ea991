package com.pinshang.qingyun.xda.product.model;

import com.pinshang.qingyun.base.po.BaseTimePO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * @description: 客户线路组时间
 * @author: hhf
 * @time: 2020/5/28 16:28
 */
@NoArgsConstructor
@Entity
@Data
@Table(name="t_store_duration")
public class StoreDuration extends BaseTimePO{

    /**开始时间 **/
    private String beginTime;
    /**截止时间 **/
    private String endTime;
    /**客户id **/
    private Long storeId;
    /**客户code **/
    private String storeCode;
    /**20 **/
    private Long createId;
    /**操作人 **/
    private String createName;
    /**线路组 **/
    private Long lineGroupId;
    /**打印状态 **/
    private Integer printStatus;
    @Column(name = "printTime")
    private Date printTime;
    private String oldLineGroupTime ;

    public StoreDuration(String beginTime, String endTime, Long storeId, String storeCode, Long createId, String createName, Long lineGroupId, Integer printStatus, Date printTime, String oldLineGroupTime, Date createTime, Date updateTime) {
        this.beginTime = beginTime;
        this.endTime = endTime;
        this.storeId = storeId;
        this.storeCode = storeCode;
        this.createId = createId;
        this.createName = createName;
        this.lineGroupId = lineGroupId;
        this.printStatus = printStatus;
        this.printTime = printTime;
        this.oldLineGroupTime = oldLineGroupTime;
        this.setCreateTime(createTime);
        this.setUpdateTime(updateTime);
    }

}

