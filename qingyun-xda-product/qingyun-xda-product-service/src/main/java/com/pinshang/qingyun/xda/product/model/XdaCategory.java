package com.pinshang.qingyun.xda.product.model;

import java.util.Date;

import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BasePO;

/**
 * 鲜达前台品类
 *
 * <AUTHOR>
 *
 * @date 2019年11月21日
 */
@Data
@NoArgsConstructor
@Table(name="t_xda_category")
public class XdaCategory extends BasePO {
    private Long parentId;		// 父节点ID
    private String cateName;	// 品类名称
    private Integer cateLevel;	// 品类级别
    private String picUrl;		// 品类图片
    private Integer sortNum;	// 排序号
    private Integer status;		// 状态：1-正常,0-逻辑删除

    public XdaCategory(Long parentId, String cateName, Integer cateLevel, String picUrl, Integer sortNum, Integer status, Long createId, Date createTime,Long updateId,Date updateTime) {
        this.parentId = parentId;
        this.cateName = cateName;
        this.cateLevel = cateLevel;
        this.picUrl = picUrl;
        this.sortNum = sortNum;
        this.status = status;
        this.setCreateId(createId);
        this.setCreateTime(createTime);
        this.setUpdateId(updateId);
        this.setUpdateTime(updateTime);
    }

    //新增
    public static XdaCategory forInsert(Long parentId, String cateName, Integer cateLevel, String picUrl, Integer sortNum, Long userId,Date date){
        return new XdaCategory(parentId,cateName,cateLevel,picUrl,sortNum,1,userId,date,userId,date);
    }

    //修改排序
    public static XdaCategory forUpdateSortNum(Long id,Integer sortNum,Long userId,Date date){
        XdaCategory xdaCategory = new XdaCategory();
        xdaCategory.setId(id);
        xdaCategory.setSortNum(sortNum);
        xdaCategory.setUpdateId(userId);
        xdaCategory.setUpdateTime(date);
        return xdaCategory;
    }

    //修改
    public static XdaCategory forUpdate(Long id,String cateName,Integer sortNum,Long userId,Date date){
        XdaCategory xdaCategory = new XdaCategory();
        xdaCategory.setId(id);
        xdaCategory.setCateName(cateName);
        xdaCategory.setSortNum(sortNum);
        xdaCategory.setUpdateId(userId);
        xdaCategory.setUpdateTime(date);
        return xdaCategory;
    }

    //更新图片
    public static XdaCategory forUpdatePicUrl(Long id,String picUrl,Long userId,Date date){
        XdaCategory xdaCategory = new XdaCategory();
        xdaCategory.setId(id);
        xdaCategory.setPicUrl(picUrl);
        xdaCategory.setUpdateId(userId);
        xdaCategory.setUpdateTime(date);
        return xdaCategory;
    }

    //删除品类
    public static XdaCategory forDelete(Long id,Long userId,Date date){
        XdaCategory xdaCategory = new XdaCategory();
        xdaCategory.setId(id);
        xdaCategory.setStatus(0);
        xdaCategory.setUpdateId(userId);
        xdaCategory.setUpdateTime(date);
        return xdaCategory;
    }
}