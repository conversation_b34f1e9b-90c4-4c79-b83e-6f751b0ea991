package com.pinshang.qingyun.xda.product.model;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.Date;


/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @time: 2020/12/16 9:55
 */
@Data
@NoArgsConstructor
@Table(name="t_xda_commodity_app_status")
public class XdaCommodityAppStatus extends BaseIDPO {
    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * 上下架状态：0-上架，1-下架
     */
    private Integer appStatus;

    /**
     * 批发上下架状态：0-上架，1-下架
     */
    private Integer pfAppStatus;

    private Long createId;

    private Date createTime;

    public XdaCommodityAppStatus(Long commodityId) {
    	this.commodityId = commodityId;
    }
    
    public XdaCommodityAppStatus(Long commodityId, Integer appStatus) {
    	this.commodityId = commodityId;
    	this.appStatus = appStatus;
    }

    /**
     * 新增
     * @param commodityId
     * @param appStatus
     * @param createId
     * @param createTime
     */
    public XdaCommodityAppStatus(Long commodityId, Integer appStatus, Long createId,
                                 Date createTime) {
        this.commodityId = commodityId;
        this.appStatus = appStatus;
        this.createId = createId;
        this.createTime = createTime;
    }

    public static XdaCommodityAppStatus forUpdateAppStatus(Long id,Integer appStatus){
        XdaCommodityAppStatus xdaCommodityAppStatus = new XdaCommodityAppStatus();
        xdaCommodityAppStatus.setId(id);
        xdaCommodityAppStatus.setAppStatus(appStatus);
        return xdaCommodityAppStatus;
    }

    public static XdaCommodityAppStatus forUpdateDownAppStatus(Long id){
        XdaCommodityAppStatus xdaCommodityAppStatus = new XdaCommodityAppStatus();
        xdaCommodityAppStatus.setId(id);
        xdaCommodityAppStatus.setAppStatus(1);
        xdaCommodityAppStatus.setPfAppStatus(1);
        return xdaCommodityAppStatus;
    }
}
