package com.pinshang.qingyun.xda.product.model;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.Date;


/**
 * @author: ch<PERSON><PERSON><PERSON>
 * @time: 2020/12/16 9:55
 */
@Data
@NoArgsConstructor
@Table(name = "t_xda_commodity_app_status_log")
public class XdaCommodityAppStatusLog extends BaseIDPO {
    /**
     * 商品id
     */
    private Long commodityId;

    /**
     * 上下架状态：0-上架，1-下架
     */
    private Integer appStatus;

    /**
     * 原因
     */
    private String reason;

    private Long createId;

    private Date createTime;


    /**
     * 新增
     *
     * @param commodityId
     * @param appStatus
     * @param reason
     * @param createId
     * @param createTime
     */
    public XdaCommodityAppStatusLog(Long commodityId, Integer appStatus, String reason, Long createId,
                                    Date createTime) {
        this.commodityId = commodityId;
        this.appStatus = appStatus;
        this.reason = reason;
        this.createId = createId;
        this.createTime = createTime;
    }
}
