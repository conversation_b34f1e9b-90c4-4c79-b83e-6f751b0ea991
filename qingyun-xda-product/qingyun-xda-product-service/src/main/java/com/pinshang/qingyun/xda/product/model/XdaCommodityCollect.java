package com.pinshang.qingyun.xda.product.model;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.Date;

@Data
@Table(name = "t_xda_commodity_collect")
@NoArgsConstructor
public class XdaCommodityCollect extends BaseIDPO{

    private Long commodityId;
    private Long storeId;
    private Date createTime;

    public XdaCommodityCollect(Long commodityId, Long storeId) {
        this.commodityId = commodityId;
        this.storeId = storeId;
        this.createTime = new Date();
    }

}
