package com.pinshang.qingyun.xda.product.model;

import java.util.Date;

import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BasePO;

/**
 * 鲜达商品文描
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@NoArgsConstructor
@Table(name="t_xda_commodity_text")
public class XdaCommodityText extends BasePO {
    private Long commodityId;				// 商品ID
    private String commodityAppName;		// 前台品名
    private String commoditySubName;		// 副标题
    private Long firstCategoryId;			// 一级前台品类ID
    private Long secondCategoryId;			// 二级前台品类ID
    private Long tagId;						// 标签ID
    private Integer sortNum;				// 排序号
    private Date sortNumUpdateTime;			// 排序更新时间
    /**是否显示保质期： 1-是 0-否**/
    private Integer qualityStatus;
    
    public XdaCommodityText(Long commodityId) {
    	this.commodityId = commodityId;
    }
    
    // 前台品名
    public static XdaCommodityText forInsertCommodityAppName(Long commodityId, String commodityAppName, Long createId, Date createTime) {
    	return forUpdateCommodityAppName(commodityId, commodityAppName, createId, createTime).setCreateInfo(createId, createTime);
    }
    public static XdaCommodityText forUpdateCommodityAppName(Long commodityId, String commodityAppName, Long updateId, Date updateTime) {
    	XdaCommodityText model = new XdaCommodityText().setUpdateInfo(updateId, updateTime);
    	model.setCommodityId(commodityId);
    	model.setCommodityAppName(commodityAppName);
    	return model;
    }
    
    // 副标题
    public static XdaCommodityText forInsertCommoditySubName(Long commodityId, String commoditySubName, Long createId, Date createTime) {
    	return forUpdateCommoditySubName(commodityId, commoditySubName, createId, createTime).setCreateInfo(createId, createTime);
    }
    public static XdaCommodityText forUpdateCommoditySubName(Long commodityId, String commoditySubName, Long updateId, Date updateTime) {
    	XdaCommodityText model = new XdaCommodityText().setUpdateInfo(updateId, updateTime);
    	model.setCommodityId(commodityId);
    	model.setCommoditySubName(commoditySubName);
    	return model;
    }

    // 前提品类
    public static XdaCommodityText forInsertCategory(Long commodityId, Long firstCategoryId, Long secondCategoryId, Long createId, Date createTime) {
    	return forUpdateCategory(commodityId, firstCategoryId, secondCategoryId, createId, createTime).setCreateInfo(createId, createTime);
    }
    public static XdaCommodityText forUpdateCategory(Long commodityId, Long firstCategoryId, Long secondCategoryId, Long updateId, Date updateTime) {
    	XdaCommodityText model = new XdaCommodityText().setUpdateInfo(updateId, updateTime);
    	model.setCommodityId(commodityId);
    	model.setFirstCategoryId(firstCategoryId);
    	model.setSecondCategoryId(secondCategoryId);
    	return model;
    }
    
    // 标签
    public static XdaCommodityText forInsertTag(Long commodityId, Long tagId, Long createId, Date createTime) {
    	return forUpdateTag(commodityId, tagId, createId, createTime).setCreateInfo(createId, createTime);
    }
    public static XdaCommodityText forUpdateTag(Long commodityId, Long tagId, Long updateId, Date updateTime) {
    	XdaCommodityText model = new XdaCommodityText().setUpdateInfo(updateId, updateTime);
    	model.setCommodityId(commodityId);
    	model.setTagId(tagId);
    	return model;
    }
    
    // 分类页排序
    public static XdaCommodityText forInsertSortNum(Long commodityId, Integer sortNum, Long createId, Date createTime) {
    	return forUpdateSortNum(commodityId, sortNum, createId, createTime).setCreateInfo(createId, createTime);
    }
    public static XdaCommodityText forUpdateSortNum(Long commodityId, Integer sortNum, Long updateId, Date updateTime) {
    	XdaCommodityText model = new XdaCommodityText().setUpdateInfo(updateId, updateTime);
    	model.setCommodityId(commodityId);
    	model.setSortNum(sortNum);
    	model.setSortNumUpdateTime(updateTime);
    	return model;
    }

    //是否显示保质期
    public static XdaCommodityText forInsertQualityStatus(Long commodityId, Integer qualityStatus, Long createId, Date createTime) {
        return forUpdateQualityStatus(commodityId, qualityStatus, createId, createTime).setCreateInfo(createId, createTime);
    }
    public static XdaCommodityText forUpdateQualityStatus(Long commodityId,Integer qualityStatus, Long updateId, Date updateTime) {
        XdaCommodityText model = new XdaCommodityText().setUpdateInfo(updateId, updateTime);
        model.setCommodityId(commodityId);
        model.setQualityStatus(qualityStatus);
        return model;
    }
    
    private XdaCommodityText setCreateInfo(Long createId, Date createTime) {
    	this.setCreateId(createId);
    	this.setCreateTime(createTime);
    	return this;
    }
    private XdaCommodityText setUpdateInfo(Long updateId, Date updateTime) {
    	this.setUpdateId(updateId);
    	this.setUpdateTime(updateTime);
    	return this;
    }
    
}