package com.pinshang.qingyun.xda.product.model;

import java.util.Date;

import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BaseSimplePO;

/**
 * 鲜达商品文描日志
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@NoArgsConstructor
@Table(name="t_xda_commodity_text_log")
public class XdaCommodityTextLog extends BaseSimplePO {
    private Long commodityId;			// 商品ID
    private Integer operateType;		// 类型		—— 参见 CommodityTextOperateTypeEnum
    private String oldValue;			// 旧值
    private String newValue;			// 新值
    private String detail;				// 详情
    
    public XdaCommodityTextLog(Long commodityId, Integer operateType, String oldValue, String newValue, String detail, Long createId, Date createTime) {
    	this.setCreateId(createId);
    	this.setCreateTime(createTime);
    	this.commodityId = commodityId;
    	this.operateType = operateType;
    	this.oldValue = oldValue;
    	this.newValue = newValue;
    	this.detail = detail;
    }

}