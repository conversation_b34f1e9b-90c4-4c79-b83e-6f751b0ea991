package com.pinshang.qingyun.xda.product.model;

import java.util.Date;

import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BaseSimplePO;

/**
 * 商品文描图片					—— 注：鲜达文描图片 和 鲜到文描图片  统一由鲜到文描管理，这里只查询，不增删改
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@NoArgsConstructor
@Table(name="t_xda_commodity_text_pic")
public class XdaCommodityTextPic extends BaseSimplePO {
    private Long commodityId;			// 商品ID
    private Integer picType;			// 图片类型：1-图片、2-长图、3-视频
    private String picUrl;				// URL
    private Integer isDefault;			// 是否默认：0-否、1-是
    
    public enum PicTypeEnums {
    	PIC(1),
    	LONG_PIC(2),
    	VIDEO(3),
    	;
    	private Integer code;
        PicTypeEnums(Integer code) {
        	this.code = code;
        }
        public Integer getCode() {
            return code;
        }
    }
    public enum IsDefaultEnums {
    	NO(0),
    	YES(1),
    	;
    	private Integer code;
    	IsDefaultEnums(Integer code) {
        	this.code = code;
        }
        public Integer getCode() {
            return code;
        }
    }
}