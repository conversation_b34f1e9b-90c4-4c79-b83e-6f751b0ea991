package com.pinshang.qingyun.xda.product.model;

import java.util.Date;

import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BaseSimplePO;

/**
 * 系列品
 * 
 * <AUTHOR>
 *
 * @date 2020年12月22日
 */
@Data
@NoArgsConstructor
@Table(name = "t_xda_serial_commodity")
public class XdaSerialCommodity extends BaseSimplePO {
	private Long commodityId;				// 商品ID
    private Long serialCommodityId;			// 系列品-主品ID：commodityId==serialCommodityId则为主品
    private String serialCommodityCode;		// 系列品-编码

    public XdaSerialCommodity(Long commodityId) {
		this.commodityId = commodityId;
	}
	
	public static XdaSerialCommodity forInsert(Long commodityId, Long serialCommodityId, String serialCommodityCode, Long createId, Date createTime) {
		XdaSerialCommodity model = new XdaSerialCommodity();
		model.setCreateId(createId);
		model.setCreateTime(createTime);
		model.commodityId = commodityId;
		model.serialCommodityId = serialCommodityId;
		model.serialCommodityCode = serialCommodityCode;
		return model;
	}
	
	public static XdaSerialCommodity forUpdateSerialCommodityId(Long id, Long serialCommodityId) {
		XdaSerialCommodity model = new XdaSerialCommodity();
		model.id = id;
		model.serialCommodityId = serialCommodityId;
		return model;
	}

}