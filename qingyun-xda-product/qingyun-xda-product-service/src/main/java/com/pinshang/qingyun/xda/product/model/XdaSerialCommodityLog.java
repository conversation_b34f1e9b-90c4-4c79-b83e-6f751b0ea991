package com.pinshang.qingyun.xda.product.model;

import java.util.Date;

import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BaseSimplePO;

/**
 * 系列品日志
 * 
 * <AUTHOR>
 *
 * @date 2020年12月22日
 */
@Data
@NoArgsConstructor
@Table(name = "t_xda_serial_commodity_log")
public class XdaSerialCommodityLog extends BaseSimplePO {
	private Integer operateType; // 操作类型： —— 参见 CommodityTextOperateTypeEnum
	private Long commodityId; // 商品ID
	private Long firstCategoryId; // 前台一级品类ID
	private Long secondCategoryId; // 前台二级品类ID
	private Long serialCommodityId; // 系列品-主品ID
	private String serialCommodityCode; // 系列品-编码
	
	public static XdaSerialCommodityLog forInsert(Integer operateType, XdaCommodityText commodityText,
			Long serialCommodityId, String serialCommodityCode, Long createId, Date createTime) {
		XdaSerialCommodityLog model = new XdaSerialCommodityLog();
		model.setCreateId(createId);
		model.setCreateTime(createTime);
		model.operateType = operateType;
		if (null != commodityText) {
			model.commodityId = commodityText.getCommodityId();
			model.firstCategoryId = commodityText.getFirstCategoryId();
			model.secondCategoryId = commodityText.getSecondCategoryId();
		}
		model.serialCommodityId = serialCommodityId;
		model.serialCommodityCode = serialCommodityCode;
		return model;
	}

}