package com.pinshang.qingyun.xda.product.model;

import java.util.Date;

import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BasePO;

/**
 * 标签
 *
 * <AUTHOR>
 *
 * @date 2019年12月26日
 */
@Data
@NoArgsConstructor
@Table(name="t_xda_tag")
public class XdaTag extends BasePO {
	/**
	 * 标签名称
	 */
	private String tagName;
	/**
	 * 标签背景色值
	 */
	private String tagBgColor;
	/**
	 * 状态：0-停用、1-启用
	 */
	private Integer status;

	public XdaTag(String tagName) {
		this.tagName = tagName;
	}

	/**
	 * 新增商品自定义标签
	 * @param tagName
	 * @param tagBgColor
	 * @param status
	 * @param createId
	 * @param createTime
	 * @param updateId
	 * @param updateTime
	 * @return
	 */
	public XdaTag saveXdaTag(String tagName,String tagBgColor,Integer status,Long createId,Date createTime,Long updateId,Date updateTime){
		XdaTag xdTag = new XdaTag();
		xdTag.setTagName(tagName);
		xdTag.setTagBgColor(tagBgColor);
		xdTag.setStatus(status);
		xdTag.setCreateId(createId);
		xdTag.setCreateTime(createTime);
		xdTag.setUpdateId(updateId);
		xdTag.setUpdateTime(updateTime);
		return xdTag;
	}

	/**
	 * 修改自定义标签
	 * @param tagName
	 * @param tagBgColor
	 * @param updateId
	 * @param updateTime
	 * @return
	 */
	public XdaTag updateXdaTag(String tagName,String tagBgColor,Long updateId,Date updateTime){
		XdaTag xdaTag = new XdaTag();
		xdaTag.setTagName(tagName);
		xdaTag.setTagBgColor(tagBgColor);
		xdaTag.setUpdateId(updateId);
		xdaTag.setUpdateTime(updateTime);
		return xdaTag;
	}

	public XdaTag updateXdaTagStatus(Integer status,Long updateId,Date updateTime){
		XdaTag xdaTag = new XdaTag();
		xdaTag.setStatus(status);
		xdaTag.setUpdateId(updateId);
		xdaTag.setUpdateTime(updateTime);
		return xdaTag;
	}
}