package com.pinshang.qingyun.xda.product.model;

import java.util.Date;

import javax.persistence.Table;

import lombok.Data;
import lombok.NoArgsConstructor;

import com.pinshang.qingyun.base.po.BaseIDPO;

/**
 * 标签日志
 *
 * <AUTHOR>
 *
 * @date 2019年12月26日
 */
@Data
@NoArgsConstructor
@Table(name="t_xda_tag_log")
public class XdaTagLog extends BaseIDPO {

	/**
	 * OperateTypeNewEnum
	 */
	private Integer operateType;
	/**
	 * 标签ID
	 */
	private Long tagId;
	/**
	 * 标签名称
	 */
	private String tagName;
	/**
	 * 标签背景色值
	 */
	private String tagBgColor;

	private Long createId;

	private Date createTime;

	public XdaTagLog saveXdaTagLog(Integer operateType,Long tagId,String tagName,String tagBgColor,Long createId,Date createTime){
		XdaTagLog xdaTagLog = new XdaTagLog();
		xdaTagLog.setOperateType(operateType);
		xdaTagLog.setTagId(tagId);
		xdaTagLog.setTagName(tagName);
		xdaTagLog.setTagBgColor(tagBgColor);
		xdaTagLog.setCreateId(createId);
		xdaTagLog.setCreateTime(createTime);
		return xdaTagLog;
	}
}