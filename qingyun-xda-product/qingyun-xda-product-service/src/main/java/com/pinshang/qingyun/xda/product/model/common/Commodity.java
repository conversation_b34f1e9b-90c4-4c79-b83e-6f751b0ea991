package com.pinshang.qingyun.xda.product.model.common;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Table;
import java.math.BigDecimal;

@Data
@Table(name = "t_commodity")
public class Commodity extends BaseIDPO {
    /** 商品编码 */
    private String commodityCode;
    /** 商品名称 */
    private String commodityName;

    private BigDecimal commodityPackageSpec;

    /**
     * 商品类型-1-普通商品，2-组合商品
     */
    private Integer productType;
}
