package com.pinshang.qingyun.xda.product.model.common;

import com.pinshang.qingyun.base.po.BaseSimplePO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * Created by hhf on 2019/9/3.
 * 组合商品明细
 */
@Entity
@Data
@Table(name="t_commodity_item")
public class CommodityItem extends BaseSimplePO {

    /**主商品id**/
    private Long commodityId;

    /**子商品id**/
    private Long commodityItemId;

    /**数量**/
    private BigDecimal commodityNum;

}
