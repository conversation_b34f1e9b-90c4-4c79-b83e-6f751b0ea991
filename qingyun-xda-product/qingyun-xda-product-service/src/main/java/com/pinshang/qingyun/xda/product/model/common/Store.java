package com.pinshang.qingyun.xda.product.model.common;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "t_store")
@Data
@NoArgsConstructor
public class Store extends BaseIDPO{
    /**所属企业ID**/
    private Long enterpriseId;
    /**店铺编号**/
    private String storeCode;
    /**店铺名称**/
    private String storeName;
    /**店铺短名称**/
    private String storeShortName;
    /**店铺助记码**/
    private String storeAid;
    /**旧代码**/
    private String storeOldCode;
    /**描述**/
    private String storeDestribe;
    /**店铺类型Id**/
    private Long storeTypeId;
    /**状态 (0启用1停用-1刚创建了客户待指定线路2待指定结账客户)**/
    private Integer storeStatus;
    /**部门ID**/
    private Long storeCompanyId;
    /**所属区域ID**/
    private Long storeDistrictId;
    /**渠道ID**/
    private Long storeChannelId;
    /**线路ID**/
    private Long storeLineId;
    /**送货员**/
    private Long deliverymanId;
    /**销售员**/
    private Long salesmanId;
    /**督导ID**/
    private Long supervisorId;
    /**办事处主任ID**/
    private Long officeDirectorId;
    /**大区经理ID**/
    private Long regionManagerId;

    /**打印份数\r\n(客户要求的小单子份数)**/
    private BigDecimal printCopies;
    /**是否显示单价**/
    private Integer showPriceRetail;
    /**是否显示金额**/
    private Integer moneyIsShow;

    /**国家ID**/
    private Long countryId;
    /**省ID**/
    private Long provinceId;
    /**市ID**/
    private Long cityId;
    /**区ID**/
    private Long areaId;
    /**详细地址:街道等**/
    private String detailAddress;
    /**送货地址**/
    private String deliveryAddress;
    /**联系人**/
    private String storeLinkman;
    /**电话**/
    private String linkmanTel;
    /**手机**/
    private String linkmanMobile;
    /**传真**/
    private String linkmanFax;
    /**邮箱**/
    private String linkmanEmail;
    /**收货时间**/
    private String receiveTime;
    /**检验报告**/
    private Integer isTestReport;
    /**打印批次**/
    private String printDeliveryBatch;
    /**创建时间**/
    private Date createTime;
    /**更新时间**/
    private Date updateTime;
    /**是否配置结算信息（-1尚未配置，0已配置）**/
    private Integer settlementStatus;
    /**打印订单的时候是否需要复写0表示需要复写1表示不需要复写**/
    private Integer rewriteable;
    /****/
    private Long createUserId  ;
    /**客户类型1-内部，2-外部**/
    private Integer storeType ;
    /**下浮状态： 0-不下浮、1-下浮**/
    private Integer downPriceStatus;
    /**起送金额**/
    private BigDecimal minDeliveryAmount;
    private Long updateId  ;
    /** 自动充值开关 1、开启 2、关闭 **/
    private Integer isRecharge;
}
