package com.pinshang.qingyun.xda.product.model.deliveryDate;
public class XdaOrderCommodity {
    private Long id;//v
    private Long updateId;//修改人ID
    private String commodityCode;//商品编号
    private java.util.Date createTime;//创建时间
    private String updateName;//修改人
    private String spec;
    private java.util.Date updateTime;//修改时间
    private Long commodityId;//商品ID
    private Long createId;//创建人
    private String deliveryDateRangeCode;//2-8
    private String deliveryDateRangeValue;//T+2~T+8
    private String pfDeliveryDateRangeCode;//2-8
    private String pfDeliveryDateRangeValue;//T+2~T+8
    private String commodityName;//商品名称
    private String createName;//创建人
    public XdaOrderCommodity() {
        super();
    }
    public XdaOrderCommodity(Long id,Long updateId,String commodityCode,java.util.Date createTime,String updateName,String spec,java.util.Date updateTime,Long commodityId,Long createId,String deliveryDateRangeCode,String deliveryDateRangeValue,String commodityName,String createName) {
        super();
        this.id = id;
        this.updateId = updateId;
        this.commodityCode = commodityCode;
        this.createTime = createTime;
        this.updateName = updateName;
        this.spec = spec;
        this.updateTime = updateTime;
        this.commodityId = commodityId;
        this.createId = createId;
        this.deliveryDateRangeCode = deliveryDateRangeCode;
        this.deliveryDateRangeValue = deliveryDateRangeValue;
        this.commodityName = commodityName;
        this.createName = createName;
    }
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUpdateId() {
        return this.updateId;
    }

    public void setUpdateId(Long updateId) {
        this.updateId = updateId;
    }

    public String getCommodityCode() {
        return this.commodityCode;
    }

    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
    }

    public String getUpdateName() {
        return this.updateName;
    }

    public void setUpdateName(String updateName) {
        this.updateName = updateName;
    }

    public String getSpec() {
        return this.spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public java.util.Date getUpdateTime() {
        return this.updateTime;
    }

    public void setUpdateTime(java.util.Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCommodityId() {
        return this.commodityId;
    }

    public void setCommodityId(Long commodityId) {
        this.commodityId = commodityId;
    }

    public Long getCreateId() {
        return this.createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getDeliveryDateRangeCode() {
        return this.deliveryDateRangeCode;
    }

    public void setDeliveryDateRangeCode(String deliveryDateRangeCode) {
        this.deliveryDateRangeCode = deliveryDateRangeCode;
    }

    public String getDeliveryDateRangeValue() {
        return this.deliveryDateRangeValue;
    }

    public void setDeliveryDateRangeValue(String deliveryDateRangeValue) {
        this.deliveryDateRangeValue = deliveryDateRangeValue;
    }

    public String getCommodityName() {
        return this.commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public String getCreateName() {
        return this.createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }
    public String getPfDeliveryDateRangeCode() {
        return pfDeliveryDateRangeCode;
    }

    public void setPfDeliveryDateRangeCode(String pfDeliveryDateRangeCode) {
        this.pfDeliveryDateRangeCode = pfDeliveryDateRangeCode;
    }

    public String getPfDeliveryDateRangeValue() {
        return pfDeliveryDateRangeValue;
    }

    public void setPfDeliveryDateRangeValue(String pfDeliveryDateRangeValue) {
        this.pfDeliveryDateRangeValue = pfDeliveryDateRangeValue;
    }
}
