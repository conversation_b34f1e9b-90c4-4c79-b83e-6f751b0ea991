package com.pinshang.qingyun.xda.product.model.deliveryDate;
public class XdaOrderCommodityLog {
    private Long id;//主键
    private String commodityCode;//商品编码
    private java.util.Date createTime;//创建时间
    private Long createId;//创建人
    private String deliveryDateRangeCode;//2-8
    private String deliveryDateRangeValue;//T+2~T+8
    private String opType;//操作类型
    private Integer changePrice;//是否可变价
    private String spec;//规格
    private String commodityName;//商品名称
    private String createName;
    public XdaOrderCommodityLog() {
        super();
    }
    public XdaOrderCommodityLog(Long id,String commodityCode,java.util.Date createTime,Long createId,String deliveryDateRangeCode,String deliveryDateRangeValue,String opType,Integer changePrice,String spec,String commodityName,String createName) {
        super();
        this.id = id;
        this.commodityCode = commodityCode;
        this.createTime = createTime;
        this.createId = createId;
        this.deliveryDateRangeCode = deliveryDateRangeCode;
        this.deliveryDateRangeValue = deliveryDateRangeValue;
        this.opType = opType;
        this.changePrice = changePrice;
        this.spec = spec;
        this.commodityName = commodityName;
        this.createName = createName;
    }
    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCommodityCode() {
        return this.commodityCode;
    }

    public void setCommodityCode(String commodityCode) {
        this.commodityCode = commodityCode;
    }

    public java.util.Date getCreateTime() {
        return this.createTime;
    }

    public void setCreateTime(java.util.Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateId() {
        return this.createId;
    }

    public void setCreateId(Long createId) {
        this.createId = createId;
    }

    public String getDeliveryDateRangeCode() {
        return this.deliveryDateRangeCode;
    }

    public void setDeliveryDateRangeCode(String deliveryDateRangeCode) {
        this.deliveryDateRangeCode = deliveryDateRangeCode;
    }

    public String getDeliveryDateRangeValue() {
        return this.deliveryDateRangeValue;
    }

    public void setDeliveryDateRangeValue(String deliveryDateRangeValue) {
        this.deliveryDateRangeValue = deliveryDateRangeValue;
    }

    public String getOpType() {
        return this.opType;
    }

    public void setOpType(String opType) {
        this.opType = opType;
    }

    public Integer getChangePrice() {
        return this.changePrice;
    }

    public void setChangePrice(Integer changePrice) {
        this.changePrice = changePrice;
    }

    public String getSpec() {
        return this.spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getCommodityName() {
        return this.commodityName;
    }

    public void setCommodityName(String commodityName) {
        this.commodityName = commodityName;
    }

    public String getCreateName() {
        return this.createName;
    }

    public void setCreateName(String createName) {
        this.createName = createName;
    }

}
