package com.pinshang.qingyun.xda.product.model.orderTargetSet;

import com.pinshang.qingyun.base.po.BasePO;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.xda.product.dto.orderTargetSet.XdaOrderTargetSetAddIDTO;
import com.pinshang.qingyun.xda.product.dto.orderTargetSet.XdaOrderTargetSetDisableIDTO;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:14
 * 鲜达特惠商品设置表
 */
@Data
@Table(name = "t_xda_order_target_set")
@Entity
@NoArgsConstructor
public class XdaOrderTargetSet extends BasePO {

    private Long storeId; //客户id
    private String orderTargetCode;//订货目标编码
    private BigDecimal orderTargetToDay; //单日订货目标
    private Date deliveryStartDate;//送货日期开始日期
    private Date deliveryEndDate;//送货日期结束日期
    private String loopSet;//循环设置
    private Integer deliveryDateRangeType;//日期范围类型：1-常规设置 2-循环设置
    private Integer orderTargetStatus;//状态：0-停用 1-启用
    private String importNo;//批次
    private String createName;//创建人名称
    private String updateName;//修改人名称


    public XdaOrderTargetSet(Long storeId,String orderTargetCode, BigDecimal orderTargetToDay, Date deliveryStartDate, Date deliveryEndDate, String loopSet, Integer deliveryDateRangeType) {
        this.storeId = storeId;
        this.orderTargetCode=orderTargetCode;
        this.orderTargetToDay = orderTargetToDay;
        this.deliveryStartDate = deliveryStartDate;
        this.deliveryEndDate = deliveryEndDate;
        this.loopSet = loopSet;
        this.deliveryDateRangeType = deliveryDateRangeType;
    }


    public static XdaOrderTargetSet toAddEntry(XdaOrderTargetSetAddIDTO vo,String orderTargetCode,Date date){
        XdaOrderTargetSet set = new XdaOrderTargetSet(vo.getStoreId(),orderTargetCode,vo.getOrderTargetToDay(),vo.getDeliveryStartDate(),
                vo.getDeliveryEndDate(),vo.getLoopSet(),vo.getDeliveryDateRangeType());
        set.setOrderTargetStatus(1);
        set.setCreateId(vo.getUserId());
        set.setCreateName(vo.getUserName());
        set.setCreateTime(date);
        set.setUpdateId(vo.getUserId());
        set.setUpdateName(vo.getUserName());
        set.setUpdateTime(date);
        return set;
    }

    public static XdaOrderTargetSet toDisableEntry(XdaOrderTargetSetDisableIDTO vo, Date date){
        XdaOrderTargetSet set = new XdaOrderTargetSet();
        set.setOrderTargetStatus(0);
        set.setUpdateId(vo.getUserId());
        set.setUpdateName(vo.getUserName());
        set.setUpdateTime(date);
        return set;
    }

    public void toDisableEntry(XdaOrderTargetSet set){
        this.setOrderTargetStatus(set.getOrderTargetStatus());
        this.setUpdateId(set.getUpdateId());
        this.setUpdateName(set.getUpdateName());
        this.setUpdateTime(set.getUpdateTime());
    }

    public static XdaOrderTargetSet toImportEntry(String[] vo,String batchNo,Long userId,String userName,Date date){
        String[] deliveryDate=vo[1].split("-");
        XdaOrderTargetSet set = new XdaOrderTargetSet(Long.valueOf(vo[0]),null,new BigDecimal(vo[2]), DateUtil.parseDate(deliveryDate[0],"yyyyMMdd"),
                DateUtil.parseDate(deliveryDate[1],"yyyyMMdd"),vo[3], StringUtils.isBlank(vo[3]) ? 1 : 2);
        set.setOrderTargetStatus(1);
        set.setImportNo(batchNo);
        set.setCreateId(userId);
        set.setCreateName(userName);
        set.setCreateTime(date);
        set.setUpdateId(userId);
        set.setUpdateName(userName);
        set.setUpdateTime(date);
        return set;
    }
}
