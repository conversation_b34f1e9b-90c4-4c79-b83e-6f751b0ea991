package com.pinshang.qingyun.xda.product.model.orderTargetSet;

import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.base.po.BasePO;
import com.pinshang.qingyun.xda.product.dto.orderTargetSet.XdaOrderTargetSetAddIDTO;
import com.pinshang.qingyun.xda.product.dto.orderTargetSet.XdaOrderTargetSetDisableIDTO;
import com.pinshang.qingyun.xda.product.enums.XdaOrderTargetSetEnums;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:14
 * 鲜达特惠商品设置日志表
 */
@Data
@Table(name = "t_xda_order_target_set_log")
@Entity
@NoArgsConstructor
public class XdaOrderTargetSetLog extends BaseIDPO {

    private Integer operationType;//操作类型：0-停用 1-新增 2-导入
    private String operationTypeName;
    private Long storeId; //客户id
    private String orderTargetCode;//订货目标编码
    private BigDecimal orderTargetToDay; //单日订货目标
    private Date deliveryStartDate;//送货日期开始日期
    private Date deliveryEndDate;//送货日期结束日期
    private String loopSet;//循环设置
    private Integer deliveryDateRangeType;//日期范围类型：1-常规设置 2-循环设置
    private Integer orderTargetStatus;//状态：0-停用 1-启用
    private String importNo;//批次
    private Long createId;
    private String createName;//创建人名称
    private Date createTime;//修改人名称


    public XdaOrderTargetSetLog(Long storeId, String orderTargetCode, BigDecimal orderTargetToDay, Date deliveryStartDate, Date deliveryEndDate, String loopSet, Integer deliveryDateRangeType, Integer orderTargetStatus, String importNo) {
        this.storeId = storeId;
        this.orderTargetCode = orderTargetCode;
        this.orderTargetToDay = orderTargetToDay;
        this.deliveryStartDate = deliveryStartDate;
        this.deliveryEndDate = deliveryEndDate;
        this.loopSet = loopSet;
        this.deliveryDateRangeType = deliveryDateRangeType;
        this.orderTargetStatus = orderTargetStatus;
        this.importNo = importNo;
    }

    public static XdaOrderTargetSetLog toAddEntry(XdaOrderTargetSet s, XdaOrderTargetSetEnums e){
        XdaOrderTargetSetLog log=new XdaOrderTargetSetLog(s.getStoreId(),s.getOrderTargetCode(),s.getOrderTargetToDay(),s.getDeliveryStartDate(),s.getDeliveryEndDate(),
                s.getLoopSet(),s.getDeliveryDateRangeType(),s.getOrderTargetStatus(),s.getImportNo());
        log.setOperationType(e.getValue());
        log.setOperationTypeName(e.getName());
        log.setCreateId(s.getUpdateId());
        log.setCreateName(s.getUpdateName());
        log.setCreateTime(s.getUpdateTime());
        return log;
    }
}
