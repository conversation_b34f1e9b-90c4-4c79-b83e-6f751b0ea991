package com.pinshang.qingyun.xda.product.model.recommend;

import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.util.Date;

@Table(name="t_xda_commodity_recommend")
@Data
@NoArgsConstructor
public class XdaCommodityRecommend extends BaseIDPO {


    private Long commodityId; // 商品id
    private Long createId; // 创建人ID
    private Date createTime; // 创建时间

    public XdaCommodityRecommend(Long id) {
        super.setId(id);
    }

    public static XdaCommodityRecommend forInsert(Long commodityId,
                                                  Long createId, Date createTime) {
        XdaCommodityRecommend xdCommodityProcessGroup = new XdaCommodityRecommend(commodityId);
        xdCommodityProcessGroup.commodityId = commodityId;
        xdCommodityProcessGroup.createId = createId;
        xdCommodityProcessGroup.createTime = createTime;
        return xdCommodityProcessGroup;
    }
}