package com.pinshang.qingyun.xda.product.model.recommend;

import com.pinshang.qingyun.base.enums.XdOperateTypeEnums;
import com.pinshang.qingyun.base.po.BaseIDPO;
import lombok.Data;

import javax.persistence.Table;
import java.util.Date;

@Table(name="t_xda_commodity_recommend_log")
@Data
public class XdaCommodityRecommendLog extends BaseIDPO {

    private Long commodityId; // 前台品类广告id
    private Integer operateType; // 操作类型：1-新增，2-删除
    private Long createId; // 创建人ID
    private Date createTime; // 创建时间

    public static XdaCommodityRecommendLog forInsert(Long commodityId, Long createId, Date createTime) {
        XdaCommodityRecommendLog xdCommodityProcessGroupLog = new XdaCommodityRecommendLog();
        xdCommodityProcessGroupLog.commodityId = commodityId;
        xdCommodityProcessGroupLog.operateType= XdOperateTypeEnums.ADD.getCode();
        xdCommodityProcessGroupLog.createId = createId;
        xdCommodityProcessGroupLog.createTime = createTime;
        return xdCommodityProcessGroupLog;
    }

    public static XdaCommodityRecommendLog forDelete(Long commodityId, Long createId, Date createTime) {
        XdaCommodityRecommendLog xdCommodityProcessGroupLog = new XdaCommodityRecommendLog();
        xdCommodityProcessGroupLog.commodityId = commodityId;
        xdCommodityProcessGroupLog.operateType= XdOperateTypeEnums.DEL.getCode();
        xdCommodityProcessGroupLog.createId = createId;
        xdCommodityProcessGroupLog.createTime = createTime;
        return xdCommodityProcessGroupLog;
    }

}