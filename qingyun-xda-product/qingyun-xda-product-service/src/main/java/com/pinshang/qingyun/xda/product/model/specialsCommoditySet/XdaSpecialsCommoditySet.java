package com.pinshang.qingyun.xda.product.model.specialsCommoditySet;

import com.pinshang.qingyun.base.po.BasePO;
import com.pinshang.qingyun.xda.product.dto.specialsCommoditySet.XdaSpecialsCommoditySetAddIDTO;
import com.pinshang.qingyun.xda.product.dto.specialsCommoditySet.XdaSpecialsCommoditySetModifyIDTO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:14
 * 鲜达特惠商品设置表
 */
@Data
@Table(name = "t_xda_specials_commodity_set")
@Entity
@NoArgsConstructor
public class XdaSpecialsCommoditySet extends BasePO {

    private Long commodityId; //商品id
    private BigDecimal commoditySpecialsPrice; //商品特惠单价
    private Integer commodityLimit;//商品限量
    private String importNo;//导入批次号
    private String createName;//创建人名称
    private String updateName;//修改人名称


    public XdaSpecialsCommoditySet(Long commodityId) {
        this.commodityId = commodityId;
    }

    public XdaSpecialsCommoditySet(Long commodityId, BigDecimal commoditySpecialsPrice, Integer commodityLimit) {
        this.commodityId = commodityId;
        this.commoditySpecialsPrice = commoditySpecialsPrice;
        this.commodityLimit = commodityLimit;
    }

    public XdaSpecialsCommoditySet(BigDecimal commoditySpecialsPrice, Integer commodityLimit) {
        this.commoditySpecialsPrice = commoditySpecialsPrice;
        this.commodityLimit = commodityLimit;
    }

    public static XdaSpecialsCommoditySet toAddEntry(XdaSpecialsCommoditySetAddIDTO vo, Date date){
        XdaSpecialsCommoditySet set = new XdaSpecialsCommoditySet(vo.getCommodityId(),vo.getCommoditySpecialsPrice(),vo.getCommodityLimit());
        set.setCreateId(vo.getUserId());
        set.setCreateName(vo.getUserName());
        set.setCreateTime(date);
        set.setUpdateId(vo.getUserId());
        set.setUpdateName(vo.getUserName());
        set.setUpdateTime(date);
        return set;
    }

    public static XdaSpecialsCommoditySet toImportEntry(String[] vo,String batchNo,Long userId,String userName, Date date){
        XdaSpecialsCommoditySet set = new XdaSpecialsCommoditySet(Long.valueOf(vo[0]),new BigDecimal(vo[1]),Integer.valueOf(vo[2]));
        set.setImportNo(batchNo);
        set.setCreateId(userId);
        set.setCreateName(userName);
        set.setCreateTime(date);
        set.setUpdateId(userId);
        set.setUpdateName(userName);
        set.setUpdateTime(date);
        return set;
    }

    public void setUserInfoEntry(Date date,Long userId,String userName){
        this.setCreateId(userId);
        this.setCreateName(userName);
        this.setCreateTime(date);
        this.setUpdateId(userId);
        this.setUpdateName(userName);
        this.setUpdateTime(date);
    }

    public static XdaSpecialsCommoditySet toUpdateEntry(XdaSpecialsCommoditySetModifyIDTO vo, Date date){
        XdaSpecialsCommoditySet set = new XdaSpecialsCommoditySet(vo.getCommoditySpecialsPrice(),vo.getCommodityLimit());
        set.setUpdateId(vo.getUserId());
        set.setUpdateName(vo.getUserName());
        set.setUpdateTime(date);
        return set;
    }

    public void toUpdateEntry(XdaSpecialsCommoditySet vo){
        this.setCommoditySpecialsPrice(vo.getCommoditySpecialsPrice());
        this.setCommodityLimit(vo.getCommodityLimit());
        this.setUpdateId(vo.getUpdateId());
        this.setUpdateName(vo.getUpdateName());
        this.setUpdateTime(vo.getUpdateTime());
    }
}
