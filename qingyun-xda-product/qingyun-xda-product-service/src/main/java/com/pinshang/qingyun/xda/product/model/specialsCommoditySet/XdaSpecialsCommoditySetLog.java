package com.pinshang.qingyun.xda.product.model.specialsCommoditySet;

import com.pinshang.qingyun.base.po.BaseIDPO;
import com.pinshang.qingyun.xda.product.enums.XdaSpecialsCommoditySetEnums;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:14
 * 鲜达特惠商品设置日志表
 */
@Data
@Table(name = "t_xda_specials_commodity_set_log")
@Entity
@NoArgsConstructor
public class XdaSpecialsCommoditySetLog extends BaseIDPO {
    private Integer operationType;//操作类型：0-停用 1-新增 2-导入
    private String operationTypeName;
    private Long commodityId; //商品id
    private BigDecimal commoditySpecialsPrice; //商品特惠单价
    private Integer commodityLimit;//商品限量
    private String importNo;//导入批次号
    private Long createId;
    private String createName;//创建人名称
    private Date createTime;//修改人名称


    public XdaSpecialsCommoditySetLog(Long commodityId, BigDecimal commoditySpecialsPrice, Integer commodityLimit, String importNo, Long createId, String createName, Date createTime) {
        this.commodityId = commodityId;
        this.commoditySpecialsPrice = commoditySpecialsPrice;
        this.commodityLimit = commodityLimit;
        this.importNo = importNo;
        this.createId = createId;
        this.createName = createName;
        this.createTime = createTime;
    }

    public static XdaSpecialsCommoditySetLog toAddEntry(XdaSpecialsCommoditySet s, XdaSpecialsCommoditySetEnums e){
        XdaSpecialsCommoditySetLog log = new XdaSpecialsCommoditySetLog(s.getCommodityId(),s.getCommoditySpecialsPrice(),s.getCommodityLimit(),s.getImportNo(),
                s.getUpdateId(),s.getUpdateName(),s.getUpdateTime());
        log.setOperationType(e.getValue());
        log.setOperationTypeName(e.getName());
        return log;
    }
}
