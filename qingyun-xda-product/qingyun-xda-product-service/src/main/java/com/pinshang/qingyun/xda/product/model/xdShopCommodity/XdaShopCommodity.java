package com.pinshang.qingyun.xda.product.model.xdShopCommodity;

import com.pinshang.qingyun.base.po.BasePO;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@Table(name = "t_xda_shop_commodity")
//前置仓商品零销价格
public class XdaShopCommodity extends BasePO {

    //门店id
    private Long shopId;

    //商品id
    private Long commodityId;

    //商品价格
    private BigDecimal commodityPrice;

    //'app状态：0-上架，1-下架'
    private Integer appStatus;

    //库存数量
    private BigDecimal stockQuantity;
    private Integer stockNumber;

    //APP上架时间
    private Date appUpTime;

    //APP下架时间
    private Date appDownTime;

    //加权平均价
    private BigDecimal weightPrice;

    //门店调价标识:0-默认未调价,1-单独调价
    private BigDecimal modifyPriceFlag;

    //商品状态:1-正常,0-淘汰
    private Integer commodityStatus;

    //是否可售:1-是,0-否
    private Integer commoditySaleStatus;

    //是否可采:1-可采,0-否,不可采  默认 1
    private Integer commodityPurchaseStatus;

    //冻结库存  默认0.00
    private Integer freezeNumber;

    //'质检库存'
    private Integer qualityNumber;

    //质检数量
    private BigDecimal qualityQuantity;

    //第一次入库日期
    private Date inStorageDate;

    //本月期初库存  默认 0.000
    private BigDecimal monthInitStock;

    private Date firstUpTime;

    public XdaShopCommodity(Long shopId, Long commodityId, BigDecimal commodityPrice, Integer appStatus,
                            BigDecimal stockQuantity, Integer stockNumber, Date appUpTime, Date appDownTime, BigDecimal weightPrice,
                            BigDecimal modifyPriceFlag, Integer commodityStatus, Integer commoditySaleStatus, Integer commodityPurchaseStatus,
                            Integer freezeNumber, Integer qualityNumber, BigDecimal qualityQuantity, Date inStorageDate, BigDecimal monthInitStock,
                            Long createId, Date createTime, Long updateId, Date updateTime
    ) {
        this.shopId = shopId;
        this.commodityId = commodityId;
        this.commodityPrice = commodityPrice;
        this.appStatus = appStatus;
        this.stockQuantity = stockQuantity;
        this.stockNumber = stockNumber;
        this.appUpTime = appUpTime;
        this.appDownTime = appDownTime;
        this.weightPrice = weightPrice;
        this.modifyPriceFlag = modifyPriceFlag;
        this.commodityStatus = commodityStatus;
        this.commoditySaleStatus = commoditySaleStatus;
        this.commodityPurchaseStatus = commodityPurchaseStatus;
        this.freezeNumber = freezeNumber;
        this.qualityNumber = qualityNumber;
        this.qualityQuantity = qualityQuantity;
        this.inStorageDate = inStorageDate;
        this.monthInitStock = monthInitStock;
        super.setCreateId(createId);
        super.setCreateTime(createTime);
        super.setUpdateId(updateId);
        super.setUpdateTime(updateTime);

    }

    public static XdaShopCommodity forInsert(Long shopId, Long commodityId, BigDecimal commodityPrice, Long createId, Date createTime){
        return new XdaShopCommodity(shopId,commodityId,commodityPrice, 1,BigDecimal.ZERO,0,null,null,BigDecimal.ZERO,BigDecimal.ZERO,
                1,1,0,0,0,BigDecimal.ZERO,null, BigDecimal.ZERO,createId,createTime,createId,createTime);
    }

    public static XdaShopCommodity updateStatus(Date appUpTime, Date appDownTime, Date updateTime, Long updateId, Integer appStatus){
        XdaShopCommodity xdShopCommodity = new XdaShopCommodity();
        if(null != appUpTime){
            xdShopCommodity.setAppUpTime(appUpTime);
        }
        if(null != appDownTime){
            xdShopCommodity.setAppDownTime(appDownTime);
        }
        xdShopCommodity.setUpdateTime(updateTime);
        xdShopCommodity.setUpdateId(updateId);
        xdShopCommodity.setAppStatus(appStatus);
        return xdShopCommodity;
    }
    public static XdaShopCommodity updateFirstUpTime(Date firstUpTime, Long updateId, Date updateTime) {
        XdaShopCommodity xdShopCommodity = new XdaShopCommodity();
        xdShopCommodity.setFirstUpTime(firstUpTime);
        xdShopCommodity.setUpdateId(updateId);
        xdShopCommodity.setUpdateTime(updateTime);
        return xdShopCommodity;
    }


}
