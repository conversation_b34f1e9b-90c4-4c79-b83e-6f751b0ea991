package com.pinshang.qingyun.xda.product.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.base.enums.SalesStatusEnums;
import com.pinshang.qingyun.base.enums.order.ProductTypeEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.product.dto.commodity.CommodityItemODTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryDetailIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryIDTO;
import com.pinshang.qingyun.storage.dto.tob.CommodityInventoryODTO;
import com.pinshang.qingyun.storage.service.tob.ToBClient;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityItemEntry;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityItemEntryList;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityItemMapper;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityMapper;
import com.pinshang.qingyun.xda.product.model.common.Commodity;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/3/29
 */
@Service
public class BStockService {

    @Autowired
    private ToBClient toBClient;

    @Autowired
    private CommodityItemMapper commodityItemMapper;
    @Autowired
    private CommodityMapper commodityMapper;

    /**
     * 查询B端库存
     *
     * @param orderTime
     * @param orderCommodityList
     * @param logisticsCenterId
     * @return
     */
    public Map<Long, CommodityInventoryODTO> getbStockMap(Date orderTime, List<CommodityInventoryDetailIDTO> orderCommodityList, Integer businessType, Long logisticsCenterId) {
        CommodityInventoryIDTO commodityInventoryIDTO = new CommodityInventoryIDTO();
        commodityInventoryIDTO.setOrderTime(orderTime != null ? orderTime : DateUtil.parseDate(DateTimeUtil.defaultDeliveryDate(), "yyyy-MM-dd"));
        commodityInventoryIDTO.setOrderCommodityList(orderCommodityList);
        commodityInventoryIDTO.setType(businessType);
        if (Objects.equals(BusinessTypeEnums.B_COUNTRY.getCode(), businessType)) {
            QYAssert.notNull(logisticsCenterId, "物流中心Id不能为空");
        }
        commodityInventoryIDTO.setLogisticsCenterId(logisticsCenterId);
        List<CommodityInventoryODTO> toBStockList = toBClient.queryCommodityWithBomInventory(commodityInventoryIDTO);
        Map<Long, CommodityInventoryODTO> toBStockMap;
        if(CollectionUtils.isNotEmpty(toBStockList)){
            toBStockMap = toBStockList.stream().collect(Collectors.toMap(CommodityInventoryODTO::getCommodityId, Function.identity()));
        } else {
            toBStockMap = new HashMap<>();
        }
        return toBStockMap;
    }

    /**
     * 实时查询鲜达详情页是否有库存
     * @return
     */
    public Integer setCommodityDetailSoldOut(Date orderTime, Long commodityId, Integer salesStatus, Integer businessType, Long logisticsCenterId){
        Integer resultSalesStatus = SalesStatusEnums.SOLD_OUT.getCode();
        Commodity commodity = commodityMapper.selectByPrimaryKey(commodityId);
        if(Objects.equals(2, commodity.getProductType())){
            // 查询组合商品的子商品
            List<CommodityItemEntryList> commodityItemList = this.getCommodityItemListByCommodityIdList(Collections.singletonList(commodityId));
            if(CollectionUtils.isNotEmpty(commodityItemList)){
                // 构建查询库存所需的商品列表
                List<CommodityInventoryDetailIDTO> orderCommodityList = commodityItemList.stream()
                        .flatMap(x->x.getCommodityItemEntry().stream())
                        .map(p -> new CommodityInventoryDetailIDTO(Long.valueOf(p.getCommodityItemId()), p.getCommodityNum(), ProductTypeEnum.NORMAL.getCode()))
                        .collect(Collectors.toList());

                Map<Long, CommodityInventoryODTO> bstockMap = getbStockMap(orderTime, orderCommodityList, businessType, logisticsCenterId);

                CommodityItemEntryList commodityItemEntry = commodityItemList.get(0);
                BigDecimal minStock = calculateMinStock(commodityItemEntry.getCommodityItemEntry(), bstockMap);
                resultSalesStatus = minStock.compareTo(BigDecimal.ONE) < 0 ? SalesStatusEnums.SOLD_OUT.getCode() : SalesStatusEnums.NORMAL.getCode();
            }
        }else {
            List<CommodityInventoryDetailIDTO> orderCommodityList = new ArrayList<>();
            CommodityInventoryDetailIDTO detailIDTO = new CommodityInventoryDetailIDTO();
            detailIDTO.setCommodityId(commodityId);
            detailIDTO.setQuantity(new BigDecimal("0.01"));
            detailIDTO.setLevel(ProductTypeEnum.NORMAL.getCode());
            orderCommodityList.add(detailIDTO);

            Map<Long, CommodityInventoryODTO> bstockMap = getbStockMap(orderTime, orderCommodityList, businessType, logisticsCenterId);
            if(bstockMap != null && bstockMap.get(commodityId) != null){
                CommodityInventoryODTO commodityInventoryODTO = bstockMap.get(commodityId);
                if(commodityInventoryODTO.getHaveInventory()){
                    resultSalesStatus = SalesStatusEnums.NORMAL.getCode();
                }else {
                    resultSalesStatus = SalesStatusEnums.SOLD_OUT.getCode();
                }
            }
        }
        return  !SalesStatusEnums.NOT_DATE.getCode().equals(resultSalesStatus) ? resultSalesStatus : salesStatus;
    }



    /**
     * 计算组合商品最小库存
     * 比如添加购物车时 3C=3（1A+2B），此时A、B的库存分别为 2、7时，
     * 则C的库存min（2/1=2，7/2=3.5）=2，则提示：库存不足，余量为2
     *
     * @param commodityItemList         子商品列表
     * @param commodityInventoryODTOMap 子商品库存
     * @return 组合商品最小库存
     */
    public BigDecimal calculateMinStock(List<CommodityItemODTO> commodityItemList, Map<Long, CommodityInventoryODTO> commodityInventoryODTOMap) {
        BigDecimal minStock = BigDecimal.valueOf(Long.MAX_VALUE);
        for (CommodityItemODTO commodityItem : commodityItemList) {
            String commodityItemId = commodityItem.getCommodityItemId();
            CommodityInventoryODTO commodityInventory = commodityInventoryODTOMap.get(Long.valueOf(commodityItemId));
            if (Objects.nonNull(commodityInventory)) {
                BigDecimal inventoryNum = commodityInventory.getInventoryQuantity();
                BigDecimal stockDivided = inventoryNum.divide(commodityItem.getCommodityNum(), 0, RoundingMode.DOWN);
                minStock = minStock.min(stockDivided);
            }
        }
        return minStock;
    }

    /**
     * 根据组合商品id 批量查询子商品列表
     */
    public List<CommodityItemEntryList> getCommodityItemListByCommodityIdList(List<Long> commodityIdList) {
        if (CollectionUtils.isEmpty(commodityIdList)) {
            return Collections.emptyList();
        }
        List<CommodityItemEntry> commodityItemList = commodityItemMapper.findCommodityItemListByCommodityIdList(commodityIdList);
        if (CollectionUtils.isEmpty(commodityItemList)) {
            return Collections.emptyList();
        }
        return commodityItemList
                .stream()
                .collect(Collectors.groupingBy(CommodityItemEntry::getCommodityId))
                .entrySet()
                .stream()
                .map(entry -> {
                    CommodityItemEntryList commodityItemEntryList = new CommodityItemEntryList();
                    commodityItemEntryList.setCommodityId(entry.getKey());
                    List<CommodityItemODTO> odtoList = BeanCloneUtils.copyTo(entry.getValue(), CommodityItemODTO.class);
                    commodityItemEntryList.setCommodityItemEntry(odtoList);
                    return commodityItemEntryList;
                }).collect(Collectors.toList());
    }

    /**
     * 根据商品等级合并订单数量
     */
    public List<CommodityInventoryDetailIDTO> mergeOrderItemsByLevel(List<CommodityInventoryDetailIDTO> orderCommodityList) {
        if (CollectionUtils.isEmpty(orderCommodityList)) {
            return Collections.emptyList();
        }
        Map<String, BigDecimal> productMap = orderCommodityList.stream()
                .collect(Collectors.groupingBy(
                        idto -> idto.getCommodityId() + "_" + idto.getLevel(),
                        Collectors.reducing(BigDecimal.ZERO, CommodityInventoryDetailIDTO::getQuantity, BigDecimal::add)
                ));
        List<CommodityInventoryDetailIDTO> mergedList = new ArrayList<>();
        // 构建聚合后的商品列表
        productMap.forEach((key, value) -> {
            String[] parts = key.split("_");
            CommodityInventoryDetailIDTO idto = new CommodityInventoryDetailIDTO();
            idto.setCommodityId(Long.parseLong(parts[0]));
            idto.setLevel(Integer.parseInt(parts[1]));
            idto.setQuantity(value);
            mergedList.add(idto);
        });
        return mergedList;
    }
}
