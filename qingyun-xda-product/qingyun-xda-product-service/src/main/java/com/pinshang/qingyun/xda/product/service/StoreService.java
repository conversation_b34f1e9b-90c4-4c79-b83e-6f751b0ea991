package com.pinshang.qingyun.xda.product.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.store.service.StoreManageClient;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * @Author: sk
 * @Date: 2025/1/13
 */

@Slf4j
@Service
public class StoreService {

    @Autowired
    private StoreManageClient storeManageClient;

    /**
     * 判断是否批发商客户
     * @param storeId
     * @return
     */
    public Boolean isPfsStore(Long storeId){
        if(storeId == null) {
            log.warn("判断是否批发商客户 StoreId是空");
            return false;
        }
        return storeManageClient.isPfsStore(storeId);
    }
}
