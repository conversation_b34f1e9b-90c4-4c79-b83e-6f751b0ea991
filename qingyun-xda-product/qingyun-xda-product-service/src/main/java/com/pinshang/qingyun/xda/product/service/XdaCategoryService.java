package com.pinshang.qingyun.xda.product.service;

import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.platformCommoditySync.PlatformCategoryOperateTypeEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.xda.product.dto.kafka.EsXdaCategoryChangeKafkaVO;
import com.pinshang.qingyun.xda.product.dto.xdaCategory.*;
import com.pinshang.qingyun.xda.product.mapper.XdaCategoryMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityTextMapper;
import com.pinshang.qingyun.xda.product.model.XdaCategory;
import com.pinshang.qingyun.xda.product.model.XdaCommodityText;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 鲜达前台品类
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Slf4j
@Service
public class XdaCategoryService {

    @Autowired
    private XdaCategoryMapper categoryMapper;
    @Autowired
    private XdaCommodityTextMapper xdaCommodityTextMapper;
    @Autowired
    private IMqSenderComponent mqSenderComponent;

    /**
     * 查询鲜达前台品类
     * @param id
     * @return
     */
    public XdaCategory selectXdaCategory(Long id) {
    	if (null != id) {
    		return categoryMapper.selectByPrimaryKey(id);
    	}
    	return null;
    }

    public List<XdaCategory> selectXdaCategoryList(List<Long> idList) {
        if (SpringUtil.isNotEmpty(idList)) {
            Example example = new Example(XdaCategory.class);
            example.createCriteria().andIn("id", idList);
            return categoryMapper.selectByExample(example);
        }
        return new ArrayList<>();
    }


    /**
     * 查询所有前台品类
     * @return
     */
    public List<XdaCategory> findAllXdaCategory(){
        Example example =  new Example(XdaCategory.class);
        example.orderBy("cateLevel").asc();
        example.orderBy("sortNum").asc();
        example.createCriteria().andEqualTo("status",1);
        return categoryMapper.selectByExample(example);
    }

    /**
     * 新增品类
     * @param vo
     * @return
     */
    public int insertXdaCategory(@RequestBody XdaCategorySaveVo vo){
        QYAssert.isTrue(null != vo, "参数有误!");
        Long parentId = null == vo.getParentId()? 0L: vo.getParentId();
        Assert.isTrue(!StringUtil.isBlank(vo.getCateName()), "'品类名称'不能为空!");

        Date date = new Date();
        int cateLevel;
        if (parentId.longValue() == 0) {
            cateLevel = 1;
        } else {
            //查询父类
            XdaCategory parent = categoryMapper.selectByPrimaryKey(parentId);
            Assert.isTrue(null != parent, "'父级分类'不存在!");
            Assert.isTrue(null != parent.getCateLevel(), "'父级分类'层级有误!");
            int parentCateLevel = parent.getCateLevel().intValue();
            Assert.isTrue(parentCateLevel == 1, "分类最多支持2级!");
            cateLevel = parentCateLevel + 1;
        }

        //同一父节点下，品类名称重复!
        this.checkCategoryName(vo.getCateName(), null);

        XdaCategory xdaCategory =XdaCategory.forInsert(parentId, vo.getCateName(), cateLevel,vo.getPicUrl(), this.maxSortNoInParent(parentId),vo.getUserId(),date);

        int result = categoryMapper.insert(xdaCategory);

        //发消息通知鲜达搜索
        List<EsXdaCategoryChangeKafkaVO> kafkaVOList = new ArrayList<>();
        kafkaVOList.add(new EsXdaCategoryChangeKafkaVO(xdaCategory.getId(), parentId, xdaCategory.getCateName(), xdaCategory.getCateLevel(), xdaCategory.getSortNum(), 1));
        mqSenderComponent.send(QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XDA_CATEGORY_CHANGE_TOPIC.getTopic(),
                kafkaVOList, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XDA_CATEGORY_CHANGE_TOPIC.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());
        return result;
    }
    /**
     * 验证名称唯一
     * @param cateName
     * @param categoryId
     */
    private void checkCategoryName(String cateName, Long categoryId) {
        Example example = new Example(XdaCategory.class);
        example.createCriteria().andEqualTo("status",1);
        List<XdaCategory> categoryList = categoryMapper.selectByExample(example);
        if(SpringUtil.isNotEmpty(categoryList)){
            categoryList.forEach(o -> {
                if (null == categoryId || !categoryId.equals(o.getId())) {
                    QYAssert.isTrue(!cateName.equals(o.getCateName()), "品类名称不能重复!");
                }
            });
        }
    }

    /**
     * 同一父类下的 最大排序值
     * @param parentId
     * @return
     */
    private Integer maxSortNoInParent(Long parentId) {
        Integer sortNo = categoryMapper.selectMaxSortNoInParent(parentId);
        sortNo = null == sortNo? 1: (sortNo + 1);
        return sortNo;
    }

    /**
     * 删除：前台分类
     * @param vo
     */
    public int deleteXdaCategory(XdaCategoryEditVo vo) {
        QYAssert.isTrue(null != vo," 参数异常");
        QYAssert.isTrue(null != vo.getId()," id 参数异常");

        XdaCategory xdaCategory = categoryMapper.selectByPrimaryKey(vo.getId());
        QYAssert.isTrue(null != xdaCategory," 分类不存在");

        Example example = new Example(XdaCategory.class);
        example.createCriteria().andEqualTo("parentId", vo.getId()).andEqualTo("status",1);
        List<XdaCategory> sonList = categoryMapper.selectByExample(example);
        Assert.isTrue(SpringUtil.isEmpty(sonList), "存在子节点的分类不能删除!");

        //1.判断当前品类下有没有商品绑定 t_xda_commodity_text  second_category_id
        Example xdaCommodityTextExample = new Example(XdaCommodityText.class);
        xdaCommodityTextExample.createCriteria().andEqualTo("secondCategoryId",vo.getId());
        List<XdaCommodityText> xdaCommodityTexts = xdaCommodityTextMapper.selectByExample(xdaCommodityTextExample);
        QYAssert.isTrue(SpringUtil.isEmpty(xdaCommodityTexts)," 该分类已关联商品,不能删除! ");

        XdaCategory delete = XdaCategory.forDelete(vo.getId(), vo.getUserId(), new Date());
        int result = categoryMapper.updateByPrimaryKeySelective(delete);

        // 发送品类变更消息 到 对接服务	—— 删除品类
//        this.setXdCategoryUpdateMsgToIntegtration(vo.getUserId(), PlatformCategoryOperateTypeEnums.DELETE.getCode(), xdaCategory.getId(), null);
//        KafkaMessageWrapper message = new KafkaMessageWrapper(KafkaMessageTypeEnum.XD_CATEGORY_CHANGE, delete, KafkaMessageOperationTypeEnum.DELETE);
//        kafkaTemplate.send(applicationNameSwitch+ KafkaTopicConstant.XD_CATEGORY_CHANGE, JsonUtil.java2json(message));

        //发消息通知鲜达搜索
        List<EsXdaCategoryChangeKafkaVO> kafkaVOList = new ArrayList<>();
        kafkaVOList.add(new EsXdaCategoryChangeKafkaVO(xdaCategory.getId(), xdaCategory.getParentId(), xdaCategory.getCateName(), xdaCategory.getCateLevel(), xdaCategory.getSortNum(), 0));
        mqSenderComponent.send(QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XDA_CATEGORY_CHANGE_TOPIC.getTopic(),
                kafkaVOList, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XDA_CATEGORY_CHANGE_TOPIC.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());
        return result;
    }

    /**
     * 修改品类
     * @param vo
     * @return
     */
    public int editXdaCategory(XdaCategoryEditVo vo){
        QYAssert.isTrue(null != vo," 参数异常");
        QYAssert.isTrue(null != vo.getId()," id 参数异常");

        Long id = vo.getId();
        String cateName = vo.getCateName();
        Integer sortNum = vo.getSortNum();
        Assert.isTrue(StringUtils.isNotEmpty(cateName), "'品类名称'不能为空!");

        Date date = new Date();
        XdaCategory xdaCategory = categoryMapper.selectByPrimaryKey(id);
        Assert.isTrue(null != xdaCategory, "该品类不存在!");
        String oldCateName = xdaCategory.getCateName();

        // 判断兄弟节点名称是否重复
        this.checkCategoryName(cateName, vo.getId());

        List<XdaCategory> editSortNumList = new ArrayList<>();
        List<Long> categoryIdList = new ArrayList<>();
        List<EsXdaCategoryChangeKafkaVO> kafkaVOList = new ArrayList<>();

        if (null == sortNum || sortNum.intValue() < 1) {
            // 未获得新排序值，则不改变原排序值
            sortNum = xdaCategory.getSortNum();
        } else {
            if (sortNum.intValue() != xdaCategory.getSortNum().intValue()) {
                Example selectExample = new Example(XdaCategory.class);
                selectExample.createCriteria().andEqualTo("parentId", xdaCategory.getParentId()).andEqualTo("status",1)
                        .andGreaterThanOrEqualTo("sortNum", sortNum);
                selectExample.orderBy("sortNum");
                List<XdaCategory> xdaCategoryList = categoryMapper.selectByExample(selectExample);
                if(SpringUtil.isNotEmpty(xdaCategoryList)){
                    int otherSortNum = sortNum.intValue();
                    for(XdaCategory category : xdaCategoryList){
                        ++otherSortNum ;
                        if( !category.getId().equals(id ) &&  category.getSortNum().intValue() != otherSortNum){
                            XdaCategory c = XdaCategory.forUpdate(category.getId(),category.getCateName(),otherSortNum, vo.getUserId(), date);
                            editSortNumList.add(c);
                            categoryIdList.add(c.getId());
                        }
                        if(!category.getId().equals(id)){
                            kafkaVOList.add(new EsXdaCategoryChangeKafkaVO(category.getId(), category.getParentId(), category.getCateName(), category.getCateLevel(), otherSortNum, 2));
                        }
                    }
                }
            }
        }

        //更新排序
        if(SpringUtil.isNotEmpty(editSortNumList)){
            categoryMapper.batchEditXdaCategorySortNum(editSortNumList);
        }

        //更新
        XdaCategory update = XdaCategory.forUpdate(id, cateName, sortNum, vo.getUserId(), date);
        int i = categoryMapper.updateByPrimaryKeySelective(update);
        editSortNumList.add( update );
//        KafkaMessageWrapper msg = new KafkaMessageWrapper(KafkaMessageTypeEnum.XD_CATEGORY_CHANGE, editSortNumList, KafkaMessageOperationTypeEnum.RATION);
//        kafkaTemplate.send(applicationNameSwitch+ KafkaTopicConstant.XD_CATEGORY_CHANGE, JsonUtil.java2json(msg));

        kafkaVOList.add(new EsXdaCategoryChangeKafkaVO(id, xdaCategory.getParentId(), cateName, xdaCategory.getCateLevel(), sortNum, 2));


        //查询品列关联排序信息
        categoryIdList.add(vo.getId());
        List<XdaCommodityText> xdaCommodity = this.findXdaCommodityByCategoryIdList(categoryIdList);
        if(SpringUtil.isNotEmpty(xdaCommodity)){

            //更新了分类名，发消息更新前置仓搜索
            if(!cateName.equals(oldCateName)){
//                List<EsShopCommodityInfoKafkaVo> kafkaVos = new ArrayList<>(xdaCommodity.size());
//                xdaCommodity.forEach(item->{
//                    EsShopCommodityInfoKafkaVo kafkaVo = new EsShopCommodityInfoKafkaVo();
//                    kafkaVo.setCommodityId(item.getCommodityId());
//                    if(xdaCategory.getCateLevel()==1){
//                        kafkaVo.setXdFirstCategoryName(cateName);
//                    }else if(xdaCategory.getCateLevel()==2){
//                        kafkaVo.setXdSecondCategoryName(cateName);
//                    }
//                    kafkaVos.add(kafkaVo);
//                });
//                xdaCommoditySendKafkaService.sendEsXdCommodityAppInfo(kafkaVos);
            }
        }

        // 发送品类变更消息 到 对接服务	—— 修改品类
        this.setXdCategoryUpdateMsgToIntegtration(vo.getUserId(), PlatformCategoryOperateTypeEnums.UPDATE.getCode(), null, categoryIdList);

        // 发送品类变更消息 到 搜索服务
        mqSenderComponent.send(QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XDA_CATEGORY_CHANGE_TOPIC.getTopic(),
                kafkaVOList, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XDA_CATEGORY_CHANGE_TOPIC.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());

        return i;
    }

    // 发送品类变更消息 到 对接服务
    private void setXdCategoryUpdateMsgToIntegtration(Long userId, Integer operateType, Long categoryId, List<Long> categoryIdList) {
//    	XdCategoryKafkaIDTO data = null;
//    	if (null != categoryId) {
//    		data = XdCategoryKafkaIDTO.byCategoryId(userId, operateType, categoryId);
//    	} else {
//    		data = XdCategoryKafkaIDTO.byCategoryIdList(userId, operateType, categoryIdList);
//    	}
//    	KafkaMessageWrapper integrationMessage = new KafkaMessageWrapper(KafkaMessageTypeEnum.INTEGRATION_XD_CATEGORY, data, KafkaMessageOperationTypeEnum.SYNC);
//        kafkaTemplate.send(applicationNameSwitch+ KafkaTopicConstant.INTEGRATION_XD_CATEGORY_TOPIC, JsonUtil.java2json(integrationMessage));
//        log.info("\n\n 发送前置仓品类变更消息   到 对接服务：message=" + integrationMessage);
    }

    /**
     * 修改品类图片
     * @param vo
     * @return
     */
    public int editXdaCategoryPicUrl(XdaCategoryEditVo vo){
        QYAssert.isTrue(null != vo," 参数异常");
        QYAssert.isTrue(null != vo.getId()," id 参数异常");
        Assert.isTrue(StringUtils.isNotEmpty(vo.getPicUrl()), "'图片'不能为空!");

        Date date = new Date();
        XdaCategory xdaCategory = categoryMapper.selectByPrimaryKey(vo.getId());
        Assert.isTrue(null != xdaCategory, "该品类不存在!");

        //更新
        XdaCategory update = XdaCategory.forUpdatePicUrl(vo.getId(), vo.getPicUrl(), vo.getUserId(), date);
        return categoryMapper.updateByPrimaryKeySelective(update);
    }

    /**
     * 查询所有二级分类 to 导出
     * @return
     */
    public List<XdaCategoryListODTO> findAllSecondXdaCategoryListToExport(){
        return categoryMapper.findAllSecondXdaCategoryListToExport();
    }

    /**
     * 查询所有一级分类
     * @return
     */
    public List<XdaCategoryListODTO> findAllFirstXdaCategoryList(){
        return categoryMapper.findAllFirstXdaCategoryList();
    }

    /**
     * 根据品类id查询商品信息
     * @param categoryIdList
     * @return
     */
    public List<XdaCommodityText> findXdaCommodityByCategoryIdList(List<Long> categoryIdList){
        Example example = new Example(XdaCommodityText.class);
        example.createCriteria().andIn("firstCategoryId",categoryIdList).orIn("secondCategoryId",categoryIdList);
        return xdaCommodityTextMapper.selectByExample(example);
    }

    /**
     * 查询  品类和父级信息  集合
     *
     * @param categoryIdList
     * @return
     */
    public List<XdaCategoryAndParentInfoODTO> selectXdaCategoryAndParentInfoList(List<Long> categoryIdList) {
    	if (SpringUtil.isNotEmpty(categoryIdList)) {
    		return categoryMapper.selectXdaCategoryAndParentInfoList(categoryIdList);
    	} else {
    		return new ArrayList<>();
    	}
    }

    /**
     * 查询  一、二级品类Id  集合
     * @return
     */
    public List<XdaCuponODTO> selectXdaCuponList() {
       return categoryMapper.selectXdaCuponList();
    }

}
