package com.pinshang.qingyun.xda.product.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.EnableStatusEnums;
import com.pinshang.qingyun.base.enums.StoreTypeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.ListUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.marketing.dto.XdaAppDownStatusSalesPromotionListIDTO;
import com.pinshang.qingyun.marketing.dto.XdaSalesPromotionListODTO;
import com.pinshang.qingyun.marketing.service.MtSalesPromotionClient;
import com.pinshang.qingyun.order.manage.service.TobCommStockClient;
import com.pinshang.qingyun.pf.product.dto.commodityAppStatus.PfCommodityAppStatusUpIDTO;
import com.pinshang.qingyun.pf.product.service.PfCommodityAppStatusClient;
import com.pinshang.qingyun.storage.dto.td.QueryTdDefaultWarehouseReqIDTO;
import com.pinshang.qingyun.storage.dto.tob.TobCommodityWarehouseODTO;
import com.pinshang.qingyun.storage.dto.warehouse.WarehouseODTO;
import com.pinshang.qingyun.storage.service.td.TdClient;
import com.pinshang.qingyun.storage.service.tob.ToBClient;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextPicODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextPicListIDTO;
import com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.*;
import com.pinshang.qingyun.xda.product.mapper.PfCommodityAppStatusLogMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityAppStatusLogMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityAppStatusMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityTextPicMapper;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityFreezeGroupMapper;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityMapper;
import com.pinshang.qingyun.xda.product.model.PfCommodityAppStatusLog;
import com.pinshang.qingyun.xda.product.model.XdaCommodityAppStatus;
import com.pinshang.qingyun.xda.product.model.XdaCommodityAppStatusLog;
import com.pinshang.qingyun.xda.product.model.XdaCommodityTextPic;
import com.pinshang.qingyun.xda.product.model.common.Commodity;
import com.pinshang.qingyun.xda.product.model.common.CommodityFreezeGroup;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @time: 2020/12/15 10:50
 */
@Service
public class XdaCommodityAppStatusService {

    @Autowired
    private XdaCommodityAppStatusMapper xdaCommodityAppStatusMapper;

    @Autowired
    private XdaCommodityAppStatusLogMapper xdaCommodityAppStatusLogMapper;

    @Autowired
    private PfCommodityAppStatusLogMapper pfCommodityAppStatusLogMapper;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private XdaCommodityTextPicMapper xdaCommodityTextPicMapper;
    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Autowired
    private TdClient tdClient;

    @Autowired
    private ToBClient toBClient;

    @Autowired
    private MtSalesPromotionClient mtSalesPromotionClient;
    @Autowired
    private TobCommStockClient tobCommStockClient;

//    @Value("${pinshang.img-server-url}")
    @Value("${pinshang.img-xd-server-url}")
    private String imgServerUrl;

    @Autowired
    private CommodityFreezeGroupMapper commodityFreezeGroupMapper;

    @Autowired
    private PfCommodityAppStatusClient pfCommodityAppStatusClient;

    /**
     * 查询鲜达上下架列表
     * @param xdaCommodityAppStatusIDTO
     * @return
     */
    public PageInfo<XdaCommodityAppStatusODTO> selectCommodityAppStatusList(XdaCommodityAppStatusIDTO xdaCommodityAppStatusIDTO){
        QYAssert.isTrue(null != xdaCommodityAppStatusIDTO, "参数有误!");
        QYAssert.isTrue(null != xdaCommodityAppStatusIDTO.getAppType(), "app类型不能为空!");
        if(null != xdaCommodityAppStatusIDTO.getStoreId()){
            Long aLong = xdaCommodityAppStatusMapper.selectProductPriceModelIdByStoreId(xdaCommodityAppStatusIDTO.getStoreId());
            long l = null == aLong ? -1L : aLong;
            xdaCommodityAppStatusIDTO.setProductPriceModelId(l);
        }
        PageInfo<XdaCommodityAppStatusODTO> pageDate = PageHelper.startPage(xdaCommodityAppStatusIDTO.getPageNo(), xdaCommodityAppStatusIDTO.getPageSize()).doSelectPageInfo(() -> {
            xdaCommodityAppStatusMapper.selectCommodityAppStatusList(xdaCommodityAppStatusIDTO);
        });
        if (pageDate != null && SpringUtil.isNotEmpty(pageDate.getList())) {
            List<XdaCommodityAppStatusODTO> list = pageDate.getList();
            Map<Long, CommodityFreezeGroup> commodityFreezeGroupMap;
            if (Objects.equals(xdaCommodityAppStatusIDTO.getAppType(), 2)) {
                Example example = new Example(CommodityFreezeGroup.class);
                example.createCriteria().andIn("commodityId", list.stream().map(XdaCommodityAppStatusODTO::getCommodityId).collect(Collectors.toList()));
                List<CommodityFreezeGroup> commodityFreezeGroupList = commodityFreezeGroupMapper.selectByExample(example);

                commodityFreezeGroupMap = commodityFreezeGroupList.stream().collect(Collectors.toMap(CommodityFreezeGroup::getCommodityId, e -> e));
            } else {
                commodityFreezeGroupMap = new HashMap<>();
            }
            List<Long> commodityIdList = list.stream().map(XdaCommodityAppStatusODTO::getCommodityId).collect(Collectors.toList());
            List<XdaCommodityTextPic> xdaCommodityTextPics = xdaCommodityTextPicMapper.selectCommodityTextPicList(new SelectCommodityTextPicListIDTO(commodityIdList));
            //图片信息
            List<XdaCommodityTextPic> picList = xdaCommodityTextPics.stream().filter(p -> p.getPicType().equals(1)).collect(Collectors.toList());
            Map<Long, List<XdaCommodityTextPic>> picMap = picList.stream().collect(Collectors.groupingBy(XdaCommodityTextPic::getCommodityId));
            //长图
            List<XdaCommodityTextPic> longPicList = xdaCommodityTextPics.stream().filter(p -> p.getPicType().equals(2)).collect(Collectors.toList());
            Map<Long,XdaCommodityTextPic> longPicMap = longPicList.stream().collect(Collectors.toMap(XdaCommodityTextPic::getCommodityId,e->e));
            list.forEach(p->{
                if(picMap.containsKey(p.getCommodityId())){
                    List<CommodityTextPicODTO> commodityTextPicODTOS = new ArrayList<>();
                    picMap.get(p.getCommodityId()).forEach(d-> commodityTextPicODTOS.add(new CommodityTextPicODTO(d.getPicUrl(),d.getIsDefault(),imgServerUrl+d.getPicUrl().trim())));
                    p.setPicList(commodityTextPicODTOS);
                    p.setPicStatus("有");
                }else {
                    p.setPicStatus("无");
                }
                if(commodityFreezeGroupMap.containsKey(p.getCommodityId())){
                    p.setCommodityFreezeGroup("是");
                }else {
                    p.setCommodityFreezeGroup("否");
                }
                if(longPicMap.containsKey(p.getCommodityId())){
                    String picUrl = longPicMap.get(p.getCommodityId()).getPicUrl().trim();
                    p.setLongPicStatus("有");
                    p.setLongPicUrl(imgServerUrl+picUrl);
                }else {
                    p.setLongPicStatus("无");
                }
            });
        }
        return pageDate;
    }

    /**
     * 鲜达商品上下架
     * @param idto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Object modifyXdaCommodityAppStatus(XdaCommodityAppStatusUpIDTO idto){
        QYAssert.isTrue(null != idto, "参数有误!");
        QYAssert.isTrue(null != idto.getAppType(), "app类型不能为空!");
        QYAssert.isTrue(StringUtils.isNotBlank(idto.getCommodityCodes()), "商品编码不能为空!");
        Map<String, Object> dataMap = new HashMap<>(0);
        List<String> errorList = new ArrayList<>();
        Long userId = idto.getUserId();
        String commodityCodeS = idto.getCommodityCodes();
        String reason = idto.getReason();
        List<String> codesList = Arrays.asList(commodityCodeS.split("\n"));
        final Integer commoditySize = 200;
        if(null!= codesList && codesList.size()>commoditySize){
            QYAssert.isTrue(false, "每次最多200个商品!");
        }
        Example example = new Example(Commodity.class);
        example.createCriteria().andIn("commodityCode",codesList);
        List<Commodity> commodityList = commodityMapper.selectByExample(example);
        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityList), "商品编码不存在!");
        if(null != commodityList && commodityList.size() > 0){
            List<Long> commodityIdList = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());
            if(idto.getStatus().equals(0)){
                if (Objects.equals(idto.getAppType(), 2)) {
                    // 批发的逻辑：组合商品不允许上架
                    List<String> commodityCodeBinationList = commodityList.stream().filter(item -> item.getProductType().equals(2)).map(Commodity::getCommodityCode).collect(Collectors.toList());
                    QYAssert.isTrue(SpringUtil.isEmpty(commodityCodeBinationList), "组合商品不允许上架:" + String.join(",", commodityCodeBinationList));
                }
                errorList = up(idto.getAppType(),commodityList,commodityIdList,userId,reason);
            }else {
                int forceStatus = Objects.nonNull(idto.getForceStatus())? idto.getForceStatus().intValue(): 0;
                if(forceStatus == 0){
                    String errMsg = downCheck(idto.getAppType(), commodityIdList,commodityList);
                    if(StringUtils.isNotEmpty(errMsg)){
                        dataMap.put("success", 1);
                        dataMap.put("forceStatus", 1);
                        dataMap.put("data",errMsg);
                        return dataMap;
                    }
                }
                errorList = down(idto.getAppType(),commodityIdList,userId,reason);
            }
        }
        if(SpringUtil.isNotEmpty(errorList)){
            errorList.add(0,"以下商品上架失败。商品上架必要条件:前台品类、前台品名、图片齐全;商品总部可售;商品送货日期范围设置完成;通达首选仓库必须设置;B端库存依据必须设置");
            dataMap.put("success", 0);
            dataMap.put("data", errorList);
        }else {
            dataMap.put("success", 1);
        }
        //如果类型是批发并且是通过校验， 调用 原来的上下架接口，维护老的数据
        if (Objects.equals(idto.getAppType(), 2) && Objects.equals(dataMap.get("success"), 1)) {
            //TODO 批发服务下架后，需注释掉
            PfCommodityAppStatusUpIDTO commodityAppStatusDto = BeanCloneUtils.copyTo(idto, PfCommodityAppStatusUpIDTO.class);
            return pfCommodityAppStatusClient.modifyPfCommodityAppStatus(commodityAppStatusDto);
        }
        return dataMap;
    }

    private String downCheck(Integer appType, List<Long> commodityIdList, List<Commodity> commodityList) {

        String errMsg = "";

        XdaAppDownStatusSalesPromotionListIDTO idto = new XdaAppDownStatusSalesPromotionListIDTO();
        idto.setCustomerType(1);
        idto.setStatus(EnableStatusEnums.ENABLE.getCode());
        idto.setExpireStatus(EnableStatusEnums.ENABLE.getCode());
        idto.setCommodityIdList(commodityIdList);
        idto.setPromotionType(34);
        List<XdaSalesPromotionListODTO> xdaSalesPromotionListODTOList = mtSalesPromotionClient.findXdaAppDownStatusRelatedSalesPromotion(idto);
        if(SpringUtil.isNotEmpty(xdaSalesPromotionListODTOList)){

            // 进行过滤,如果是鲜达下架。找出促销里面客户类型包含鲜达的促销
            // 如果是批发下架，则找出促销里面客户类型包含批发的促销
            xdaSalesPromotionListODTOList = filterPromotionByStoreTypeId(appType, xdaSalesPromotionListODTOList);
            if(CollectionUtils.isEmpty(xdaSalesPromotionListODTOList)){
                return null;
            }

            List<Long> giftCommodityIdList = xdaSalesPromotionListODTOList.stream().map(XdaSalesPromotionListODTO::getCommodityId).distinct().collect(Collectors.toList());
            Map<Long, String> commodityIdCodeMap = commodityList.stream().collect(Collectors.toMap(Commodity::getId, Commodity::getCommodityCode));
            List<String> giftCommodityCodeList = new ArrayList<>();
            giftCommodityIdList.forEach(
                    giftCommodityId->{
                        giftCommodityCodeList.add(commodityIdCodeMap.get(giftCommodityId));
                    }
            );

            String giftCommodityCodeStr = giftCommodityCodeList.stream().collect(Collectors.joining("，"));
            String promotionCodeStr = xdaSalesPromotionListODTOList.stream().map(XdaSalesPromotionListODTO::getPromotionCode).distinct().collect(Collectors.joining("，"));
            errMsg = "商品【"+giftCommodityCodeStr+"】已被设置为赠品，冲突促销活动编码【"+promotionCodeStr+"】，下架后将会影响促销活动，确定继续下架？";
        }

        return errMsg;
    }

    private @NotNull List<XdaSalesPromotionListODTO> filterPromotionByStoreTypeId(Integer appType, List<XdaSalesPromotionListODTO> xdaSalesPromotionListODTOList) {
        return xdaSalesPromotionListODTOList.stream().filter(i -> {
            Boolean isContainsStoreType = false;
            if(StringUtils.isNotBlank(i.getStoreTypeIds())) {
                List<Long> storeTypeIdList = JSONObject.parseObject(i.getStoreTypeIds(), List.class);
                if(Objects.equals(appType, 1)) {

                    Boolean isXda = (storeTypeIdList.contains(StoreTypeEnums.PFS.getId()) && storeTypeIdList.size() > 1)
                            || !storeTypeIdList.contains(StoreTypeEnums.PFS.getId());
                    // 鲜达下架，过滤出包含鲜达客户类型的促销活动
                    isContainsStoreType = isXda;
                }else {

                    // 批发下架，过滤出包含批发客户类型的促销活动
                    isContainsStoreType = storeTypeIdList.contains(StoreTypeEnums.PFS.getId());
                }

            }else {
                isContainsStoreType = true;
            }
            return isContainsStoreType;
        }).collect(Collectors.toList());
    }

    /**
     * 查询鲜达商品上下架日志
     * @param idto
     * @return
     */
    public PageInfo<XdaCommodityAppStatusLogODTO> selectCommodityAppStatusLogList(XdaCommodityAppStatusLogIDTO idto){
        QYAssert.isTrue(null != idto.getAppType(), "app类型不能为空!");
        if(null != idto.getBDate() && null != idto.getEDate()){
            idto.setBDate(idto.getBDate() +" 00:00:00");
            idto.setEDate(idto.getEDate() +" 23:59:59");
        }
        PageInfo<XdaCommodityAppStatusLogODTO> pageDate;
        if (idto.getAppType().equals(1)) {
            pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize())
                    .doSelectPageInfo(() -> xdaCommodityAppStatusLogMapper.selectCommodityAppStatusLogList(idto));
        } else {
            pageDate = PageHelper.startPage(idto.getPageNo(), idto.getPageSize())
                    .doSelectPageInfo(() -> pfCommodityAppStatusLogMapper.selectCommodityAppStatusLogList(idto));
        }
        return pageDate;
    }

    /**
     * 鲜达商品一键上架
     * 满足条件
     */
    @Transactional(rollbackFor = Exception.class)
    public void upCommodity(Long userId,Integer appType){
        QYAssert.isTrue(null != appType, "app类型不能为空!");
        List<XdaCommodityAppStatusUpODTO> xdaCommodityAppStatusUpODTOS = xdaCommodityAppStatusMapper.selectUpCommodityList(appType);
        if(SpringUtil.isNotEmpty(xdaCommodityAppStatusUpODTOS)){
            List<Long> commodityIdList = xdaCommodityAppStatusUpODTOS.stream().map(XdaCommodityAppStatusUpODTO::getCommodityId).distinct().collect(Collectors.toList());
            List<XdaCommodityTextPic> xdaCommodityTextPics = xdaCommodityTextPicMapper.selectCommodityTextPicList(new SelectCommodityTextPicListIDTO(commodityIdList));
            List<XdaCommodityTextPic> picList = xdaCommodityTextPics.stream().filter(p -> p.getPicType().equals(1)).collect(Collectors.toList());
            Map<Long, List<XdaCommodityTextPic>> picStatusMap = picList.stream().collect(Collectors.groupingBy(XdaCommodityTextPic::getCommodityId));
            List<Long> commodityIdsToProcess = commodityIdList.stream().filter(p -> picStatusMap.keySet().contains(p)).collect(Collectors.toList());
            if(SpringUtil.isNotEmpty(commodityIdsToProcess)){
                //校验通达首选仓库
                QueryTdDefaultWarehouseReqIDTO queryTdDefaultWarehouseReqIDTO = new QueryTdDefaultWarehouseReqIDTO();
                queryTdDefaultWarehouseReqIDTO.setCommodityIds(commodityIdList);
                Map<Long, WarehouseODTO> tdDefaultWarehouse = tdClient.getTdDefaultWarehouse(queryTdDefaultWarehouseReqIDTO);

                List<List<Long>> lists = ListUtil.splitSubList(commodityIdList, 100);
                Map<Long, TobCommodityWarehouseODTO> longTobCommodityWarehouseODTOMap = new HashMap<>();
                for (List<Long> list : lists) {
                    Map<Long, TobCommodityWarehouseODTO> map = toBClient.queryTobCommodityWarehouse(list);
                    if(null != map){
                        longTobCommodityWarehouseODTOMap.putAll(map);
                    }
                }
                commodityIdsToProcess.removeIf(item-> !(tdDefaultWarehouse.containsKey(item))
                        || null == tdDefaultWarehouse.get(item).getWarehouseId());

                commodityIdsToProcess.removeIf(item-> !(longTobCommodityWarehouseODTOMap.containsKey(item))
                        || null == longTobCommodityWarehouseODTOMap.get(item).getStockType());

                if(SpringUtil.isEmpty(commodityIdsToProcess)){
                    return;
                }

                // 过滤库存依据.返回存在库存依据的商品idList
                commodityIdsToProcess = tobCommStockClient.queryExistToBCommodityStockIds(commodityIdsToProcess);
                if(SpringUtil.isEmpty(commodityIdsToProcess)){
                    return;
                }

                // 上架
                processToUp(userId, appType, commodityIdsToProcess, "");
                //如果类型是批发， 调用 原来的一键上架接口，维护老的数据
                if (Objects.equals(appType, 2)) {
                    //TODO 批发服务下架后，需注释掉
                    pfCommodityAppStatusClient.upCommodity();
                }
            }
        }
    }

    /**
     * 上架
     *
     * @param appType
     * @param commodityList
     * @param commodityIdList
     * @param userId
     * @param reason
     * @return
     */
    public List<String> up(Integer appType, List<Commodity> commodityList, List<Long> commodityIdList, Long userId, String reason){
        List<String> errorList = new ArrayList<>();
        //删除相应的数据
        Example selectExample = new Example(XdaCommodityAppStatus.class);
        String statusField = Objects.equals(appType, 1) ? "appStatus" : "pfAppStatus";
        selectExample.createCriteria().andIn("commodityId", commodityIdList)
                .andEqualTo(statusField, 0);
        List<XdaCommodityAppStatus> xdaCommodityAppStatuseList = xdaCommodityAppStatusMapper.selectByExample(selectExample);
        List<Long> commodityIdListRemove = xdaCommodityAppStatuseList.stream().map(XdaCommodityAppStatus::getCommodityId).collect(Collectors.toList());
        if(SpringUtil.isNotEmpty(commodityIdListRemove)){
            commodityList.removeIf(item-> commodityIdListRemove.contains(item.getId()));
            commodityIdList.removeIf(item-> commodityIdListRemove.contains(item));
        }
        if(SpringUtil.isEmpty(commodityList)){
            return errorList;
        }

        List<XdaCommodityAppStatusUpODTO> xdaCommodityAppStatusUpODTOS = xdaCommodityAppStatusMapper.selectCommodityTextByCommodityIdList(commodityIdList,appType);
        List<XdaCommodityTextPic> xdaCommodityTextPics = xdaCommodityTextPicMapper.selectCommodityTextPicList(new SelectCommodityTextPicListIDTO(commodityIdList));

        //前台品名
        Map<Long,String> commodityAppNameMap = xdaCommodityAppStatusUpODTOS.stream().filter(p ->!(null == p.getCommodityAppName())).collect(Collectors.toMap(XdaCommodityAppStatusUpODTO::getCommodityId, XdaCommodityAppStatusUpODTO::getCommodityAppName));
        //前台品类
        Map<Long,String> appCategoryNameMap = xdaCommodityAppStatusUpODTOS.stream().filter(p -> !(null == p.getAppCategoryName())).collect(Collectors.toMap(XdaCommodityAppStatusUpODTO::getCommodityId, XdaCommodityAppStatusUpODTO::getAppCategoryName));
        //总部是否可售
        Map<Long,Integer> commodityStatusMap = xdaCommodityAppStatusUpODTOS.stream().filter(p -> !(null == p.getCommodityStatus()) && p.getCommodityStatus().equals(1)).collect(Collectors.toMap(XdaCommodityAppStatusUpODTO::getCommodityId, XdaCommodityAppStatusUpODTO::getCommodityStatus));
        //送货时间段
        Map<Long, String> deliveryDateRangeValueMap = xdaCommodityAppStatusUpODTOS.stream().filter(p -> StringUtils.isNotBlank(p.getDeliveryDateRangeValue())).collect(Collectors.toMap(XdaCommodityAppStatusUpODTO::getCommodityId, XdaCommodityAppStatusUpODTO::getDeliveryDateRangeValue));
        //图片
        List<XdaCommodityTextPic> picList = xdaCommodityTextPics.stream().filter(p -> p.getPicType().equals(1)).collect(Collectors.toList());
        Map<Long, List<XdaCommodityTextPic>> picStatusMap = picList.stream().collect(Collectors.groupingBy(XdaCommodityTextPic::getCommodityId));

        //校验通达首选仓库
        QueryTdDefaultWarehouseReqIDTO queryTdDefaultWarehouseReqIDTO = new QueryTdDefaultWarehouseReqIDTO();
        queryTdDefaultWarehouseReqIDTO.setCommodityIds(commodityIdList);
        Map<Long, WarehouseODTO> tdDefaultWarehouse = tdClient.getTdDefaultWarehouse(queryTdDefaultWarehouseReqIDTO);
        Map<Long, TobCommodityWarehouseODTO> longTobCommodityWarehouseODTOMap = toBClient.queryTobCommodityWarehouse(commodityIdList);

        // 判断商品库存依据存在,存在库存依据的商品idList
        List<Long> existToBCommodityIds = tobCommStockClient.queryExistToBCommodityStockIds(commodityIdList);
        Map<Long, Long> existToBCommodityIdsMap = existToBCommodityIds.stream().collect(Collectors.toMap(Function.identity(), p -> p));

        //可上架的商品ID
        List<Long> commodityIdsToProcess = new ArrayList<>();

        commodityList.forEach(p->{
            TobCommodityWarehouseODTO tobCommodityWarehouseODTO = longTobCommodityWarehouseODTOMap.get(p.getId());
            WarehouseODTO warehouseODTO = tdDefaultWarehouse.get(p.getId());
            if(!appCategoryNameMap.containsKey(p.getId())
              || !commodityAppNameMap.containsKey(p.getId())
              || !picStatusMap.containsKey(p.getId())
              || !commodityStatusMap.containsKey(p.getId())
              || !deliveryDateRangeValueMap.containsKey(p.getId())
              || null == warehouseODTO || null == warehouseODTO.getWarehouseId()
              || null == tobCommodityWarehouseODTO || null == tobCommodityWarehouseODTO.getStockType()
              || !existToBCommodityIdsMap.containsKey(p.getId())
            ){
                errorList.add(p.getCommodityCode());
            }else {
                commodityIdsToProcess.add(p.getId());
            }
        });

        // 上架
        processToUp(userId, appType, commodityIdsToProcess, reason);
        return errorList;
    }

    /**
     * 上架
     */
    public void processToUp(Long userId, Integer appType, List<Long> commodityIdsToProcess, String reason) {
        if (CollectionUtils.isEmpty(commodityIdsToProcess)) {
            return;
        }
        Date date = new Date();
        Example selectExample = new Example(XdaCommodityAppStatus.class);
        selectExample.createCriteria().andIn("commodityId", commodityIdsToProcess);
        //查询之前已经上下架的数据
        List<XdaCommodityAppStatus> existingCommodityStatuses = xdaCommodityAppStatusMapper.selectByExample(selectExample);

        // 获取待更新和待插入的商品ID列表
        List<Long> existingCommodityIds = existingCommodityStatuses.stream()
                .map(XdaCommodityAppStatus::getCommodityId)
                .collect(Collectors.toList());
        List<Long> commodityIdsToUpdate = commodityIdsToProcess.stream()
                .filter(existingCommodityIds::contains)
                .collect(Collectors.toList());
        List<Long> commodityIdsToInsert = commodityIdsToProcess.stream()
                .filter(id -> !existingCommodityIds.contains(id))
                .collect(Collectors.toList());

        // 处理待更新的商品
        List<XdaCommodityAppStatus> commodityStatusesToUpdate = processCommodityStatuses(existingCommodityStatuses, commodityIdsToUpdate, appType, 0, userId, date);

        // 批量更新商品上下架状态
        if (CollectionUtils.isNotEmpty(commodityStatusesToUpdate)) {
            xdaCommodityAppStatusMapper.updateBatchByIds(commodityStatusesToUpdate);
        }

        // 处理待插入的商品
        List<XdaCommodityAppStatus> commodityStatusesToInsert = processCommodityStatuses(existingCommodityStatuses, commodityIdsToInsert, appType, 0, userId, date);

        // 批量插入上下架状态表
        if (CollectionUtils.isNotEmpty(commodityStatusesToInsert)) {
            xdaCommodityAppStatusMapper.insertList(commodityStatusesToInsert);
        }

        List<XdaCommodityAppStatusLog> allXdaCommodityAppStatusLogs = new ArrayList<>();
        List<PfCommodityAppStatusLog> allPfCommodityAppStatusLogs = new ArrayList<>();
        //批量插入商品状态日志
        createCommodityAppStatusLogs(appType, 0, userId, reason, commodityStatusesToUpdate, commodityStatusesToInsert, date, allXdaCommodityAppStatusLogs, allPfCommodityAppStatusLogs);
        if (CollectionUtils.isNotEmpty(allXdaCommodityAppStatusLogs)) {
            xdaCommodityAppStatusLogMapper.insertList(allXdaCommodityAppStatusLogs);
        }
        if (CollectionUtils.isNotEmpty(allPfCommodityAppStatusLogs)) {
            pfCommodityAppStatusLogMapper.insertList(allPfCommodityAppStatusLogs);
        }

        //之前操作过上下架的商品ID集合
        List<Long> oldCommodityIdList = SpringUtil.isNotEmpty(existingCommodityStatuses) ? existingCommodityIds : null;
        //新上架的商品ID集合
        List<Long> newCommodityIdList = SpringUtil.isNotEmpty(oldCommodityIdList) ? commodityIdsToProcess.stream().filter(p -> !oldCommodityIdList.contains(p)).collect(Collectors.toList()) : commodityIdsToProcess;

        //发消息通知鲜达搜索
        sendMsgByEditStatus(oldCommodityIdList, newCommodityIdList, 0, appType);
    }

    /**
     * 下架
     *
     * @param appType
     * @param commodityIdsToProcess
     * @param userId
     * @param reason
     * @return
     */
    public List<String> down(Integer appType, List<Long> commodityIdsToProcess, Long userId, String reason){
        Date date = new Date();
        Example example = new Example(XdaCommodityAppStatus.class);
        example.createCriteria().andIn("commodityId", commodityIdsToProcess);
        //查询之前已经上下架的数据
        List<XdaCommodityAppStatus> existingCommodityStatuses = xdaCommodityAppStatusMapper.selectByExample(example);

        // 获取待更新和待插入的商品ID列表
        List<Long> existingCommodityIds = existingCommodityStatuses.stream()
                .map(XdaCommodityAppStatus::getCommodityId)
                .collect(Collectors.toList());
        List<Long> commodityIdsToUpdate = commodityIdsToProcess.stream()
                .filter(existingCommodityIds::contains)
                .collect(Collectors.toList());
        List<Long> commodityIdsToInsert = commodityIdsToProcess.stream()
                .filter(id -> !existingCommodityIds.contains(id))
                .collect(Collectors.toList());

        // 处理待更新的商品
        List<XdaCommodityAppStatus> commodityStatusesToUpdate = processCommodityStatuses(existingCommodityStatuses, commodityIdsToUpdate, appType,1, userId, date);

        // 批量更新商品上下架状态
        if (CollectionUtils.isNotEmpty(commodityStatusesToUpdate)) {
            xdaCommodityAppStatusMapper.updateBatchByIds(commodityStatusesToUpdate);
        }

        // 处理待插入的商品
        List<XdaCommodityAppStatus> commodityStatusesToInsert = processCommodityStatuses(existingCommodityStatuses, commodityIdsToInsert, appType,1, userId, date);

        // 批量插入上下架状态表
        if (CollectionUtils.isNotEmpty(commodityStatusesToInsert)) {
            xdaCommodityAppStatusMapper.insertList(commodityStatusesToInsert);
        }

        List<XdaCommodityAppStatusLog> allXdaCommodityAppStatusLogs = new ArrayList<>();
        List<PfCommodityAppStatusLog> allPfCommodityAppStatusLogs = new ArrayList<>();
        //批量插入商品状态日志
        createCommodityAppStatusLogs(appType, 1, userId, reason, commodityStatusesToUpdate, commodityStatusesToInsert, date, allXdaCommodityAppStatusLogs, allPfCommodityAppStatusLogs);
        if (CollectionUtils.isNotEmpty(allXdaCommodityAppStatusLogs)) {
            xdaCommodityAppStatusLogMapper.insertList(allXdaCommodityAppStatusLogs);
        }
        if (CollectionUtils.isNotEmpty(allPfCommodityAppStatusLogs)) {
            pfCommodityAppStatusLogMapper.insertList(allPfCommodityAppStatusLogs);
        }

        //发消息通知鲜达搜索
        sendMsgByEditStatus(commodityIdsToProcess, null, 1, appType);

        return new ArrayList<>(0);
    }

    /**
     * 处理商品列表，生成待更新或待插入的商品状态列表
     */
    private List<XdaCommodityAppStatus> processCommodityStatuses(List<XdaCommodityAppStatus> existingCommodityStatuses, List<Long> targetCommodityIds, Integer appType,Integer appStatus, Long userId, Date currentDate) {

        Map<Long, XdaCommodityAppStatus> appStatusMap = existingCommodityStatuses.stream().collect(Collectors.toMap(XdaCommodityAppStatus::getCommodityId, Function.identity()));

        List<XdaCommodityAppStatus> resultList = new ArrayList<>();

        for (Long commodityId : targetCommodityIds) {
            XdaCommodityAppStatus commodityAppStatus = appStatusMap.get(commodityId);
            XdaCommodityAppStatus clonedStatus = Objects.nonNull(commodityAppStatus) ? BeanCloneUtils.copyTo(commodityAppStatus, XdaCommodityAppStatus.class) : new XdaCommodityAppStatus();
            clonedStatus.setCommodityId(commodityId);
            if (Objects.equals(appType, 1)) {
                clonedStatus.setAppStatus(appStatus);
            } else {
                clonedStatus.setPfAppStatus(appStatus);
            }
            if (Objects.isNull(clonedStatus.getAppStatus())) {
                clonedStatus.setAppStatus(1);
            }
            if (Objects.isNull(clonedStatus.getPfAppStatus())) {
                clonedStatus.setPfAppStatus(1);
            }
            clonedStatus.setCreateId(userId);
            clonedStatus.setCreateTime(currentDate);
            resultList.add(clonedStatus);
        }
        return resultList;
    }

    private void createCommodityAppStatusLogs(Integer appType, Integer status, Long userId, String reason,
                                              List<XdaCommodityAppStatus> commodityStatusesToUpdate, List<XdaCommodityAppStatus> commodityStatusesToInsert,
                                              Date date, List<XdaCommodityAppStatusLog> allXdaCommodityAppStatusLogs,
                                              List<PfCommodityAppStatusLog> allPfCommodityAppStatusLogs) {
        if (SpringUtil.isNotEmpty(commodityStatusesToUpdate)) {
            if (Objects.equals(appType, 1)) {
                // 鲜达日志
                List<XdaCommodityAppStatusLog> updateLogs = commodityStatusesToUpdate.stream()
                        .map(item -> new XdaCommodityAppStatusLog(item.getCommodityId(), status,
                                reason, userId, date))
                        .collect(Collectors.toList());
                allXdaCommodityAppStatusLogs.addAll(updateLogs);
            } else if (Objects.equals(appType, 2)) {
                // 批发日志
                List<PfCommodityAppStatusLog> updateLogs = commodityStatusesToUpdate.stream()
                        .map(item -> new PfCommodityAppStatusLog(item.getCommodityId(), status,
                                reason, userId, date))
                        .collect(Collectors.toList());
                allPfCommodityAppStatusLogs.addAll(updateLogs);
            }
        }

        if (SpringUtil.isNotEmpty(commodityStatusesToInsert)) {
            if (Objects.equals(appType, 1)) {
                // 鲜达日志
                List<XdaCommodityAppStatusLog> insertLogs = commodityStatusesToInsert.stream()
                        .map(item -> new XdaCommodityAppStatusLog(item.getCommodityId(), status,
                                reason, userId, date))
                        .collect(Collectors.toList());
                allXdaCommodityAppStatusLogs.addAll(insertLogs);
            } else if (Objects.equals(appType, 2)) {
                // 批发日志
                List<PfCommodityAppStatusLog> insertLogs = commodityStatusesToInsert.stream()
                        .map(item -> new PfCommodityAppStatusLog(item.getCommodityId(), status,
                                reason, userId, date))
                        .collect(Collectors.toList());
                allPfCommodityAppStatusLogs.addAll(insertLogs);
            }
        }
    }

    /**
     * 总部商品不可售-操作鲜达商品下架
     * 
     * 20250106-mergePf，改为鲜达和批发一起下架
     * @param idto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Long updateXdaCommodityAppStatusToDown(XdaCommodityAppStatusToDownIDTO idto){
        QYAssert.isTrue(null != idto," 参数有误!");
        QYAssert.isTrue(null != idto.getCommodityId()," 参数有误!");

        //商品id查询是否有数据
        Example example = new Example(XdaCommodityAppStatus.class);
        example.createCriteria().andEqualTo("commodityId",idto.getCommodityId());
        XdaCommodityAppStatus xdaCommodityAppStatus = xdaCommodityAppStatusMapper.selectOneByExample(example);
        if(null != xdaCommodityAppStatus && null != xdaCommodityAppStatus.getId()){
            // 更新为下架
            XdaCommodityAppStatus xdaCommodityAppStatus1 = XdaCommodityAppStatus.forUpdateDownAppStatus(xdaCommodityAppStatus.getId());
            xdaCommodityAppStatusMapper.updateByPrimaryKeySelective(xdaCommodityAppStatus1);

            //保存日志
            XdaCommodityAppStatusLog xdaCommodityAppStatusLog = new XdaCommodityAppStatusLog(idto.getCommodityId(), 1, "商品总部不可售,鲜达上下架状态做相应的下架状态", idto.getUserId(), new Date());
            xdaCommodityAppStatusLogMapper.insertSelective(xdaCommodityAppStatusLog);
            PfCommodityAppStatusLog pfCommodityAppStatusLog = new PfCommodityAppStatusLog(idto.getCommodityId(), 1, "商品总部不可售,批发上下架状态做相应的下架状态", idto.getUserId(), new Date());
            pfCommodityAppStatusLogMapper.insertSelective(pfCommodityAppStatusLog);

            //发消息通知鲜达搜索
            sendMsgByEditStatus(Collections.singletonList(idto.getCommodityId()), null ,1,0);
        }

        return idto.getCommodityId();
    }


    /**
     * 发消息通知鲜达搜索
     * @param appType 1-鲜达，2-批发，0为所有
     */
    public void sendMsgByEditStatus(List<Long> commodityIdList, List<Long> newCommodityIdList, Integer appStatus, Integer appType) {
        // 事务提交后发送消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                //发消息通知鲜达搜索
                CommodityAppStatusDTO commodityAppStatusDTO = new CommodityAppStatusDTO(commodityIdList, newCommodityIdList, appStatus, appType);
                mqSenderComponent.send(QYApplicationContext.applicationNameSwitch + KafkaTopicEnum.ES_XDA_COMMODITY_APP_STATUS.getTopic(),
                        commodityAppStatusDTO, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.ES_XDA_COMMODITY_APP_STATUS.name(),
                        KafkaMessageOperationTypeEnum.UPDATE.name());
            }
        });
    }

    /**
     * 根据商品id 批量查询商品上下架状态
     */
    public List<XdaCommodityAppStatusODTO> batchSelectCommodityAppStatus(List<Long> commodityIdList) {
        QYAssert.isTrue(CollectionUtils.isNotEmpty(commodityIdList), " 参数有误!");
        return xdaCommodityAppStatusMapper.batchSelectCommodityAppStatus(commodityIdList);
    }
}
