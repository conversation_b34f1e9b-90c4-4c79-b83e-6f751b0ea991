package com.pinshang.qingyun.xda.product.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.product.dto.recommend.*;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityRecommendLogMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityRecommendMapper;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityMapper;
import com.pinshang.qingyun.xda.product.model.common.Commodity;
import com.pinshang.qingyun.xda.product.model.recommend.XdaCommodityRecommend;
import com.pinshang.qingyun.xda.product.model.recommend.XdaCommodityRecommendLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class XdaCommodityRecommendService {

    @Autowired
    private XdaCommodityRecommendMapper xdCommodityRecommendMapper;
    @Autowired
    private XdaCommodityRecommendLogMapper xdCommodityRecommendLogMapper;
    @Autowired
    private CommodityMapper commodityMapper;


    /**
     * 推荐方式列表
     * @param xdaCommodityRecommendIDTO
     * @return
     */
    public PageInfo<XdaCommodityRecommendODTO> findList(XdaCommodityRecommendIDTO xdaCommodityRecommendIDTO){
        PageInfo<XdaCommodityRecommendODTO> pageDate = PageHelper.startPage(xdaCommodityRecommendIDTO.getPageNo(), xdaCommodityRecommendIDTO.getPageSize()).doSelectPageInfo(() -> {
            xdCommodityRecommendMapper.findList(xdaCommodityRecommendIDTO);
        });
        return pageDate;
    }

    /**
     * 推荐方式列表 log
     * @param xdaCommodityRecommendLogIDTO
     * @return
     */
    public PageInfo<XdaCommodityRecommendLogODTO> findLogList(XdaCommodityRecommendLogIDTO xdaCommodityRecommendLogIDTO){
        PageInfo<XdaCommodityRecommendLogODTO> pageDate = PageHelper.startPage(xdaCommodityRecommendLogIDTO.getPageNo(), xdaCommodityRecommendLogIDTO.getPageSize()).doSelectPageInfo(() -> {
            xdCommodityRecommendLogMapper.findList(xdaCommodityRecommendLogIDTO);
        });
        return pageDate;
    }

    /**
     * 保存推荐方式
     * @param xdCommodityRecommendIDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void save(XdaCommodityRecommendSaveIDTO xdCommodityRecommendIDTO){
        log.error("xdCommodityRecommendIDTO.getCommodityCodes() = {}" ,xdCommodityRecommendIDTO.getCommodityCodes());
        QYAssert.isTrue(StringUtils.isNotBlank(xdCommodityRecommendIDTO.getCommodityCodes()), "推荐商品不能为空");
        String[] commodityArray = xdCommodityRecommendIDTO.getCommodityCodes().split("\n");
        QYAssert.isTrue(commodityArray.length <= 500,"推荐商品个数一次最多500个");
        List<String> codeLists = Stream.of(commodityArray).distinct().collect(Collectors.toList());
        List<String> codeExits = this.checkCommodityCodeExits(codeLists);
        QYAssert.isTrue(SpringUtil.isEmpty(codeExits), "商品编码不存在："+ StringUtils.join(codeExits,","));
        List<Commodity> commodities = this.getCommodities(codeLists);
        Date now = new Date();

        /***
         * 获取数据库中所有的推荐商品
         */
        List<XdaCommodityRecommend> xdCommodityRecommends = xdCommodityRecommendMapper.selectAll();
        List<Long> retainCommodity=new ArrayList<Long>();
        if(xdCommodityRecommends!=null && xdCommodityRecommends.size()>0){
            List<Long> exitsCommodity= xdCommodityRecommends.stream().map(XdaCommodityRecommend::getCommodityId).collect(Collectors.toList());
            /***
             * 获取差集失败
             */
           Map<Long,String> maps= commodities.stream().collect(Collectors.toMap(Commodity::getId,Commodity::getCommodityCode));
           Iterator<Long> iterator= exitsCommodity.iterator();
           while(iterator.hasNext()){
               Long cId=iterator.next();
               if(maps.containsKey(cId)){
                   retainCommodity.add(cId);
                   iterator.remove();
               }
           }

            Integer deleteCount = xdCommodityRecommendMapper.deleteAll();
            log.warn("删除推荐商品影响行数：{}",deleteCount);
            exitsCommodity.forEach(id ->{
                XdaCommodityRecommendLog xdAppQaLog = XdaCommodityRecommendLog.forDelete(id,xdCommodityRecommendIDTO.getUserId(),now);
                xdCommodityRecommendLogMapper.insert(xdAppQaLog);
            });
        }


        for (Commodity commodity : commodities) {
            XdaCommodityRecommend xdAppQa = XdaCommodityRecommend.forInsert(commodity.getId(),xdCommodityRecommendIDTO.getUserId(),now);
            xdCommodityRecommendMapper.insert(xdAppQa);
            if(retainCommodity.size()>0){
                if(retainCommodity.contains(commodity.getId())){
                    continue;
                }
            }
            XdaCommodityRecommendLog xdAppQaLog = XdaCommodityRecommendLog.forInsert(commodity.getId(),xdAppQa.getCreateId(),now);
            xdCommodityRecommendLogMapper.insert(xdAppQaLog);
        }
    }

    /**
     * 删除推荐方式
     * @param id
     */
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id,Long userId){
        XdaCommodityRecommend xdCommodityRecommend1 = xdCommodityRecommendMapper.selectByPrimaryKey(id);
        QYAssert.isTrue(null != xdCommodityRecommend1, "删除失败，推荐商品不存在");
        Date now = new Date();
        int delete = xdCommodityRecommendMapper.deleteByPrimaryKey(id);
        log.warn("删除推荐商品影响行数：{}",delete);
        XdaCommodityRecommendLog xdAppQaLog = XdaCommodityRecommendLog.forDelete(xdCommodityRecommend1.getCommodityId(),userId,now);
        xdCommodityRecommendLogMapper.insert(xdAppQaLog);
    }

    /**
     * 查询不存在的商品编码
     * @param commodityCodes
     * @return 不存在的商品编码
     */
    public List<String> checkCommodityCodeExits(List<String> commodityCodes){
        /*** 数据库存在的商品 */
        List<Commodity> commodityDataCodes = getCommodities(commodityCodes);
        if(commodityDataCodes==null || commodityDataCodes.isEmpty()){
            return commodityCodes;
        }
        if(commodityCodes.size() != commodityDataCodes.size()){
            List<String> exitsCommodityCode= commodityDataCodes.stream().map(Commodity::getCommodityCode).collect(Collectors.toList());
            /***
             * 当removeAll 处理差集失败后
             * 走循环iterator.remove处理
             */
            if(!commodityCodes.removeAll(exitsCommodityCode)){
                Map<String,String> maps=commodityDataCodes.stream().collect(Collectors.toMap(Commodity::getCommodityCode,Commodity::getCommodityName));
                Iterator<String> iterator = commodityCodes.iterator();
                while (iterator.hasNext()){
                    String commodityCode = iterator.next();
                    if(StringUtils.isBlank(commodityCode)){
                        iterator.remove();
                    }else {
                        if (maps.containsKey(commodityCode)) {
                            iterator.remove();
                        }
                    }
                }
            }
            return commodityCodes;
        }else{
            return null;
        }
    }



    public List<Commodity> getCommodities(List<String> commodityCodes) {
        Example ex = new Example(Commodity.class);
        ex.createCriteria().andIn("commodityCode", commodityCodes);
        return commodityMapper.selectByExample(ex);
    }

}