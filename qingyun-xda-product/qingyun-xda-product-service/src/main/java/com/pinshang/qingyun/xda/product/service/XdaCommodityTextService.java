package com.pinshang.qingyun.xda.product.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.commodity.CommodityTextOperateTypeEnum;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.xda.product.dto.SpecialResultODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.*;
import com.pinshang.qingyun.xda.product.dto.kafka.EsXdaCommodityTextChangeKafkaVO;
import com.pinshang.qingyun.xda.product.helper.CommonHelper;
import com.pinshang.qingyun.xda.product.mapper.*;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityMapper;
import com.pinshang.qingyun.xda.product.model.*;
import com.pinshang.qingyun.xda.product.model.XdaCommodityTextPic.PicTypeEnums;
import com.pinshang.qingyun.xda.product.model.common.Commodity;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 鲜达商品文描
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Service
public class XdaCommodityTextService {
	
	// 商品图片-普通图片，默认值
	private static final Integer DEFAULT_YES = XdaCommodityTextPic.IsDefaultEnums.YES.getCode();
	
	// 商品图片类型：普通图片、长图
	private static final Integer PIC_TYPE = PicTypeEnums.PIC.getCode();
	private static final Integer LONG_PIC_TYPE = PicTypeEnums.LONG_PIC.getCode();
	
	// 文描管理相关-商品日志类型
	private static final Integer IMPORT_COMMODITY_APP_NAME = CommodityTextOperateTypeEnum.IMPORT_COMMODITY_APP_NAME.getCode();
	private static final Integer IMPORT_COMMODITY_SUB_NAME = CommodityTextOperateTypeEnum.IMPORT_COMMODITY_SUB_NAME.getCode();
	private static final Integer IMPORT_FRONT_CATEGORY = CommodityTextOperateTypeEnum.IMPORT_FRONT_CATEGORY.getCode();
	private static final Integer IMPORT_TAG = CommodityTextOperateTypeEnum.IMPORT_TAG.getCode();
	private static final Integer UPDATE_COMMODITY_APP_NAME = CommodityTextOperateTypeEnum.UPDATE_COMMODITY_APP_NAME.getCode();
	private static final Integer UPDATE_COMMODITY_SUB_NAME = CommodityTextOperateTypeEnum.UPDATE_COMMODITY_SUB_NAME.getCode();
	private static final Integer UPDATE_FRONT_CATEGORY = CommodityTextOperateTypeEnum.UPDATE_FRONT_CATEGORY.getCode();
	private static final Integer IMPORT_SORT_NUM = CommodityTextOperateTypeEnum.IMPORT_SORT_NUM.getCode();
	private static final Integer UPDATE_SORT_NUM = CommodityTextOperateTypeEnum.UPDATE_SORT_NUM.getCode();
	private static final Integer IMPORT_QUALITY_STATUS = CommodityTextOperateTypeEnum.IMPORT_QUALITY_STATUS.getCode();

	@Autowired
	private XdaTagMapper xdaTagMapper;
	
	@Autowired
    private CommonHelper commonHelper;
	
	@Autowired
	private CommodityMapper commodityMapper;
	
	@Autowired
    private XdaCategoryMapper xdaCategoryMapper;
	
    @Autowired
    private XdaCommodityTextMapper xdaCommodityTextMapper;
    
    @Autowired
    private XdaCommodityTextPicMapper xdaCommodityTextPicMapper;
    
    @Autowired
    private XdaCommodityTextLogMapper xdaCommodityTextLogMapper;
    
    @Autowired
    private XdaCommodityAppStatusMapper xdaCommodityAppStatusMapper;

	@Autowired
	private IMqSenderComponent mqSenderComponent;
    
    @Value("${application.name.switch}")
    private String applicationNameSwitch;
    
//    @Value("${pinshang.img-server-url}")
//    private String imgServerUrl;
	@Value("${pinshang.img-xd-server-url}")
	private String imgXdServerUrl;

    /**
     * 分页查询  商品文描信息列表
     * 
     * @param idto
     * @return
     */
    @Transactional(readOnly = true)
	public PageInfo<CommodityTextInfoODTO> selectCommodityTextInfoPage(SelectCommodityTextInfoPageIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		
		// 根据storeId  子查询会太慢，拆解参数
		Long storeId = idto.getStoreId();
		if (null != storeId) {
			List<Long> commodityIdListByStoreId = xdaCommodityTextMapper.selectCommodityIdListByStoreId(storeId);
			idto.setCommodityIdListByStoreId(commodityIdListByStoreId);
		}
		
		PageInfo<CommodityTextInfoODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
			xdaCommodityTextMapper.selectCommodityTextInfoList(idto);
		});
		
		List<CommodityTextInfoODTO> list = pageInfo.getList();
		if (SpringUtil.isNotEmpty(list)) {
			// 商品单位
			List<Long> dictionaryIdList = list.stream().filter(o -> {return null != o.getCommodityUnitId();}).map(CommodityTextInfoODTO::getCommodityUnitId).collect(Collectors.toList());
			Map<Long, String> dictionaryNameMap = commonHelper.getDictionaryNameMap(dictionaryIdList);
			
			// 前台品类
			List<Long> xdaCategoryIdList = list.stream().filter(o -> {return null != o.getXdaFirstCategoryId();}).map(CommodityTextInfoODTO::getXdaFirstCategoryId).collect(Collectors.toList());
			xdaCategoryIdList.addAll(list.stream().filter(o -> {return null != o.getXdaSecondCategoryId();}).map(CommodityTextInfoODTO::getXdaSecondCategoryId).collect(Collectors.toList()));
			Map<Long, String> xdaCategoryNameMap = commonHelper.getXdaCategoryNameMap(xdaCategoryIdList);
			
			// 图片/长图
			List<Long> commodityIdList = list.stream().map(CommodityTextInfoODTO::getId).collect(Collectors.toList());
			Map<Long, List<CommodityTextPicODTO>> picListMap = new HashMap<>();
			Map<Long, String> longPicUrlMap = new HashMap<>();
			List<XdaCommodityTextPic> textPicList = xdaCommodityTextPicMapper.selectCommodityTextPicList(new SelectCommodityTextPicListIDTO(commodityIdList));
			if (SpringUtil.isNotEmpty(textPicList)) {
				List<XdaCommodityTextPic> picList = textPicList.stream().filter(commoditypic -> {return PIC_TYPE.equals(commoditypic.getPicType());}).collect(Collectors.toList());
				List<XdaCommodityTextPic> longPicList = textPicList.stream().filter(commoditypic -> {return LONG_PIC_TYPE.equals(commoditypic.getPicType());}).collect(Collectors.toList());
				picList.forEach(pic -> {this.buildPicListMap(picListMap, pic);});
				longPicUrlMap.putAll(longPicList.stream().collect(Collectors.toMap(XdaCommodityTextPic::getCommodityId, XdaCommodityTextPic::getPicUrl)));
			}
			
			// 标签
			List<Long> tagIdList = list.stream().filter(o -> {return null != o.getTagId();}).map(CommodityTextInfoODTO::getTagId).collect(Collectors.toList());
			Map<Long, XdaTag> tagMap = commonHelper.getTagMap(tagIdList);
			
			// 后台品类
			List<Long> categoryIdList = list.stream().map(CommodityTextInfoODTO::getFirstCategoryId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
			categoryIdList.addAll(list.stream().map(CommodityTextInfoODTO::getSecondCategoryId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
			categoryIdList.addAll(list.stream().map(CommodityTextInfoODTO::getThirdCategoryId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
			Map<String, String> categoryNameMap = commonHelper.getCategoryNameMap(categoryIdList);
			
			// 商品条码
			Map<Long, String> subBarCodesMap = commonHelper.getSubBarCodesMap(commodityIdList);
			
			list.forEach(o -> {
				// 商品单位CommonHelper
				o.setCommodityUnitName(dictionaryNameMap.get(o.getCommodityUnitId()));
				
				// 前台品类
				o.setXdaFirstCategoryName(xdaCategoryNameMap.get(o.getXdaFirstCategoryId()));
				o.setXdaSecondCategoryName(xdaCategoryNameMap.get(o.getXdaSecondCategoryId()));
				
				// 图片/长图
				Long commodityId = o.getId();
				o.setPicList(picListMap.get(commodityId));
				o.setLongPicUrl(StringUtil.isNullOrEmpty(longPicUrlMap.get(commodityId))? "": longPicUrlMap.get(commodityId).trim());
				o.setVisitLongPicUrl(StringUtil.isNullOrEmpty(longPicUrlMap.get(commodityId))? "": imgXdServerUrl + longPicUrlMap.get(commodityId).trim());
				
				// 标签
				XdaTag tag = tagMap.get(o.getTagId());
				if (null != tag) {
					o.setTagName(tag.getTagName());
					o.setTagBgColor(tag.getTagBgColor());
				}
				
				// 后台品类
				o.setFirstCategoryName(categoryNameMap.get(o.getFirstCategoryId() + ""));
				o.setSecondCategoryName(categoryNameMap.get(o.getSecondCategoryId() + ""));
				o.setThirdCategoryName(categoryNameMap.get(o.getThirdCategoryId() + ""));
				
				// 商品条码
				o.setSubBarCodes(subBarCodesMap.get(commodityId));
			});
		}
		return pageInfo;
	}
	private void buildPicListMap(Map<Long, List<CommodityTextPicODTO>> picListMap, XdaCommodityTextPic commodityPic) {
		if (null != commodityPic) {
			Long commodityId = commodityPic.getCommodityId();
			List<CommodityTextPicODTO> picList = null != picListMap.get(commodityId)? picListMap.get(commodityId): new ArrayList<>();;
			picList.add(new CommodityTextPicODTO(commodityPic.getPicUrl(), commodityPic.getIsDefault(), imgXdServerUrl + commodityPic.getPicUrl()));
			picListMap.put(commodityId, picList);
		}
	}
	
	/**
     * 分页查询  商品排序信息  列表
     *
     * @param idto
     * @return
     */
    @Transactional(readOnly = true)
	public PageInfo<CommoditySortNumInfoODTO> selectCommoditySortNumInfoPage(SelectCommoditySortNumInfoPageIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		PageInfo<CommoditySortNumInfoODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
			xdaCommodityTextMapper.selectCommoditySortNumInfoList(idto);
		});
		
		List<CommoditySortNumInfoODTO> list = pageInfo.getList();
		if (SpringUtil.isNotEmpty(list)) {
			// 后台品类
			List<Long> categoryIdList = list.stream().map(CommoditySortNumInfoODTO::getFirstCategoryId).filter(Objects::nonNull).collect(Collectors.toList());
			categoryIdList.addAll(list.stream().map(CommoditySortNumInfoODTO::getSecondCategoryId).filter(Objects::nonNull).collect(Collectors.toList()));
			categoryIdList.addAll(list.stream().map(CommoditySortNumInfoODTO::getThirdCategoryId).filter(Objects::nonNull).collect(Collectors.toList()));
			Map<String, String> categoryMap = commonHelper.getCategoryNameMap(categoryIdList);
			list.forEach(o -> {
				String firstCategoryName = categoryMap.get(o.getFirstCategoryId() + "");
				String secondCategoryName = categoryMap.get(o.getSecondCategoryId() + "");
				String thirdCategoryName = categoryMap.get(o.getThirdCategoryId() + "");
				if (!StringUtil.isNullOrEmpty(firstCategoryName) && !StringUtil.isNullOrEmpty(secondCategoryName) && !StringUtil.isNullOrEmpty(thirdCategoryName)) {
					o.setCategoryName(firstCategoryName.trim() + " / " + secondCategoryName.trim() + " / " + thirdCategoryName.trim());
				}
			});
		}
		return pageInfo;
	}
	
	/**
	 * 分页查询  商品文描日志信息列表
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(readOnly = true)
	public PageInfo<CommodityTextLogInfoODTO> selectCommodityTextLogInfoPage(SelectCommodityTextLogInfoPageIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		PageInfo<CommodityTextLogInfoODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
			xdaCommodityTextMapper.selectCommodityTextLogInfoList(idto);
		});
		return pageInfo;
	}
	
	/**
	 * 批量更新  商品文描-前台品名
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public SpecialResultODTO batchUpdateCommodityAppName(BatchUpdateCommodityAppNameIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		List<CommodityAppNameIDTO> commodityAppNameInfoList = idto.getCommodityAppNameList();

		// 校验参数
		QYAssert.isTrue(SpringUtil.isNotEmpty(commodityAppNameInfoList), "未获得文档信息!");
		List<String> commodityCodeList = commodityAppNameInfoList.stream().filter(o -> {return null != o && !StringUtil.isNullOrEmpty(o.getCommodityCode());}).map(CommodityAppNameIDTO::getCommodityCode).collect(Collectors.toList());
		Map<String, XdaPriceStructureCommodityTextInfoODTO> xdaPriceStructureCommodityTextInfoMap = this.checkXdPriceStructureCommodityTextInfoAndGetMap(commodityCodeList);

    	Date updateTime = new Date();
    	Long updateId = idto.getUserId();
    	List<Long> commodityIdList = new ArrayList<>();
    	List<String> commodityCodeEmptyList = new ArrayList<>();
    	List<String> commodityAppNameErrorList = new ArrayList<>();
		List<String> commodityCodeErrorList = new ArrayList<>();
		List<UpdateCommodityAppNameIDTO> newCommodityAppNameList = new ArrayList<>();
		List<XdaCommodityTextLog> commodityTextLogList = new ArrayList<>();
		List<EsXdaCommodityTextChangeKafkaVO> kafkaVoList = new ArrayList<>();
		for (int i = 0; i < commodityAppNameInfoList.size(); i ++) {
			CommodityAppNameIDTO commodityAppNameInfo = commodityAppNameInfoList.get(i);
			if (null == commodityAppNameInfo || StringUtil.isNullOrEmpty(commodityAppNameInfo.getCommodityCode())) {
				commodityCodeEmptyList.add("第" + (i + 1 + 1) + "行");
				continue;
			}
			if (StringUtil.isNullOrEmpty(commodityAppNameInfo.getCommodityAppName()) || commodityAppNameInfo.getCommodityAppName().trim().length() > 20) {
				commodityAppNameErrorList.add("第" + (i + 1 + 1) + "行，" + (StringUtil.isNullOrEmpty(commodityAppNameInfo.getCommodityAppName())? "": commodityAppNameInfo.getCommodityAppName()));
				continue;
			}
			commodityAppNameInfo.setCommodityAppName(commodityAppNameInfo.getCommodityAppName().trim());
			
			String commodityCode = commodityAppNameInfo.getCommodityCode().trim();
			XdaPriceStructureCommodityTextInfoODTO xdaPriceStructureCommodityTextInfo = xdaPriceStructureCommodityTextInfoMap.get(commodityCode);
			if (null == xdaPriceStructureCommodityTextInfo) {
				commodityCodeErrorList.add("第" + (i + 1 + 1) + "行，" + commodityCode);
				continue;
			}
			Long commodityId = xdaPriceStructureCommodityTextInfo.getCommodityId();
			String newCommodityAppName = StringUtil.isNullOrEmpty(commodityAppNameInfo.getCommodityAppName())? null: commodityAppNameInfo.getCommodityAppName();
			newCommodityAppNameList.add(new UpdateCommodityAppNameIDTO(commodityId, newCommodityAppName));
			commodityTextLogList.add(new XdaCommodityTextLog(commodityId, IMPORT_COMMODITY_APP_NAME, xdaPriceStructureCommodityTextInfo.getCommodityAppName(), newCommodityAppName, JSON.toJSONString(commodityAppNameInfo), updateId, updateTime));
			commodityIdList.add(commodityId);

			//消息数据
			kafkaVoList.add(new EsXdaCommodityTextChangeKafkaVO(commodityId));
		}
		
		List<String> errorMsgList = new ArrayList<>();
		if (SpringUtil.isNotEmpty(commodityCodeEmptyList)) {
			commodityCodeEmptyList.add(0, "以下商品编码不能为空：");
			errorMsgList.addAll(commodityCodeEmptyList);
		}
		if (SpringUtil.isNotEmpty(commodityAppNameErrorList)) {
			commodityAppNameErrorList.add(0, "以下前台品名  不能为空且不能超过20个字：");
			errorMsgList.addAll(commodityAppNameErrorList);
		}
		if (SpringUtil.isNotEmpty(commodityCodeErrorList)) {
			commodityCodeErrorList.add(0, "未找到以下商品编码：");
			errorMsgList.addAll(commodityCodeErrorList);
		}
		if (SpringUtil.isNotEmpty(errorMsgList)) {
			errorMsgList.add(0, "导入失败。");
			return new SpecialResultODTO(false, errorMsgList);
		}
		
		List<String> commodityCodeRepeatList = this.checkCommodityCodeRepeated(commodityCodeList);
		if (SpringUtil.isNotEmpty(commodityCodeRepeatList)) {
			commodityCodeRepeatList.add(0, "导入失败。以下商品编码重复：");
			return new SpecialResultODTO(false, commodityCodeRepeatList);
		}

		// 批量更新  商品文描-前台品名
		newCommodityAppNameList.forEach(o -> {
			this.updateCommodityAppName(o.getCommodityId(), o.getCommodityAppName(), updateId, updateTime);
		});

		// 批量插入日志
		if (SpringUtil.isNotEmpty(commodityTextLogList)) {
			xdaCommodityTextLogMapper.insertList(commodityTextLogList);
		}

		//发消息通知鲜达搜索
		if(SpringUtil.isNotEmpty(kafkaVoList)){
			mqSenderComponent.send(QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XDA_COMMODITY_TEXT_CHANGE_TOPIC.getTopic(),
					kafkaVoList, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XDA_COMMODITY_TEXT_CHANGE_TOPIC.name(),
					KafkaMessageOperationTypeEnum.UPDATE.name());
		}

        return new SpecialResultODTO(true, newCommodityAppNameList.size());
	}
	
	/**
	 * 批量更新  商品文描-副标题
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public SpecialResultODTO batchUpdateCommoditySubName(BatchUpdateCommoditySubNameIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		List<CommoditySubNameIDTO> commoditySubNameInfoList = idto.getCommoditySubNameList();

		// 校验参数
		QYAssert.isTrue(SpringUtil.isNotEmpty(commoditySubNameInfoList), "未获得文档信息!");
		List<String> commodityCodeList = commoditySubNameInfoList.stream().filter(o -> {return null != o && !StringUtil.isNullOrEmpty(o.getCommodityCode());}).map(CommoditySubNameIDTO::getCommodityCode).collect(Collectors.toList());
		Map<String, XdaPriceStructureCommodityTextInfoODTO> xdaPriceStructureCommodityTextInfoMap = this.checkXdPriceStructureCommodityTextInfoAndGetMap(commodityCodeList);

    	Date updateTime = new Date();
    	Long updateId = idto.getUserId();
    	List<Long> commodityIdList = new ArrayList<>();
    	List<String> commodityCodeEmptyList = new ArrayList<>();
    	List<String> commoditySubNameErrorList = new ArrayList<>();
		List<String> commodityCodeErrorList = new ArrayList<>();
		List<UpdateCommoditySubNameIDTO> newCommoditySubNameList = new ArrayList<>();
		List<XdaCommodityTextLog> commodityTextLogList = new ArrayList<>();
		List<EsXdaCommodityTextChangeKafkaVO> kafkaVoList = new ArrayList<>();
		for (int i = 0; i < commoditySubNameInfoList.size(); i ++) {
			CommoditySubNameIDTO commoditySubNameInfo = commoditySubNameInfoList.get(i);
			if (null == commoditySubNameInfo || StringUtil.isNullOrEmpty(commoditySubNameInfo.getCommodityCode())) {
				commodityCodeEmptyList.add("第" + (i + 1 + 1) + "行");
				continue;
			}
			if (!StringUtil.isNullOrEmpty(commoditySubNameInfo.getCommoditySubName()) && commoditySubNameInfo.getCommoditySubName().trim().length() > 20) {
				commoditySubNameErrorList.add("第" + (i + 1 + 1) + "行，" + commoditySubNameInfo.getCommoditySubName());
				continue;
			}
			String commodityCode = commoditySubNameInfo.getCommodityCode().trim();
			XdaPriceStructureCommodityTextInfoODTO xdaPriceStructureCommodityTextInfo = xdaPriceStructureCommodityTextInfoMap.get(commodityCode);
			if (null == xdaPriceStructureCommodityTextInfo) {
				commodityCodeErrorList.add("第" + (i + 1 + 1) + "行，" + commodityCode);
				continue;
			}
			Long commodityId = xdaPriceStructureCommodityTextInfo.getCommodityId();
			String newCommoditySubName = StringUtil.isNullOrEmpty(commoditySubNameInfo.getCommoditySubName())? null: commoditySubNameInfo.getCommoditySubName();
			newCommoditySubNameList.add(new UpdateCommoditySubNameIDTO(commodityId, newCommoditySubName));
			commodityTextLogList.add(new XdaCommodityTextLog(commodityId, IMPORT_COMMODITY_SUB_NAME, xdaPriceStructureCommodityTextInfo.getCommoditySubName(), newCommoditySubName, JSON.toJSONString(commoditySubNameInfo), updateId, updateTime));
			commodityIdList.add(commodityId);

			//消息数据
			kafkaVoList.add(new EsXdaCommodityTextChangeKafkaVO(commodityId));
		}
		
		List<String> errorMsgList = new ArrayList<>();
		if (SpringUtil.isNotEmpty(commodityCodeEmptyList)) {
			commodityCodeEmptyList.add(0, "以下商品编码不能为空：");
			errorMsgList.addAll(commodityCodeEmptyList);
		}
		if (SpringUtil.isNotEmpty(commoditySubNameErrorList)) {
			commoditySubNameErrorList.add(0, "以下副标题  不能超过20个字：");
			errorMsgList.addAll(commoditySubNameErrorList);
		}
		if (SpringUtil.isNotEmpty(commodityCodeErrorList)) {
			commodityCodeErrorList.add(0, "未找到以下商品编码：");
			errorMsgList.addAll(commodityCodeErrorList);
		}
		if (SpringUtil.isNotEmpty(errorMsgList)) {
			errorMsgList.add(0, "导入失败。");
			return new SpecialResultODTO(false, errorMsgList);
		}
		
		List<String> commodityCodeRepeatList = this.checkCommodityCodeRepeated(commodityCodeList);
		if (SpringUtil.isNotEmpty(commodityCodeRepeatList)) {
			commodityCodeRepeatList.add(0, "导入失败。以下商品编码重复：");
			return new SpecialResultODTO(false, commodityCodeRepeatList);
		}

		// 批量更新  商品文描-副标题
		newCommoditySubNameList.forEach(o -> {
			this.updateCommoditySubName(o.getCommodityId(), o.getCommoditySubName(), updateId, updateTime);
		});

		// 批量插入日志
		if (SpringUtil.isNotEmpty(commodityTextLogList)) {
			xdaCommodityTextLogMapper.insertList(commodityTextLogList);
		}

		//发消息通知鲜达搜索
		if(SpringUtil.isNotEmpty(kafkaVoList)){
			mqSenderComponent.send(QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XDA_COMMODITY_TEXT_CHANGE_TOPIC.getTopic(),
					kafkaVoList, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XDA_COMMODITY_TEXT_CHANGE_TOPIC.name(),
					KafkaMessageOperationTypeEnum.UPDATE.name());
		}

        return new SpecialResultODTO(true, newCommoditySubNameList.size());
	}
	
	/**
	 * 批量更新  商品文描-前台品类
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public SpecialResultODTO batchUpdateCommodityXdaCategory(BatchUpdateCommodityXdaCategoryIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		List<CommodityXdaCategoryIDTO> commodityXdaCategoryInfoList = idto.getCommodityXdaCategoryList();

		// 校验参数
		QYAssert.isTrue(SpringUtil.isNotEmpty(commodityXdaCategoryInfoList), "未获得文档信息!");
		List<String> commodityCodeList = commodityXdaCategoryInfoList.stream().filter(o -> {return null != o && !StringUtil.isNullOrEmpty(o.getCommodityCode());}).map(CommodityXdaCategoryIDTO::getCommodityCode).collect(Collectors.toList());
    	Map<String, XdaPriceStructureCommodityTextInfoODTO> xdaPriceStructureCommodityTextInfoMap = this.checkXdPriceStructureCommodityTextInfoAndGetMap(commodityCodeList);

    	Date updateTime = new Date();
    	Long updateId = idto.getUserId();
    	List<Long> commodityIdList = new ArrayList<>();
    	List<String> commodityCodeEmptyList = new ArrayList<>();
    	List<String> commodityCodeErrorList = new ArrayList<>();
		List<String> xdaCategoryMsgList = new ArrayList<>();
		List<XdaCommodityCategory> xdaCommodityCategoryList = new ArrayList<>();
		List<XdaCommodityTextLog> commodityTextLogList = new ArrayList<>();
		List<EsXdaCommodityTextChangeKafkaVO> kafkaVoList = new ArrayList<>();
		for (int i = 0; i < commodityXdaCategoryInfoList.size(); i ++) {
			CommodityXdaCategoryIDTO commodityXdaCategoryInfo = commodityXdaCategoryInfoList.get(i);
			if (null == commodityXdaCategoryInfo || StringUtil.isNullOrEmpty(commodityXdaCategoryInfo.getCommodityCode())) {
				commodityCodeEmptyList.add("第" + (i + 1 + 1) + "行");
				continue;
			}
			String commodityCode = commodityXdaCategoryInfo.getCommodityCode().trim();
			XdaPriceStructureCommodityTextInfoODTO commodity = xdaPriceStructureCommodityTextInfoMap.get(commodityCode);
			if (null == commodity) {
				commodityCodeErrorList.add("第" + (i + 1 + 1) + "行，" + commodityCode);
				continue;
			}

			String xdaCategoryName = null;
			Long xdaFirstCategoryId = null, xdaSecondCategoryId = null;
			String xdaFirstCategoryName = commodityXdaCategoryInfo.getXdaFirstCategoryName();
			String xdaSecondCategoryName = commodityXdaCategoryInfo.getXdaSecondCategoryName();
			if (!StringUtil.isNullOrEmpty(xdaFirstCategoryName) && !StringUtil.isNullOrEmpty(xdaSecondCategoryName)) {
				XdaCommodityTextCategoryInfoODTO xdaCategoryInfo = xdaCommodityTextMapper.selectXdaCommodityTextCategoryInfo(new SelectXdaCommodityTextCategoryInfoIDTO(xdaFirstCategoryName.trim(), xdaSecondCategoryName.trim()));
				if (null != xdaCategoryInfo) {
					xdaCategoryName = xdaFirstCategoryName.trim() + " / " + xdaSecondCategoryName.trim();
					xdaFirstCategoryId = xdaCategoryInfo.getXdaFirstCategoryId();
					xdaSecondCategoryId = xdaCategoryInfo.getXdaSecondCategoryId();
				}
			}
			if (null == xdaFirstCategoryId || null == xdaSecondCategoryId) {
				xdaCategoryMsgList.add("第" + (i + 1 + 1) + "行，前台品类不能为空，且真实存在");
				continue;
			}

			Long commodityId = commodity.getCommodityId();
			String oldXdCategoryName = StringUtil.isNullOrEmpty(commodity.getXdaFirstCategoryName())? null: commodity.getXdaFirstCategoryName() + " / " + commodity.getXdaSecondCategoryName();
			xdaCommodityCategoryList.add(new XdaCommodityCategory(commodityId, xdaFirstCategoryId, xdaSecondCategoryId));
			commodityTextLogList.add(new XdaCommodityTextLog(commodityId, IMPORT_FRONT_CATEGORY, oldXdCategoryName, xdaCategoryName, JSON.toJSONString(commodityXdaCategoryInfo), updateId, updateTime));
			commodityIdList.add(commodityId);

			//消息数据
			kafkaVoList.add(new EsXdaCommodityTextChangeKafkaVO(commodityId));
		}
		
		List<String> errorMsgList = new ArrayList<>();
		if (SpringUtil.isNotEmpty(commodityCodeEmptyList)) {
			commodityCodeEmptyList.add(0, "以下商品编码不能为空：");
			errorMsgList.addAll(commodityCodeEmptyList);
		}
		if (SpringUtil.isNotEmpty(commodityCodeErrorList)) {
			commodityCodeErrorList.add(0, "未找到以下商品编码：");
			errorMsgList.addAll(commodityCodeErrorList);
		}
		if (SpringUtil.isNotEmpty(xdaCategoryMsgList)) {
			xdaCategoryMsgList.add(0, "以下前台品类不合法：");
			errorMsgList.addAll(xdaCategoryMsgList);
		}
		if (SpringUtil.isNotEmpty(errorMsgList)) {
			errorMsgList.add(0, "导入失败。");
			return new SpecialResultODTO(false, errorMsgList);
		}
		
		List<String> commodityCodeRepeatList = this.checkCommodityCodeRepeated(commodityCodeList);
		if (SpringUtil.isNotEmpty(commodityCodeRepeatList)) {
			commodityCodeRepeatList.add(0, "导入失败。以下商品编码重复：");
			return new SpecialResultODTO(false, commodityCodeRepeatList);
		}

		// 批量更新  商品文描-前台品类
		xdaCommodityCategoryList.forEach(o -> {
			this.updateCommodityXdaCategory(o.getCommodityId(), o.getXdaFirstCategoryId(), o.getXdaSecondCategoryId(), updateId, updateTime);
		});

		// 批量插入日志
		if (SpringUtil.isNotEmpty(commodityTextLogList)) {
			xdaCommodityTextLogMapper.insertList(commodityTextLogList);
		}

		//发消息通知鲜达搜索
		if(SpringUtil.isNotEmpty(kafkaVoList)){
			mqSenderComponent.send(QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XDA_COMMODITY_TEXT_CHANGE_TOPIC.getTopic(),
					kafkaVoList, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XDA_COMMODITY_TEXT_CHANGE_TOPIC.name(),
					KafkaMessageOperationTypeEnum.UPDATE.name());
		}
        return new SpecialResultODTO(true, xdaCommodityCategoryList.size());
	}


	
	/**
	 * 批量更新  商品文描-标签
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public SpecialResultODTO batchUpdateCommodityTag(BatchUpdateCommodityTagIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		List<CommodityTagIDTO> commodityTagInfoList = idto.getCommodityTagList();

		// 校验参数
		QYAssert.isTrue(SpringUtil.isNotEmpty(commodityTagInfoList), "未获得文档信息!");
		List<String> commodityCodeList = commodityTagInfoList.stream().filter(o -> {return null != o && !StringUtil.isNullOrEmpty(o.getCommodityCode());}).map(CommodityTagIDTO::getCommodityCode).collect(Collectors.toList());
		Map<String, XdaPriceStructureCommodityTextInfoODTO> xdaPriceStructureCommodityTextInfoMap = this.checkXdPriceStructureCommodityTextInfoAndGetMap(commodityCodeList);

    	Date updateTime = new Date();
    	Long updateId = idto.getUserId();
    	List<Long> commodityIdList = new ArrayList<>();
    	List<String> commodityCodeEmptyList = new ArrayList<>();
		List<String> commodityCodeErrorList = new ArrayList<>();
		List<String> tagErrorList = new ArrayList<>();
		List<UpdateCommodityTagIDTO> newCommodityTagList = new ArrayList<>();
		List<XdaCommodityTextLog> commodityTextLogList = new ArrayList<>();
		List<EsXdaCommodityTextChangeKafkaVO> kafkaVoList = new ArrayList<>();
		for (int i = 0; i < commodityTagInfoList.size(); i ++) {
			CommodityTagIDTO commodityTagInfo = commodityTagInfoList.get(i);
			if (null == commodityTagInfo || StringUtil.isNullOrEmpty(commodityTagInfo.getCommodityCode())) {
				commodityCodeEmptyList.add("第" + (i + 1 + 1) + "行");
				continue;
			}
			String commodityCode = commodityTagInfo.getCommodityCode().trim();
			XdaPriceStructureCommodityTextInfoODTO xdaPriceStructureCommodityTextInfo = xdaPriceStructureCommodityTextInfoMap.get(commodityCode);
			if (null == xdaPriceStructureCommodityTextInfo) {
				commodityCodeErrorList.add("第" + (i + 1 + 1) + "行，" + commodityCode);
				continue;
			}
			
			Long tagId = null;
			String tagName = commodityTagInfo.getTagName();
			if (!StringUtil.isNullOrEmpty(tagName)) {
				XdaTag tag = xdaTagMapper.selectOne(new XdaTag(tagName.trim()));
				if (null != tag && tag.getStatus().equals(1)) {
					tagId = tag.getId();
				}
				if (null == tagId) {
					tagErrorList.add("第" + (i + 1 + 1) + "行，标签无效");
					continue;
				}
			}
			if (null == tagId) {
				tagErrorList.add("第" + (i + 1 + 1) + "行，标签不能为空，且真实有效");
				continue;
			}
			
			Long commodityId = xdaPriceStructureCommodityTextInfo.getCommodityId();
			newCommodityTagList.add(new UpdateCommodityTagIDTO(commodityId, tagId));
			commodityTextLogList.add(new XdaCommodityTextLog(commodityId, IMPORT_TAG, xdaPriceStructureCommodityTextInfo.getTagName(), tagName, JSON.toJSONString(commodityTagInfo), updateId, updateTime));
			commodityIdList.add(commodityId);

			//消息数据
			kafkaVoList.add(new EsXdaCommodityTextChangeKafkaVO(commodityId));
		}
		
		List<String> errorMsgList = new ArrayList<>();
		if (SpringUtil.isNotEmpty(commodityCodeEmptyList)) {
			commodityCodeEmptyList.add(0, "以下商品编码不能为空：");
			errorMsgList.addAll(commodityCodeEmptyList);
		}
		if (SpringUtil.isNotEmpty(commodityCodeErrorList)) {
			commodityCodeErrorList.add(0, "未找到以下商品编码：");
			errorMsgList.addAll(commodityCodeErrorList);
		}
		if (SpringUtil.isNotEmpty(tagErrorList)) {
			tagErrorList.add(0, "以下标签不合法：");
			errorMsgList.addAll(tagErrorList);
		}
		if (SpringUtil.isNotEmpty(errorMsgList)) {
			errorMsgList.add(0, "导入失败。");
			return new SpecialResultODTO(false, errorMsgList);
		}
		
		List<String> commodityCodeRepeatList = this.checkCommodityCodeRepeated(commodityCodeList);
		if (SpringUtil.isNotEmpty(commodityCodeRepeatList)) {
			commodityCodeRepeatList.add(0, "导入失败。以下商品编码重复：");
			return new SpecialResultODTO(false, commodityCodeRepeatList);
		}

		// 批量更新  商品文描-标签
		newCommodityTagList.forEach(o -> {
			this.updateCommodityTag(o.getCommodityId(), o.getTagId(), updateId, updateTime);
		});

		// 批量插入日志
		if (SpringUtil.isNotEmpty(commodityTextLogList)) {
			xdaCommodityTextLogMapper.insertList(commodityTextLogList);
		}

		//发消息通知鲜达搜索
		if(SpringUtil.isNotEmpty(kafkaVoList)) {
			mqSenderComponent.send(QYApplicationContext.applicationNameSwitch + KafkaTopicEnum.XDA_COMMODITY_TEXT_CHANGE_TOPIC.getTopic(),
					kafkaVoList, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XDA_COMMODITY_TEXT_CHANGE_TOPIC.name(),
					KafkaMessageOperationTypeEnum.UPDATE.name());
		}
		
        return new SpecialResultODTO(true, newCommodityTagList.size());
	}
	
	/**
	 * 批量更新  商品文描-排序
	 *
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public SpecialResultODTO batchUpdateCommoditySortNum(BatchUpdateCommoditySortNumIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		List<CommoditySortNumIDTO> commoditySortNumInfoList = idto.getCommoditySortNumList();

		// 校验参数
		QYAssert.isTrue(SpringUtil.isNotEmpty(commoditySortNumInfoList), "未获得文档信息!");
		List<String> commodityCodeList = commoditySortNumInfoList.stream().filter(o -> {return null != o && !StringUtil.isNullOrEmpty(o.getCommodityCode());}).map(CommoditySortNumIDTO::getCommodityCode).collect(Collectors.toList());
		Map<String, XdaPriceStructureCommodityTextInfoODTO> xdaPriceStructureCommodityTextInfoMap = this.checkXdPriceStructureCommodityTextInfoAndGetMap(commodityCodeList);

    	Date updateTime = new Date();
    	Long updateId = idto.getUserId();
    	List<Long> commodityIdList = new ArrayList<>();
    	List<String> commodityCodeEmptyList = new ArrayList<>();
    	List<String> commoditySortNumErrorList = new ArrayList<>();
		List<String> commodityCodeErrorList = new ArrayList<>();
		List<UpdateCommoditySortNumIDTO> newCommoditySortNumList = new ArrayList<>();
		List<XdaCommodityTextLog> commodityTextLogList = new ArrayList<>();
		List<EsXdaCommodityTextChangeKafkaVO> kafkaVoList = new ArrayList<>();
		for (int i = 0; i < commoditySortNumInfoList.size(); i ++) {
			CommoditySortNumIDTO commoditySortNumInfo = commoditySortNumInfoList.get(i);
			if (null == commoditySortNumInfo || StringUtil.isNullOrEmpty(commoditySortNumInfo.getCommodityCode())) {
				commodityCodeEmptyList.add("第" + (i + 1 + 1) + "行");
				continue;
			}
			Integer sortNum = commoditySortNumInfo.getSortNum();
			if (null == sortNum || sortNum.intValue() < 1 || sortNum.intValue() > 99999) {
				commoditySortNumErrorList.add("第" + (i + 1 + 1) + "行，" + (null == sortNum? "空": sortNum));
				continue;
			}
			String commodityCode = commoditySortNumInfo.getCommodityCode().trim();
			XdaPriceStructureCommodityTextInfoODTO xdaPriceStructureCommodityTextInfo = xdaPriceStructureCommodityTextInfoMap.get(commodityCode);
			if (null == xdaPriceStructureCommodityTextInfo) {
				commodityCodeErrorList.add("第" + (i + 1 + 1) + "行，" + commodityCode);
				continue;
			}
			Long commodityId = xdaPriceStructureCommodityTextInfo.getCommodityId();
			newCommoditySortNumList.add(new UpdateCommoditySortNumIDTO(commodityId, sortNum));

			Integer oldSortNum = xdaPriceStructureCommodityTextInfo.getSortNum();
			commodityTextLogList.add(new XdaCommodityTextLog(commodityId, IMPORT_SORT_NUM, null == oldSortNum? "": oldSortNum + "", null == sortNum? "": sortNum + "", JSON.toJSONString(commoditySortNumInfo), updateId, updateTime));
			commodityIdList.add(commodityId);

			//消息数据
			kafkaVoList.add(new EsXdaCommodityTextChangeKafkaVO(commodityId));
		}

		List<String> errorMsgList = new ArrayList<>();
		if (SpringUtil.isNotEmpty(commodityCodeEmptyList)) {
			commodityCodeEmptyList.add(0, "以下商品编码不能为空：");
			errorMsgList.addAll(commodityCodeEmptyList);
		}
		if (SpringUtil.isNotEmpty(commoditySortNumErrorList)) {
			commoditySortNumErrorList.add(0, "以下排序值  不符合[1,99999]：");
			errorMsgList.addAll(commoditySortNumErrorList);
		}
		if (SpringUtil.isNotEmpty(commodityCodeErrorList)) {
			commodityCodeErrorList.add(0, "未找到以下商品编码：");
			errorMsgList.addAll(commodityCodeErrorList);
		}
		if (SpringUtil.isNotEmpty(errorMsgList)) {
			errorMsgList.add(0, "导入失败。");
			return new SpecialResultODTO(false, errorMsgList);
		}

		List<String> commodityCodeRepeatList = this.checkCommodityCodeRepeated(commodityCodeList);
		if (SpringUtil.isNotEmpty(commodityCodeRepeatList)) {
			commodityCodeRepeatList.add(0, "导入失败。以下商品编码重复：");
			return new SpecialResultODTO(false, commodityCodeRepeatList);
		}

		// 批量更新  商品文描-排序
		newCommoditySortNumList.forEach(o -> {
			this.updateCommoditySortNum(o.getCommodityId(), o.getSortNum(), updateId, updateTime);
		});

		// 批量插入日志
		if (SpringUtil.isNotEmpty(commodityTextLogList)) {
			xdaCommodityTextLogMapper.insertList(commodityTextLogList);
		}

		//发消息通知鲜达搜索
		if(SpringUtil.isNotEmpty(kafkaVoList)) {
			mqSenderComponent.send(QYApplicationContext.applicationNameSwitch + KafkaTopicEnum.XDA_COMMODITY_TEXT_CHANGE_TOPIC.getTopic(),
					kafkaVoList, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XDA_COMMODITY_TEXT_CHANGE_TOPIC.name(),
					KafkaMessageOperationTypeEnum.UPDATE.name());
		}
        return new SpecialResultODTO(true, newCommoditySortNumList.size());
	}

	/**
	 * 批量更新 商品文描-是否显示保质期
	 * @param idto
	 * @return
	 */
	@Transactional
	public SpecialResultODTO batchUpdateCommodityQualityStatus(BatchUpdateCommodityQualityStatusIDTO idto){
		QYAssert.isTrue(null != idto, "参数有误!");
		List<CommodityQualityStatusIDTO> commodityQualityStatusList = idto.getCommodityQualityStatusList();

		//校验参数
		QYAssert.isTrue(SpringUtil.isNotEmpty(commodityQualityStatusList), "未获得文档信息!");
		List<String> commodityCodeList = commodityQualityStatusList.stream().filter(o -> {return null != o && !StringUtil.isNullOrEmpty(o.getCommodityCode());}).map(CommodityQualityStatusIDTO::getCommodityCode).collect(Collectors.toList());
		Map<String, XdaPriceStructureCommodityTextInfoODTO> xdaPriceStructureCommodityTextInfoMap = this.checkXdPriceStructureCommodityTextInfoAndGetMap(commodityCodeList);

		Date updateTime = new Date();
		Long updateId = idto.getUserId();
		List<Long> commodityIdList = new ArrayList<>();
		List<String> commodityCodeEmptyList = new ArrayList<>();
		List<String> commodityQualityStatusErrorList = new ArrayList<>();
		List<String> commodityCodeErrorList = new ArrayList<>();
		List<UpdateCommodityQualityStatusIDTO> newCommodityQualityStatusList = new ArrayList<>();
		List<XdaCommodityTextLog> commodityTextLogList = new ArrayList<>();

		for (int i = 0; i < commodityQualityStatusList.size(); i ++) {
			CommodityQualityStatusIDTO commodityQualityStatusIDTO = commodityQualityStatusList.get(i);
			if (null == commodityQualityStatusIDTO || StringUtil.isNullOrEmpty(commodityQualityStatusIDTO.getCommodityCode())) {
				commodityCodeEmptyList.add("第" + (i + 1 + 1) + "行");
				continue;
			}
			String qualityStatusName = commodityQualityStatusIDTO.getQualityStatusName();
			if (StringUtils.isEmpty(qualityStatusName) ) {
				commodityQualityStatusErrorList.add("第" + (i + 1 + 1) + "行，");
				continue;
			} else {
				if(!qualityStatusName.equals("显示") && ! qualityStatusName.equals("不显示")){
					commodityQualityStatusErrorList.add("第" + (i + 1 + 1) + "行，" + qualityStatusName);
					continue;
				}
			}

			String commodityCode = commodityQualityStatusIDTO.getCommodityCode().trim();
			Integer qualityStatus = qualityStatusName.equals("显示") ? 1 : 0;
			XdaPriceStructureCommodityTextInfoODTO xdaPriceStructureCommodityTextInfo = xdaPriceStructureCommodityTextInfoMap.get(commodityCode);
			if (null == xdaPriceStructureCommodityTextInfo) {
				commodityCodeErrorList.add("第" + (i + 1 + 1) + "行，" + commodityCode);
				continue;
			}
			Long commodityId = xdaPriceStructureCommodityTextInfo.getCommodityId();
			newCommodityQualityStatusList.add(new UpdateCommodityQualityStatusIDTO(commodityId, qualityStatus));

			Integer oldQualityStatus = xdaPriceStructureCommodityTextInfo.getQualityStatus();
			commodityTextLogList.add(new XdaCommodityTextLog(commodityId, IMPORT_QUALITY_STATUS, null != oldQualityStatus && oldQualityStatus == 1? "显示": "不显示", StringUtils.isNotEmpty(qualityStatusName) ? qualityStatusName : "", JSON.toJSONString(commodityQualityStatusIDTO), updateId, updateTime));
			commodityIdList.add(commodityId);
		}

		List<String> errorMsgList = new ArrayList<>();
		if (SpringUtil.isNotEmpty(commodityCodeEmptyList)) {
			commodityCodeEmptyList.add(0, "以下商品编码不能为空：");
			errorMsgList.addAll(commodityCodeEmptyList);
		}
		if (SpringUtil.isNotEmpty(commodityQualityStatusErrorList)) {
			commodityQualityStatusErrorList.add(0, "以下商品是否显示保质期  不能为空且只能输入[显示,不显示]：");
			errorMsgList.addAll(commodityQualityStatusErrorList);
		}
		if (SpringUtil.isNotEmpty(commodityCodeErrorList)) {
			commodityCodeErrorList.add(0, "未找到以下商品编码：");
			errorMsgList.addAll(commodityCodeErrorList);
		}
		if (SpringUtil.isNotEmpty(errorMsgList)) {
			errorMsgList.add(0, "导入失败。");
			return new SpecialResultODTO(false, errorMsgList);
		}

		List<String> commodityCodeRepeatList = this.checkCommodityCodeRepeated(commodityCodeList);
		if (SpringUtil.isNotEmpty(commodityCodeRepeatList)) {
			commodityCodeRepeatList.add(0, "导入失败。以下商品编码重复：");
			return new SpecialResultODTO(false, commodityCodeRepeatList);
		}

		// 批量更新  商品文描-重量
		newCommodityQualityStatusList.forEach(o -> {
			this.updateCommodityQualityStatus(o.getCommodityId(), o.getQualityStatus(), updateId, updateTime);
		});

		// 批量插入日志
		if (SpringUtil.isNotEmpty(commodityTextLogList)) {
			xdaCommodityTextLogMapper.insertList(commodityTextLogList);
		}

		//发消息通知鲜达搜索
		return new SpecialResultODTO(true, newCommodityQualityStatusList.size());

	}


	// 校验  鲜达价格体系-商品文描信息 && getMap
	private Map<String, XdaPriceStructureCommodityTextInfoODTO> checkXdPriceStructureCommodityTextInfoAndGetMap(List<String> commodityCodeList) {
    	List<XdaPriceStructureCommodityTextInfoODTO> xdaPriceStructureCommodityTextInfoList = xdaCommodityTextMapper.selectXdaPriceStructureCommodityTextInfoList(new SelectXdaPriceStructureCommodityTextInfoListIDTO(commodityCodeList));
    	return xdaPriceStructureCommodityTextInfoList.stream().collect(Collectors.toMap(XdaPriceStructureCommodityTextInfoODTO::getCommodityCode, commodity -> commodity));
	}


	@Data
	@NoArgsConstructor
	public class XdaCommodityCategory {
		private Long commodityId;
		private Long xdaFirstCategoryId;
		private Long xdaSecondCategoryId;
		public XdaCommodityCategory(Long commodityId, Long xdaFirstCategoryId, Long xdaSecondCategoryId) {
			this.commodityId = commodityId;
			this.xdaFirstCategoryId = xdaFirstCategoryId;
			this.xdaSecondCategoryId = xdaSecondCategoryId;
		}
	}
	
	/**
	 * 更新  商品文描-前台品名
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public int updateCommodityAppName(UpdateCommodityAppNameIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		Long commodityId = idto.getCommodityId();
		String commodityAppName = idto.getCommodityAppName();
		
		QYAssert.isTrue(!StringUtil.isNullOrEmpty(commodityAppName), "前台品名不能为空!");
		commodityAppName = commodityAppName.trim();
		QYAssert.isTrue(commodityAppName.length() < 21, "前台品名不能超过20个字!");
		this.checkXdCommodity(commodityId);
		
        Date updateTime = new Date();
        Long updateId = idto.getUserId();
        String oldCommodityAppName = this.updateCommodityAppName(commodityId, commodityAppName, updateId, updateTime);
        xdaCommodityTextLogMapper.insert(new XdaCommodityTextLog(commodityId, UPDATE_COMMODITY_APP_NAME, oldCommodityAppName, commodityAppName, JSON.toJSONString(idto), updateId, updateTime));

		//发消息通知鲜达搜索
		sendCommodityTextMsgToXdaEs(commodityId);

		return 1;
	}
	
	/**
	 * 更新  商品文描-副标题
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public int updateCommoditySubName(UpdateCommoditySubNameIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		Long commodityId = idto.getCommodityId();
		String commoditySubName = idto.getCommoditySubName();
		
		if (!StringUtil.isNullOrEmpty(commoditySubName)) {
			commoditySubName = commoditySubName.trim();
			QYAssert.isTrue(commoditySubName.length() < 21, "副标题不能超过20个字!");
		}
		this.checkXdCommodity(commodityId);
		
		// 更新商品副标题
        Date updateTime = new Date();
        Long updateId = idto.getUserId();
        String oldCommoditySubName = this.updateCommoditySubName(commodityId, commoditySubName, updateId, updateTime);
        xdaCommodityTextLogMapper.insert(new XdaCommodityTextLog(commodityId, UPDATE_COMMODITY_SUB_NAME, oldCommoditySubName, commoditySubName, JSON.toJSONString(idto), updateId, updateTime));

		//发消息通知鲜达搜索
		sendCommodityTextMsgToXdaEs(commodityId);

		return 1;
	}

	/**
	 * 发消息通知鲜达搜索
	 * @param commodityId
	 */
	private void sendCommodityTextMsgToXdaEs(Long commodityId) {
		List<EsXdaCommodityTextChangeKafkaVO> kafkaVoList = new ArrayList<>();
		kafkaVoList.add(new EsXdaCommodityTextChangeKafkaVO(commodityId));
		if(SpringUtil.isNotEmpty(kafkaVoList)){
			mqSenderComponent.send(QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XDA_COMMODITY_TEXT_CHANGE_TOPIC.getTopic(),
					kafkaVoList, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XDA_COMMODITY_TEXT_CHANGE_TOPIC.name(),
					KafkaMessageOperationTypeEnum.UPDATE.name());
		}
	}

	/**
	 * 更新  商品文描-前台品类
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public int updateCommodityXdaCategory(UpdateCommodityXdaCategoryIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		Long commodityId = idto.getCommodityId();
		Long xdaSecondCategoryId = idto.getXdaSecondCategoryId();
		QYAssert.isTrue(null != xdaSecondCategoryId, "前台品类不能为空!");
		
		this.checkXdCommodity(commodityId);
		
		String newValue = null;
		String oldValue = null;
		Date updateTime = new Date();
		Long updateId = idto.getUserId();
		
		Long xdaFirstCategoryId = null;
		if (null != xdaSecondCategoryId) {
			XdaCategory secondCategory = this.getXDCategory(xdaSecondCategoryId);
			QYAssert.isTrue(null != secondCategory && secondCategory.getStatus().equals(1), "二级品类不存在!");
			xdaFirstCategoryId = secondCategory.getParentId();
			XdaCategory firstCategory = this.getXDCategory(xdaFirstCategoryId);
			QYAssert.isTrue(null != firstCategory && firstCategory.getStatus().equals(1), "一级品类不存在!");
			newValue = firstCategory.getCateName() + " / " + secondCategory.getCateName();
		}
		Long[] oldCategroyIdArray = this.updateCommodityXdaCategory(commodityId, xdaFirstCategoryId, xdaSecondCategoryId, updateId, updateTime);
		
		if (null != oldCategroyIdArray) {
			Long oldFirstCategoryId = oldCategroyIdArray[0];
			Long oldSecondCategoryId = oldCategroyIdArray[1];
			XdaCategory oldFirstCategory = this.getXDCategory(oldFirstCategoryId);
			XdaCategory oldSecondCategory = this.getXDCategory(oldSecondCategoryId);
			if (null != oldFirstCategory && null != oldSecondCategory) {
				oldValue = oldFirstCategory.getCateName() + " / " + oldSecondCategory.getCateName();
			}
		}
		xdaCommodityTextLogMapper.insert(new XdaCommodityTextLog(commodityId, UPDATE_FRONT_CATEGORY, oldValue, newValue, JSON.toJSONString(idto), updateId, updateTime));

		//发消息通知鲜达搜索
		sendCommodityTextMsgToXdaEs(commodityId);

		return 1;
	}

	/**
	 * 更新  商品文描-排序
	 *
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public int updateCommoditySortNum(UpdateCommoditySortNumIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		Long commodityId = idto.getCommodityId();
		Integer sortNum = idto.getSortNum();

		QYAssert.isTrue(null != sortNum, "排序不能为空!");
		QYAssert.isTrue(sortNum.intValue() > 0 && sortNum.intValue() <100000, "排序值范围[1,99999]!");

		this.checkXdCommodity(commodityId);

		// 更新商品排序
        Date updateTime = new Date();
        Long updateId = idto.getUserId();
        Integer oldSortNum = this.updateCommoditySortNum(commodityId, sortNum, updateId, updateTime);
        xdaCommodityTextLogMapper.insert(new XdaCommodityTextLog(commodityId, UPDATE_SORT_NUM, null == oldSortNum? "": oldSortNum + "", null == sortNum? "": sortNum + "", JSON.toJSONString(idto), updateId, updateTime));

		//发消息通知鲜达搜索
		sendCommodityTextMsgToXdaEs(commodityId);

		return 1;
	}
	
	// 查询  鲜达前台品类
	private XdaCategory getXDCategory(Long xdaCategoryId) {
		return xdaCategoryMapper.selectByPrimaryKey(xdaCategoryId);
	}
	// 更新  商品文描-前台品名
	private String updateCommodityAppName(Long commodityId, String commodityAppName, Long updateId, Date updateTime) {
		XdaCommodityText commodityText = xdaCommodityTextMapper.selectOne(new XdaCommodityText(commodityId));
		if (null == commodityText) {
			xdaCommodityTextMapper.insert(XdaCommodityText.forInsertCommodityAppName(commodityId, commodityAppName, updateId, updateTime));
			return null;
		} else {
			xdaCommodityTextMapper.updateCommodityAppName(XdaCommodityText.forUpdateCommodityAppName(commodityId, commodityAppName, updateId, updateTime));
			return commodityText.getCommodityAppName();
		}
	}
	// 更新  商品文描-副标题
	private String updateCommoditySubName(Long commodityId, String commoditySubName, Long updateId, Date updateTime) {
		XdaCommodityText commodityText = xdaCommodityTextMapper.selectOne(new XdaCommodityText(commodityId));
		if (null == commodityText) {
			xdaCommodityTextMapper.insert(XdaCommodityText.forInsertCommoditySubName(commodityId, commoditySubName, updateId, updateTime));
			return null;
		} else {
			xdaCommodityTextMapper.updateCommoditySubName(XdaCommodityText.forUpdateCommoditySubName(commodityId, commoditySubName, updateId, updateTime));
			return commodityText.getCommoditySubName();
		}
	}

	// 更新  商品文描-前台分类
	private Long[] updateCommodityXdaCategory(Long commodityId, Long xdaFirstCategoryId, Long xdaSecondCategoryId, Long updateId, Date updateTime) {
		XdaCommodityText commodityText = xdaCommodityTextMapper.selectOne(new XdaCommodityText(commodityId));
		if (null == commodityText) {
			xdaCommodityTextMapper.insert(XdaCommodityText.forInsertCategory(commodityId, xdaFirstCategoryId, xdaSecondCategoryId, updateId, updateTime));
			return null;
		} else {
			xdaCommodityTextMapper.updateCommodityCategory(XdaCommodityText.forUpdateCategory(commodityId, xdaFirstCategoryId, xdaSecondCategoryId, updateId, updateTime));
			return new Long[] {commodityText.getFirstCategoryId(), commodityText.getSecondCategoryId()};
		}
	}
	// 更新  商品文描-标签
	private Long updateCommodityTag(Long commodityId, Long tagId, Long updateId, Date updateTime) {
		XdaCommodityText commodityText = xdaCommodityTextMapper.selectOne(new XdaCommodityText(commodityId));
		if (null == commodityText) {
			xdaCommodityTextMapper.insert(XdaCommodityText.forInsertTag(commodityId, tagId, updateId, updateTime));
			return null;
		} else {
			xdaCommodityTextMapper.updateCommodityTag(XdaCommodityText.forUpdateTag(commodityId, tagId, updateId, updateTime));
			return commodityText.getTagId();
		}
	}
	// 更新  商品文描-排序
	private Integer updateCommoditySortNum(Long commodityId, Integer sortNum, Long updateId, Date updateTime) {
		XdaCommodityText commodityText = xdaCommodityTextMapper.selectOne(new XdaCommodityText(commodityId));
		if (null == commodityText) {
			xdaCommodityTextMapper.insert(XdaCommodityText.forInsertSortNum(commodityId, sortNum, updateId, updateTime));
			return null;
		} else {
			xdaCommodityTextMapper.updateCommoditySortNum(XdaCommodityText.forUpdateSortNum(commodityId, sortNum, updateId, updateTime));
			return commodityText.getSortNum();
		}
	}

	// 更新  商品文描-是否显示保质期
	private Integer updateCommodityQualityStatus(Long commodityId, Integer qualityStatus, Long updateId, Date updateTime) {
		XdaCommodityText commodityText = xdaCommodityTextMapper.selectOne(new XdaCommodityText(commodityId));
		if (null == commodityText) {
			xdaCommodityTextMapper.insert(XdaCommodityText.forInsertQualityStatus(commodityId, qualityStatus, updateId, updateTime));
			return null;
		} else {
			xdaCommodityTextMapper.updateCommodityQualityStatus(XdaCommodityText.forUpdateQualityStatus(commodityId, qualityStatus, updateId, updateTime));
			return commodityText.getQualityStatus();
		}
	}


	// 校验  商品是否存在
	private void checkXdCommodity(Long commodityId) {
		QYAssert.isTrue(null != commodityId, "商品标识不能为空!");
		Commodity commodity = new Commodity();
		commodity.setId(commodityId);
		commodity = commodityMapper.selectOne(commodity);
		QYAssert.isTrue(null != commodity, "该商品不存在!");
	}
	// 校验：商品编码是否重复
	protected List<String> checkCommodityCodeRepeated(List<String> commodityCodeList) {
		List<String> commodityCodeRepeatedList = commodityCodeList.stream()
			.collect(Collectors.toMap(e -> e,  e -> 1, (a, b) -> a + b))// 获得元素出现频率的 Map，键为元素，值为元素出现的次数
			.entrySet().stream().filter(entry -> entry.getValue() > 1)	// 过滤出元素出现次数大于 1 的 entry
			.map(entry -> entry.getKey())								// 获得 entry 的键（重复元素）对应的 Stream
			.collect(Collectors.toList());
		return commodityCodeRepeatedList;
	}

    /**
     * 查询   鲜达商品下拉信息  列表
     *
     * @param idto
     * @return
     */
    public List<XdaCommodityDropdownInfoODTO> selectXdaCommodityDropdownInfoList(SelectXdaCommodityDropdownInfoListIDTO idto) {
    	return xdaCommodityTextMapper.selectXdaCommodityDropdownInfoList(idto);
	}

	/**
     * 查询  鲜达商品信息 列表
     * 
     * @param idto
     * @return
     */
    @Transactional(readOnly = true)
    public List<XdaCommodityInfoODTO> selectXdaCommodityInfoList(SelectXdaCommodityInfoListIDTO idto) {
    	if (null == idto || SpringUtil.isEmpty(idto.getCommodityIdList())) {
    		return new ArrayList<>();
    	}
    	List<XdaCommodityInfoODTO> commodityList = xdaCommodityTextMapper.selectXdaCommodityInfoList(idto);
    	if (SpringUtil.isNotEmpty(commodityList)) {
    		List<Long> commodityIdList = idto.getCommodityIdList();
    		List<XdaCommodityTextPic> commodityTextPicList = xdaCommodityTextPicMapper.selectCommodityTextPicList(new SelectCommodityTextPicListIDTO(commodityIdList, PIC_TYPE, DEFAULT_YES));
    		Map<Long, String> commodityTextPicMap = commodityTextPicList.stream().collect(Collectors.toMap(XdaCommodityTextPic::getCommodityId, XdaCommodityTextPic::getPicUrl));
			commodityList.forEach(commodity -> {
				Long commodityId = commodity.getCommodityId();
				String defaultPicUrl = commodityTextPicMap.get(commodityId);
				commodity.setDefaultPicUrl(StringUtil.isNullOrEmpty(defaultPicUrl)? "": imgXdServerUrl + defaultPicUrl);
			});
    	}
    	return commodityList;
	}


	public List<CommodityTextTagInfoODTO> selectCommodityTextTagInfoList(List<Long> commodityIdList) {
		return xdaCommodityTextMapper.selectCommodityTextTagInfoList(new SelectCommodityTextTagInfoListIDTO(commodityIdList));
	}

	public List<XdaCommodityTextPic> selectCommodityTextPicList(List<Long> commodityIdList) {
		return xdaCommodityTextPicMapper.selectCommodityTextPicList(new SelectCommodityTextPicListIDTO(commodityIdList, PIC_TYPE, DEFAULT_YES));
	}


	/**
	 * 查询所有文描信息(1级2级分类，是否上下架)
	 * @param idto
	 * @return
	 */
	public List<CommodityTextInfoODTO> queryAllXdaCommodityText(SelectXdaCommodityInfoListIDTO idto) {
		return xdaCommodityTextMapper.queryAllXdaCommodityText(idto.getCommodityIdList());
	}

	@Transactional
	public List<CommodityTextEsODTO> selectCommodityTextEsList(SelectXdaCommodityInfoListIDTO dto) {
		return xdaCommodityTextMapper.selectCommodityTextEsList(dto.getCommodityIdList());
	}

	@Transactional
	public List<XdaCommodityText> selectCommodityByCateId(Long cateId){
		return xdaCommodityTextMapper.selectCommodityByCateId(cateId);
	}
}
