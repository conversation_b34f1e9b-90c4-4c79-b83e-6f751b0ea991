package com.pinshang.qingyun.xda.product.service;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.service.CodeClient;
import com.pinshang.qingyun.xda.product.dto.XdaImportExcelResultBasicEntry;
import com.pinshang.qingyun.xda.product.dto.orderTargetSet.*;
import com.pinshang.qingyun.xda.product.enums.XdaOrderTargetSetEnums;
import com.pinshang.qingyun.xda.product.mapper.XdaOrderTargetSetLogMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaOrderTargetSetMapper;
import com.pinshang.qingyun.xda.product.mapper.common.StoreMapper;
import com.pinshang.qingyun.xda.product.model.common.Store;
import com.pinshang.qingyun.xda.product.model.orderTargetSet.XdaOrderTargetSet;
import com.pinshang.qingyun.xda.product.model.orderTargetSet.XdaOrderTargetSetLog;
import com.pinshang.qingyun.xda.product.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <AUTHOR>
 * @date 2023/6/5 11:36
 */
@Service
@Slf4j
public class XdaOrderTargetSetService {

    @Autowired
    private XdaOrderTargetSetMapper xdaOrderTargetSetMapper;

    @Autowired
    private XdaOrderTargetSetLogMapper xdaOrderTargetSetLogMapper;

    @Autowired
    private CodeClient codeClient;

    @Autowired
    private StoreMapper storeMapper;

    public static final Map<String,Integer> loopSetMap=new HashMap<>();

    static {
        loopSetMap.put("一",1);
        loopSetMap.put("二",2);
        loopSetMap.put("三",3);
        loopSetMap.put("四",4);
        loopSetMap.put("五",5);
        loopSetMap.put("六",6);
        loopSetMap.put("七",7);
    }


    /**
     * 根据客户id和送货日期查询当天是否有符合的订货目标
     * @param storeId
     * @param orderTime
     * @return
     */
    public XdaOrderTargetSet findOrderTargetByStoreIdAndOrderTime(Long storeId, Date orderTime){
        if(orderTime == null) {
            log.warn("orderTime传值异常, storeId {}", storeId);
            return null;
        }
        //QYAssert.isTrue(orderTime != null, "orderTime传值异常");

        //查询日期范围符合的数据
        XdaOrderTargetSet orderTarget = xdaOrderTargetSetMapper.findOrderTargetByStoreIdAndOrderTime(storeId, orderTime);
        //判断循环
        if(null != orderTarget){
            if(orderTarget.getDeliveryDateRangeType().equals(2)){
                String loopSet = orderTarget.getLoopSet();
                if(StringUtils.isNotEmpty(loopSet)){
                    String[] strings = loopSet.split(";");
                    //验证当天是否符合
                    if(DateUtils.isContainWeek(strings,orderTime)){
                      return orderTarget;
                    }
                }
            }else {
                return orderTarget;
            }
        }
        return null;
    }

    /***
     * 新增订货目标设置
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int addOrderTargetSet(XdaOrderTargetSetAddIDTO vo) throws Exception {
        QYAssert.isTrue(this.checkDeliveryDate(vo.getDeliveryStartDate(), vo.getDeliveryEndDate()), "送货日期范围有误，日期范围为：T≤送货日期范围≤T+92！");
        XdaOrderTargetSet xdaOrderTargetSet=XdaOrderTargetSet.toAddEntry(vo, codeClient.createCode("XDA_ORDER_TARGET_SET_CODE"), new Date());
        xdaOrderTargetSetMapper.insert(xdaOrderTargetSet);
        return this.addXdaOrderTargetSetLog(xdaOrderTargetSet,XdaOrderTargetSetEnums.ADD);
    }


    /***
     * 停用订货目标设置
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int disableOrderTargetSet(XdaOrderTargetSetDisableIDTO vo) {
        XdaOrderTargetSet set = xdaOrderTargetSetMapper.selectByPrimaryKey(vo.getId());
        QYAssert.isTrue(set != null, "订货目标设置记录不存在");
        QYAssert.isTrue(set.getOrderTargetStatus() != 0, "此订货目标设置已是停用状态，请勿重复操作");
        Example example = new Example(XdaOrderTargetSet.class);
        example.createCriteria().andEqualTo("id", vo.getId());
        XdaOrderTargetSet xdaOrderTargetSet=XdaOrderTargetSet.toDisableEntry(vo,new Date());
        QYAssert.isTrue(xdaOrderTargetSetMapper.updateByExampleSelective(xdaOrderTargetSet, example)>0,"停用订货目标设置失败");
        set.toDisableEntry(xdaOrderTargetSet);
        return this.addXdaOrderTargetSetLog(set,XdaOrderTargetSetEnums.DISABLE);
    }

    /***
     * 获取订货目标设置列表
     * @param vo
     * @return
     */
    public PageInfo<XdaOrderTargetSetQueryODTO> findXdaOrderTargetSetList(XdaOrderTargetSetQueryIDTO vo){
        return PageHelper.startPage(vo.getPageNo(),vo.getPageSize()).doSelectPageInfo(() ->{
            xdaOrderTargetSetMapper.findXdaOrderTargetSetList(vo);
        });
    }

    /***
     * 获取订货目标设置日志列表
     * @param vo
     * @return
     */
    public List<XdaOrderTargetSetLogQueryODTO> findXdaOrderTargetSetLogList(XdaOrderTargetSetLogQueryIDTO vo){
        return xdaOrderTargetSetLogMapper.findXdaOrderTargetSetLogList(vo);
    }


    /***
     * 获取订货目标设置详情
     * @param id
     * @return
     */
    public XdaOrderTargetSetDetailODTO findXdaOrderTargetSetDetailById(Long id) {
        return xdaOrderTargetSetMapper.findXdaOrderTargetSetDetailById(id);
    }


    /***
     * 订货目标设置 excel 导入
     * @param sheet
     * @param header
     * @param tokenInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public XdaImportExcelResultBasicEntry importOrderTargetSet(Sheet sheet, String[] header, TokenInfo tokenInfo) throws Exception {
        List<String> errorMsg = new ArrayList<>();
        Set<String> storeCodeSet = new HashSet<>();
        Map<String, List<String>> loopSetMap = new HashMap<>();
        String[][] arrayData = new String[sheet.getPhysicalNumberOfRows() - 1][header.length];
        int i = 0;
        int index = 0;
        for (Row row : sheet) {
            if (i == 0) {
                i++;
                continue;
            }
            i++;
            if (row == null) {
                continue;
            }
            Cell cell;
            String value;
            for (int n = 0; n < header.length; n++) {
                value = "";
                cell = row.getCell(n);
                if (cell == null && n != 3) {
                    errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "不能为空！");
                    continue;
                }

                if (cell != null) {
                    cell.setCellType(CellType.STRING);
                    value = cell.getStringCellValue();
                    if (StringUtils.isBlank(value) && n != 3) {
                        errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "不能为空！");
                        continue;
                    }
                }
                value = StringUtils.isNotBlank(value) ? StringUtils.deleteWhitespace(value) : "";
                if (n == 0) {
                    storeCodeSet.add(value);
                    arrayData[index][n] = value;
                } else if (n == 1) {
                    String[] deliveryDateArray = value.split("-");
                    if (deliveryDateArray.length != 2) {
                        errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "输入有误，格式为：(yyyyMMdd-yyyyMMdd) ！");
                        continue;
                    }
                    if (deliveryDateArray[0].length() != 8 || deliveryDateArray[1].length() != 8) {
                        errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "输入有误，格式为：(yyyyMMdd-yyyyMMdd) ！");
                        continue;
                    }
                    try {
                        SimpleDateFormat simpleDateFormate = new SimpleDateFormat("yyyyMMdd");
                        if (!checkDeliveryDate(simpleDateFormate.parse(deliveryDateArray[0]), simpleDateFormate.parse(deliveryDateArray[1]))) {
                            errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "日期范围为：T≤送货日期范围≤T+92！");
                            continue;
                        }
                        arrayData[index][n] = String.format("%s%s%s", value, "@@@@@@", i);
                    } catch (ParseException e) {
                        errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "输入有误，格式为：(yyyyMMdd-yyyyMMdd) ！");
                        continue;
                    }
                } else if (n == 2) {
                    Pattern pattern = Pattern.compile("^([1-9]{1}\\d{0,6})$");
                    if (!pattern.matcher(value).matches()) {
                        errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "输入有误，范围为：正整数，0＜订货目标＜9999999！");
                        continue;
                    }
                    arrayData[index][n] = value;
                } else if (n == 3) {
                    arrayData[index][n] = value;
                    if (StringUtils.isNotBlank(value)) {
                        boolean isFlag = false;
                        String[] loopSetArray = value.split(";");
                        Map<Integer, String> map = new TreeMap<>();
                        for (String s : loopSetArray) {
                            s = s.trim();
                            Integer val = XdaOrderTargetSetService.loopSetMap.get(s);
                            if (val == null) {
                                isFlag = true;
                                errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "输入有误，只允许输入一~七，以英文分号分割，至少要有1个！");
                                continue;
                            }
                            map.put(val, s);
                        }
                        if (isFlag) {
                            continue;
                        }
                        arrayData[index][n] = map.values().stream().collect(Collectors.joining(";"));
                    }

                    if (StringUtils.isNotBlank(arrayData[index][0]) && StringUtils.isNotBlank(arrayData[index][1])) {
                        String key = String.format("%s%s%s", arrayData[index][0], "-", (arrayData[index][1].split("@@@@@@"))[0]);
                        if (loopSetMap.containsKey(key)) {
                            List<String> v = loopSetMap.get(key);
                            if (v == null) {
                                if (StringUtils.isBlank(value)) {
                                    errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "输入有误，客户+送货日期范围相同的情况下,非循环设置不能重叠！");
                                } else {
                                    errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "输入有误，客户+送货日期范围相同的情况下,非循环设置与循环设置互斥不能同时存在！");
                                }
                            } else {
                                if (StringUtils.isBlank(value)) {
                                    errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "输入有误，客户+送货日期范围相同的情况下,非循环设置与循环设置互斥不能同时存在！");
                                    continue;
                                }

                                if (v.contains(value)) {
                                    errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "输入有误，客户+送货日期范围相同的情况下,循环设置不能重叠！");
                                    continue;
                                }
                                v.add(value);
                            }
                        } else {
                            loopSetMap.put(key, StringUtils.isBlank(value) ? null : Stream.of(value).collect(Collectors.toList()));
                        }
                    }
                }

            }

            index++;
        }

        if (SpringUtil.isNotEmpty(errorMsg)) {
            return new XdaImportExcelResultBasicEntry(null, false, errorMsg);
        }

        /***
         * 验证客户编码系统是否存在
         */
        checkStoreCodeIsExists(arrayData,storeCodeSet,errorMsg);
        if (SpringUtil.isNotEmpty(errorMsg)) {
            return new XdaImportExcelResultBasicEntry(null, false, errorMsg);
        }

        /***
         * 封装XdaOrderTargetSet 对象
         */
        List<XdaOrderTargetSet> list = new ArrayList<>();
        String batchNo = UUID.randomUUID().toString();
        Date date = new Date();
        for (String[] entry : arrayData) {
            if(StringUtils.isNotBlank(entry[0])) {
                list.add(XdaOrderTargetSet.toImportEntry(entry, batchNo, tokenInfo.getUserId(), tokenInfo.getRealName(), date));
            }
        }
        this.batchAddOrderTargetSet(list);
        return new XdaImportExcelResultBasicEntry(batchNo, true, null);
    }

    private void checkStoreCodeIsExists(String[][] arrayData,Set<String> storeCodeSet,List<String> errorMsg) {
        Example example = new Example(Store.class); example.createCriteria().andIn("storeCode", storeCodeSet);
        example.selectProperties("id", "storeCode");
        List<Store> storeList = storeMapper.selectByExample(example);
        Map<String, String> storeMap = SpringUtil.isEmpty(storeList) ? new HashMap<>() : storeList.stream().collect(Collectors.toMap(Store::getStoreCode, v -> v.getId().toString()));
        String[] str;
        for (String[] e : arrayData) {
            str=e[1].split("@@@@@@");
            if (storeMap.containsKey(e[0])) {
                e[0] = storeMap.get(e[0]);
            } else {
                errorMsg.add("第" + str[1] + "行第1列客户编码系统不存在，请确认！");
            }
            e[1]=str[0];
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void batchAddOrderTargetSet(List<XdaOrderTargetSet> excelList) {
        List<XdaOrderTargetSet> batchAddList = new ArrayList<>();
        List<String> codeList = codeClient.batchCreateCode("XDA_ORDER_TARGET_SET_CODE", excelList.size());
        for (int i = 0; i < excelList.size(); i++) {
            XdaOrderTargetSet s = excelList.get(i);
            if (batchAddList.size() == 500) {
                xdaOrderTargetSetMapper.insertList(batchAddList);
                this.batchAddXdaOrderTargetSetLog(batchAddList,XdaOrderTargetSetEnums.IMPORTS);
                batchAddList.clear();
            }
            s.setOrderTargetCode(codeList.get(i));
            batchAddList.add(s);
        }

        if (SpringUtil.isNotEmpty(batchAddList)) {
            xdaOrderTargetSetMapper.insertList(batchAddList);
            this.batchAddXdaOrderTargetSetLog(batchAddList,XdaOrderTargetSetEnums.IMPORTS);
            batchAddList.clear();
        }
    }

    /***
     * 记录日志
     * @param set
     * @param enums
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int addXdaOrderTargetSetLog(XdaOrderTargetSet set, XdaOrderTargetSetEnums enums){
        return xdaOrderTargetSetLogMapper.insert(XdaOrderTargetSetLog.toAddEntry(set,enums));
    }

    /***
     * 导入批量添加日志
     * @param sets
     * @param enums
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchAddXdaOrderTargetSetLog(List<XdaOrderTargetSet> sets, XdaOrderTargetSetEnums enums){
        List<XdaOrderTargetSetLog> list=new ArrayList<>();
        sets.forEach(s -> list.add(XdaOrderTargetSetLog.toAddEntry(s,enums)) );
        return xdaOrderTargetSetLogMapper.insertList(list);
    }

    private boolean checkDeliveryDate(Date deliveryStartDate, Date deliveryEndDate) throws Exception {
        Date now = DateUtil.getDatePart(new Date());
        if (now.compareTo(deliveryStartDate) > 0) {
            return false;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 92);
        Date date = DateUtil.getDatePart(calendar.getTime());
        if (deliveryEndDate.compareTo(date) > 0) {
            return false;
        }
        return true;
    }
}
