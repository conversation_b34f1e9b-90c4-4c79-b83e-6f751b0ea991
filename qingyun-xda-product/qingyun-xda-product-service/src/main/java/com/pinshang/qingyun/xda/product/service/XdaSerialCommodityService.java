package com.pinshang.qingyun.xda.product.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.commodity.CommodityTextOperateTypeEnum;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.xda.product.dto.front.XdaSerialCommodityODTO;
import com.pinshang.qingyun.xda.product.dto.serialCommodity.*;
import com.pinshang.qingyun.xda.product.helper.CommonHelper;
import com.pinshang.qingyun.xda.product.mapper.XdaCategoryMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityTextMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaSerialCommodityLogMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaSerialCommodityMapper;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityMapper;
import com.pinshang.qingyun.xda.product.model.XdaCommodityText;
import com.pinshang.qingyun.xda.product.model.XdaSerialCommodity;
import com.pinshang.qingyun.xda.product.model.XdaSerialCommodityLog;
import com.pinshang.qingyun.xda.product.model.common.Commodity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 系列品
 * 
 * <AUTHOR>
 *
 * @date 2020年12月22日
 */
@Service
public class XdaSerialCommodityService {
	
	@Autowired
    private CommonHelper commonHelper;
	
	@Autowired
	private CommodityMapper commodityMapper;
	
	@Autowired
	private XdaCategoryMapper xdaCategoryMapper;
	
	@Autowired
	private XdaCommodityTextMapper xdaCommodityTextMapper;
	
	@Autowired
	private XdaSerialCommodityMapper xdaSerialCommodityMapper;
	
	@Autowired
	private XdaSerialCommodityLogMapper xdaSerialCommodityLogMapper;
	
	/**
	 * 分页查询  系列品日志信息  列表
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(readOnly = true)
	public PageInfo<SerialCommodityLogInfoODTO> selectSerialCommodityLogInfoPage(SelectSerialCommodityLogInfoPageIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		PageInfo<SerialCommodityLogInfoODTO> pageInfo = PageHelper.startPage(idto.getPageNo(), idto.getPageSize()).doSelectPageInfo(() -> {
			xdaSerialCommodityMapper.selectSerialCommodityLogInfoList(idto);
		});
		List<SerialCommodityLogInfoODTO> list = pageInfo.getList();
		if (SpringUtil.isNotEmpty(list)) {
			// 前台品类
			List<Long> xdaCategoryIdList = list.stream().filter(o -> {return null != o.getXdaFirstCategoryId();}).map(SerialCommodityLogInfoODTO::getXdaFirstCategoryId).collect(Collectors.toList());
			xdaCategoryIdList.addAll(list.stream().filter(o -> {return null != o.getXdaSecondCategoryId();}).map(SerialCommodityLogInfoODTO::getXdaSecondCategoryId).collect(Collectors.toList()));
			Map<Long, String> xdaCategoryNameMap = commonHelper.getXdaCategoryNameMap(xdaCategoryIdList);
			list.forEach(o -> {
				// 前台品类
				o.setXdaFirstCategoryName(xdaCategoryNameMap.get(o.getXdaFirstCategoryId()));
				o.setXdaSecondCategoryName(xdaCategoryNameMap.get(o.getXdaSecondCategoryId()));
			});
		}
		
		return pageInfo;
	}
	
	/**
	 * 查询  系列品信息  列表
	 * 
	 * @param idto
	 * @return
	 */
	public List<SerialCommodityInfoODTO> selectSerialCommodityInfoList(SelectSerialCommodityInfoListIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		QYAssert.isTrue(!StringUtil.isNullOrEmpty(idto.getSerialCommodityCode()) || null != idto.getCommodityId() || !StringUtil.isNullOrEmpty(idto.getBarCode()), "至少选择一个查询条件!");
		XdaSerialCommodity model = xdaSerialCommodityMapper.selectSerialCommodity(idto);
		if (null == model) {
			return Collections.emptyList();
		}
		List<SerialCommodityInfoODTO> list = xdaSerialCommodityMapper.selectSerialCommodityInfoList(model.getSerialCommodityId());
		
		List<SerialCommodityInfoODTO> masterList = list.stream().filter(o -> o.getCommodityId().equals(o.getSerialCommodityId())).collect(Collectors.toList());
		if (SpringUtil.isEmpty(masterList)) {
			// 没有主品，直接返回空集合
			return Collections.emptyList();
		}
		
		SerialCommodityInfoODTO master = masterList.get(0);
		List<SerialCommodityInfoODTO> slaveList = list.stream().filter(o -> !o.equals(master)).collect(Collectors.toList());
		
		List<SerialCommodityInfoODTO> result = new ArrayList<>();
		result.add(master);
		result.addAll(slaveList);
		
		return result;
	}
	
	/**
	 * 查询  商品下拉信息  列表 （for系列品）
	 * 
	 * @param idto
	 * @return
	 */
	public List<SerialCommodityInfoODTO> selectCommodityDropdownInfoList4SerialCommodity(SelectCommodityDropdownInfoList4SerialCommodityIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		return xdaSerialCommodityMapper.selectCommodityDropdownInfoList4SerialCommodity(idto);
	}
	
	/**
	 * 批量添加  系列品
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public int batchInsertSerialCommodity(BatchInsertSerialCommodityIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		List<String> commodityCodeList = idto.getCommodityCodeList();
		QYAssert.isTrue(SpringUtil.isNotEmpty(commodityCodeList), "请录入商品编码!");
		int commodityQuantity = commodityCodeList.size();
		QYAssert.isTrue(commodityQuantity > 1 && commodityQuantity < 11, "商品数量必须大于等于2，小于等于10!");
		
		// 检查  商品是否存在
		List<Commodity> commodityList = this.getCommodityList(commodityCodeList);
		Map<String, Commodity> commodityMap = commodityList.stream().collect(Collectors.toMap(Commodity::getCommodityCode, p -> p));
		if (commodityQuantity != commodityList.size()) {
			// 如果数量不相等，意味着某些商品编码不存在
			commodityCodeList.forEach(commodityCode -> {
				Commodity commodity = commodityMap.get(commodityCode);
				QYAssert.isTrue(null != commodity, "商品编码" + commodityCode + "不存在!");
			});
		}
		List<Long> commodityIdList = commodityList.stream().map(Commodity::getId).collect(Collectors.toList());
		
		// 检查  商品是否已经是系列品
		List<XdaSerialCommodity> serialCommodityListOld = this.getSerialCommodityList(commodityIdList);
		if (0 != serialCommodityListOld.size()) {
			Map<Long, XdaSerialCommodity> serialCommodityMap = serialCommodityListOld.stream().collect(Collectors.toMap(XdaSerialCommodity::getCommodityId, p -> p));
			commodityCodeList.forEach(commodityCode -> {
				// 经过前面的判断，这里commodity必不为空
				Commodity commodity = commodityMap.get(commodityCode);
				XdaSerialCommodity serialCommodity = serialCommodityMap.get(commodity.getId());
				QYAssert.isTrue(null == serialCommodity, "商品编码" + commodityCode + "已经是系列品!");
			});
		}
		
		// 检查  是否设置了文描信息（主要是前台品类，为下面进一步检查）
		List<XdaCommodityText> commodityTextList = this.getCommodityTextList(commodityIdList);
		QYAssert.isTrue(commodityQuantity == commodityTextList.size(), "商品 必须设置相同的前台品类!");
		
		// 检查  商品前台品类是否都相同
		commodityTextList = commodityTextList.stream().filter(o -> null != o.getSecondCategoryId()).collect(Collectors.toList());
		XdaCommodityText commodityText0 = commodityTextList.get(0);
		Long firstCategoryId0 = commodityText0.getFirstCategoryId();
		Long secondCategoryId0 = commodityText0.getSecondCategoryId();
		commodityTextList.forEach(o -> {
			QYAssert.isTrue(firstCategoryId0.equals(o.getFirstCategoryId()) && secondCategoryId0.equals(o.getSecondCategoryId()), "商品必须设置相同的前台品类!");
		});
		
		// 组装数据
		Date createTime = new Date();
		Long createId = idto.getUserId();
		Long serialCommodityId = null;
		String serialCommodityCode = null; // TODO 设置系列品编码
		Map<Long, XdaCommodityText> commodityTextMap = commodityTextList.stream().collect(Collectors.toMap(XdaCommodityText::getCommodityId, p -> p));
		
		List<XdaSerialCommodity> serialCommodityList = new ArrayList<>();
		List<XdaSerialCommodityLog> serialCommodityLogList = new ArrayList<>();
		for (int i = 0; i < commodityQuantity; i ++) {
			String commodityCode = commodityCodeList.get(i);
			Commodity commodity = commodityMap.get(commodityCode);
			Long commodityId = commodity.getId();
			XdaCommodityText commodityText = commodityTextMap.get(commodityId);
			if (i == 0) {
				// 这组商品中的第一个商品，将作为主品
				serialCommodityId = commodity.getId();
				serialCommodityCode = "XL" + commodity.getCommodityCode();  // TODO 设置系列品编码
				serialCommodityLogList.add(XdaSerialCommodityLog.forInsert(CommodityTextOperateTypeEnum.MASTER_SERIAL_COMMODITY.getCode(), commodityText, serialCommodityId, serialCommodityCode, createId, createTime));
			}
			serialCommodityList.add(XdaSerialCommodity.forInsert(commodityId, serialCommodityId, serialCommodityCode, createId, createTime));
			serialCommodityLogList.add(XdaSerialCommodityLog.forInsert(CommodityTextOperateTypeEnum.BATCH_INSERT_SERIAL_COMMODITY.getCode(), commodityText, serialCommodityId, serialCommodityCode, createId, createTime));
		}
		
		// 插入数据
		xdaSerialCommodityMapper.insertList(serialCommodityList);
		xdaSerialCommodityLogMapper.insertList(serialCommodityLogList);
		return commodityQuantity;
	}
	// 查询  商品集合
	private List<Commodity> getCommodityList(List<String> commodityCodeList) {
		Example example = new Example(Commodity.class);
        example.createCriteria().andIn("commodityCode", commodityCodeList);
        example.selectProperties("id", "commodityCode", "commodityName");
        List<Commodity> commodityList = commodityMapper.selectByExample(example);
        return commodityList;
	}
	// 查询  商品文描集合
	private List<XdaCommodityText> getCommodityTextList(List<Long> commodityIdList) {
		Example example = new Example(XdaCommodityText.class);
        example.createCriteria().andIn("commodityId", commodityIdList);
        example.selectProperties("commodityId", "firstCategoryId", "secondCategoryId");
        List<XdaCommodityText> commodityTextList = xdaCommodityTextMapper.selectByExample(example);
        return commodityTextList;
	}
	// 根据商品Id集合，查询系列品集合
	private List<XdaSerialCommodity> getSerialCommodityList(List<Long> commodityIdList) {
		Example example = new Example(XdaSerialCommodity.class);
        example.createCriteria().andIn("commodityId", commodityIdList);
        List<XdaSerialCommodity> serialCommodityList = xdaSerialCommodityMapper.selectByExample(example);
        return serialCommodityList;
	}
	
	/**
	 * 新增  系列品
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public int insertSerialCommodity(InsertSerialCommodityIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		Long serialCommodityId = idto.getSerialCommodityId();
		Long commodityId = idto.getCommodityId();
		QYAssert.isTrue(null != serialCommodityId, "主品标识不能为空!");
		QYAssert.isTrue(null != commodityId, "商品标识不能为空!");
		
		// 检查  主品
		XdaSerialCommodity masterSerialCommodity = xdaSerialCommodityMapper.selectOne(new XdaSerialCommodity(serialCommodityId));
		QYAssert.isTrue(null != masterSerialCommodity, "'主品'标识，尚不是系列品!");
		QYAssert.isTrue(masterSerialCommodity.getCommodityId().equals(masterSerialCommodity.getSerialCommodityId()), "'主品'标识，不是系列品的主品!");
		
		// 检查  副品
		XdaSerialCommodity slaveSerialCommodity = xdaSerialCommodityMapper.selectOne(new XdaSerialCommodity(commodityId));
		if (null != slaveSerialCommodity) {
			if (slaveSerialCommodity.getSerialCommodityId().equals(serialCommodityId)) {
				QYAssert.isTrue(false, "该商品已经是该主品的副品，无需重复添加!");
			}
			QYAssert.isTrue(false, "该商品已经是系列品，不能再次添加!");
		}
		
		// 检查  主品文描（主要是前台品类）
		XdaCommodityText masterCommodityText = xdaCommodityTextMapper.selectOne(new XdaCommodityText(serialCommodityId));
		QYAssert.isTrue(null != masterCommodityText, "主副品的前台品类必须相同!");
		Long masterFirstCategoryId = masterCommodityText.getFirstCategoryId();
		Long masterSecondCategoryId = masterCommodityText.getSecondCategoryId();
		QYAssert.isTrue(null != masterFirstCategoryId && null != masterSecondCategoryId, "主副品的前台品类必须相同!");
		
		// 检查  副品文描（主要是前台品类）
		XdaCommodityText slaveCommodityText = xdaCommodityTextMapper.selectOne(new XdaCommodityText(commodityId));
		QYAssert.isTrue(null != slaveCommodityText, "主副品的前台品类必须相同!");
		Long slaveFirstCategoryId = slaveCommodityText.getFirstCategoryId();
		Long slaveSecondCategoryId = slaveCommodityText.getSecondCategoryId();
		QYAssert.isTrue(null != slaveFirstCategoryId && null != slaveSecondCategoryId, "主副品的前台品类必须相同!");
		
		// 检查  主副品文描-前台品类  是否相同
		QYAssert.isTrue(masterFirstCategoryId.equals(slaveFirstCategoryId) && masterSecondCategoryId.equals(slaveSecondCategoryId), "主副品的前台品类必须相同!");
		
    	// 组装数据
		Date createTime = new Date();
		Long createId = idto.getUserId();
		String serialCommodityCode = masterSerialCommodity.getSerialCommodityCode();
		XdaSerialCommodity serialCommodity = XdaSerialCommodity.forInsert(commodityId, serialCommodityId, serialCommodityCode, createId, createTime);
		XdaSerialCommodityLog serialCommodityLog = XdaSerialCommodityLog.forInsert(CommodityTextOperateTypeEnum.INSERT_SERIAL_COMMODITY.getCode(), slaveCommodityText, serialCommodityId, serialCommodityCode, createId, createTime);
	
		// 插入数据
		xdaSerialCommodityMapper.insert(serialCommodity);
		xdaSerialCommodityLogMapper.insert(serialCommodityLog);
    	return 1;
    }
	
	/**
	 * 删除  系列品
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public int deleteSerialCommodity(DeleteSerialCommodityIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		Long commodityId = idto.getCommodityId();
		QYAssert.isTrue(null != commodityId, "商品标识不能为空!");
		
		// 检查  商品是否系列品
		XdaSerialCommodity slaveSerialCommodity = xdaSerialCommodityMapper.selectOne(new XdaSerialCommodity(commodityId));
		QYAssert.isTrue(null != slaveSerialCommodity, "该商品不是系列品，无需删除!");
		Long serialCommodityId = slaveSerialCommodity.getSerialCommodityId();
		
        // 删除  系列品&记录日志
		int result = 1;
        Date createTime = new Date();
		Long createId = idto.getUserId();
        List<XdaSerialCommodity> serialCommodityList = this.getSerialCommodityList(serialCommodityId);
        if (2 == serialCommodityList.size()) {
        	result = 2;
        	serialCommodityList.forEach(serialCommodity -> {
        		this.deleteSerialCommodity(serialCommodity, createId, createTime);
        	});
        } else {
        	this.deleteSerialCommodity(slaveSerialCommodity, createId, createTime);
        }
		
    	return result;
    }
	// 根据主品ID，查询系列品集合
	private List<XdaSerialCommodity> getSerialCommodityList(Long serialCommodityId) {
		Example example = new Example(XdaSerialCommodity.class);
        example.createCriteria().andEqualTo("serialCommodityId", serialCommodityId);
        List<XdaSerialCommodity> serialCommodityList = xdaSerialCommodityMapper.selectByExample(example);
        return serialCommodityList;
	}
	// 删除  系列品&记录日志
	private void deleteSerialCommodity(XdaSerialCommodity serialCommodity, Long createId, Date createTime) {
		XdaCommodityText commodityText = xdaCommodityTextMapper.selectOne(new XdaCommodityText(serialCommodity.getCommodityId()));
		XdaSerialCommodityLog serialCommodityLog = XdaSerialCommodityLog.forInsert(CommodityTextOperateTypeEnum.DELETE_SERIAL_COMMODITY.getCode(), commodityText, serialCommodity.getSerialCommodityId(), serialCommodity.getSerialCommodityCode(), createId, createTime);
		xdaSerialCommodityMapper.deleteByPrimaryKey(serialCommodity.getId());
		xdaSerialCommodityLogMapper.insert(serialCommodityLog);
	}
	
	/**
	 * 设置主品  系列品
	 * 
	 * @param idto
	 * @return
	 */
	@Transactional(rollbackFor = Exception.class)
	public int masterSerialCommodity(MasterSerialCommodityIDTO idto) {
		QYAssert.isTrue(null != idto, "参数有误!");
		Long commodityId = idto.getCommodityId();
		QYAssert.isTrue(null != commodityId, "商品标识不能为空!");
		
		// 检查  商品是否系列品
		XdaSerialCommodity slaveSerialCommodity = xdaSerialCommodityMapper.selectOne(new XdaSerialCommodity(commodityId));
		QYAssert.isTrue(null != slaveSerialCommodity, "该商品不是系列品，不能设置为主品!");
		Long serialCommodityId = slaveSerialCommodity.getSerialCommodityId();
		QYAssert.isTrue(!commodityId.equals(serialCommodityId), "该商品已经是主品，无需重复设置!");
		List<XdaSerialCommodity> serialCommodityList = this.getSerialCommodityList(serialCommodityId);
		
		// 把系列品，设置新的主品
		Date createTime = new Date();
		Long createId = idto.getUserId();
		Long newSerialCommodityId = commodityId;
		serialCommodityList.forEach(serialCommodity -> {
			xdaSerialCommodityMapper.updateByPrimaryKeySelective(XdaSerialCommodity.forUpdateSerialCommodityId(serialCommodity.getId(), newSerialCommodityId));
		});
		
		// 记录新主品日志
		XdaCommodityText commodityText = xdaCommodityTextMapper.selectOne(new XdaCommodityText(commodityId));
		XdaSerialCommodityLog serialCommodityLog = XdaSerialCommodityLog.forInsert(CommodityTextOperateTypeEnum.MASTER_SERIAL_COMMODITY.getCode(), commodityText, newSerialCommodityId, slaveSerialCommodity.getSerialCommodityCode(), createId, createTime);
		xdaSerialCommodityLogMapper.insert(serialCommodityLog);
		
    	return 1;
    }


	public List<XdaSerialCommodityODTO> querySerialCommodityListFront(Long commodityId,Long xdaSecondCategoryId){
		return xdaSerialCommodityMapper.querySerialCommodityListFront(Collections.singletonList(commodityId), xdaSecondCategoryId);
	}
}
