package com.pinshang.qingyun.xda.product.service;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.ExcelUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.store.service.StoreManageClient;
import com.pinshang.qingyun.xda.product.dto.XdaImportExcelResultBasicEntry;
import com.pinshang.qingyun.xda.product.dto.specialsCommoditySet.*;
import com.pinshang.qingyun.xda.product.enums.XdaSpecialsCommoditySetEnums;
import com.pinshang.qingyun.xda.product.mapper.XdaSpecialsCommoditySetLogMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaSpecialsCommoditySetMapper;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityMapper;
import com.pinshang.qingyun.xda.product.model.common.Commodity;
import com.pinshang.qingyun.xda.product.model.specialsCommoditySet.XdaSpecialsCommoditySet;
import com.pinshang.qingyun.xda.product.model.specialsCommoditySet.XdaSpecialsCommoditySetLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/6/5 11:39
 */
@Service
@Slf4j
public class XdaSpecialsCommoditySetService {

    @Autowired
    private XdaSpecialsCommoditySetMapper xdaSpecialsCommoditySetMapper;

    @Autowired
    private XdaSpecialsCommoditySetLogMapper xdaSpecialsCommoditySetLogMapper;

    @Autowired
    private CommodityMapper commodityMapper;

    @Autowired
    private StoreService storeService;

    /***
     * 添加特惠商品设置
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int addSpecialsCommoditySet(XdaSpecialsCommoditySetAddIDTO vo) {
        QYAssert.isTrue(commodityMapper.selectByPrimaryKey(vo.getCommodityId()) != null, "商品系统不存在，请确认！");
        QYAssert.isTrue(xdaSpecialsCommoditySetMapper.selectOne(new XdaSpecialsCommoditySet(vo.getCommodityId())) == null,"特惠商品已存在！");
        XdaSpecialsCommoditySet xdaSpecialsCommoditySet = XdaSpecialsCommoditySet.toAddEntry(vo, new Date());
        xdaSpecialsCommoditySetMapper.insert(xdaSpecialsCommoditySet);
        return this.addXdaSpecialsCommoditySetLog(xdaSpecialsCommoditySet,XdaSpecialsCommoditySetEnums.ADD);
    }

    /***
     * 修改特惠商品设置
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int updateSpecialsCommoditySet(XdaSpecialsCommoditySetModifyIDTO vo) {
        XdaSpecialsCommoditySet set=xdaSpecialsCommoditySetMapper.selectByPrimaryKey(vo.getId());
        QYAssert.isTrue( set!= null, "未查询到此特惠商品记录信息，请确认！");
        XdaSpecialsCommoditySet xdaSpecialsCommoditySet = XdaSpecialsCommoditySet.toUpdateEntry(vo, new Date());
        Example example=new Example(XdaSpecialsCommoditySet.class); example.createCriteria().andEqualTo("id",vo.getId());
        QYAssert.isTrue(xdaSpecialsCommoditySetMapper.updateByExampleSelective(xdaSpecialsCommoditySet,example)>0,"修改特惠商品设置失败！");
        set.toUpdateEntry(xdaSpecialsCommoditySet);
        return this.addXdaSpecialsCommoditySetLog(set,XdaSpecialsCommoditySetEnums.MODIFY);
    }

    /***
     * 删除特惠商品设置
     * @param vo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteSpecialsCommoditySetById(XdaSpecialsCommoditySetRemoveIDTO vo) {
        XdaSpecialsCommoditySet set=xdaSpecialsCommoditySetMapper.selectByPrimaryKey(vo.getId());
        QYAssert.isTrue( set!= null, "未查询到此特惠商品设置记录，请确认！");
        QYAssert.isTrue(xdaSpecialsCommoditySetMapper.deleteByPrimaryKey(vo.getId())>0,"删除特惠商品设置失败！");
        set.setUserInfoEntry(new Date(),vo.getUserId(),vo.getUserName());
        return this.addXdaSpecialsCommoditySetLog(set,XdaSpecialsCommoditySetEnums.REMOVE);
    }

    /***
     * 特惠商品设置列表
     * @param vo
     * @return
     */
    public PageInfo<XdaSpecialsCommoditySetQueryODTO> findSpecialsCommoditySetList(XdaSpecialsCommoditySetQueryIDTO vo) {
        return PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            xdaSpecialsCommoditySetMapper.findSpecialsCommoditySetList(vo);
        });
    }

    /***
     * 特惠商品设置日志列表
     * @param vo
     * @return
     */
    public PageInfo<XdaSpecialsCommoditySetLogQueryODTO> findSpecialsCommoditySetLogList(XdaSpecialsCommoditySetLogQueryIDTO vo) {
        return PageHelper.startPage(vo.getPageNo(), vo.getPageSize()).doSelectPageInfo(() -> {
            xdaSpecialsCommoditySetLogMapper.findSpecialsCommoditySetLogList(vo);
        });
    }

    /***
     * 特惠商品设置列表导出excel
     * @param vo
     * @return
     */
    public void exportSpecialsCommoditySetList(XdaSpecialsCommoditySetExportIDTO vo, HttpServletResponse response) throws IOException {
        List<XdaSpecialsCommoditySetQueryODTO> items=xdaSpecialsCommoditySetMapper.findSpecialsCommoditySetList(new XdaSpecialsCommoditySetQueryIDTO(vo.getCommodityId(),
                vo.getBarCode(),vo.getCommodityAppName()));
        try {
            ExcelUtil.setFileNameAndHead(response, "特惠商品设置列表" + DateUtil.get4yMdNoDash(new Date()));
            EasyExcel.write(response.getOutputStream(), XdaSpecialsCommoditySetQueryODTO.class).autoCloseStream(Boolean.FALSE).sheet("特惠商品设置列表").doWrite(items);
        } catch (Exception e) {
            log.error("特惠商品设置列表导出报错", e);
            ExcelUtil.setExceptionResponse(response);
        }
    }

    /***
     * 特惠商品设置 excel 导入
     * @param sheet
     * @param header
     * @param tokenInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public XdaImportExcelResultBasicEntry importSpecialsCommoditySet(Sheet sheet, String[] header, TokenInfo tokenInfo) {
        List<String> errorMsg = new ArrayList<>();
        String[][] arrayData = new String[sheet.getPhysicalNumberOfRows() - 1][header.length];
        Set<String> repeatCommoditySet = new HashSet<>();
        int i = 0;
        int index = 0;
        for (Row row : sheet) {
            if (i == 0) {
                i++;
                continue;
            }
            i++;
            if (row == null) {
                continue;
            }
            Cell cell;
            for (int n = 0; n < header.length; n++) {
                cell = row.getCell(n);
                if (cell == null) {
                    errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "，不能为空！");
                    continue;
                }
                cell.setCellType(CellType.STRING);
                String value = cell.getStringCellValue();
                if (StringUtils.isBlank(value)) {
                    errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "，不能为空！");
                    continue;
                }
                value = StringUtils.isNotBlank(value) ? StringUtils.deleteWhitespace(value) : "";
                if (n == 0) {
                    if (!repeatCommoditySet.add(value)) {
                        errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "文档中已重复存在，请确认！");
                        continue;
                    }
                    arrayData[index][n] = value;
                } else if (n == 1) {
                    Pattern patternPrice = Pattern.compile("^(([1-9]{1}\\d{0,4})(\\.(\\d){1,2})?)|(0\\.([1-9]{1,2}|0[1-9]{1}))$");
                    if (!patternPrice.matcher(value).matches()) {
                        errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "输入有误，范围为：0.00<特惠单价≤99999.99！");
                        continue;
                    }
                    arrayData[index][n] = value;
                } else if (n == 2) {
                    Pattern patternLimit = Pattern.compile("^([1-9]{1}\\d{0,4})$");
                    if (!patternLimit.matcher(value).matches()) {
                        errorMsg.add("第" + i + "行第" + (n + 1) + "列" + header[n] + "输入有误，范围为：1≤限量≤99999！");
                        continue;
                    }
                    arrayData[index][n] = String.format("%s%s%s", value, "@@@@@@", i);
                }
            }

            index++;
        }

        if (SpringUtil.isNotEmpty(errorMsg)) {
            return new XdaImportExcelResultBasicEntry(null, false, errorMsg);
        }

        /***
         * 验证商品编码系统是否存在
         */
        checkCommodityCodeIsExists(arrayData, repeatCommoditySet, errorMsg);
        if (SpringUtil.isNotEmpty(errorMsg)) {
            return new XdaImportExcelResultBasicEntry(null, false, errorMsg);
        }

        /***
         * 封装XdaSpecialsCommoditySet对象
         */
        List<XdaSpecialsCommoditySet> list = new ArrayList<>();
        String batchNo = UUID.randomUUID().toString();
        Date date = new Date();
        for (String[] entry : arrayData) {
            if(StringUtils.isNotBlank(entry[0])) {
                list.add(XdaSpecialsCommoditySet.toImportEntry(entry, batchNo, tokenInfo.getUserId(), tokenInfo.getRealName(), date));
            }
        }

        this.batchAddxdaSpecialsCommoditySet(list);
        return new XdaImportExcelResultBasicEntry(batchNo, true, null);
    }


    private void checkCommodityCodeIsExists(String[][] arrayData,Set<String> commdityCodes,List<String> errorMsg) {
        Example example = new Example(Commodity.class);
        example.createCriteria().andIn("commodityCode", commdityCodes);
        example.selectProperties("id", "commodityCode");
        List<Commodity> commodityList = commodityMapper.selectByExample(example);
        Map<String, String> commodityListMap = SpringUtil.isEmpty(commodityList) ? new HashMap<>() : commodityList.stream().collect(Collectors.toMap(Commodity::getCommodityCode, v -> v.getId().toString()));
        String[] str;
        for (String[] e : arrayData) {
            str = e[2].split("@@@@@@");
            if (commodityListMap.containsKey(e[0])) {
                e[0] = commodityListMap.get(e[0]);
            } else {
                errorMsg.add("第" + str[1] + "行第1列商品编码系统不存在，请确认！");
            }
            e[2] = str[0];
        }
    }


    /***
     * 获取特惠商品详情
     * @param id
     * @return
     */
    public XdaSpecialsCommoditySetDetailODTO findSpecialsCommoditySetDetailsById(Long id) {
        return xdaSpecialsCommoditySetMapper.findSpecialsCommoditySetDetails(id);
    }

    public List<XdaSpecialsCommoditydropDownODTO> selectCommodityDropDownList(XdaSpecialsCommoditydropDownIDTO vo) {
        return xdaSpecialsCommoditySetMapper.selectCommodityDropDownList(vo);
    }

    @Transactional(rollbackFor = Exception.class)
    public void batchAddxdaSpecialsCommoditySet(List<XdaSpecialsCommoditySet> excelList) {
        Example example=new Example(XdaSpecialsCommoditySet.class);
        example.createCriteria().andIn("commodityId",excelList.stream().map(XdaSpecialsCommoditySet::getCommodityId).distinct().collect(Collectors.toList()));
        List<XdaSpecialsCommoditySet> list=xdaSpecialsCommoditySetMapper.selectByExample(example);
        Map<Long, XdaSpecialsCommoditySet> map = SpringUtil.isEmpty(list) ? new HashMap<>() : list.stream().collect(Collectors.toMap(XdaSpecialsCommoditySet::getCommodityId, Function.identity()));
        List<XdaSpecialsCommoditySet> batchAddList = new ArrayList<>();
        List<XdaSpecialsCommoditySet> batchModifyList = new ArrayList<>();
        excelList.forEach(s -> {
            if (batchAddList.size() == 100) {
                xdaSpecialsCommoditySetMapper.insertList(batchAddList);
                this.batchAddXdaSpecialsCommoditySetLog(batchAddList,XdaSpecialsCommoditySetEnums.ADD);
                batchAddList.clear();
            }

            if (batchModifyList.size() == 100) {
                xdaSpecialsCommoditySetMapper.batchUpdateSpecialsCommoditySet(batchModifyList);
                this.batchAddXdaSpecialsCommoditySetLog(batchModifyList,XdaSpecialsCommoditySetEnums.MODIFY);
                batchModifyList.clear();
            }

            if (map.containsKey(s.getCommodityId())) {
                batchModifyList.add(s);
            } else {
                batchAddList.add(s);
            }
        });

        if (SpringUtil.isNotEmpty(batchAddList)) {
            xdaSpecialsCommoditySetMapper.insertList(batchAddList);
            this.batchAddXdaSpecialsCommoditySetLog(batchAddList,XdaSpecialsCommoditySetEnums.ADD);
            batchAddList.clear();
        }

        if (SpringUtil.isNotEmpty(batchModifyList)) {
            xdaSpecialsCommoditySetMapper.batchUpdateSpecialsCommoditySet(batchModifyList);
            this.batchAddXdaSpecialsCommoditySetLog(batchModifyList,XdaSpecialsCommoditySetEnums.MODIFY);
            batchModifyList.clear();
        }
    }


    /***
     * 记录日志
     * @param set
     * @param enums
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int addXdaSpecialsCommoditySetLog(XdaSpecialsCommoditySet set, XdaSpecialsCommoditySetEnums enums){
        return xdaSpecialsCommoditySetLogMapper.insert(XdaSpecialsCommoditySetLog.toAddEntry(set,enums));
    }

    /***
     * 导入批量添加日志
     * @param sets
     * @param enums
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int batchAddXdaSpecialsCommoditySetLog(List<XdaSpecialsCommoditySet> sets, XdaSpecialsCommoditySetEnums enums){
        List<XdaSpecialsCommoditySetLog> list=new ArrayList<>();
        sets.forEach(s -> list.add(XdaSpecialsCommoditySetLog.toAddEntry(s,enums)) );
        return xdaSpecialsCommoditySetLogMapper.insertList(list);
    }


    //------外部接口--------------------------------------------------------------------------------

    /**
     * 客户id和送货日期查询符合条件的商品集合
     * 特惠商品列表=当前客户价格方案∩鲜达已上架商品∩鲜达特惠商品∩特惠商品送货日期范围包含指定日期的
     * @param storeId
     * @return
     */
    public List<XdaSpecialsCommoditySet> findCommodityListByStoreAndOrderTime(Long storeId){
        Boolean isPfsStore = storeService.isPfsStore(storeId);
        List<XdaSpecialsCommoditySet> commodityList = xdaSpecialsCommoditySetMapper.findCommodityListByStoreAndOrderTime(storeId,isPfsStore);
        if(SpringUtil.isNotEmpty(commodityList)){
            return commodityList;
        }
        return Collections.emptyList();
    }

    /**
     * 根据商品id查询特惠商品详细: 特惠价和限量
     * @param commodityId
     * @return
     */
    public XdaSpecialsCommoditySet findXdaSpecialsCommodityInfoByCommodityId(Long commodityId){
        Example example = new Example(XdaSpecialsCommoditySet.class);
        example.createCriteria().andEqualTo("commodityId",commodityId);
        return xdaSpecialsCommoditySetMapper.selectOneByExample(example);
    }

    public List<Long> selectXdaSpecialsCommoditySet(List<Long> commodityIdList){
        Example example = new Example(XdaSpecialsCommoditySet.class);
        example.createCriteria().andIn("commodityId",commodityIdList);
        List<XdaSpecialsCommoditySet> xdaSpecialsCommoditySets = xdaSpecialsCommoditySetMapper.selectByExample(example);
        if(SpringUtil.isNotEmpty(xdaSpecialsCommoditySets)){
           return xdaSpecialsCommoditySets.stream().map(XdaSpecialsCommoditySet::getCommodityId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    public List<XdaSpecialsCommoditySet> selectXdaSpecialsCommoditySetListByCommodityIdList(List<Long> commodityIdList){
        Example example = new Example(XdaSpecialsCommoditySet.class);
        example.createCriteria().andIn("commodityId",commodityIdList);
        List<XdaSpecialsCommoditySet> xdaSpecialsCommoditySets = xdaSpecialsCommoditySetMapper.selectByExample(example);
        if(SpringUtil.isNotEmpty(xdaSpecialsCommoditySets)){
            return xdaSpecialsCommoditySets;
        }
        return new ArrayList<>();
    }
}
