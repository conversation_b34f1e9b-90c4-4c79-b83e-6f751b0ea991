package com.pinshang.qingyun.xda.product.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.XSCouponPublishRuleStatusEnums;
import com.pinshang.qingyun.base.enums.XdOperateTypeEnums;
import com.pinshang.qingyun.base.enums.settlement.OperateTypeNewEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.ListUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.product.dto.commodityTag.*;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityTextMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaTagLogMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaTagMapper;
import com.pinshang.qingyun.xda.product.model.XdaTag;
import com.pinshang.qingyun.xda.product.model.XdaTagLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * @author: chenqiang
 * @time: 2020/12/22 14:28
 */
@Service
public class XdaTagService {

    @Autowired
    private XdaTagMapper xdaTagMapper;

    @Autowired
    private XdaTagLogMapper xdaTagLogMapper;

    @Autowired
    private XdaCommodityTextMapper xdaCommodityTextMapper;
    /**
     * 查询鲜达商品自定义标签列表
     * @param commodityTagIDTO
     * @return
     */
    public PageInfo<XdaCommodityTagODTO> selectXdaCommodityTagList(XdaCommodityTagIDTO commodityTagIDTO){
        QYAssert.isTrue(null != commodityTagIDTO, "参数有误!");
        PageInfo<XdaCommodityTagODTO> pageDate = PageHelper.startPage(commodityTagIDTO.getPageNo(), commodityTagIDTO.getPageSize()).doSelectPageInfo(() -> {
            xdaTagMapper.selectXdaCommodityTagList(commodityTagIDTO);
        });
        return pageDate;
    }

    /**
     * 新增鲜达商品自定义标签
     * @param commodityTagSaveIDTO
     * @return
     */
    public Integer saveXdaCommodityTag(XdaCommodityTagSaveIDTO commodityTagSaveIDTO){
        QYAssert.isTrue(null != commodityTagSaveIDTO, "参数有误!");
        QYAssert.isTrue(null != commodityTagSaveIDTO.getTagName(), "标签名称不能为空!");
        QYAssert.isTrue(null != commodityTagSaveIDTO.getTagBgColor(), "标签底色不能为空!");
        QYAssert.isTrue(commodityTagSaveIDTO.getTagName().length()<(5+1), "标签名称最多五位!");
        this.checkTagName(commodityTagSaveIDTO.getTagName(),null);
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        Date date =  new Date();
        XdaTag xdTag =  new XdaTag().saveXdaTag(commodityTagSaveIDTO.getTagName(),commodityTagSaveIDTO.getTagBgColor(), XSCouponPublishRuleStatusEnums.ENABLE.getCode(),userId,date,userId,date);
        xdaTagMapper.insert(xdTag);
        return xdaTagLogMapper.insert(new XdaTagLog().saveXdaTagLog(1,xdTag.getId(),commodityTagSaveIDTO.getTagName(),commodityTagSaveIDTO.getTagBgColor(), userId,date));
    }

    /**
     * 修改鲜达商品自定义标签
     * @param commodityTagUpdateIDTO
     * @return
     */
    public Integer updateXdaCommodityTag(XdaCommodityTagUpdateIDTO commodityTagUpdateIDTO){
        QYAssert.isTrue(null != commodityTagUpdateIDTO, "参数有误!");
        QYAssert.isTrue(null != commodityTagUpdateIDTO.getId(), "操作对象有误!");
        QYAssert.isTrue(null != commodityTagUpdateIDTO.getTagName(), "标签名称不能为空!");
        QYAssert.isTrue(null != commodityTagUpdateIDTO.getTagBgColor(), "标签底色不能为空!");
        QYAssert.isTrue(commodityTagUpdateIDTO.getTagName().length()<(5+1), "标签名称最多五位!");
        this.checkTagName(commodityTagUpdateIDTO.getTagName(),commodityTagUpdateIDTO.getId());
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        Date date =  new Date();
        Example example = new Example(XdaTag.class);
        example.createCriteria().andEqualTo("id", commodityTagUpdateIDTO.getId());
        XdaTag xdTag = new XdaTag().updateXdaTag(commodityTagUpdateIDTO.getTagName(),commodityTagUpdateIDTO.getTagBgColor(),userId,date);
        xdaTagMapper.updateByExampleSelective(xdTag, example);
        return xdaTagLogMapper.insert(new XdaTagLog().saveXdaTagLog(OperateTypeNewEnum.EDIT.getCode(),commodityTagUpdateIDTO.getId(),commodityTagUpdateIDTO.getTagName(),commodityTagUpdateIDTO.getTagBgColor(), userId,date));
    }

    /**
     * 修改鲜达商品自定义标签状态
     * @param commodityTagUpdateStatusIDTO
     * @return
     */
    public Integer updateXdaCommodityTagStatus(XdaCommodityTagUpdateStatusIDTO commodityTagUpdateStatusIDTO) {
        QYAssert.isTrue(null != commodityTagUpdateStatusIDTO, "参数有误!");
        QYAssert.isTrue(null != commodityTagUpdateStatusIDTO.getId(), "操作对象有误!");
        QYAssert.isTrue(null != commodityTagUpdateStatusIDTO.getStatus(), "操作状态有误!");
        XdaTag xdTag = xdaTagMapper.selectByPrimaryKey(commodityTagUpdateStatusIDTO.getId());
        QYAssert.isTrue(null != xdTag, "操作对象不存在!");
        Long userId = FastThreadLocalUtil.getQY().getUserId();
        Date date =  new Date();
        Example example = new Example(XdaTag.class);
        example.createCriteria().andEqualTo("id", commodityTagUpdateStatusIDTO.getId());
        xdaTagMapper.updateByExampleSelective(new XdaTag().updateXdaTagStatus(commodityTagUpdateStatusIDTO.getStatus(),userId,date), example);
        return xdaTagLogMapper.insert(new XdaTagLog().saveXdaTagLog(commodityTagUpdateStatusIDTO.getStatus().equals(XSCouponPublishRuleStatusEnums.ENABLE.getCode())? OperateTypeNewEnum.ENABLE.getCode():OperateTypeNewEnum.DISABLE.getCode(), xdTag.getId(),xdTag.getTagName(),xdTag.getTagBgColor(), userId,date));
    }

    /**
     * 查询鲜达商品自定义标签日志
     * @param commodityTagLogIDTO
     * @return
     */
    public PageInfo<XdaCommodityTagLogODTO> selectXdaCommodityTagLogList(XdaCommodityTagLogIDTO commodityTagLogIDTO) {
        QYAssert.isTrue(null != commodityTagLogIDTO, "参数有误!");
        PageInfo<XdaCommodityTagLogODTO> pageDate = PageHelper.startPage(commodityTagLogIDTO.getPageNo(), commodityTagLogIDTO.getPageSize()).doSelectPageInfo(() -> {
            xdaTagLogMapper.selectXdaCommodityTagLogList(commodityTagLogIDTO);
        });
        return pageDate;
    }

    /**
     * 判断名称是否重复
     * @param tagName
     * @param id
     */
    private void checkTagName(String tagName,Long id){
        Example ex = new Example(XdaTag.class);
        if(null != id){
            ex.createCriteria().andEqualTo("tagName",tagName).andNotEqualTo("id",id);
        }else {
            ex.createCriteria().andEqualTo("tagName",tagName);
        }
        List<XdaTag> xdTagList = xdaTagMapper.selectByExample(ex);
        QYAssert.isTrue(SpringUtil.isEmpty(xdTagList)," 标签名称重复,请修改!");
    }
}
