package com.pinshang.qingyun.xda.product.service.deliveryDate;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityLogAddVO;
import com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityLogDTO;
import com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityLogVO;
import com.pinshang.qingyun.xda.product.mapper.deliveryDate.PfOrderCommodityLogDao;
import com.pinshang.qingyun.xda.product.mapper.deliveryDate.XdaOrderCommodityDao;
import com.pinshang.qingyun.xda.product.mapper.deliveryDate.XdaOrderCommodityLogDao;
import com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodity;
import com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodityLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;


@Service
@Slf4j
public class XdaOrderCommodityLogService {
    @Resource
    XdaOrderCommodityLogDao xdaOrderCommodityLogDao;

    @Resource
    XdaOrderCommodityDao xdaOrderCommodityDao;

    @Resource
    PfOrderCommodityLogDao pfOrderCommodityLogDao;
    

    /**
     * 根据商品编码，商品名称，操作类型，查询日志
     * @param xdaOrderCommodityLogVO
     * @return
     */
    public PageInfo<XdaOrderCommodityLogDTO> getXdaOrderCommodityLog(XdaOrderCommodityLogVO xdaOrderCommodityLogVO){
        log.info("GetXdaOrderCommodityLog With Param:{}",xdaOrderCommodityLogVO);
        QYAssert.isTrue(null != xdaOrderCommodityLogVO.getAppType(),"app类型不能为空!");
        if (xdaOrderCommodityLogVO.getAppType() == 1){
            return PageHelper.startPage(xdaOrderCommodityLogVO.getPageNo(),xdaOrderCommodityLogVO.getPageSize()).doSelectPageInfo(()->
                    xdaOrderCommodityLogDao.getXdaOrderCommodityLogList(xdaOrderCommodityLogVO));
        }else {
            return PageHelper.startPage(xdaOrderCommodityLogVO.getPageNo(),xdaOrderCommodityLogVO.getPageSize()).doSelectPageInfo(()->
                    pfOrderCommodityLogDao.getPfOrderCommodityLogList(xdaOrderCommodityLogVO));
        }
    }

    /**
     * 新增日志
     * @param xdaOrderCommodityLogAddVO
     */
    @Transactional(propagation= Propagation.REQUIRED,rollbackFor = Exception.class)
    public void addXdaOrderCommodityLog(XdaOrderCommodityLogAddVO xdaOrderCommodityLogAddVO,Integer appType){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        log.info("Add XdaOrderCommodityLog{} With Token:{}",xdaOrderCommodityLogAddVO,tokenInfo);
        XdaOrderCommodity xdaOrderCommodity = xdaOrderCommodityDao.selectXdaOrderCommodityById(xdaOrderCommodityLogAddVO.getDeliveryOrderId());
        XdaOrderCommodityLog xdaOrderCommodityLog = BeanCloneUtils.copyTo(xdaOrderCommodity, XdaOrderCommodityLog.class);
        xdaOrderCommodityLog.setOpType(xdaOrderCommodityLogAddVO.getOpType());
        xdaOrderCommodityLog.setCreateId(tokenInfo.getUserId());
        xdaOrderCommodityLog.setCreateName(tokenInfo.getRealName());
        xdaOrderCommodityLog.setCreateTime(new Date());
        xdaOrderCommodityLog.setId(null);
        if (appType == 1) {
            xdaOrderCommodityLogDao.insertXdaOrderCommodityLog(xdaOrderCommodityLog);
        } else {
            pfOrderCommodityLogDao.insertPfOrderCommodityLog(xdaOrderCommodityLog);
        }
    }

}
