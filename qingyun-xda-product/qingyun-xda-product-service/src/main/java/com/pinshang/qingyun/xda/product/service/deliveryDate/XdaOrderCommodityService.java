package com.pinshang.qingyun.xda.product.service.deliveryDate;


import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.pf.product.dto.ordercommodity.PfOrderCommodityAddVO;
import com.pinshang.qingyun.pf.product.dto.ordercommodity.PfOrderCommodityBatchAddVO;
import com.pinshang.qingyun.pf.product.service.PfOrderCommodityClient;
import com.pinshang.qingyun.storage.service.CommoditySupplierClient;
import com.pinshang.qingyun.xda.product.dto.deliveryDate.*;
import com.pinshang.qingyun.xda.product.dto.kafka.ESXdaOrderCommodityVO;
import com.pinshang.qingyun.xda.product.mapper.deliveryDate.XdaOrderCommodityDao;
import com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class XdaOrderCommodityService {
    
    @Resource
    XdaOrderCommodityDao xdaOrderCommodityDao;

    @Resource
    CommoditySupplierClient commoditySupplierClient;

    @Resource
    XdaOrderCommodityLogService xdaOrderCommodityLogService;

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Autowired
    private PfOrderCommodityClient pfOrderCommodityClient;

    public List<Integer> getDeliveryDateRangeCode(){
        List<Integer> deliveryRageCode = new ArrayList<>();
        List<XdaOrderCommodity> xdaOrderCommodities = xdaOrderCommodityDao.getDistinctXdaOrderCommodityList();
        if(!CollectionUtils.isEmpty(xdaOrderCommodities)){
           for(XdaOrderCommodity xdaOrderCommodity:xdaOrderCommodities){
               try {
                   String[] values = xdaOrderCommodity.getDeliveryDateRangeCode().split("-");
                   Integer a = Integer.valueOf(values[0]);
                   Integer b = Integer.valueOf(values[1]);
                   deliveryRageCode.add(a);
                   deliveryRageCode.add(b);
               }catch (Exception ex){

               }
           }
        }
        deliveryRageCode.stream().sorted();
        return deliveryRageCode;
    }


    public PageInfo<XdaOrderCommodityDTO> selectOrderCommodityList(XdaOrderCommodityVO xdaOrderCommodityVO){
        QYAssert.isTrue(null != xdaOrderCommodityVO.getAppType(),"app类型不能为空!");
        log.info("selectOrderCommodityList With Param:{}",xdaOrderCommodityVO);
        if(null!=xdaOrderCommodityVO.getSupplierId()){
            List<Long> commodityIds = commoditySupplierClient.queryCommodityIdsByDefaultSupplier(xdaOrderCommodityVO.getSupplierId());
            if(!CollectionUtils.isEmpty(commodityIds)){
                xdaOrderCommodityVO.setCommodityIds(commodityIds);
            }else{
                xdaOrderCommodityVO.setCommodityIds(Arrays.asList(0L));
            }
        }

        return PageHelper.startPage(xdaOrderCommodityVO.getPageNo(),xdaOrderCommodityVO.getPageSize()).doSelectPageInfo(()->
                xdaOrderCommodityDao.getXdaOrderCommodityList(xdaOrderCommodityVO));

    }
    @Transactional(propagation= Propagation.REQUIRED,rollbackFor = Exception.class)
    public void addOrderCommodityDate(XdaOrderCommodityAddVO xdaOrderCommodityAddVO){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        log.info("addOrderCommodityDate{} With Token:{}",xdaOrderCommodityAddVO,tokenInfo);
        log.info("recived obj:{}",xdaOrderCommodityAddVO);
        XdaOrderCommodity xdaOrderCommodity = new XdaOrderCommodity();
        XdaOrderCommodity xdaOrderCommodity1 = xdaOrderCommodityDao.selectCommodityById(xdaOrderCommodityAddVO.getOrderCommodityId());
        QYAssert.isTrue(null != xdaOrderCommodity1,"该商品不存在");
        XdaOrderCommodity dbCommodity = xdaOrderCommodityDao.selectXdaOrderCommodityByCommodityId(xdaOrderCommodity1.getCommodityId());

        xdaOrderCommodity.setCommodityId(xdaOrderCommodity1.getCommodityId());
        xdaOrderCommodity.setCommodityName(xdaOrderCommodity1.getCommodityName());
        xdaOrderCommodity.setCommodityCode(xdaOrderCommodity1.getCommodityCode());
        if (Objects.equals(xdaOrderCommodityAddVO.getAppType(), 1)) {
            xdaOrderCommodity.setDeliveryDateRangeCode(xdaOrderCommodityAddVO.getCommodityDateCode());
            xdaOrderCommodity.setDeliveryDateRangeValue(xdaOrderCommodityAddVO.getCommodityDateValue());
        } else {
            xdaOrderCommodity.setPfDeliveryDateRangeCode(xdaOrderCommodityAddVO.getCommodityDateCode());
            xdaOrderCommodity.setPfDeliveryDateRangeValue(xdaOrderCommodityAddVO.getCommodityDateValue());
        }
        xdaOrderCommodity.setSpec(xdaOrderCommodity1.getSpec());
        xdaOrderCommodity.setCreateId(tokenInfo.getUserId());
        xdaOrderCommodity.setCreateName(tokenInfo.getRealName());
        xdaOrderCommodity.setCreateTime(new Date());
        xdaOrderCommodity.setUpdateId(tokenInfo.getUserId());
        xdaOrderCommodity.setUpdateName(tokenInfo.getRealName());
        xdaOrderCommodity.setUpdateTime(new Date());
        if(null != dbCommodity){
            xdaOrderCommodity.setId(dbCommodity.getId());
            xdaOrderCommodityDao.updateNonEmptyXdaOrderCommodityById(xdaOrderCommodity);
        } else {
            if (Objects.isNull(xdaOrderCommodity.getDeliveryDateRangeCode())) {
                xdaOrderCommodity.setDeliveryDateRangeCode(StringUtils.EMPTY);
            }
            if (Objects.isNull(xdaOrderCommodity.getDeliveryDateRangeValue())) {
                xdaOrderCommodity.setDeliveryDateRangeValue(StringUtils.EMPTY);
            }
            if (Objects.isNull(xdaOrderCommodity.getPfDeliveryDateRangeCode())) {
                xdaOrderCommodity.setPfDeliveryDateRangeCode(StringUtils.EMPTY);
            }
            if (Objects.isNull(xdaOrderCommodity.getPfDeliveryDateRangeValue())) {
                xdaOrderCommodity.setPfDeliveryDateRangeValue(StringUtils.EMPTY);
            }
            xdaOrderCommodityDao.insertNonEmptyXdaOrderCommodity(xdaOrderCommodity);
        }

        //记录日志
        XdaOrderCommodityLogAddVO xdaOrderCommodityLogAddVO = new XdaOrderCommodityLogAddVO();
        xdaOrderCommodityLogAddVO.setCommodityCode(xdaOrderCommodity1.getCommodityCode());
        xdaOrderCommodityLogAddVO.setCommodityName(xdaOrderCommodity1.getCommodityName());
        if (Objects.equals(xdaOrderCommodityAddVO.getAppType(), 1)) {
            xdaOrderCommodityLogAddVO.setDeliveryDateRangeCode(xdaOrderCommodity.getDeliveryDateRangeCode());
            xdaOrderCommodityLogAddVO.setDeliveryDateRangeValue(xdaOrderCommodity.getDeliveryDateRangeValue());
        }else {
            xdaOrderCommodityLogAddVO.setDeliveryDateRangeCode(xdaOrderCommodity.getPfDeliveryDateRangeCode());
            xdaOrderCommodityLogAddVO.setDeliveryDateRangeValue(xdaOrderCommodity.getDeliveryDateRangeValue());
        }
        xdaOrderCommodityLogAddVO.setOpType("0");
        xdaOrderCommodityLogAddVO.setDeliveryOrderId(xdaOrderCommodity.getId());
        xdaOrderCommodityLogAddVO.setSpec(xdaOrderCommodity.getSpec());
        xdaOrderCommodityLogService.addXdaOrderCommodityLog(xdaOrderCommodityLogAddVO, xdaOrderCommodityAddVO.getAppType());

        //发消息通知鲜达搜索
        List<ESXdaOrderCommodityVO> kafkaVoList = new ArrayList<>();
        kafkaVoList.add(new ESXdaOrderCommodityVO(xdaOrderCommodityAddVO.getOrderCommodityId(), xdaOrderCommodityAddVO.getCommodityDateCode(), xdaOrderCommodityAddVO.getCommodityDateValue(),xdaOrderCommodityAddVO.getAppType()));
        mqSenderComponent.send(QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XDA_ORDER_COMMODITY_CHANGE_TOPIC.getTopic(),
                kafkaVoList, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XDA_ORDER_COMMODITY_CHANGE_TOPIC.name(),
                KafkaMessageOperationTypeEnum.UPDATE.name());

        //如果类型是批发， 调用 原来的接口，维护老的数据
        if (Objects.equals(xdaOrderCommodityAddVO.getAppType(), 2)) {
            //TODO 批发服务下架后，需注释掉
            PfOrderCommodityAddVO pfOrderCommodityBatchAddVO = BeanCloneUtils.copyTo(xdaOrderCommodityAddVO, PfOrderCommodityAddVO.class);
            pfOrderCommodityClient.set(pfOrderCommodityBatchAddVO);
        }
    }

    @Transactional(propagation= Propagation.REQUIRED,rollbackFor = Exception.class)
    public void batchAddOrderCommodityDate(XdaOrderCommodityBatchAddVO xdaOrderCommodityBatchAddVO){
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        log.info("batchAddOrderCommodityDate{} With Token:{}",xdaOrderCommodityBatchAddVO,tokenInfo);

        if(!CollectionUtils.isEmpty(xdaOrderCommodityBatchAddVO.getOrderCommodityIds())){
            List<ESXdaOrderCommodityVO> kafkaVoList = new ArrayList<>();
            for(Long commodityId:xdaOrderCommodityBatchAddVO.getOrderCommodityIds()){
                XdaOrderCommodity xdaOrderCommodity = new XdaOrderCommodity();
                XdaOrderCommodity xdaOrderCommodity1 = xdaOrderCommodityDao.selectCommodityById(commodityId);
                QYAssert.isTrue(null != xdaOrderCommodity1,"该商品不存在");
                XdaOrderCommodity dbCommodity = xdaOrderCommodityDao.selectXdaOrderCommodityByCommodityId(commodityId);

                xdaOrderCommodity.setCommodityName(xdaOrderCommodity1.getCommodityName());
                xdaOrderCommodity.setCommodityCode(xdaOrderCommodity1.getCommodityCode());
                xdaOrderCommodity.setCommodityId(xdaOrderCommodity1.getCommodityId());
                if (Objects.equals(xdaOrderCommodityBatchAddVO.getAppType(), 1)) {
                    xdaOrderCommodity.setDeliveryDateRangeCode(xdaOrderCommodityBatchAddVO.getCommodityDateCode());
                    xdaOrderCommodity.setDeliveryDateRangeValue(xdaOrderCommodityBatchAddVO.getCommodityDateValue());
                } else {
                    xdaOrderCommodity.setPfDeliveryDateRangeCode(xdaOrderCommodityBatchAddVO.getCommodityDateCode());
                    xdaOrderCommodity.setPfDeliveryDateRangeValue(xdaOrderCommodityBatchAddVO.getCommodityDateValue());
                }
                xdaOrderCommodity.setSpec(xdaOrderCommodity1.getSpec());
                xdaOrderCommodity.setCreateId(tokenInfo.getUserId());
                xdaOrderCommodity.setCreateName(tokenInfo.getRealName());
                xdaOrderCommodity.setCreateTime(new Date());
                xdaOrderCommodity.setUpdateId(tokenInfo.getUserId());
                xdaOrderCommodity.setUpdateName(tokenInfo.getRealName());
                xdaOrderCommodity.setUpdateTime(new Date());
                if(null != dbCommodity){
                    xdaOrderCommodity.setId(dbCommodity.getId());
                    xdaOrderCommodityDao.updateNonEmptyXdaOrderCommodityById(xdaOrderCommodity);
                }else{
                    if (Objects.isNull(xdaOrderCommodity.getDeliveryDateRangeCode())) {
                        xdaOrderCommodity.setDeliveryDateRangeCode(StringUtils.EMPTY);
                    }
                    if (Objects.isNull(xdaOrderCommodity.getDeliveryDateRangeValue())) {
                        xdaOrderCommodity.setDeliveryDateRangeValue(StringUtils.EMPTY);
                    }
                    if (Objects.isNull(xdaOrderCommodity.getPfDeliveryDateRangeCode())) {
                        xdaOrderCommodity.setPfDeliveryDateRangeCode(StringUtils.EMPTY);
                    }
                    if (Objects.isNull(xdaOrderCommodity.getPfDeliveryDateRangeValue())) {
                        xdaOrderCommodity.setPfDeliveryDateRangeValue(StringUtils.EMPTY);
                    }
                    xdaOrderCommodityDao.insertNonEmptyXdaOrderCommodity(xdaOrderCommodity);
                }
                //记录日志
                XdaOrderCommodityLogAddVO xdaOrderCommodityLogAddVO = new XdaOrderCommodityLogAddVO();
                xdaOrderCommodityLogAddVO.setCommodityCode(xdaOrderCommodity1.getCommodityCode());
                xdaOrderCommodityLogAddVO.setCommodityName(xdaOrderCommodity1.getCommodityName());
                if (Objects.equals(xdaOrderCommodityBatchAddVO.getAppType(), 1)) {
                    xdaOrderCommodityLogAddVO.setDeliveryDateRangeCode(xdaOrderCommodity.getDeliveryDateRangeCode());
                    xdaOrderCommodityLogAddVO.setDeliveryDateRangeValue(xdaOrderCommodity.getDeliveryDateRangeValue());
                }else {
                    xdaOrderCommodityLogAddVO.setDeliveryDateRangeCode(xdaOrderCommodity.getPfDeliveryDateRangeCode());
                    xdaOrderCommodityLogAddVO.setDeliveryDateRangeValue(xdaOrderCommodity.getPfDeliveryDateRangeValue());
                }
                xdaOrderCommodityLogAddVO.setOpType("1");
                xdaOrderCommodityLogAddVO.setSpec(xdaOrderCommodity.getSpec());
                xdaOrderCommodityLogAddVO.setDeliveryOrderId(xdaOrderCommodity.getId());
                xdaOrderCommodityLogService.addXdaOrderCommodityLog(xdaOrderCommodityLogAddVO, xdaOrderCommodityBatchAddVO.getAppType());

                //消息对象
                kafkaVoList.add(new ESXdaOrderCommodityVO(commodityId, xdaOrderCommodityBatchAddVO.getCommodityDateCode(), xdaOrderCommodityBatchAddVO.getCommodityDateValue(),xdaOrderCommodityBatchAddVO.getAppType()));
            }
            //发消息通知鲜达搜索
            mqSenderComponent.send(QYApplicationContext.applicationNameSwitch+ KafkaTopicEnum.XDA_ORDER_COMMODITY_CHANGE_TOPIC.getTopic(),
                    kafkaVoList, MqMessage.MQ_KAFKA, KafkaMessageTypeEnum.XDA_ORDER_COMMODITY_CHANGE_TOPIC.name(),
                    KafkaMessageOperationTypeEnum.UPDATE.name());

            //如果类型是批发， 调用 原来的接口，维护老的数据
            if (Objects.equals(xdaOrderCommodityBatchAddVO.getAppType(), 2)) {
                //TODO 批发服务下架后，需注释掉
                PfOrderCommodityBatchAddVO pfOrderCommodityBatchAddVO = BeanCloneUtils.copyTo(xdaOrderCommodityBatchAddVO, PfOrderCommodityBatchAddVO.class);
                pfOrderCommodityClient.batchSet(pfOrderCommodityBatchAddVO);
            }
        }
    }

    public XdaOrderCommodityDTO getDetailById(Long id){
         XdaOrderCommodity xdaOrderCommodity = xdaOrderCommodityDao.selectXdaOrderCommodityById(id);
        XdaOrderCommodityDTO xdaOrderCommodityDTO = BeanCloneUtils.copyTo(xdaOrderCommodity, XdaOrderCommodityDTO.class);
         return xdaOrderCommodityDTO;
    }

}
