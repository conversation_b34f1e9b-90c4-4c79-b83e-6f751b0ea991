package com.pinshang.qingyun.xda.product.service.front;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.enums.CategoryLevelEnums;
import com.pinshang.qingyun.base.enums.pic.PicSizeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.NumberUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.product.conf.XdaProductApolloConfig;
import com.pinshang.qingyun.xda.product.constant.XdaConstant;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaReCommendAppODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaSerialCommodityDetailODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.*;
import com.pinshang.qingyun.xda.product.mapper.XdaCategoryFrontMapper;
import com.pinshang.qingyun.xda.product.service.StoreService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;


@Service
@Slf4j
public class XdaCategoryFrontService {

    @Autowired
    private XdaCategoryFrontMapper xdaCategoryFrontMapper;
    @Autowired
    private XdaCommodityFrontService xdaCommodityFrontService;

    @Autowired
    private XdaProductApolloConfig xdaProductApolloConfig;
    @Autowired
    private StoreService storeService;

    /**
     * 查询分类
     * @param orderTime
     * @param storeId
     * @return
     */
    public XdaCategoryResODTO queryXdaCategoryList(Date orderTime, Long storeId) {
        List<XdaCategoryBaseODTO> firstList = xdaCategoryFrontMapper.queryXdaCategoryList(null,storeId, CategoryLevelEnums.FIRST.getCode());
        if(CollectionUtils.isEmpty(firstList)){
            return null;
        }
        //查找所有的二级分类
        List<Long> firstIdList = firstList.stream().map(XdaCategoryBaseODTO::getXdaCategoryId).collect(Collectors.toList());
        List<XdaCategoryBaseODTO> allSecondList = xdaCategoryFrontMapper.queryXdaCategoryList(firstIdList,storeId, CategoryLevelEnums.SECOND.getCode());
        if(CollectionUtils.isEmpty(allSecondList)){
            return null;
        }
        Map<Long,List<XdaCategoryBaseODTO>> secondMap = allSecondList.stream().collect(Collectors.groupingBy(XdaCategoryBaseODTO::getParentId,LinkedHashMap::new,Collectors.toList()));

        /**查找一级分类下的推荐商品**/
        Map<Long,List<Long>> recommendMap = this.getRecommendMap(firstIdList,storeId);

        List<XdaCategoryODTO> xdaCategoryODTOList = new ArrayList<>();
        XdaCategoryCommodityResODTO commodityAppODTO = new XdaCategoryCommodityResODTO();
        Boolean flag = false;
        for(XdaCategoryBaseODTO firstCategory : firstList){
            Long firstCategoryId = firstCategory.getXdaCategoryId();
            List<XdaCategoryBaseODTO> secondList = secondMap.get(firstCategoryId);
            if(CollectionUtils.isEmpty(secondList)){
                continue;
            }
            if(SpringUtil.isNotEmpty(recommendMap) && CollectionUtils.isNotEmpty(recommendMap.get(firstCategoryId))){
                secondList.add(0,XdaCategoryBaseODTO.buildRecommendLabel(firstCategoryId));
            }
            if(!flag){
                XdaCategoryAppIDTO commodityAppIDTO = XdaCategoryAppIDTO.init(orderTime,storeId,firstCategory.getXdaCategoryId(),secondList.get(0).getXdaCategoryId());
                commodityAppODTO = this.queryXdaCategoryCommodityList(commodityAppIDTO);
                if(commodityAppODTO == null || CollectionUtils.isEmpty(commodityAppODTO.getCommodityList())){
                    continue;
                }
                flag = true;
            }
            XdaCategoryODTO firstODTO = BeanCloneUtils.copyTo(firstCategory, XdaCategoryODTO.class);
            firstODTO.setSecondCategoryList(secondList);
            xdaCategoryODTOList.add(firstODTO);
        }
        if(CollectionUtils.isEmpty(xdaCategoryODTOList)){
            return null;
        }
        if(!FastThreadLocalUtil.getXDA().getIsTouristStore()){
            xdaCategoryODTOList.add(0,XdaCategoryODTO.buildDefaultLabel());
        }
        return new XdaCategoryResODTO(xdaCategoryODTOList,commodityAppODTO);
    }

    /**
     * 查询分类商品
     * @param appIDTO
     * @return
     */
    public XdaCategoryCommodityResODTO queryXdaCategoryCommodityList(XdaCategoryAppIDTO appIDTO) {
        if(appIDTO.getXdaFirstCategoryId()==null || appIDTO.getXdaSecondCategoryId()==null){
            log.error("查询分类商品，一级分类和二级分类都必传");
            return null;
        }
        Boolean isVisitor = FastThreadLocalUtil.getXDA().getIsTouristStore();
        List<Long> commodityIdList = null;
        Boolean oftenBuyFlag = appIDTO.getXdaFirstCategoryId().equals(XdaConstant.FIRST_CATEGORY_OFTEN_BUY);
        Boolean reommendFlag = appIDTO.getXdaSecondCategoryId().equals(0L);
        if(oftenBuyFlag){//常购清单 或推荐商品
            if(isVisitor){
                return null;
            }
            if(oftenBuyFlag && appIDTO.getXdaSecondCategoryId().equals(XdaConstant.SECOND_CATEGORY_MY_OFTEN_BUY)){
                commodityIdList = xdaCategoryFrontMapper.queryOftenBuyCommodityIdList(appIDTO.getStoreId());
            }else if(oftenBuyFlag && appIDTO.getXdaSecondCategoryId().equals(XdaConstant.SECOND_CATEGORY_MY_COLLECT)){
                commodityIdList = xdaCategoryFrontMapper.queryCollectCommodityIdList(appIDTO.getStoreId());
            }
            if(CollectionUtils.isEmpty(commodityIdList)){
                return null;
            }
        }
        if(reommendFlag){
            List<XdaReCommendAppODTO> recommendList = xdaCategoryFrontMapper.queryXdaReCommendFront(Collections.singletonList(appIDTO.getXdaFirstCategoryId()),appIDTO.getStoreId());
            if (CollectionUtils.isNotEmpty(recommendList)){
                commodityIdList = recommendList.stream().map(XdaReCommendAppODTO::getCommodityId).collect(toList());
            }
            if(CollectionUtils.isEmpty(commodityIdList)){
                return null;
            }
        }
        List<XdaCategoryCommodityODTO> commodityODTOList = this.processXdaCategoryCommodity(XdaSearchAppIDTO.convert(appIDTO),commodityIdList);
        if(CollectionUtils.isEmpty(commodityODTOList)){
            return null;
        }
        return new XdaCategoryCommodityResODTO(appIDTO.getXdaFirstCategoryId(),appIDTO.getXdaSecondCategoryId(),commodityODTOList);
    }

    private List<XdaCategoryCommodityODTO> processXdaCategoryCommodity(XdaSearchAppIDTO searchIDTO, List<Long> searchCommodityIdList) {
        List<XdaCommodityAppODTO> appODTOList = this.queryXdaCommodityList(searchIDTO,searchCommodityIdList,PicSizeEnums.PIC_120x120);
        if(CollectionUtils.isEmpty(appODTOList)){
            return null;
        }
        Map<Long,List<XdaCommodityAppODTO>> serialMap = appODTOList.stream().collect(Collectors.groupingBy(XdaCommodityAppODTO::getSerialCommodityId,LinkedHashMap::new,Collectors.toList()));
        List<XdaCategoryCommodityODTO> commodityODTOList = new ArrayList<>();
        appODTOList.forEach(appODTO -> {
            XdaCategoryCommodityODTO categoryCommodityODTO = XdaCategoryCommodityODTO.convert(appODTO);
            Long commodityId = appODTO.getCommodityId();
            if(searchIDTO.getXdaFirstCategoryId()==null || searchIDTO.getXdaFirstCategoryId().equals(XdaConstant.FIRST_CATEGORY_OFTEN_BUY)
                    || appODTO.getSerialCommodityId()==0 ){
                categoryCommodityODTO.setIsSerial(0);
                commodityODTOList.add(categoryCommodityODTO);
            }else{
                List<XdaCommodityAppODTO> serialList = serialMap.get(appODTO.getSerialCommodityId());
                boolean isSerial = CollectionUtils.isNotEmpty(serialList) && serialList.size()>1 ;
                isSerial = isSerial && serialList.stream().anyMatch(bean-> bean.getCommodityId().equals(appODTO.getSerialCommodityId() )) ;

                if(!isSerial){
                    categoryCommodityODTO.setIsSerial(0);
                    categoryCommodityODTO.setSerialCommodityId(0L);
                    commodityODTOList.add(categoryCommodityODTO);
                    return;
                }
                if(appODTO.getSerialCommodityId().equals(commodityId)){
                    categoryCommodityODTO.setIsSerial(1);
                    this.processXdaSerialCommodityInfo(categoryCommodityODTO,serialList);
                    commodityODTOList.add(categoryCommodityODTO);
                }
            }
        });
        return commodityODTOList;
    }
    private List<XdaCommodityAppODTO> queryXdaCommodityList(XdaSearchAppIDTO searchIDTO, List<Long> searchCommodityIdList,PicSizeEnums picSizeEnums) {
        if(CollectionUtils.isNotEmpty(searchCommodityIdList)){
            searchIDTO.setCommodityIdList(searchCommodityIdList);
        }

        Boolean isPfsStore = storeService.isPfsStore(searchIDTO.getStoreId());
        searchIDTO.setIsPfsStore(isPfsStore);
        List<XdaCommodityAppODTO> appODTOList = xdaCategoryFrontMapper.queryXdaCategoryCommoditySearch(searchIDTO);
        if(CollectionUtils.isEmpty(appODTOList)){
            return null;
        }
        XdaCommodityAppIDTO appIDTO = XdaCommodityAppIDTO.builder().orderTime(searchIDTO.getOrderTime())
                .storeId(searchIDTO.getStoreId()).defaultImageSize(picSizeEnums).build();
        if(searchIDTO.getXdaFirstCategoryId()!=null){
            appIDTO.setNeedCartQuantity(true);
        }
        xdaCommodityFrontService.setXdaCommodityInfo(appODTOList,appIDTO);
        return appODTOList;
    }

    /**
     * 处理系列品展示信息
     * @param categoryCommodityODTO
     * @param serialCommodityInfoList
     * @return
     */
    public void processXdaSerialCommodityInfo(XdaCategoryCommodityODTO categoryCommodityODTO,List<XdaCommodityAppODTO> serialCommodityInfoList){
        if(CollectionUtils.isEmpty(serialCommodityInfoList)){
            return;
        }
        List<XdaCommodityAppODTO> sortList = serialCommodityInfoList.stream().sorted(Comparator.comparing(XdaCommodityAppODTO::getSortPrice)).collect(Collectors.toList());
        Set<String> specList = new LinkedHashSet<>();
        Set<String> priceList = new LinkedHashSet<>();
        List<XdaSerialCommodityDetailODTO> serialDetailList = new ArrayList<>();
        for(int i=0;i<sortList.size();i++){
            specList.add(sortList.get(i).getCommoditySpec());
            if(i==0 || i==sortList.size()-1){
                priceList.add(NumberUtil.format_2_roundHalfUp(sortList.get(i).getSortPrice()));
            }
            serialDetailList.add(XdaSerialCommodityDetailODTO.convert(sortList.get(i)));
        }
        categoryCommodityODTO.setSerialCommoditySpec(String.join("|",specList));
        categoryCommodityODTO.setSerialCommodityPrice(String.join("-",priceList));
        categoryCommodityODTO.setSerialCommodityDetailList(serialDetailList);
    }

    private <T extends XdaCommodityAppODTO> void processSort(List<T> commodityODTOList,XdaCategoryBaseIDTO appIDTO,Boolean defaultSort){
        Collections.sort(commodityODTOList, new Comparator<XdaCommodityAppODTO>() {
            @Override
            public int compare(XdaCommodityAppODTO o1, XdaCommodityAppODTO o2) {
                if(appIDTO.getSaleSort()==1){
                    return o2.getSaleQuantity().compareTo(o1.getSaleQuantity());
                }
                if(appIDTO.getPriceSort()!=0){
                    return appIDTO.getPriceSort()==1?o1.getSortPrice().compareTo(o2.getSortPrice())
                            :o2.getSortPrice().compareTo(o1.getSortPrice());
                }
                if(defaultSort){
                    return o1.getSortNum().compareTo(o2.getSortNum());
                }
                return 0;
            }
        });
    }

    /**
     * 搜索商品
     * @param searchIDTO
     * @return
     */
    public PageInfo<XdaCommodityAppODTO> queryXdaCommoditySearch(XdaSearchAppIDTO searchIDTO) {
        PageHelper.startPage(searchIDTO.getPageNo(), searchIDTO.getPageSize());
        List<XdaCommodityAppODTO> appODTOList = this.queryXdaCommodityList(searchIDTO,null,PicSizeEnums.PIC_346x346);
        if(CollectionUtils.isEmpty(appODTOList)){
            return null;
        }

        return new PageInfo<>(appODTOList);
    }

     private Map<Long,List<Long>> getRecommendMap(List<Long> firstIdList,Long storeId){
         Map<Long,List<Long>> recommendMap = new HashMap<>();
         List<XdaReCommendAppODTO> recommendList = xdaCategoryFrontMapper.queryXdaReCommendFront(firstIdList,storeId);
         if (CollectionUtils.isNotEmpty(recommendList)){
              recommendList.stream().collect(groupingBy(XdaReCommendAppODTO::getFirstCateId)).forEach((k,v)->{
                  recommendMap.put(k,v.stream().map(XdaReCommendAppODTO::getCommodityId).collect(toList()));
             });
         }
         return recommendMap;
     }

}
