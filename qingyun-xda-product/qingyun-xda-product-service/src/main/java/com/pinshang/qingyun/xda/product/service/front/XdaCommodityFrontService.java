package com.pinshang.qingyun.xda.product.service.front;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.pic.PicCutTypeEnum;
import com.pinshang.qingyun.base.enums.pic.PicSizeEnums;
import com.pinshang.qingyun.box.utils.*;
import com.pinshang.qingyun.common.dto.ImageLibraryODTO;
import com.pinshang.qingyun.common.service.ImageLibraryClient;
import com.pinshang.qingyun.price.dto.storePromotion.StorePromotionCommodityPriceODTO;
import com.pinshang.qingyun.price.dto.storePromotion.StorePromotionCommodityPriceSearchIDTO;
import com.pinshang.qingyun.price.service.StorePromotionClient;
import com.pinshang.qingyun.store.service.StoreManageClient;
import com.pinshang.qingyun.xda.product.dto.XdaCommodityAppStatusODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextPicListIDTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextTagInfoListIDTO;
import com.pinshang.qingyun.xda.product.dto.front.*;
import com.pinshang.qingyun.xda.product.enums.XdaRedisEnums;
import com.pinshang.qingyun.xda.product.mapper.*;
import com.pinshang.qingyun.xda.product.model.StoreDuration;
import com.pinshang.qingyun.xda.product.model.XdaCommodityCollect;
import com.pinshang.qingyun.xda.product.model.XdaCommodityTextPic;
import com.pinshang.qingyun.xda.product.service.StoreService;
import com.pinshang.qingyun.xda.product.service.front.v2.RedisDB;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.misc.RedissonPromise;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;


@Service
@Slf4j
public class XdaCommodityFrontService {

    @Autowired
    private XdaCommodityFrontMapper xdaCommodityFrontMapper;
    @Autowired
    private XdaCommodityTextMapper xdaCommodityTextMapper;
    @Autowired
    private XdaCommodityTextPicMapper xdaCommodityTextPicMapper;
    @Autowired
    private XdaCommodityCollectMapper xdaCommodityCollectMapper;
    @Autowired
    private XdaSerialCommodityMapper xdaSerialCommodityMapper;

    @Autowired
    private ImageLibraryClient imageLibraryClient;
    @Value("${pinshang.img-xd-server-url}")
    private String imgServerUrl;

    @Autowired
    private RedisDB redisDB;

    @Autowired
    private StorePromotionClient storePromotionClient;
    @Autowired
    private StoreService storeService;


    /**
     * 鲜达商品详情
     * @param detailAppIDTO
     * @return
     */
    public XdaCommodityDetailAppODTO queryXdaCommodityDetailForApp(XdaCommodityDetailAppIDTO detailAppIDTO){
        if(detailAppIDTO==null){
            log.error("查询商品详情参数空异常");
            return null;
        }
        Long commodityId = detailAppIDTO.getCommodityId();
        if(commodityId == null || detailAppIDTO.getStoreId()==null || detailAppIDTO.getOrderTime()==null){
            log.error("查询商品详情参数空异常");
            return null;
        }
        XdaCommodityAppIDTO appIDTO = XdaCommodityAppIDTO.builder().orderTime(detailAppIDTO.getOrderTime())
                .storeId(detailAppIDTO.getStoreId()).commodityIdList(Collections.singletonList(commodityId))
                .defaultImageSize(PicSizeEnums.PIC_750x750).needCartQuantity(true).build();
        List<XdaCommodityAppODTO> appODTOList = this.queryXdaCommodityListForApp(appIDTO);
        if(CollectionUtils.isEmpty(appODTOList)){
            return null;
        }
        XdaCommodityAppODTO appODTO = appODTOList.get(0);
        XdaCommodityDetailAppODTO detailAppODTO = BeanCloneUtils.copyTo(appODTO, XdaCommodityDetailAppODTO.class);
        this.processSerialCommodityDetail(detailAppODTO,detailAppIDTO);

        //获取收藏状态
        this.queryXdaCommodityCollect(detailAppODTO,commodityId,detailAppIDTO.getStoreId());

        //设置banner列表和长图
        this.setXdaCommodityImage(detailAppODTO,commodityId);
        //设置最快送达日期
        if(appODTO.getDeliveryTimeODTO()!=null){
            detailAppODTO.setDistributionTipList(appODTO.getDeliveryTimeODTO().getDistributionTipList());
        }
        return detailAppODTO;
    }

    /**
     * 鲜达商品列表
     * @param appIDTO
     * @return
     */
    public List<XdaCommodityAppODTO> queryXdaCommodityListForApp(XdaCommodityAppIDTO appIDTO){
        if(appIDTO.getStoreId()==null || appIDTO.getOrderTime()==null){
            return Collections.EMPTY_LIST;
        }

        Boolean isPfsStore = storeService.isPfsStore(appIDTO.getStoreId());
        appIDTO.setIsPfsStore(isPfsStore);
        List<XdaCommodityAppODTO> appODTOList = xdaCommodityFrontMapper.queryXdaCommodityListForApp(appIDTO);
        if (CollectionUtils.isEmpty(appODTOList)) {
            log.error("APP未获取到商品信息，请确认B端销售商品范围。查询参数：storeId={},commodityId={}",appIDTO.getStoreId(),appIDTO.getCommodityIdList());
            return Collections.EMPTY_LIST;
        }
        this.setXdaCommodityInfo(appODTOList,appIDTO);
        return appODTOList;
    }

    /**
     * set商品信息
     * @param appODTOList
     * @param appIDTO
     */
    public void setXdaCommodityInfo(List<XdaCommodityAppODTO> appODTOList,XdaCommodityAppIDTO appIDTO){
        if(CollectionUtils.isEmpty(appODTOList)){
            return;
        }
        Long storeId = appIDTO.getStoreId();
        Date orderTime = appIDTO.getOrderTime();
        List<Long> commodityIdList = appODTOList.stream().map(XdaCommodityAppODTO::getCommodityId).collect(Collectors.toList());
        //特价
        Map<Long, BigDecimal> priceMap = null;
        if(appIDTO.getNeedSpecialPrice()){
            priceMap = this.queryXdaSpecialPrice(orderTime,storeId,new ArrayList<>(commodityIdList));
        }

        //促销
        Map<Long, List<XdaStorePromotionODTO>> promotionMap = null;
        if(appIDTO.getNeedPromotion()){
            promotionMap = this.queryXdaPromotion(orderTime,storeId,new ArrayList<>(commodityIdList));
        }

        //自定义标签
        Map<Long, List<CommodityTextTagInfoODTO>> tagMap = null;
        if(appIDTO.getNeedTag()){
            tagMap = this.queryXdaCommodityTextTag(new ArrayList<>(commodityIdList));

        }

        //是否可订货
        Map<Long,XdaCommodityDeliveryTimeODTO> deliveryTimeMap = null;
        if(appIDTO.getNeedCanOrder()){
            deliveryTimeMap = this.queryXdaCommodityDeliveryTime(orderTime,storeId,new ArrayList<>(commodityIdList));
        }

        //商品限量
        Map<Long, XdaCommodityLimitODTO> limitQuantityMap = null;
        if(appIDTO.getNeedLimit()){
            limitQuantityMap = this.queryXdaCommodityLimit(orderTime,new ArrayList<>(commodityIdList));
        }

        //购物车数量
        Map<Long,BigDecimal> shopCartQuantityMap = null;
        if(appIDTO.getNeedCartQuantity()){
            shopCartQuantityMap = this.queryShopCartQuantityMap(appIDTO.getStoreId(),new ArrayList<>(commodityIdList));
        }

        for(XdaCommodityAppODTO appODTO : appODTOList){
            Long commodityId = appODTO.getCommodityId();
            if(appIDTO.getNeedDefaultImage() && StringUtils.isNotEmpty(appODTO.getImageUrl())){
                appODTO.setImageUrl(imgServerUrl+ ImageUtils.getXdImgUrlV2(appODTO.getImageUrl(),appIDTO.getDefaultImageSize()==null?null:appIDTO.getDefaultImageSize().getSize()));
            }
            if(priceMap!=null && priceMap.get(commodityId)!=null){
                BigDecimal specialPrice = priceMap.get(commodityId);
                if(appODTO.getCommodityPrice()!=null && appODTO.getCommodityPrice().compareTo(specialPrice)>0){
                    appODTO.setIsSpecialPrice(1);
                    appODTO.setSpecialPrice(priceMap.get(commodityId));
                }
            }
            if(promotionMap!=null && CollectionUtils.isNotEmpty(promotionMap.get(commodityId))){
                appODTO.setIsPromotion(1);
                appODTO.setPromotionList(promotionMap.get(commodityId));
            }
            if(tagMap!=null && CollectionUtils.isNotEmpty(tagMap.get(commodityId))){
                appODTO.setTagList(tagMap.get(commodityId));
            }
            if(deliveryTimeMap!=null && deliveryTimeMap.get(commodityId)!=null){
                XdaCommodityDeliveryTimeODTO deliveryTimeODTO = deliveryTimeMap.get(commodityId);
                appODTO.setIsCanOrder(deliveryTimeODTO.getIsCanOrder());
                appODTO.setDeliveryTimeODTO(deliveryTimeODTO);
                List<Date> xdaDeliveryTimeList = deliveryTimeODTO.getDeliveryDateList();
                if(CollectionUtils.isNotEmpty(xdaDeliveryTimeList)){
                    appODTO.setBeginDeliveryTime(xdaDeliveryTimeList.get(0));
                    appODTO.setEndDeliveryTime(xdaDeliveryTimeList.get(xdaDeliveryTimeList.size()-1));
                }
            }
            if(limitQuantityMap!=null && limitQuantityMap.get(commodityId)!=null){
                XdaCommodityLimitODTO xdaCommodityLimitODTO = limitQuantityMap.get(commodityId);
                appODTO.setIsLimit(1);
                appODTO.setLimitNumber(xdaCommodityLimitODTO.getLimitNumber());
                appODTO.setLimitStartTime(xdaCommodityLimitODTO.getBeginTime());
                appODTO.setLimitEndTime(xdaCommodityLimitODTO.getEndTime());
            }
            if(shopCartQuantityMap!=null && shopCartQuantityMap.get(commodityId)!=null){
                appODTO.setShoppingCartQuantity(shopCartQuantityMap.get(commodityId));
            }
        }
    }

    private void queryXdaCommodityCollect(XdaCommodityDetailAppODTO detailAppODTO,Long commodityId,Long storeId){
        Example example = new Example(XdaCommodityCollect.class);
        example.createCriteria().andEqualTo("commodityId",commodityId).andEqualTo("storeId",storeId);
        List<XdaCommodityCollect> collectList = xdaCommodityCollectMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(collectList)){
            detailAppODTO.setIsCollect(0);
        }else{
            detailAppODTO.setIsCollect(1);
        }
    }

    //设置商品详情页：banner、长图
    private void setXdaCommodityImage(XdaCommodityDetailAppODTO detailAppODTO,Long commodityId){
        List<XdaCommodityTextPic> picList = xdaCommodityTextPicMapper.selectCommodityTextPicList(new SelectCommodityTextPicListIDTO(commodityId));
        if(CollectionUtils.isEmpty(picList)){
            return;
        }
        PicSizeEnums picSizeEnums = PicSizeEnums.PIC_750x750;
        picList.stream().collect(Collectors.groupingBy(XdaCommodityTextPic::getPicType)).forEach((picK,picV)->{
            if(CollectionUtils.isEmpty(picV)){
                return;
            }
            if(picK.intValue()== XdaCommodityTextPic.PicTypeEnums.PIC.getCode() ){
                List<String> imageUrlList = picV.stream().map(pic -> imgServerUrl +(ImageUtils.getXdImgUrlV2(pic.getPicUrl(),picSizeEnums.getSize()))).collect(Collectors.toList());
                detailAppODTO.setImageUrlList(imageUrlList);
            }
            if(picK.intValue()== XdaCommodityTextPic.PicTypeEnums.LONG_PIC.getCode() ){
                detailAppODTO.setLongPicList(this.querySplitLongPicUrlList(picV.get(0).getPicUrl()));
            }
        });
    }
    //查询长图
    private List<XdaCommodityLongPicODTO> querySplitLongPicUrlList(String longPic){
        //<长图原始路径，长图切割后的list>
        List<ImageLibraryODTO> imageODTOList = imageLibraryClient.findSingleImgAnyCondition(longPic, PicCutTypeEnum.CUT_BY_HEIGHT, PicSizeEnums.PIC_750);
        if(CollectionUtils.isNotEmpty(imageODTOList)) {
            return imageODTOList.stream().map(XdaCommodityLongPicODTO::convert).collect(Collectors.toList());
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 查询特价
     * 取客户送货日期生效的特价方案并集，相同商品多个特价取最小值
     * @param orderTime：送货日期，必传
     * @param storeId：客户ID，必传
     * @param commodityIdList：商品ID集合，不必传
     * @return
     */
    public Map<Long,BigDecimal> queryXdaSpecialPrice(Date orderTime,Long storeId,List<Long> commodityIdList){
        if(SpringUtil.isEmpty(commodityIdList)){
            List<XdaStoreSpecialPriceODTO> specialPriceODTOList = xdaCommodityFrontMapper.queryXdaStoreSpecialPriceList(orderTime,storeId,commodityIdList);
            Map<Long,BigDecimal> map = new HashMap<>();
            if(CollectionUtils.isNotEmpty(specialPriceODTOList)){
                specialPriceODTOList.stream().collect(groupingBy(XdaStoreSpecialPriceODTO::getCommodityId)).forEach((k,v)->{
                    if(CollectionUtils.isEmpty(v)){
                        return;
                    }
                    XdaStoreSpecialPriceODTO minPriceODTO = v.size()==1?v.get(0):v.stream().min(Comparator.comparing(XdaStoreSpecialPriceODTO::getPrice)).get();
                    map.put(k,minPriceODTO.getPrice());
                });
                return map;
            }
            return new HashMap<>();
        }
        Map<Long, BigDecimal> result = new HashMap<>();
        // 查询B端特价
        StorePromotionCommodityPriceSearchIDTO priceSearchIDTO = new StorePromotionCommodityPriceSearchIDTO();
        priceSearchIDTO.setStoreId(storeId);
        priceSearchIDTO.setOrderTime(orderTime);
        priceSearchIDTO.setCommodityIdList(commodityIdList);
        priceSearchIDTO.setNeedAvailableLimit(1);
        Map<Long, StorePromotionCommodityPriceODTO> commodityPriceMap = storePromotionClient.getStorePromotionCommodityMapByParams(priceSearchIDTO);
        if (MapUtils.isNotEmpty(commodityPriceMap)) {
            commodityPriceMap.forEach((key, value) -> result.put(key, value.getPrice()));
        }
        return result;
    }

    /**
     * 查询标签
     * @param commodityIdList
     * @return
     */
    public Map<Long, List<CommodityTextTagInfoODTO>> queryXdaCommodityTextTag(List<Long> commodityIdList){
        Map<Long, List<CommodityTextTagInfoODTO>> tagMap = new HashMap<>();
        Map<Long, Object> commodityTextTagRedisMap;

        int size = commodityIdList.size();
        if(SpringUtil.isNotEmpty((commodityTextTagRedisMap = redisDB.getRedisXdaSpecialPrice(XdaRedisEnums.COMMODITYTAG,null,null,commodityIdList)))){
            commodityTextTagRedisMap.forEach((key,value)->{
                RedissonPromise redissonPromise = (RedissonPromise)value ;
                try {
                    List<CommodityTextTagInfoODTO> o = (List<CommodityTextTagInfoODTO>)redissonPromise.get();
                    tagMap.put(key, o);

                } catch (Exception ex) {
                    log.error("鲜达获取基本信息异常，异常堆栈：{}",ex);
                }
            });
            commodityIdList.removeIf(item-> commodityTextTagRedisMap.containsKey(item));
        }
        if(size != 0 && commodityIdList.isEmpty()){
            return tagMap;
        }

        List<CommodityTextTagInfoODTO> tagList = xdaCommodityTextMapper.selectCommodityTextTagInfoList(new SelectCommodityTextTagInfoListIDTO(commodityIdList));

        Map<Long, List<CommodityTextTagInfoODTO>> daMap = tagList.stream().collect(groupingBy(CommodityTextTagInfoODTO::getCommodityId));
        Map<Long, Object> redisMap = new HashMap<>();
        daMap.forEach((key,value)->{
            tagMap.put(key,value);
            redisMap.put(key,value);
        });
        if(SpringUtil.isNotEmpty(redisMap)){
            redisDB.setRedisXdaSpecialPrice(XdaRedisEnums.COMMODITYTAG,null,null,redisMap);
        }
        return tagMap;
    }
    /**
     * 查询促销
     * @param orderTime
     * @param storeId
     * @param commodityIdList
     * @return
     */
    public Map<Long,List<XdaStorePromotionODTO>> queryXdaPromotion(Date orderTime,Long storeId,List<Long> commodityIdList){
        Map<Long,List<XdaStorePromotionODTO>> xdaStorePromotionODTOMap = new HashMap<>();
        Map<Long, Object> commoditySpecialPriceRedisMap;
        int size = commodityIdList.size();
        if(SpringUtil.isNotEmpty((commoditySpecialPriceRedisMap = redisDB.getRedisXdaSpecialPrice(XdaRedisEnums.PROMOTION,orderTime,storeId,commodityIdList)))){
            commoditySpecialPriceRedisMap.forEach((key,value)->{
                RedissonPromise redissonPromise = (RedissonPromise)value ;
                try {
                    List<XdaStorePromotionODTO> o = (List<XdaStorePromotionODTO>)redissonPromise.get();
                    xdaStorePromotionODTOMap.put(key, o);

                } catch (Exception ex) {
                    log.error("鲜达获取基本信息异常，异常堆栈：{}",ex);
                }
            });
            commodityIdList.removeIf(item-> xdaStorePromotionODTOMap.containsKey(item));
        }
        if(size != 0 && commodityIdList.isEmpty()){
            return xdaStorePromotionODTOMap;
        }

        List<XdaStorePromotionODTO> promotionODTOList = xdaCommodityFrontMapper.queryXdaStorePromotionList(orderTime,storeId,commodityIdList);
        if(CollectionUtils.isEmpty(promotionODTOList)){
            return xdaStorePromotionODTOMap;
        }
        Map<Long,List<XdaStorePromotionODTO>> dbMap = promotionODTOList.stream().collect(Collectors.groupingBy(XdaStorePromotionODTO::getCommodityId,
                LinkedHashMap::new,Collectors.toList()));
        Map<Long,Object> redisMap = new HashMap<>();
        dbMap.forEach((key,value)->{
            redisMap.put(key,value);
            xdaStorePromotionODTOMap.put(key,value);
        });
        if(size != 0 && SpringUtil.isNotEmpty(redisMap)){
            redisDB.setRedisXdaSpecialPrice(XdaRedisEnums.PROMOTION,orderTime,storeId,redisMap);
        }
        return xdaStorePromotionODTOMap;
    }

    /**
     * 查询商品库存限量
     * 一个商品只能创建一条限量信息，商品ID限量表唯一
     * @param orderTime
     * @param commodityIdList
     * @return
     */
    public Map<Long,XdaCommodityLimitODTO> queryXdaCommodityLimit(Date orderTime,List<Long> commodityIdList){
        Map<Long, Object> deliveryTimeMap = new HashMap<>();
        Map<Long, XdaCommodityLimitODTO> xdaCommodityLimitODTOMap = new HashMap<>();

        int size = commodityIdList.size();
        if(SpringUtil.isNotEmpty((deliveryTimeMap = redisDB.getRedisXdaSpecialPrice(XdaRedisEnums.COMMODITYLIMIT,orderTime,null,commodityIdList)))){
            deliveryTimeMap.forEach((key,value)->{
                RedissonPromise redissonPromise = (RedissonPromise)value ;
                try {
                    XdaCommodityLimitODTO o = (XdaCommodityLimitODTO)redissonPromise.get();
                    xdaCommodityLimitODTOMap.put(key, o);

                } catch (Exception ex) {
                    log.error("鲜达获取基本信息异常，异常堆栈：{}",ex);
                }
            });
            commodityIdList.removeIf(xdaCommodityLimitODTOMap::containsKey);
        }

        if(size != 0 && commodityIdList.isEmpty()){
            return xdaCommodityLimitODTOMap;
        }

        List<XdaCommodityLimitODTO> commodityLimitList = xdaCommodityFrontMapper.queryXdaCommodityLimitList(orderTime,commodityIdList);
        if(CollectionUtils.isEmpty(commodityLimitList)){
            return xdaCommodityLimitODTOMap;
        }
        Map<Long, Object> redisMap = new HashMap<>();
        commodityLimitList.stream().collect(groupingBy(XdaCommodityLimitODTO::getCommodityId)).forEach((k,v)->{
            if(CollectionUtils.isEmpty(v)){
                return;
            }
            xdaCommodityLimitODTOMap.put(k,v.get(0));
            redisMap.put(k,v.get(0));
        });
        if(size != 0 && SpringUtil.isNotEmpty(xdaCommodityLimitODTOMap)){
            redisDB.setRedisXdaSpecialPrice(XdaRedisEnums.COMMODITYLIMIT,orderTime,null,redisMap);
        }
        return xdaCommodityLimitODTOMap;
    }

    /**
     * 查询商品送货日期范围，验证是否可送货
     * @param orderTime
     * @param storeId
     * @param commodityIdList
     *
     * 注意：
     *   鲜达APP搜素框、优惠券去使用、分类页商品根据ES里面存的商品送货日期进行判断，
     *   此处只是详情页、购物车、首页H5等页面调用，去掉缓存。直接查询mysql
     */
    public Map<Long,XdaCommodityDeliveryTimeODTO> queryXdaCommodityDeliveryTime(Date orderTime,Long storeId,List<Long> commodityIdList){
        //<Long, Object> deliveryTimeMap = new HashMap<>();
        Map<Long, XdaCommodityDeliveryTimeODTO> deliveryTimeODTOMap = new HashMap<>();

        /*int size = commodityIdList.size();
        if(SpringUtil.isNotEmpty((deliveryTimeMap = redisDB.getRedisXdaSpecialPrice(XdaRedisEnums.DELIVERYTIME,orderTime,storeId,commodityIdList)))){
            deliveryTimeMap.forEach((key,value)->{
                RedissonPromise redissonPromise = (RedissonPromise)value ;
                try {
                    XdaCommodityDeliveryTimeODTO o = (XdaCommodityDeliveryTimeODTO)redissonPromise.get();
                    deliveryTimeODTOMap.put(key, o);

                } catch (Exception ex) {
                    log.error("鲜达获取基本信息异常，异常堆栈：{}",ex);
                }
            });
            commodityIdList.removeIf(deliveryTimeMap::containsKey);
        }

        if(size != 0 && commodityIdList.isEmpty()){
            return deliveryTimeODTOMap;
        }*/

        Boolean isPfsStore = storeService.isPfsStore(storeId);
        List<XdaCommodityDeliveryTimeODTO> deliveryTimeList = xdaCommodityFrontMapper.queryXdaCommodityDeliveryTime(commodityIdList, isPfsStore);
        if(CollectionUtils.isEmpty(deliveryTimeList)){
            log.error("客户Id" + storeId +"鲜达上架商品没有设置送货日期范围");
            return Collections.EMPTY_MAP;
        }
        //客户截单时间
        StoreDuration sd = xdaCommodityFrontMapper.queryStoreDuration(storeId);
        if(sd==null || StringUtils.isEmpty(sd.getBeginTime()) || StringUtils.isEmpty(sd.getEndTime())){
            log.error("客户Id" + storeId +"鲜达客户未设置下单时间范围");
            return Collections.EMPTY_MAP;
        }
        //Map<Long, Object> redisMap = new HashMap<>();

        deliveryTimeList.stream().collect(groupingBy(XdaCommodityDeliveryTimeODTO::getCommodityId)).forEach((k,v)->{
            if(CollectionUtils.isEmpty(v) || StringUtils.isEmpty(v.get(0).getDeliveryDateRangeCode())){
                return;
            }
            try {
                XdaCommodityDeliveryTimeODTO deliveryTimeODTO = v.get(0);
                List<Date> dateList = deliveryTimeODTO.getDeliveryDateList();
                deliveryTimeODTO.setIsCanOrder(false);
                if(dateList.contains(orderTime)){
                    Boolean flag1 = orderTime.equals(dateList.get(0)) && DateTimeUtil.compareNewDate(sd.getBeginTime(), sd.getEndTime());
                    Boolean flag2 = orderTime.compareTo(dateList.get(0))>0 && DateTimeUtil.compareTime(sd.getBeginTime(),DateTimeUtil.getHourAndMinute(0));
                    if( flag1 || flag2){
                        deliveryTimeODTO.setIsCanOrder(Boolean.TRUE);
                    }
                }
                deliveryTimeODTO.setDistributionTipList(deliveryTimeODTO.processDistributionTips(dateList,sd.getBeginTime(),sd.getEndTime()));

                // 商品详情页配送提示文案优化，商品的送货日期范围包含T+0时，文案统一显示为“若送货日期选择今日，仅支持自提
                Date nowDate = DateUtil.getDatePart(new Date());
                if(dateList.contains(nowDate)) {
                    List<String> distributionTipList = new ArrayList<>();
                    distributionTipList.add("若送货日期选择今日，仅支持自提。");
                    deliveryTimeODTO.setDistributionTipList(distributionTipList);
                }

                deliveryTimeODTOMap.put(k,deliveryTimeODTO);
                //redisMap.put(k,deliveryTimeODTO);
            }catch (Exception e){
                log.error("获取商品可送货日期范围异常",e);
            }
        });
        /*if(size != 0 && SpringUtil.isNotEmpty(redisMap)){
            redisDB.setRedisXdaSpecialPrice(XdaRedisEnums.DELIVERYTIME,orderTime,storeId,redisMap);
        }*/
        return deliveryTimeODTOMap;
    }

    /**
     * 收藏/取消收藏
     * @param commodityId
     * @param collectStatus
     * @param storeId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Integer updateXdaCommodityCollect(Long commodityId,Integer collectStatus,Long storeId){
        if(commodityId==null || collectStatus==null || storeId==null){
            QYAssert.isFalse("收藏参数有误");
        }
        Example example = new Example(XdaCommodityCollect.class);
        example.createCriteria().andEqualTo("commodityId",commodityId).andEqualTo("storeId",storeId);
        if(collectStatus.intValue()==1){//收藏
            List<XdaCommodityCollect> collectList = xdaCommodityCollectMapper.selectByExample(example);
            if(CollectionUtils.isEmpty(collectList)){
                XdaCommodityCollect cookbookCollect = new XdaCommodityCollect(commodityId,storeId);
                xdaCommodityCollectMapper.insertSelective(cookbookCollect);
            }
        }else{//取消收藏
            xdaCommodityCollectMapper.deleteByExample(example);
        }
        return collectStatus;
    }

    private void processSerialCommodityDetail(XdaCommodityDetailAppODTO detailAppODTO,XdaCommodityDetailAppIDTO detailAppIDTO){
        if(detailAppODTO.getXdaSecondCategoryId()==null){
            return;
        }
        List<XdaSerialCommodityODTO> serialCommodityList = xdaSerialCommodityMapper.querySerialCommodityListFront(Collections.singletonList(detailAppODTO.getCommodityId()),
                detailAppODTO.getXdaSecondCategoryId());
        if(CollectionUtils.isEmpty(serialCommodityList) || serialCommodityList.stream().noneMatch(item->item.getIsMain()==1)
            || serialCommodityList.size()<2){
            XdaSerialCommodityDetailODTO serialDetail = XdaSerialCommodityDetailODTO.convert(detailAppODTO);
            serialDetail.setIsCurrentCommodity(1);
            detailAppODTO.setSerialCommodityDetailList(Collections.singletonList(serialDetail));
            return;
        }
        List<XdaCommodityAppODTO> appODTOList = new ArrayList<>();
        appODTOList.add(detailAppODTO);
        List<Long> serialCommodityIdList = serialCommodityList.stream()
                .filter(item->!item.getCommodityId().equals(detailAppODTO.getCommodityId()))
                .map(XdaSerialCommodityODTO::getCommodityId).collect(Collectors.toList());
        XdaCommodityAppIDTO appIDTO = XdaCommodityAppIDTO.builder().orderTime(detailAppIDTO.getOrderTime())
                .storeId(detailAppIDTO.getStoreId()).commodityIdList(serialCommodityIdList)
                .needDefaultImage(false).build();
        List<XdaCommodityAppODTO> serialList = this.queryXdaCommodityListForApp(appIDTO);
        if(CollectionUtils.isNotEmpty(serialList)){
            appODTOList.addAll(serialList);
            detailAppODTO.setIsSerial(1);
        }
        List<XdaSerialCommodityDetailODTO> serialCommodityODTOList = appODTOList.stream().map(XdaSerialCommodityDetailODTO::convert)
                .sorted(Comparator.comparing(XdaSerialCommodityDetailODTO::getSortPrice)).collect(Collectors.toList());
        serialCommodityODTOList.forEach(item-> {
            if (item.getCommodityId().equals(detailAppODTO.getCommodityId())) {
                item.setIsCurrentCommodity(1);
            }else{
                item.setIsCurrentCommodity(0);
            }
        });
        detailAppODTO.setSerialCommodityDetailList(serialCommodityODTOList);
    }

    /**
     * 查询商品已加入购物车数量
     * @param storeId
     * @param commodityIdList
     * @return
     */
    public Map<Long,BigDecimal> queryShopCartQuantityMap(Long storeId,List<Long> commodityIdList){
        List<XdaShoppingCartODTO> shoppingCartODTOList = xdaCommodityFrontMapper.queryXdaShoppingCartQuantity(storeId,commodityIdList);
        if(CollectionUtils.isEmpty(shoppingCartODTOList)){
            return Collections.EMPTY_MAP;
        }
        Map<Long, BigDecimal> quantityMap = new HashMap<>();
        shoppingCartODTOList.stream().collect(groupingBy(XdaShoppingCartODTO::getCommodityId)).forEach((k,v)->{
            if(CollectionUtils.isEmpty(v)){
                return;
            }
            quantityMap.put(k,v.get(0).getQuantity());
        });
        return quantityMap;
    }

    public Map<Long, Integer> queryXdaCommodityAppStatus(List<Long> commodityIdList){
        List<XdaCommodityAppStatusODTO> commodityAppStatus = xdaCommodityFrontMapper.queryXdaCommodityAppStatus(commodityIdList);
        if(CollectionUtils.isEmpty(commodityAppStatus)){
            return Collections.EMPTY_MAP;
        }
        Map<Long, Integer> appStatusMap = new HashMap<>();
        commodityAppStatus.stream().collect(groupingBy(XdaCommodityAppStatusODTO::getCommodityId)).forEach((k,v)->{
            if(CollectionUtils.isEmpty(v)){
                return;
            }
            appStatusMap.put(k,v.get(0).getAppStatus());
        });
        return appStatusMap;
    }

}
