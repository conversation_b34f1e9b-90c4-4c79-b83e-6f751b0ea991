package com.pinshang.qingyun.xda.product.service.front.shoppingCart;

import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDeliveryTimeODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityLimitODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaStorePromotionODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3ODTO;
import com.pinshang.qingyun.xda.product.model.specialsCommoditySet.XdaSpecialsCommoditySet;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class ShoppingCartComputer {
    /** 基础数据 */
    private List<Long> commodityIdList = new ArrayList<>();
    // 特价
    private final Map<Long, BigDecimal> specialPriceMap = new HashMap<>();
    // 促销
    private final Map<Long, List<XdaStorePromotionODTO>> xdaPromotionMap = new HashMap<>();
    // 标签
    private final Map<Long, List<CommodityTextTagInfoODTO>> commodityTextTagInfo = new HashMap<>();
    // 是否可订货
    private final Map<Long, XdaCommodityDeliveryTimeODTO> commodityDeliveryTime = new HashMap<>();
    // 商品限量
    private final Map<Long, XdaCommodityLimitODTO> xdaCommodityLimit = new HashMap<>();
    // 是否凑整
    private final List<Long> commodityFreezeGroup = new ArrayList<>();
    // 购物车
    private final Map<Long,BigDecimal> shoppingCartQuantity = new HashMap<>();

    private BigDecimal orderTargetToDay = null;

    private final Map<Long, XdaSpecialsCommoditySet> xdaSpecialsCommoditySetMap = new HashMap();

    /** 返回数据 */
    private final List<XdaShoppingCartV3ODTO> xdaCommodityAppV2ODTOList = new ArrayList<>();

    public void init(List<XdaShoppingCartV3ODTO> list) {
        xdaCommodityAppV2ODTOList.addAll(list);
        commodityIdList.clear();
    }

    public List<Long> getCommodityIdList() {
        if(SpringUtil.isNotEmpty(commodityIdList)){
            return commodityIdList;
        }
        commodityIdList = xdaCommodityAppV2ODTOList.stream().map(XdaShoppingCartV3ODTO::getCommodityId).collect(Collectors.toList());
        return commodityIdList;
    }
    /**
     * 返回数据
     * @return 商品相关信息
     */
    public List<XdaShoppingCartV3ODTO> assemble() {
        if(CollectionUtils.isEmpty(xdaCommodityAppV2ODTOList)){
            return Collections.EMPTY_LIST;
        }
        // 数据组装
        xdaCommodityAppV2ODTOList.forEach(item->{
            Long commodityId = item.getCommodityId();
            /* 特价相关数据 */
            if(SpringUtil.isNotEmpty(specialPriceMap) && specialPriceMap.containsKey(commodityId)){
                BigDecimal specialPrice = specialPriceMap.get(commodityId);
                // 判断原价是否大于特价,如果特价大于原价不显示特价标签和价格
                if(item.getCommodityPrice()!=null && item.getCommodityPrice().compareTo(specialPrice)>0){
                    item.setIsSpecialPrice(1);
                    item.setSpecialPrice(specialPrice);
                }
            }

            /* 是否可订货数据组装*/
            if(SpringUtil.isNotEmpty(commodityDeliveryTime) && commodityDeliveryTime.containsKey(commodityId)){
                XdaCommodityDeliveryTimeODTO deliveryTimeODTO = commodityDeliveryTime.get(commodityId);
                item.setIsCanOrder(deliveryTimeODTO.getIsCanOrder());
                List<Date> xdaDeliveryTimeList = deliveryTimeODTO.getDeliveryDateList();
                if(CollectionUtils.isNotEmpty(xdaDeliveryTimeList)){
                    item.setBeginDeliveryTime(xdaDeliveryTimeList.get(0));
                    item.setEndDeliveryTime(xdaDeliveryTimeList.get(xdaDeliveryTimeList.size()-1));
                }
            }
            /*商品限量相关数据*/
            if(SpringUtil.isNotEmpty(xdaCommodityLimit) && xdaCommodityLimit.containsKey(commodityId)){
                XdaCommodityLimitODTO xdaCommodityLimitODTO = xdaCommodityLimit.get(commodityId);
                item.setIsLimit(1);
                item.setLimitNumber(xdaCommodityLimitODTO.getLimitNumber());
                item.setLimitStartTime(xdaCommodityLimitODTO.getBeginTime());
                item.setLimitEndTime(xdaCommodityLimitODTO.getEndTime());
            }

            /*是否凑整相关数据*/
            item.setIsFreezeRounding(0);
            if(SpringUtil.isNotEmpty(commodityFreezeGroup) && commodityFreezeGroup.contains(commodityId)){
                item.setIsFreezeRounding(1);
                item.setIsFreezeRoundingMultiple(30);
            }

            /*特惠处理 */
            if(null != orderTargetToDay && xdaSpecialsCommoditySetMap.containsKey(commodityId)){
                XdaSpecialsCommoditySet xdaSpecialsCommoditySet = xdaSpecialsCommoditySetMap.get(commodityId);
                item.setIsThPrice(1);
                item.setThFullPrice(orderTargetToDay);
                item.setThPrice(xdaSpecialsCommoditySet.getCommoditySpecialsPrice());
                item.setThLimitNumber(new BigDecimal(xdaSpecialsCommoditySet.getCommodityLimit()));
            }
        });
        return xdaCommodityAppV2ODTOList;
    }

    public void setSpecialPriceMap(Map<Long, BigDecimal> map) {
        specialPriceMap.putAll(map);
    }

    public void setXdaPromotionMap(Map<Long, List<XdaStorePromotionODTO>> map) {
        xdaPromotionMap.putAll(map);
    }

    public void setCommodityTextTagInfo(Map<Long, List<CommodityTextTagInfoODTO>> map) {
        commodityTextTagInfo.putAll(map);
    }

    public void setCommodityDeliveryTime(Map<Long, XdaCommodityDeliveryTimeODTO> map){
        commodityDeliveryTime.putAll(map);
    }

    public void setXdaCommodityLimit(Map<Long, XdaCommodityLimitODTO> map){
        xdaCommodityLimit.putAll(map);
    }

    public void setShoppingCartQuantity(Map<Long, BigDecimal> map){
        shoppingCartQuantity.putAll(map);
    }

    public void setOrderTargetToDay(BigDecimal bigDecimal){
        orderTargetToDay = bigDecimal;
    }

    public void setXdaSpecialsCommoditySetMap(Map<Long, XdaSpecialsCommoditySet> map){
        xdaSpecialsCommoditySetMap.putAll(map);
    }

    public void setCommodityFreezeGroup(List<Long> commodityIdList){
        commodityFreezeGroup.addAll(commodityIdList);
    }
}
