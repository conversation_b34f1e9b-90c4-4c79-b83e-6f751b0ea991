package com.pinshang.qingyun.xda.product.service.front.shoppingCart;

import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3IDTO;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * 构建
 */
@Component
public abstract class XdaShoppingCartBuilder {
    /**
     * 组装商品基本信息
     */
    public abstract void buildXdaCommodityInfo(XdaShoppingCartV3IDTO xdaShoppingCartV3IDTO);

    /**
     * 组装商品特价信息
     * @see com.pinshang.qingyun.marketing.service.MtPromotionClient
     */
    public abstract void buildXdaSpecialPrice(Date orderTime, Long storeId);

    /**
     * 组装商品促销信息
     * @see com.pinshang.qingyun.marketing.service.MtPromotionClient
     */
    public abstract void buildXdaPromotion(Date orderTime, Long storeId);


    /**
     * 组装商品标签信息
     */
    public abstract void buildCommodityTextTagInfo();

    /**
     * 组装是否可订货标识
     */
    public abstract void buildXdaCommodityDeliveryTime(Date orderTime, Long storeId);

    /**
     * 组装商品限量信息
     */
    public abstract void buildXdaCommodityLimit(Date orderTime);

    /**
     * 获取购物车商品数量
     */
    public abstract void buildShopCartQuantity(Long storeId);

    /**
     * 组装特惠商品信息
     */
    public abstract void buildXdaTHGroup(Date orderTime,Long storeId);

    /**
     * 组装是否凑整信息
     */
    public abstract void buildIsFreezeRoundingGroup();

    /**
     * 组装完成
     */
    public abstract ShoppingCartComputer getComputer();
}
