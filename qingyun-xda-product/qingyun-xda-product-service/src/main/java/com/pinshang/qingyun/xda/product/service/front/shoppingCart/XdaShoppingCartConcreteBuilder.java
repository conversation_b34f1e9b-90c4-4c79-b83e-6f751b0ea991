package com.pinshang.qingyun.xda.product.service.front.shoppingCart;

import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.store.service.StoreManageClient;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import com.pinshang.qingyun.xda.product.dto.front.*;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3ODTO;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityFrontMapper;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityFreezeGroupMapper;
import com.pinshang.qingyun.xda.product.model.common.CommodityFreezeGroup;
import com.pinshang.qingyun.xda.product.model.orderTargetSet.XdaOrderTargetSet;
import com.pinshang.qingyun.xda.product.model.specialsCommoditySet.XdaSpecialsCommoditySet;
import com.pinshang.qingyun.xda.product.service.StoreService;
import com.pinshang.qingyun.xda.product.service.XdaOrderTargetSetService;
import com.pinshang.qingyun.xda.product.service.XdaSpecialsCommoditySetService;
import com.pinshang.qingyun.xda.product.service.front.XdaCommodityFrontService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * 组装数据
 */
@Slf4j
@Service
public class XdaShoppingCartConcreteBuilder extends XdaShoppingCartBuilder {

    private final ShoppingCartComputer computer;

    private final XdaSpecialsCommoditySetService xdaSpecialsCommoditySetService;

    private final XdaOrderTargetSetService xdaOrderTargetSetService;

    private final XdaCommodityFrontMapper xdaCommodityFrontMapper;

    private final XdaCommodityFrontService xdaCommodityFrontService;

    private final CommodityFreezeGroupMapper commodityFreezeGroupMapper;

    private final StoreManageClient storeManageClient;
    private final StoreService storeService;

    public XdaShoppingCartConcreteBuilder(XdaSpecialsCommoditySetService xdaSpecialsCommoditySetService, XdaOrderTargetSetService xdaOrderTargetSetService, XdaCommodityFrontMapper xdaCommodityFrontMapper, XdaCommodityFrontService xdaCommodityFrontService, CommodityFreezeGroupMapper commodityFreezeGroupMapper, StoreManageClient storeManageClient, StoreService storeService){
        this.xdaSpecialsCommoditySetService = xdaSpecialsCommoditySetService;
        this.xdaOrderTargetSetService = xdaOrderTargetSetService;
        this.xdaCommodityFrontMapper = xdaCommodityFrontMapper;
        this.xdaCommodityFrontService = xdaCommodityFrontService;
        this.commodityFreezeGroupMapper = commodityFreezeGroupMapper;
        this.storeManageClient = storeManageClient;
        this.storeService = storeService;
        this.computer = new ShoppingCartComputer();
    }

    @Override
    public void buildXdaCommodityInfo(XdaShoppingCartV3IDTO xdaShoppingCartV3IDTO) {
        Boolean isPfsStore = storeService.isPfsStore(xdaShoppingCartV3IDTO.getStoreId());
        xdaShoppingCartV3IDTO.setIsPfsStore(isPfsStore);
        List<XdaCommodityAppODTO> appODTOList = xdaCommodityFrontMapper.queryXdaCommodityListForAppV3(xdaShoppingCartV3IDTO);
        if (CollectionUtils.isEmpty(appODTOList)) {
            log.error("APP未获取到商品信息，请确认B端销售商品范围。查询参数：storeId={},commodityId={}",xdaShoppingCartV3IDTO.getStoreId(),xdaShoppingCartV3IDTO.getCommodityIdList());
            return;
        }
        List<XdaShoppingCartV3ODTO> xdaCommodityAppV2ODTOList = BeanCloneUtils.copyTo(appODTOList, XdaShoppingCartV3ODTO.class);
        computer.init(xdaCommodityAppV2ODTOList);
    }

    @Override
    public void buildXdaSpecialPrice(Date orderTime, Long storeId) {
        List<Long> commodityIdList = computer.getCommodityIdList();
        if(SpringUtil.isNotEmpty(commodityIdList)){
            Map<Long, BigDecimal> commoditySpecialPriceMap = xdaCommodityFrontService.queryXdaSpecialPrice(orderTime, storeId, new ArrayList<>(commodityIdList));
            computer.setSpecialPriceMap(commoditySpecialPriceMap);
        }

    }

    @Override
    public void buildXdaPromotion(Date orderTime, Long storeId) {
        List<Long> commodityIdList = computer.getCommodityIdList();
        if(SpringUtil.isNotEmpty(commodityIdList)){
            Map<Long, List<XdaStorePromotionODTO>> promotionODTOList = xdaCommodityFrontService.queryXdaPromotion(orderTime, storeId, commodityIdList);
            computer.setXdaPromotionMap(promotionODTOList);
        }
    }

    @Override
    public void buildCommodityTextTagInfo() {
        List<Long> commodityIdList = computer.getCommodityIdList();
        if(SpringUtil.isNotEmpty(commodityIdList)){
            Map<Long, List<CommodityTextTagInfoODTO>> deliveryTimeODTOMap = xdaCommodityFrontService.queryXdaCommodityTextTag(commodityIdList);
            computer.setCommodityTextTagInfo(deliveryTimeODTOMap);
        }
    }

    @Override
    public void buildXdaCommodityDeliveryTime(Date orderTime, Long storeId) {
        List<Long> commodityIdList = computer.getCommodityIdList();
        if(SpringUtil.isNotEmpty(commodityIdList)){
            Map<Long, XdaCommodityDeliveryTimeODTO> deliveryTimeODTOMap = xdaCommodityFrontService.queryXdaCommodityDeliveryTime(orderTime, storeId, new ArrayList<>(commodityIdList));
            computer.setCommodityDeliveryTime(deliveryTimeODTOMap);
        }
    }

    @Override
    public void buildXdaCommodityLimit(Date orderTime) {
        List<Long> commodityIdList = computer.getCommodityIdList();
        if(SpringUtil.isNotEmpty(commodityIdList)){
            Map<Long, XdaCommodityLimitODTO> xdaCommodityLimitODTOMap = xdaCommodityFrontService.queryXdaCommodityLimit(orderTime, new ArrayList<>(commodityIdList));
            computer.setXdaCommodityLimit(xdaCommodityLimitODTOMap);
        }
    }

    @Override
    public void buildShopCartQuantity(Long storeId) {
        List<Long> commodityIdList = computer.getCommodityIdList();
        if(SpringUtil.isNotEmpty(commodityIdList)){
            List<XdaShoppingCartODTO> shoppingCartODTOList = xdaCommodityFrontMapper.queryXdaShoppingCartQuantity(storeId,commodityIdList);
            if(CollectionUtils.isEmpty(shoppingCartODTOList)){
                return;
            }
            Map<Long, BigDecimal> quantityMap = new HashMap<>();
            shoppingCartODTOList.stream().collect(groupingBy(XdaShoppingCartODTO::getCommodityId)).forEach((k,v)->{
                if(CollectionUtils.isEmpty(v)){
                    return;
                }
                quantityMap.put(k,v.get(0).getQuantity());
            });
            computer.setShoppingCartQuantity(quantityMap);
        }
    }


    @Override
    public void buildXdaTHGroup(Date orderTime,Long storeId) {
        XdaOrderTargetSet xdaOrderTargetSet;
        if(null != (xdaOrderTargetSet = xdaOrderTargetSetService.findOrderTargetByStoreIdAndOrderTime(storeId, orderTime))){
            List<XdaSpecialsCommoditySet> commodityListByStoreAndOrderTime = xdaSpecialsCommoditySetService.findCommodityListByStoreAndOrderTime(storeId);
            Map<Long, XdaSpecialsCommoditySet> xdaSpecialsCommoditySetMap = commodityListByStoreAndOrderTime.stream().collect(Collectors.toMap(XdaSpecialsCommoditySet::getCommodityId, e -> e));
            computer.setOrderTargetToDay(xdaOrderTargetSet.getOrderTargetToDay());
            computer.setXdaSpecialsCommoditySetMap(xdaSpecialsCommoditySetMap);
        }
    }

    @Override
    public void buildIsFreezeRoundingGroup() {
        List<Long> commodityIdList = computer.getCommodityIdList();
        if(SpringUtil.isNotEmpty(commodityIdList)){
            Example example = new Example(CommodityFreezeGroup.class);
            example.createCriteria().andIn("commodityId", commodityIdList);
            example.selectProperties("commodityId");
            List<CommodityFreezeGroup> commodityFreezeGroups = commodityFreezeGroupMapper.selectByExample(example);
            if(SpringUtil.isNotEmpty(commodityFreezeGroups)){
                computer.setCommodityFreezeGroup(commodityFreezeGroups.stream().map(CommodityFreezeGroup::getCommodityId).collect(Collectors.toList()));
            }
        }
    }

    @Override
    public ShoppingCartComputer getComputer() {
        return computer;
    }
}
