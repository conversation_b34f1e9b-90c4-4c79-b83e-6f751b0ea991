package com.pinshang.qingyun.xda.product.service.front.shoppingCart;

import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3IDTO;
import org.springframework.stereotype.Component;

import java.util.Date;

@Component
public class XdaShoppingCartDirector {

    /**
     * 获取刷新购物车相关数据
     * @param builder 构建员
     * @param xdaShoppingCartV3IDTO 条件
     * 以后价格相关请访问一下接口
     * @see com.pinshang.qingyun.marketing.service.MtPromotionClient
     */
    public static void constructShopCartList(XdaShoppingCartBuilder builder, XdaShoppingCartV3IDTO xdaShoppingCartV3IDTO) {
        Date orderTime = xdaShoppingCartV3IDTO.getOrderTime();
        Long storeId = xdaShoppingCartV3IDTO.getStoreId();
        builder.buildXdaCommodityInfo(xdaShoppingCartV3IDTO);
        builder.buildXdaSpecialPrice(orderTime,storeId);
//      builder.buildXdaPromotion(orderTime,storeId);
        builder.buildXdaCommodityDeliveryTime(orderTime,storeId);
        builder.buildXdaCommodityLimit(orderTime);
        builder.buildXdaTHGroup(orderTime,storeId);
        builder.buildIsFreezeRoundingGroup();
    }

    /**
     * 获取品类数量
     * @param builder 构建员
     * @param xdaShoppingCartV3IDTO 条件
     */
    public static void constructVarietySum(XdaShoppingCartBuilder builder, XdaShoppingCartV3IDTO xdaShoppingCartV3IDTO) {
        Date orderTime = xdaShoppingCartV3IDTO.getOrderTime();
        Long storeId = xdaShoppingCartV3IDTO.getStoreId();
        builder.buildXdaCommodityInfo(xdaShoppingCartV3IDTO);
        builder.buildXdaCommodityDeliveryTime(orderTime,storeId);
    }
}
