package com.pinshang.qingyun.xda.product.service.front.shoppingCart;

import com.pinshang.qingyun.base.enums.pic.PicSizeEnums;
import com.pinshang.qingyun.box.utils.ImageUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.store.service.StoreManageClient;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDeliveryTimeODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityLimitODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3ODTO;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityFrontMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityTextMapper;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityFreezeGroupMapper;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityMapper;
import com.pinshang.qingyun.xda.product.model.XdaCommodityText;
import com.pinshang.qingyun.xda.product.model.common.Commodity;
import com.pinshang.qingyun.xda.product.model.specialsCommoditySet.XdaSpecialsCommoditySet;
import com.pinshang.qingyun.xda.product.service.StoreService;
import com.pinshang.qingyun.xda.product.service.XdaOrderTargetSetService;
import com.pinshang.qingyun.xda.product.service.XdaSpecialsCommoditySetService;
import com.pinshang.qingyun.xda.product.service.front.XdaCommodityFrontService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class XdaShoppingCartService {

    @Autowired
    private XdaCommodityFrontMapper xdaCommodityFrontMapper;

    @Autowired
    private XdaCommodityFrontService xdaCommodityFrontService;

    @Autowired
    private XdaSpecialsCommoditySetService xdaSpecialsCommoditySetService;

    @Autowired
    private XdaOrderTargetSetService xdaOrderTargetSetService;

    @Autowired
    private CommodityFreezeGroupMapper commodityFreezeGroupMapper;

    @Autowired
    private XdaCommodityTextMapper xdaCommodityTextMapper;
    @Autowired
    private CommodityMapper commodityMapper;
    @Value("${pinshang.img-xd-server-url}")
    private String imgServerUrl;

    @Autowired
    private StoreManageClient storeManageClient;
    @Autowired
    private StoreService storeService;

    /**
     * 获取商品销售箱规
     * @param appIDTO
     * @return
     */
    public XdaShoppingCartV3ODTO getXdaCommoditySalesBoxCapacity(XdaShoppingCartV3IDTO appIDTO){
        Boolean isPfsStore = storeService.isPfsStore(appIDTO.getStoreId());
        appIDTO.setIsPfsStore(isPfsStore);
        return xdaCommodityFrontMapper.getXdaCommoditySalesBoxCapacity(appIDTO);
    }

    /**
     * 获取鲜达限量
     * @param appIDTO
     * @return
     */
    public XdaShoppingCartV3ODTO getXdaCommodityLimit(XdaShoppingCartV3IDTO appIDTO){
        Long commodityId = appIDTO.getCommodityId();
        Date orderTime = appIDTO.getOrderTime();
        Long storeId = appIDTO.getStoreId();

        Boolean isPfsStore = storeService.isPfsStore(appIDTO.getStoreId());
        appIDTO.setIsPfsStore(isPfsStore);
        XdaShoppingCartV3ODTO xdaShoppingCartV3ODTO = xdaCommodityFrontMapper.getXdaCommoditySalesBoxCapacity(appIDTO);
        List<Long> commodityIdList = Stream.of(appIDTO.getCommodityId()).collect(Collectors.toList());
        /*商品库存货**/
        Map<Long, XdaCommodityLimitODTO> limitQuantityMap = xdaCommodityFrontService.queryXdaCommodityLimit(orderTime,commodityIdList);
        xdaShoppingCartV3ODTO.setIsLimit(0);
        if(limitQuantityMap.containsKey(commodityId)){
            xdaShoppingCartV3ODTO.setIsLimit(1);
            xdaShoppingCartV3ODTO.setLimitNumber(limitQuantityMap.get(commodityId).getLimitNumber());
        }
        /*是否可订货**/
        Map<Long, XdaCommodityDeliveryTimeODTO> xdaCommodityDeliveryTimeODTOMap = xdaCommodityFrontService.queryXdaCommodityDeliveryTime(orderTime, storeId, commodityIdList);
        if(xdaCommodityDeliveryTimeODTOMap.containsKey(commodityId)){
            xdaShoppingCartV3ODTO.setIsCanOrder(xdaCommodityDeliveryTimeODTOMap.get(commodityId).getIsCanOrder());
        }

        /*特惠商品判断**/
        if(appIDTO.getCommodityType().equals(2)){
            List<XdaSpecialsCommoditySet> commodityListByStoreAndOrderTime = xdaSpecialsCommoditySetService.findCommodityListByStoreAndOrderTime(appIDTO.getStoreId());
            Map<Long, XdaSpecialsCommoditySet> xdaSpecialsCommoditySetMap = commodityListByStoreAndOrderTime.stream().collect(Collectors.toMap(XdaSpecialsCommoditySet::getCommodityId, e -> e));
            if(xdaSpecialsCommoditySetMap.containsKey(commodityId)){
                Integer commodityLimit = xdaSpecialsCommoditySetMap.get(commodityId).getCommodityLimit();
                xdaShoppingCartV3ODTO.setIsThPrice(1);
                xdaShoppingCartV3ODTO.setThLimitNumber(new BigDecimal(commodityLimit));
            }
        }
        return xdaShoppingCartV3ODTO;
    }

    /**
     * 获取商品数量
     * @param xdaShoppingCartV3IDTO 条件
     * @return 商品基本信息
     */
    public List<XdaShoppingCartV3ODTO> getVarietySum(XdaShoppingCartV3IDTO xdaShoppingCartV3IDTO){
        xdaShoppingCartV3IDTO.setStoreId(xdaShoppingCartV3IDTO.getStoreId());
        xdaShoppingCartV3IDTO.setOrderTime(xdaShoppingCartV3IDTO.getOrderTime());
        XdaShoppingCartBuilder builder = xdaShoppingCartConcreteBuilder();
        XdaShoppingCartDirector.constructVarietySum(builder,xdaShoppingCartV3IDTO);
        ShoppingCartComputer computer = builder.getComputer();
        return computer.assemble();
    }


    /**
     * 刷新购物车 获取商品对应信息
     * @param xdaShoppingCartV3IDTO 请求参数
     * @return 商品基本信息
     */
    public List<XdaShoppingCartV3ODTO> getShopCartList(XdaShoppingCartV3IDTO xdaShoppingCartV3IDTO){
        xdaShoppingCartV3IDTO.setStoreId(xdaShoppingCartV3IDTO.getStoreId());
        xdaShoppingCartV3IDTO.setOrderTime(xdaShoppingCartV3IDTO.getOrderTime());
        XdaShoppingCartBuilder builder = xdaShoppingCartConcreteBuilder();
        XdaShoppingCartDirector.constructShopCartList(builder,xdaShoppingCartV3IDTO);
        ShoppingCartComputer computer = builder.getComputer();
        List<XdaShoppingCartV3ODTO> assemble = computer.assemble();
        assemble.forEach(item->{

            item.setImageUrl(imgServerUrl+ ImageUtils.getXdImgUrlV2(item.getImageUrl(),PicSizeEnums.PIC_120x120.getSize()));
        });
        return assemble;
    }

    public List<XdaShoppingCartV3ODTO> getXdaCommodityTextList(XdaShoppingCartV3IDTO xdaShoppingCartV3IDTO){
        Example example = new Example(XdaCommodityText.class);
        example.createCriteria()
                .andIn("commodityId",xdaShoppingCartV3IDTO.getCommodityIdList());
        List<XdaCommodityText> xdaCommodityTexts = xdaCommodityTextMapper.selectByExample(example);
        if(SpringUtil.isNotEmpty(xdaCommodityTexts)){
            List<XdaShoppingCartV3ODTO> xdaShoppingCartV3ODTOList = new ArrayList<>();
            for (XdaCommodityText xdaCommodityText : xdaCommodityTexts) {
                xdaShoppingCartV3ODTOList.add(XdaShoppingCartV3ODTO.forInsert(xdaCommodityText.getCommodityId(),xdaCommodityText.getSecondCategoryId()));
            }
            return xdaShoppingCartV3ODTOList;
        }
        return new ArrayList<>();
    }

    public XdaShoppingCartV3ODTO getXdaCommodityTextListIsCanOrder(XdaShoppingCartV3IDTO xdaShoppingCartV3IDTO){
        XdaShoppingCartV3ODTO xdaShoppingCartV3ODTOList = new XdaShoppingCartV3ODTO();
        Date orderTime = xdaShoppingCartV3IDTO.getOrderTime();
        Long storeId = xdaShoppingCartV3IDTO.getStoreId();
        Long commodityId = xdaShoppingCartV3IDTO.getCommodityId();
        List<Long> commodityIdList = Stream.of(xdaShoppingCartV3IDTO.getCommodityId()).collect(Collectors.toList());
        Map<Long, XdaCommodityDeliveryTimeODTO> xdaCommodityDeliveryTimeODTOMap = xdaCommodityFrontService.queryXdaCommodityDeliveryTime(orderTime, storeId, commodityIdList);
        if(xdaCommodityDeliveryTimeODTOMap.containsKey(commodityId)){
            xdaShoppingCartV3ODTOList.setIsCanOrder(xdaCommodityDeliveryTimeODTOMap.get(commodityId).getIsCanOrder());
        }
        /*特惠商品判断**/
        if(xdaShoppingCartV3IDTO.getCommodityType().equals(2)){
            List<XdaSpecialsCommoditySet> commodityListByStoreAndOrderTime = xdaSpecialsCommoditySetService.findCommodityListByStoreAndOrderTime(xdaShoppingCartV3IDTO.getStoreId());
            Map<Long, XdaSpecialsCommoditySet> xdaSpecialsCommoditySetMap = commodityListByStoreAndOrderTime.stream().collect(Collectors.toMap(XdaSpecialsCommoditySet::getCommodityId, e -> e));
            if(xdaSpecialsCommoditySetMap.containsKey(commodityId)){
                Integer commodityLimit = xdaSpecialsCommoditySetMap.get(commodityId).getCommodityLimit();
                xdaShoppingCartV3ODTOList.setIsThPrice(1);
                xdaShoppingCartV3ODTOList.setThLimitNumber(new BigDecimal(commodityLimit));
            }
        }
        return xdaShoppingCartV3ODTOList;
    }
    public Map<Long, BigDecimal> selectCommodityPackageSpecByCommodityIdList(List<Long> commodityIdList){
        Example example = new Example(Commodity.class);
        example.createCriteria().andIn("id",commodityIdList);
        List<Commodity> commodities = commodityMapper.selectByExample(example);
        if(SpringUtil.isNotEmpty(commodities)){
            return commodities.stream().collect(Collectors.toMap(Commodity::getId, Commodity::getCommodityPackageSpec));
        }
        return new HashMap<>();
    }

    public List<XdaShoppingCartV3ODTO> getValidCommodityGift(XdaShoppingCartV3IDTO appIDTO){
        Boolean isPfsStore = storeService.isPfsStore(appIDTO.getStoreId());
        appIDTO.setIsPfsStore(isPfsStore);
        List<XdaShoppingCartV3ODTO> xdaShoppingCartV3ODTOList =  xdaCommodityTextMapper.selectCommodityTextByShoppingCartGiftList(appIDTO);
        if(SpringUtil.isNotEmpty(xdaShoppingCartV3ODTOList)){
            List<Long> commodityIdList = xdaShoppingCartV3ODTOList.stream().map(XdaShoppingCartV3ODTO::getCommodityId).collect(Collectors.toList());
            Map<Long, XdaCommodityDeliveryTimeODTO> xdaCommodityDeliveryTimeODTOMap = xdaCommodityFrontService.queryXdaCommodityDeliveryTime(appIDTO.getOrderTime(), appIDTO.getStoreId(), commodityIdList);
            Map<Long, XdaCommodityLimitODTO> xdaCommodityLimitODTOMap = xdaCommodityFrontService.queryXdaCommodityLimit(appIDTO.getOrderTime(), commodityIdList);
            xdaShoppingCartV3ODTOList.forEach(item->{
                item.setIsSpecialPrice(0);
                item.setImageUrl(imgServerUrl+ ImageUtils.getXdImgUrlV2(item.getImageUrl(),PicSizeEnums.PIC_120x120.getSize()));
                if(xdaCommodityDeliveryTimeODTOMap.containsKey(item.getCommodityId())){
                    XdaCommodityDeliveryTimeODTO xdaCommodityDeliveryTimeODTO = xdaCommodityDeliveryTimeODTOMap.get(item.getCommodityId());
                    item.setIsCanOrder(xdaCommodityDeliveryTimeODTO.getIsCanOrder());
                    item.setBeginDeliveryTime(xdaCommodityDeliveryTimeODTO.getDeliveryDateList().get(0));
                }
                if(xdaCommodityLimitODTOMap.containsKey(item.getCommodityId())){
                    XdaCommodityLimitODTO xdaCommodityLimitODTO = xdaCommodityLimitODTOMap.get(item.getCommodityId());
                    item.setIsLimit(1);
                    item.setLimitNumber(xdaCommodityLimitODTO.getLimitNumber());
                    item.setLimitStartTime(xdaCommodityLimitODTO.getBeginTime());
                    item.setLimitEndTime(xdaCommodityLimitODTO.getEndTime());
                }
            });
        }

        return xdaShoppingCartV3ODTOList;
    }
    /**
     * 获取购物车具体实现类
     * @return
     */
    public XdaShoppingCartBuilder xdaShoppingCartConcreteBuilder(){
        return new XdaShoppingCartConcreteBuilder(
                xdaSpecialsCommoditySetService,xdaOrderTargetSetService,xdaCommodityFrontMapper,
                xdaCommodityFrontService,commodityFreezeGroupMapper,storeManageClient,storeService);
    }
}
