package com.pinshang.qingyun.xda.product.service.front.v2;

import com.pinshang.qingyun.base.enums.pic.PicCutTypeEnum;
import com.pinshang.qingyun.base.enums.pic.PicSizeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.ImageUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.ImageLibraryODTO;
import com.pinshang.qingyun.common.service.ImageLibraryClient;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextPicListIDTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextTagInfoListIDTO;
import com.pinshang.qingyun.xda.product.dto.front.*;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.*;
import com.pinshang.qingyun.xda.product.mapper.*;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityFreezeGroupMapper;
import com.pinshang.qingyun.xda.product.model.XdaCommodityCollect;
import com.pinshang.qingyun.xda.product.model.XdaCommodityTextPic;
import com.pinshang.qingyun.xda.product.model.common.CommodityFreezeGroup;
import com.pinshang.qingyun.xda.product.model.orderTargetSet.XdaOrderTargetSet;
import com.pinshang.qingyun.xda.product.model.specialsCommoditySet.XdaSpecialsCommoditySet;
import com.pinshang.qingyun.xda.product.service.XdaOrderTargetSetService;
import com.pinshang.qingyun.xda.product.service.XdaSpecialsCommoditySetService;
import com.pinshang.qingyun.xda.product.service.front.XdaCommodityFrontService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * <AUTHOR>
 * @date 23/6/14/014 15:42
 */
@Service
@Slf4j
public class XdaCommodityFrontV2Service {

    @Autowired
    private XdaCommodityFrontService xdaCommodityFrontService;

    @Autowired
    private XdaCommodityFrontMapper xdaCommodityFrontMapper;

    @Autowired
    private XdaSpecialsCommoditySetService xdaSpecialsCommoditySetService;

    @Autowired
    private XdaOrderTargetSetService xdaOrderTargetSetService;

    @Autowired
    private XdaCommodityTextMapper xdaCommodityTextMapper;
    @Autowired
    private CommodityFreezeGroupMapper commodityFreezeGroupMapper;
    @Autowired
    private XdaSerialCommodityMapper xdaSerialCommodityMapper;
    @Autowired
    private XdaCommodityCollectMapper xdaCommodityCollectMapper;
    @Autowired
    private XdaCommodityTextPicMapper xdaCommodityTextPicMapper;
    @Autowired
    private ImageLibraryClient imageLibraryClient;

    @Value("${pinshang.img-xd-server-url}")
    private String imgServerUrl;

    /**
     * APP查询商品列表信息
     * @param appIDTO
     * @return
     */
    public List<XdaCommodityAppV2ODTO> queryXdaCommodityListForV2App(XdaCommodityAppV2IDTO appIDTO){
        if(appIDTO.getStoreId()==null || appIDTO.getOrderTime()==null){
            return Collections.EMPTY_LIST;
        }
        XdaCommodityAppIDTO xdaCommodityAppIDTO = BeanCloneUtils.copyTo(appIDTO,XdaCommodityAppIDTO.class);

        List<XdaCommodityAppODTO> appODTOList = xdaCommodityFrontMapper.queryXdaCommodityListForApp(xdaCommodityAppIDTO);
        if (CollectionUtils.isEmpty(appODTOList)) {
            log.error("APP未获取到商品信息，请确认B端销售商品范围。查询参数：storeId={},commodityId={}",appIDTO.getStoreId(),appIDTO.getCommodityIdList());
            return Collections.EMPTY_LIST;
        }
        xdaCommodityFrontService.setXdaCommodityInfo(appODTOList,xdaCommodityAppIDTO);

        List<XdaCommodityAppV2ODTO> xdaCommodityAppV2ODTOList = BeanCloneUtils.copyTo(appODTOList,XdaCommodityAppV2ODTO.class);

        /**
         * 设置特惠商品
         */
        XdaOrderTargetSet xdaOrderTargetSet = null;
        if(null != (xdaOrderTargetSet = xdaOrderTargetSetService.findOrderTargetByStoreIdAndOrderTime(appIDTO.getStoreId(), appIDTO.getOrderTime()))){
            List<XdaSpecialsCommoditySet> commodityListByStoreAndOrderTime = xdaSpecialsCommoditySetService.findCommodityListByStoreAndOrderTime(appIDTO.getStoreId());
            Map<Long, XdaSpecialsCommoditySet> xdaSpecialsCommoditySetMap = commodityListByStoreAndOrderTime.stream().collect(Collectors.toMap(XdaSpecialsCommoditySet::getCommodityId, e -> e));
            XdaOrderTargetSet finalXdaOrderTargetSet = xdaOrderTargetSet;
            xdaCommodityAppV2ODTOList.forEach(item->{
                if(xdaSpecialsCommoditySetMap.containsKey(item.getCommodityId())){
                    XdaSpecialsCommoditySet xdaSpecialsCommoditySet = xdaSpecialsCommoditySetMap.get(item.getCommodityId());
                    item.setIsThPrice(1);
                    item.setThFullPrice(finalXdaOrderTargetSet.getOrderTargetToDay());
                    item.setThPrice(xdaSpecialsCommoditySet.getCommoditySpecialsPrice());
                    item.setThLimitNumber(new BigDecimal(xdaSpecialsCommoditySet.getCommodityLimit()));
                }
            });
        }
        return xdaCommodityAppV2ODTOList;
    }

    /**
     * 获取订货目标 如果没有返回空
     * @param xdaShoppingCartV2IDTO
     * @return
     */
    public BigDecimal findOrderTargetByStoreIdAndOrderTime(XdaShoppingCartV2IDTO xdaShoppingCartV2IDTO){
        Long storeId = xdaShoppingCartV2IDTO.getStoreId();
        Date orderTime = xdaShoppingCartV2IDTO.getOrderTime();
        XdaOrderTargetSet orderTargetByStoreIdAndOrderTime = xdaOrderTargetSetService.findOrderTargetByStoreIdAndOrderTime(storeId, orderTime);
        if(null != orderTargetByStoreIdAndOrderTime){
            return orderTargetByStoreIdAndOrderTime.getOrderTargetToDay();
        }
        return null;
    }

    /**
     * 根据客户信息查询特惠方案
     * @param xdaShoppingCartV2IDTO
     * @return
     */
    public XdaOrderTargetSetV2ODTO findXdaOrderTargetIdByStoreIdAndOrderTime(XdaShoppingCartV2IDTO xdaShoppingCartV2IDTO){
        Long storeId = xdaShoppingCartV2IDTO.getStoreId();
        Date orderTime = xdaShoppingCartV2IDTO.getOrderTime();
        XdaOrderTargetSet orderTargetByStoreIdAndOrderTime = xdaOrderTargetSetService.findOrderTargetByStoreIdAndOrderTime(storeId, orderTime);
        if(null != orderTargetByStoreIdAndOrderTime){
            return XdaOrderTargetSetV2ODTO.init(orderTargetByStoreIdAndOrderTime.getId());
        }
        return null;
    }

    /**
     * set商品信息
     * @param appODTOList
     * @param appIDTO
     */
    public void setXdaCommodityInfo(List<XdaCommodityAppV2ODTO> appODTOList,XdaCommodityAppV2IDTO appIDTO){
        if(CollectionUtils.isEmpty(appODTOList)){
            return;
        }
        Long storeId = appIDTO.getStoreId();
        Date orderTime = appIDTO.getOrderTime();
        List<Long> commodityIdList = appODTOList.stream().map(XdaCommodityAppV2ODTO::getCommodityId).collect(Collectors.toList());
        //特价
        Map<Long, BigDecimal> priceMap = null;
        if(appIDTO.getNeedSpecialPrice()){
            priceMap = xdaCommodityFrontService.queryXdaSpecialPrice(orderTime,storeId,new ArrayList<>(commodityIdList));
        }

        //促销
        Map<Long, List<XdaStorePromotionODTO>> promotionMap = null;
        if(appIDTO.getNeedPromotion()){
            promotionMap = xdaCommodityFrontService.queryXdaPromotion(orderTime,storeId,new ArrayList<>(commodityIdList));
        }

        //自定义标签
        Map<Long, List<CommodityTextTagInfoODTO>> tagMap = null;
        if(appIDTO.getNeedTag()){
            List<CommodityTextTagInfoODTO> tagList = xdaCommodityTextMapper.selectCommodityTextTagInfoList(new SelectCommodityTextTagInfoListIDTO(commodityIdList));
            tagMap = tagList.stream().collect(Collectors.groupingBy(CommodityTextTagInfoODTO::getCommodityId));
        }

        //是否可订货
        Map<Long, XdaCommodityDeliveryTimeODTO> deliveryTimeMap = null;
        if(appIDTO.getNeedCanOrder()){
            deliveryTimeMap = xdaCommodityFrontService.queryXdaCommodityDeliveryTime(orderTime,storeId,new ArrayList<>(commodityIdList));
        }

        //商品限量
        Map<Long, XdaCommodityLimitODTO> limitQuantityMap = null;
        if(appIDTO.getNeedLimit()){
            limitQuantityMap = xdaCommodityFrontService.queryXdaCommodityLimit(orderTime,new ArrayList<>(commodityIdList));
        }

        //购物车数量
        Map<Long,BigDecimal> shopCartQuantityMap = null;
        if(appIDTO.getNeedCartQuantity()){
            shopCartQuantityMap = this.queryShopCartQuantityMapV2(appIDTO.getStoreId(),new ArrayList<>(commodityIdList),1);
        }

        //凑整商品集合
        List<Long> freezeGroupList = this.queryFreezeGroupMap(new ArrayList<>(commodityIdList));

        for(XdaCommodityAppV2ODTO appODTO : appODTOList){
            //声明标签集合
            List<CommodityTextTagInfoODTO> tagV2List = new ArrayList<>();
            Long commodityId = appODTO.getCommodityId();
            //图片
            if(appIDTO.getNeedDefaultImage() && StringUtils.isNotEmpty(appODTO.getImageUrl())){
                appODTO.setImageUrl(imgServerUrl+ ImageUtils.getXdImgUrlV2(appODTO.getImageUrl(),appIDTO.getDefaultImageSize()==null?null:appIDTO.getDefaultImageSize().getSize()));
            }
            //特价
            if(priceMap!=null && priceMap.get(commodityId)!=null){
                BigDecimal specialPrice = priceMap.get(commodityId);
                if(appODTO.getCommodityPrice()!=null && appODTO.getCommodityPrice().compareTo(specialPrice)>0){
                    appODTO.setIsSpecialPrice(1);
                    appODTO.setSpecialPrice(priceMap.get(commodityId));
                    tagV2List.add(new CommodityTextTagInfoODTO("特价", "#FF5733", commodityId));

                }
            }
            //促销
            if(promotionMap!=null && CollectionUtils.isNotEmpty(promotionMap.get(commodityId))){
                appODTO.setIsPromotion(1);
                appODTO.setPromotionList(promotionMap.get(commodityId));
            }
            //标签
            if(tagMap!=null && CollectionUtils.isNotEmpty(tagMap.get(commodityId))){
                appODTO.setTagList(tagMap.get(commodityId));
            }
            //送货日期范围
            if(deliveryTimeMap!=null && deliveryTimeMap.get(commodityId)!=null){
                XdaCommodityDeliveryTimeODTO deliveryTimeODTO = deliveryTimeMap.get(commodityId);
                appODTO.setIsCanOrder(deliveryTimeODTO.getIsCanOrder());
                appODTO.setDeliveryTimeODTO(deliveryTimeODTO);
                List<Date> xdaDeliveryTimeList = deliveryTimeODTO.getDeliveryDateList();
                if(CollectionUtils.isNotEmpty(xdaDeliveryTimeList)){
                    appODTO.setBeginDeliveryTime(xdaDeliveryTimeList.get(0));
                    appODTO.setEndDeliveryTime(xdaDeliveryTimeList.get(xdaDeliveryTimeList.size()-1));
                }
            }
            //凑整
            if(freezeGroupList.contains(commodityId)){
                appODTO.setIsFreezeRounding(1);
                appODTO.setIsFreezeRoundingMultiple(30);
                tagV2List.add(new CommodityTextTagInfoODTO("凑整", "#FF5733", commodityId));
            }

            //限量
//            if(limitQuantityMap!=null && limitQuantityMap.get(commodityId)!=null){
//                appODTO.setIsLimit(1);
//                appODTO.setLimitNumber(limitQuantityMap.get(commodityId));
//                tagV2List.add(new CommodityTextTagInfoODTO("限量", "#FF5733", commodityId));
//            }
            if(limitQuantityMap!=null && limitQuantityMap.get(commodityId)!=null){
                XdaCommodityLimitODTO xdaCommodityLimitODTO = limitQuantityMap.get(commodityId);
                appODTO.setIsLimit(1);
                appODTO.setLimitNumber(xdaCommodityLimitODTO.getLimitNumber());
                appODTO.setLimitStartTime(xdaCommodityLimitODTO.getBeginTime());
                appODTO.setLimitEndTime(xdaCommodityLimitODTO.getEndTime());
                tagV2List.add(new CommodityTextTagInfoODTO("限量", "#FF5733", commodityId));

            }
            //购物车数量
            if(shopCartQuantityMap!=null && shopCartQuantityMap.get(commodityId)!=null){
                appODTO.setShoppingCartQuantity(shopCartQuantityMap.get(commodityId));
            }
            //其他标签列表
            appODTO.setTagV2List(tagV2List);
        }
    }


    /**
     * set鲜达特惠商品信息
     * @param appODTOList
     * @param appIDTO
     */
    public void setXdaThCommodityInfo(List<XdaCommodityAppV2ODTO> appODTOList,XdaCommodityAppV2IDTO appIDTO){
        if(CollectionUtils.isEmpty(appODTOList)){
            return;
        }
        Long storeId = appIDTO.getStoreId();
        Date orderTime = appIDTO.getOrderTime();
        List<Long> commodityIdList = appODTOList.stream().map(XdaCommodityAppV2ODTO::getCommodityId).collect(Collectors.toList());

        //自定义标签
        Map<Long, List<CommodityTextTagInfoODTO>> tagMap = null;
        if(appIDTO.getNeedTag()){
            List<CommodityTextTagInfoODTO> tagList = xdaCommodityTextMapper.selectCommodityTextTagInfoList(new SelectCommodityTextTagInfoListIDTO(commodityIdList));
            tagMap = tagList.stream().collect(Collectors.groupingBy(CommodityTextTagInfoODTO::getCommodityId));
        }

        //是否可订货
        Map<Long, XdaCommodityDeliveryTimeODTO> deliveryTimeMap = null;
        if(appIDTO.getNeedCanOrder()){
            deliveryTimeMap = xdaCommodityFrontService.queryXdaCommodityDeliveryTime(orderTime,storeId,new ArrayList<>(commodityIdList));
        }

        //商品限量
        Map<Long, XdaCommodityLimitODTO> limitQuantityMap = null;
        if(appIDTO.getNeedLimit()){
            limitQuantityMap = xdaCommodityFrontService.queryXdaCommodityLimit(orderTime,commodityIdList);
        }

        //购物车数量
        Map<Long,BigDecimal> shopCartQuantityMap = null;
        if(appIDTO.getNeedCartQuantity()){
            shopCartQuantityMap = this.queryShopCartQuantityMapV2(appIDTO.getStoreId(),commodityIdList,2);
        }

        //凑整商品集合
        List<Long> freezeGroupList = this.queryFreezeGroupMap(commodityIdList);

        for(XdaCommodityAppV2ODTO appODTO : appODTOList){
            //声明标签集合
            List<CommodityTextTagInfoODTO> tagV2List = new ArrayList<>();
            Long commodityId = appODTO.getCommodityId();
            tagV2List.add(new CommodityTextTagInfoODTO("特惠", "#FF5733", commodityId));
            //图片
            if(appIDTO.getNeedDefaultImage() && StringUtils.isNotEmpty(appODTO.getImageUrl())){
                appODTO.setImageUrl(imgServerUrl+ ImageUtils.getXdImgUrlV2(appODTO.getImageUrl(),appIDTO.getDefaultImageSize()==null?null:appIDTO.getDefaultImageSize().getSize()));
            }

            //标签
            if(tagMap!=null && CollectionUtils.isNotEmpty(tagMap.get(commodityId))){
                appODTO.setTagList(tagMap.get(commodityId));
            }
            //送货日期范围
            if(deliveryTimeMap!=null && deliveryTimeMap.get(commodityId)!=null){
                XdaCommodityDeliveryTimeODTO deliveryTimeODTO = deliveryTimeMap.get(commodityId);
                appODTO.setIsCanOrder(deliveryTimeODTO.getIsCanOrder());
                appODTO.setDeliveryTimeODTO(deliveryTimeODTO);
                List<Date> xdaDeliveryTimeList = deliveryTimeODTO.getDeliveryDateList();
                if(CollectionUtils.isNotEmpty(xdaDeliveryTimeList)){
                    appODTO.setBeginDeliveryTime(xdaDeliveryTimeList.get(0));
                    appODTO.setEndDeliveryTime(xdaDeliveryTimeList.get(xdaDeliveryTimeList.size()-1));
                }
            }
            //凑整
            if(freezeGroupList.contains(commodityId)){
                appODTO.setIsFreezeRounding(1);
                appODTO.setIsFreezeRoundingMultiple(30);
                tagV2List.add(new CommodityTextTagInfoODTO("凑整", "#FF5733", commodityId));
            }

            //限量
            if(limitQuantityMap!=null && limitQuantityMap.get(commodityId)!=null){
                XdaCommodityLimitODTO xdaCommodityLimitODTO = limitQuantityMap.get(commodityId);
                appODTO.setIsLimit(1);
                appODTO.setLimitNumber(xdaCommodityLimitODTO.getLimitNumber());
                appODTO.setLimitStartTime(xdaCommodityLimitODTO.getBeginTime());
                appODTO.setLimitEndTime(xdaCommodityLimitODTO.getEndTime());
            }
            //购物车数量
            if(shopCartQuantityMap!=null && shopCartQuantityMap.get(commodityId)!=null){
                appODTO.setShoppingCartQuantity(shopCartQuantityMap.get(commodityId));
            }
            //其他标签列表
            tagV2List.add(new CommodityTextTagInfoODTO("限量", "#FF5733", commodityId));
            appODTO.setTagV2List(tagV2List);
        }
    }


    /**
     * 查询商品是否凑整商品
     * @param commodityIdList
     * @return
     */
    public List<Long> queryFreezeGroupMap(List<Long> commodityIdList){
        if(SpringUtil.isEmpty(commodityIdList)){
            return Collections.EMPTY_LIST;
        }
        Example example = new Example(CommodityFreezeGroup.class);
        example.createCriteria().andIn("commodityId",commodityIdList);
        List<CommodityFreezeGroup> commodityFreezeGroups = commodityFreezeGroupMapper.selectByExample(example);

        return commodityFreezeGroups.stream().map(CommodityFreezeGroup::getCommodityId).collect(Collectors.toList());
    }


    /**
     * 鲜达商品详情
     * @param detailAppIDTO
     * @return
     */
    @Deprecated
    public XdaCommodityDetailAppV2ODTO queryXdaCommodityDetailForAppV2(XdaCommodityDetailAppV2IDTO detailAppIDTO){
        if(detailAppIDTO==null){
            log.error("查询商品详情参数空异常");
            return null;
        }
        Long commodityId = detailAppIDTO.getCommodityId();
        if(commodityId == null || detailAppIDTO.getStoreId()==null || detailAppIDTO.getOrderTime()==null){
            log.error("查询商品详情参数空异常");
            return null;
        }

        //set查询商品详情参数
        XdaCommodityAppV2IDTO appIDTO = XdaCommodityAppV2IDTO.builder().orderTime(detailAppIDTO.getOrderTime())
                .storeId(detailAppIDTO.getStoreId()).commodityIdList(Collections.singletonList(commodityId))
                .defaultImageSize(PicSizeEnums.PIC_750x750).needCartQuantity(true).build();
        List<XdaCommodityAppV2ODTO> appODTOList = this.queryXdaCommodityDetailsListForApp(appIDTO,detailAppIDTO.getIsThPrice());
        if(CollectionUtils.isEmpty(appODTOList)){
            return null;
        }

        XdaCommodityAppV2ODTO appODTO = appODTOList.get(0);
        XdaCommodityDetailAppV2ODTO detailAppODTO = BeanCloneUtils.copyTo(appODTO, XdaCommodityDetailAppV2ODTO.class);
        //判断商品是否特惠
        if(null !=detailAppIDTO.getIsThPrice() &&  detailAppIDTO.getIsThPrice().equals(1)){
            //设置特惠信息
           this.setXdaCommodityThPrice(detailAppODTO,commodityId);
        }else{
            //非特惠商品处理系列品
            this.processSerialCommodityDetail(detailAppODTO,detailAppIDTO);
        }

        //获取收藏状态
        this.queryXdaCommodityCollect(detailAppODTO,commodityId,detailAppIDTO.getStoreId());

        //设置banner列表和长图
        this.setXdaCommodityImage(detailAppODTO,commodityId);
        //设置最快送达日期
        if(appODTO.getDeliveryTimeODTO()!=null){
            detailAppODTO.setDistributionTipList(appODTO.getDeliveryTimeODTO().getDistributionTipList());
        }
        return detailAppODTO;
    }

    /**
     * 鲜达商品列表
     * @param appIDTO
     * @return
     */
    public List<XdaCommodityAppV2ODTO> queryXdaCommodityDetailsListForApp(XdaCommodityAppV2IDTO appIDTO,Integer isThPrice){
        if(appIDTO.getStoreId()==null || appIDTO.getOrderTime()==null){
            return Collections.EMPTY_LIST;
        }
        List<XdaCommodityAppV2ODTO> appODTOList = xdaCommodityFrontMapper.queryXdaCommodityDetailsForApp(appIDTO);
        if (CollectionUtils.isEmpty(appODTOList)) {
            log.error("APP未获取到商品信息，请确认B端销售商品范围。查询参数：storeId={},commodityId={}",appIDTO.getStoreId(),appIDTO.getCommodityIdList());
            return Collections.EMPTY_LIST;
        }
        if(null != isThPrice &&  isThPrice.equals(1)){
            this.setXdaThCommodityInfo(appODTOList,appIDTO);
        }else{
            this.setXdaCommodityInfo(appODTOList,appIDTO);
        }
        return appODTOList;
    }

    /**
     * set特惠商品特惠价
     * @param detailAppODTO
     * @param commodityId
     */
    public void setXdaCommodityThPrice(XdaCommodityDetailAppV2ODTO detailAppODTO,Long commodityId){
        XdaSpecialsCommoditySet xdaSpecialsCommodityInfo = xdaSpecialsCommoditySetService.findXdaSpecialsCommodityInfoByCommodityId(commodityId);
        if(null != xdaSpecialsCommodityInfo){
            detailAppODTO.setIsThPrice(1);
            detailAppODTO.setThPrice(xdaSpecialsCommodityInfo.getCommoditySpecialsPrice());
            detailAppODTO.setThLimitNumber(new BigDecimal(xdaSpecialsCommodityInfo.getCommodityLimit()));
            //特惠提示信息
            String thCategoryTipsDetails = "已加购物车的正常商品的实付总金额≥所选送货日期的订货目标，则客户可享受特惠商品，以特惠的价格限量加购指定商品；其中，\n" +
                    "1.订货目标由销售部门设置，若当天未设置订货目标，则客户不能享受特惠商品加购，特惠商品分类不可见；\n" +
                    "2.正常商品，指特惠商品之外的商品，特惠商品的订货金额不能参与订货目标的计算；\n" +
                    "3.实付总金额，指特价、促销（比如满减活动）后实付总金额；\n" +
                    "4.特惠商品不参与任何特价活动、促销活动的计算，不能与任何活动叠加优惠；\n" +
                    "5.特惠商品不参与结算返利。";
            detailAppODTO.setThTipsDetails(thCategoryTipsDetails);
        }
    }


    /**
     * 根据类型区分: 特惠商品和普通商品购物车数量
     * @param storeId
     * @param commodityIdList
     * @return
     */
    public Map<Long,BigDecimal> queryShopCartQuantityMapV2(Long storeId,List<Long> commodityIdList,Integer commodityType){
        List<XdaShoppingCartODTO> shoppingCartODTOList = xdaCommodityFrontMapper.queryXdaShoppingCartQuantityV2(storeId,commodityIdList,commodityType);
        if(CollectionUtils.isEmpty(shoppingCartODTOList)){
            return Collections.EMPTY_MAP;
        }
        Map<Long, BigDecimal> quantityMap = new HashMap<>();
        shoppingCartODTOList.stream().collect(groupingBy(XdaShoppingCartODTO::getCommodityId)).forEach((k,v)->{
            if(CollectionUtils.isEmpty(v)){
                return;
            }
            quantityMap.put(k,v.get(0).getQuantity());
        });
        return quantityMap;
    }

    /**
     * 处理系列品
     * @param detailAppODTO
     * @param detailAppIDTO
     */
    private void processSerialCommodityDetail(XdaCommodityDetailAppV2ODTO detailAppODTO,XdaCommodityDetailAppV2IDTO detailAppIDTO){
        if(detailAppODTO.getXdaSecondCategoryId()==null){
            return;
        }
        List<XdaSerialCommodityODTO> serialCommodityList = xdaSerialCommodityMapper.querySerialCommodityListFront(Collections.singletonList(detailAppODTO.getCommodityId()), detailAppODTO.getXdaSecondCategoryId());
        if(CollectionUtils.isEmpty(serialCommodityList) || serialCommodityList.stream().noneMatch(item->item.getIsMain()==1) || serialCommodityList.size()<2){
            XdaSerialCommodityDetailV2ODTO serialDetail = XdaSerialCommodityDetailV2ODTO.convert(detailAppODTO);
            serialDetail.setIsCurrentCommodity(1);
            detailAppODTO.setSerialCommodityDetailList(Collections.singletonList(serialDetail));
            return;
        }
        List<XdaCommodityAppV2ODTO> appODTOList = new ArrayList<>();
        appODTOList.add(detailAppODTO);
        List<Long> serialCommodityIdList = serialCommodityList.stream()
                .filter(item->!item.getCommodityId().equals(detailAppODTO.getCommodityId()))
                .map(XdaSerialCommodityODTO::getCommodityId).collect(Collectors.toList());
        XdaCommodityAppV2IDTO appIDTO = XdaCommodityAppV2IDTO.builder().orderTime(detailAppIDTO.getOrderTime())
                .storeId(detailAppIDTO.getStoreId()).commodityIdList(serialCommodityIdList)
                .needDefaultImage(false).build();
        List<XdaCommodityAppV2ODTO> serialList = this.queryXdaCommodityDetailsListForApp(appIDTO,null);
        if(CollectionUtils.isNotEmpty(serialList)){
            appODTOList.addAll(serialList);
            detailAppODTO.setIsSerial(1);
        }
        List<XdaSerialCommodityDetailV2ODTO> serialCommodityODTOList = appODTOList.stream().map(XdaSerialCommodityDetailV2ODTO::convert)
                .sorted(Comparator.comparing(XdaSerialCommodityDetailV2ODTO::getSortPrice)).collect(Collectors.toList());
        serialCommodityODTOList.forEach(item-> {
            if (item.getCommodityId().equals(detailAppODTO.getCommodityId())) {
                item.setIsCurrentCommodity(1);
            }else{
                item.setIsCurrentCommodity(0);
            }
        });
        detailAppODTO.setSerialCommodityDetailList(serialCommodityODTOList);
    }


    /**
     * 获取收藏状态
     * @param detailAppODTO
     * @param commodityId
     * @param storeId
     */
    private void queryXdaCommodityCollect(XdaCommodityDetailAppV2ODTO detailAppODTO,Long commodityId,Long storeId){
        Example example = new Example(XdaCommodityCollect.class);
        example.createCriteria().andEqualTo("commodityId",commodityId).andEqualTo("storeId",storeId);
        List<XdaCommodityCollect> collectList = xdaCommodityCollectMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(collectList)){
            detailAppODTO.setIsCollect(0);
        }else{
            detailAppODTO.setIsCollect(1);
        }
    }

    //设置商品详情页：banner、长图
    private void setXdaCommodityImage(XdaCommodityDetailAppV2ODTO detailAppODTO,Long commodityId){
        List<XdaCommodityTextPic> picList = xdaCommodityTextPicMapper.selectCommodityTextPicList(new SelectCommodityTextPicListIDTO(commodityId));
        if(CollectionUtils.isEmpty(picList)){
            return;
        }
        PicSizeEnums picSizeEnums = PicSizeEnums.PIC_750x750;
        picList.stream().collect(Collectors.groupingBy(XdaCommodityTextPic::getPicType)).forEach((picK,picV)->{
            if(CollectionUtils.isEmpty(picV)){
                return;
            }
            if(picK.intValue()== XdaCommodityTextPic.PicTypeEnums.PIC.getCode() ){
                List<String> imageUrlList = picV.stream().map(pic -> imgServerUrl +(ImageUtils.getXdImgUrlV2(pic.getPicUrl(),picSizeEnums.getSize()))).collect(Collectors.toList());
                detailAppODTO.setImageUrlList(imageUrlList);
            }
            if(picK.intValue()== XdaCommodityTextPic.PicTypeEnums.LONG_PIC.getCode() ){
                detailAppODTO.setLongPicList(this.querySplitLongPicUrlList(picV.get(0).getPicUrl()));
            }
        });
    }

    //查询长图
    private List<XdaCommodityLongPicODTO> querySplitLongPicUrlList(String longPic){
        //<长图原始路径，长图切割后的list>
        List<ImageLibraryODTO> imageODTOList = imageLibraryClient.findSingleImgAnyCondition(longPic, PicCutTypeEnum.CUT_BY_HEIGHT, PicSizeEnums.PIC_750);
        if(CollectionUtils.isNotEmpty(imageODTOList)) {
            return imageODTOList.stream().map(XdaCommodityLongPicODTO::convert).collect(Collectors.toList());
        }
        return Collections.EMPTY_LIST;
    }

}
