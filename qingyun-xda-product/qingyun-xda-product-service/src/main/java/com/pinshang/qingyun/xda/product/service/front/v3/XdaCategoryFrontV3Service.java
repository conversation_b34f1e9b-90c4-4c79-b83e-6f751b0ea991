package com.pinshang.qingyun.xda.product.service.front.v3;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.enums.CategoryLevelEnums;
import com.pinshang.qingyun.base.enums.pic.PicSizeEnums;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.NumberUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.order.service.XDAShoppingCartClient;
import com.pinshang.qingyun.store.service.StoreManageClient;
import com.pinshang.qingyun.xda.product.constant.XdaConstant;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaReCommendAppODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaCategoryBaseODTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaSearchAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.categoryFront.v3.*;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.FromPageEnums;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityAppV3IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityAppV3ODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaSerialCommodityDetailV3ODTO;
import com.pinshang.qingyun.xda.product.mapper.XdaCategoryFrontMapper;
import com.pinshang.qingyun.xda.product.model.orderTargetSet.XdaOrderTargetSet;
import com.pinshang.qingyun.xda.product.model.specialsCommoditySet.XdaSpecialsCommoditySet;
import com.pinshang.qingyun.xda.product.service.StoreService;
import com.pinshang.qingyun.xda.product.service.XdaOrderTargetSetService;
import com.pinshang.qingyun.xda.product.service.XdaSpecialsCommoditySetService;
import com.pinshang.qingyun.xda.product.service.front.XdaCommodityFrontService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

@Slf4j
@Service
public class XdaCategoryFrontV3Service {

	@Autowired
	private XDAShoppingCartClient xdaShoppingCartClient;
	 
    @Autowired
    private XdaCategoryFrontMapper xdaCategoryFrontMapper;
    
    @Autowired
    private XdaCommodityFrontService xdaCommodityFrontService;
    
    @Autowired
    private XdaOrderTargetSetService xdaOrderTargetSetService;
    
    @Autowired
    private XdaCommodityFrontV3Service xdaCommodityFrontV3Service;
    
    @Autowired
    private XdaSpecialsCommoditySetService xdaSpecialsCommoditySetService;
    @Autowired
    private StoreService storeService;

    /**
     * 查询分类
     * 
     * @param orderTime
     * @param storeId
     * @return
     */
    public XdaCategoryResV3ODTO queryXdaCategoryList(Date orderTime, Long storeId) {
    	long beginTime = System.currentTimeMillis();
        /**根据客户id和分类等级 -- 查询所有一级分类**/
        List<XdaCategoryBaseODTO> firstList = xdaCategoryFrontMapper.queryXdaCategoryList(null,storeId, CategoryLevelEnums.FIRST.getCode());
        if(CollectionUtils.isEmpty(firstList)){
            return null;
        }
        /**根据所有一级分类id集合,客户id 查询所有二级分类**/
        List<Long> firstIdList = firstList.stream().map(XdaCategoryBaseODTO::getXdaCategoryId).collect(Collectors.toList());
        List<XdaCategoryBaseODTO> allSecondList = xdaCategoryFrontMapper.queryXdaCategoryList(firstIdList,storeId, CategoryLevelEnums.SECOND.getCode());
        if(CollectionUtils.isEmpty(allSecondList)){
            return null;
        }
        Map<Long,List<XdaCategoryBaseODTO>> secondMap = allSecondList.stream().collect(Collectors.groupingBy(XdaCategoryBaseODTO::getParentId, LinkedHashMap::new,Collectors.toList()));

        /**查找一级分类下的推荐商品**/
        Map<Long,List<Long>> recommendMap = this.getRecommendMap(firstIdList,storeId);

        //返回对象声明
        List<XdaCategoryV3ODTO> xdaCategoryV3ODTOList = new ArrayList<>();
        XdaCategoryCommodityResV3ODTO xdaCategoryCommodityResV3ODTO = new XdaCategoryCommodityResV3ODTO();

        Boolean flag = false;

        for(XdaCategoryBaseODTO firstCategory : firstList){
            Long firstCategoryId = firstCategory.getXdaCategoryId();
            List<XdaCategoryBaseODTO> secondList = secondMap.get(firstCategoryId);
            if(CollectionUtils.isEmpty(secondList)){
                continue;
            }
            /**判断当前一级分类下是否有推荐商品, 如果有的话 显示一个二级分类: 推荐**/
            if(SpringUtil.isNotEmpty(recommendMap) && CollectionUtils.isNotEmpty(recommendMap.get(firstCategoryId))){
                secondList.add(0,XdaCategoryBaseODTO.buildRecommendLabel(firstCategoryId));
            }
            if(!flag){
                /**查询当前分类下的第一个二级分类下的商品列表信息**/
                XdaCategoryAppIDTO commodityAppIDTO = XdaCategoryAppIDTO.init(orderTime,storeId,firstCategory.getXdaCategoryId(),secondList.get(0).getXdaCategoryId());
                xdaCategoryCommodityResV3ODTO = this.queryXdaCategoryCommodityList(commodityAppIDTO, FromPageEnums.XDA_APP_CATEGORY_COMMODITY_LIST);
                if(xdaCategoryCommodityResV3ODTO == null || CollectionUtils.isEmpty(xdaCategoryCommodityResV3ODTO.getCommodityList())){
                    continue;
                }
                flag = true;
            }

            /**处理返回对象**/
            XdaCategoryV3ODTO firstV3ODTO = BeanCloneUtils.copyTo(firstCategory, XdaCategoryV3ODTO.class);
            List<XdaCategoryBaseV3ODTO> secondV3List = BeanCloneUtils.copyTo(secondList, XdaCategoryBaseV3ODTO.class);
            firstV3ODTO.setSecondCategoryList(secondV3List);
            xdaCategoryV3ODTOList.add(firstV3ODTO);
        }
        
        if (CollectionUtils.isEmpty(xdaCategoryV3ODTOList)) {
            return null;
        }

        //处理特殊分类: 常购清单 - 促销商品 - 特惠商品
        if (!FastThreadLocalUtil.getXDA().getIsTouristStore()) {
            xdaCategoryV3ODTOList.add(0, XdaCategoryV3ODTO.buildDefaultLabel());

            /**查询促销商品, 判断是否需要促销一级分类**/
            Map<Long, BigDecimal> specialPriceMap = xdaCommodityFrontService.queryXdaSpecialPrice(orderTime, storeId, null);
            if (SpringUtil.isNotEmpty(specialPriceMap)) {
            	xdaCategoryV3ODTOList.add(0, XdaCategoryV3ODTO.buildSpecialLabel());
            }
            //查询当天是否有特惠商品
            BigDecimal orderTargetToDay = validThCategory(storeId, orderTime);
            if(null != orderTargetToDay){
                String thCategoryTips = "";
                Boolean isThInvalidate = Boolean.FALSE;
                //查询订货目标金额-判断是否能订货特惠商品
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
                BigDecimal normalGroupAmountV3 = xdaShoppingCartClient.getNormalGroupAmountV3(format.format(orderTime), storeId);
                if(normalGroupAmountV3.compareTo(orderTargetToDay) < 0){
                    //订货金额< 目标金额
                    BigDecimal subtract = orderTargetToDay.subtract(normalGroupAmountV3).setScale(2, BigDecimal.ROUND_HALF_UP);
                    thCategoryTips = "订货目标"+ orderTargetToDay + "元, 还差"+ subtract +"元可选购特惠商品";

                }else {
                    thCategoryTips = "订货目标已满"+ orderTargetToDay + "元, 可选购特惠商品";
                    isThInvalidate = Boolean.TRUE;
                }
                xdaCategoryV3ODTOList.add(0, XdaCategoryV3ODTO.buildThLabel(thCategoryTips, isThInvalidate));
            }
        }

        log.info("\n\n\n==========>>>鲜达APP.分类：耗时={}毫秒，orderTime={}，storeId={}", (System.currentTimeMillis() - beginTime), orderTime, storeId);
        return new XdaCategoryResV3ODTO(xdaCategoryV3ODTOList, xdaCategoryCommodityResV3ODTO);
    }

    /**
     * 查询分类商品
     * 
     * @param idto
     * @return
     */
    public XdaCategoryCommodityResV3ODTO queryXdaCategoryCommodityList(XdaCategoryAppIDTO idto, FromPageEnums fromPage) {
    	long beginTime = System.currentTimeMillis();
        if(null == idto.getXdaFirstCategoryId() || null == idto.getXdaSecondCategoryId()) {
            log.error("查询分类商品，一级分类和二级分类都必传");
            return null;
        }
        
        Boolean isVisitor = FastThreadLocalUtil.getXDA().getIsTouristStore();
        List<Long> commodityIdList = null;
        // 常购清单一级分类标识
        Boolean oftenBuyFlag = idto.getXdaFirstCategoryId().equals(XdaConstant.FIRST_CATEGORY_OFTEN_BUY);
        // 促销商品一级分类标识
        Boolean specialFlag = idto.getXdaFirstCategoryId().equals(XdaConstant.FIRST_CATEGORY_SPECIAL);
        // 特惠商品一级分类标识
        Boolean thFlag = idto.getXdaFirstCategoryId().equals(XdaConstant.FIRST_CATEGORY_TH);
        // 推荐商品二级分类标识
        Boolean reommendFlag = idto.getXdaSecondCategoryId().equals(0L);

        // 声明返回商品信息对象
        List<XdaCategoryCommodityV3ODTO> commodityODTOList = null;

        if (thFlag) {
            // 查询特惠商品详情
            commodityODTOList = this.processXdaThCategoryCommodity(XdaSearchAppIDTO.convert(idto));
        } else {
            if(oftenBuyFlag || specialFlag){ //常购清单 或推荐商品
                if(isVisitor){
                    return null;
                }
                
                if (oftenBuyFlag && idto.getXdaSecondCategoryId().equals(XdaConstant.SECOND_CATEGORY_MY_OFTEN_BUY)) {
                    commodityIdList = xdaCategoryFrontMapper.queryOftenBuyCommodityIdList(idto.getStoreId());
                } else if (oftenBuyFlag && idto.getXdaSecondCategoryId().equals(XdaConstant.SECOND_CATEGORY_MY_COLLECT)) {
                    commodityIdList = xdaCategoryFrontMapper.queryCollectCommodityIdList(idto.getStoreId());
                } else if (specialFlag){
                    // 查询促销商品id集合
                    Map<Long, BigDecimal> specialPriceMap = xdaCommodityFrontService.queryXdaSpecialPrice(idto.getOrderTime(), idto.getStoreId(), null);
                    if(SpringUtil.isNotEmpty(specialPriceMap)){
                        commodityIdList = new ArrayList<>(specialPriceMap.keySet());
                    }
                }
                
                if (CollectionUtils.isEmpty(commodityIdList)) {
                    return null;
                }
            }
            if (reommendFlag) {
                List<XdaReCommendAppODTO> recommendList = xdaCategoryFrontMapper.queryXdaReCommendFront(Collections.singletonList(idto.getXdaFirstCategoryId()), idto.getStoreId());
                if (CollectionUtils.isNotEmpty(recommendList)){
                    commodityIdList = recommendList.stream().map(XdaReCommendAppODTO::getCommodityId).collect(toList());
                }
                if(CollectionUtils.isEmpty(commodityIdList)){
                    return null;
                }
            }
            //查询商品信息 和 系列品信息
            commodityODTOList = this.processXdaCategoryCommodity(XdaSearchAppIDTO.convert(idto), commodityIdList, fromPage);
        }

        if (CollectionUtils.isEmpty(commodityODTOList)) {
            return null;
        }
        log.info("\n\n\n==========>>>鲜达APP.分类商品：耗时={}毫秒，idto={}", (System.currentTimeMillis() - beginTime), idto);
        return new XdaCategoryCommodityResV3ODTO(idto.getXdaFirstCategoryId(), idto.getXdaSecondCategoryId(), commodityODTOList);
    }

    /**
     * 查询品类商品信息 和系列品信息
     * @param searchIDTO
     * @param searchCommodityIdList
     * @return
     */
    private List<XdaCategoryCommodityV3ODTO> processXdaCategoryCommodity(XdaSearchAppIDTO searchIDTO, List<Long> searchCommodityIdList, FromPageEnums fromPage) {
        /**查询分类下的商品信息**/
        List<XdaCommodityAppV3ODTO> appODTOList = this.queryXdaCommodityList(searchIDTO, searchCommodityIdList, PicSizeEnums.PIC_120x120, fromPage);
        if(CollectionUtils.isEmpty(appODTOList)){
            return null;
        }
        
        /**系列品**/
        Map<Long,List<XdaCommodityAppV3ODTO>> serialMap = appODTOList.stream().collect(Collectors.groupingBy(XdaCommodityAppV3ODTO::getSerialCommodityId,LinkedHashMap::new, Collectors.toList()));
        List<XdaCategoryCommodityV3ODTO> commodityODTOList = new ArrayList<>();
        appODTOList.forEach(appODTO -> {
            XdaCategoryCommodityV3ODTO categoryCommodityODTO = XdaCategoryCommodityV3ODTO.convert(appODTO);
            Long commodityId = appODTO.getCommodityId();
            /** 一级分类id为空/一级分类id等于常购清单/一级分类id等于特惠商品/系列品主品ID=0 的时候不考虑系列品 **/
            if(null == searchIDTO.getXdaFirstCategoryId() || searchIDTO.getXdaFirstCategoryId().equals(XdaConstant.FIRST_CATEGORY_OFTEN_BUY)
                    || searchIDTO.getXdaFirstCategoryId().equals(XdaConstant.FIRST_CATEGORY_TH) || appODTO.getSerialCommodityId() == 0 ){
                categoryCommodityODTO.setIsSerial(0);
                commodityODTOList.add(categoryCommodityODTO);
            } else {
                List<XdaCommodityAppV3ODTO> serialList = serialMap.get(appODTO.getSerialCommodityId());
                boolean isSerial = CollectionUtils.isNotEmpty(serialList) && serialList.size()>1 ;
                isSerial = isSerial && serialList.stream().anyMatch(bean-> bean.getCommodityId().equals(appODTO.getSerialCommodityId())) ;

                if (!isSerial) {
                    categoryCommodityODTO.setIsSerial(0);
                    categoryCommodityODTO.setSerialCommodityId(0L);
                    commodityODTOList.add(categoryCommodityODTO);
                    return;
                }
                if(appODTO.getSerialCommodityId().equals(commodityId)){
                    categoryCommodityODTO.setIsSerial(1);
                    // 处理系列品展示信息
                    this.processXdaSerialCommodityInfo(categoryCommodityODTO, serialList);
                    commodityODTOList.add(categoryCommodityODTO);
                }
            }
        });
        return commodityODTOList;
    }

    public PageInfo<XdaCommodityAppV3ODTO> queryXdaCommodityPageInfo(XdaSearchAppIDTO searchIDTO){
        PageInfo<XdaCommodityAppV3ODTO> pageData = PageHelper.startPage(searchIDTO.getPageNo(), searchIDTO.getPageSize()).doSelectPageInfo(() -> {
            xdaCategoryFrontMapper.queryXdaCategoryCommoditySearchV3(searchIDTO);
        });
        if(SpringUtil.isNotEmpty(pageData.getList())){
            List<XdaCommodityAppV3ODTO> xdaCommodityAppV3ODTOList = pageData.getList();
            XdaCommodityAppV3IDTO appIDTO = XdaCommodityAppV3IDTO.builder().orderTime(searchIDTO.getOrderTime())
                    .storeId(searchIDTO.getStoreId()).defaultImageSize(PicSizeEnums.PIC_346x346).build();
            if(null != searchIDTO.getXdaFirstCategoryId()){
                appIDTO.setNeedCartQuantity(true);
            }
            xdaCommodityFrontV3Service.setXdaCommodityInfo(xdaCommodityAppV3ODTOList, appIDTO, FromPageEnums.XDA_APP_CATEGORY_COMMODITY_LIST);
        }
        return pageData;
    }
    /**
     * 查询鲜达商品信息集合
     * 
     * @param searchIDTO
     * @param searchCommodityIdList
     * @param picSizeEnums
     * @return
     */
    private List<XdaCommodityAppV3ODTO> queryXdaCommodityList(XdaSearchAppIDTO searchIDTO, List<Long> searchCommodityIdList, PicSizeEnums picSizeEnums, FromPageEnums fromPage) {
        if(CollectionUtils.isNotEmpty(searchCommodityIdList)){
            searchIDTO.setCommodityIdList(searchCommodityIdList);
        }

        Boolean isPfsStore = storeService.isPfsStore(searchIDTO.getStoreId());
        searchIDTO.setIsPfsStore(isPfsStore);
        List<XdaCommodityAppODTO> appODTOList = xdaCategoryFrontMapper.queryXdaCategoryCommoditySearch(searchIDTO);
        if(CollectionUtils.isEmpty(appODTOList)){
            return null;
        }
        List<XdaCommodityAppV3ODTO> xdaCommodityAppV3ODTOList = BeanCloneUtils.copyTo(appODTOList,XdaCommodityAppV3ODTO.class);

        XdaCommodityAppV3IDTO appIDTO = XdaCommodityAppV3IDTO.builder().orderTime(searchIDTO.getOrderTime())
                .storeId(searchIDTO.getStoreId()).defaultImageSize(picSizeEnums).build();
        if(null != searchIDTO.getXdaFirstCategoryId()){
            appIDTO.setNeedCartQuantity(true);
        }
        xdaCommodityFrontV3Service.setXdaCommodityInfo(xdaCommodityAppV3ODTOList, appIDTO, fromPage);

        return xdaCommodityAppV3ODTOList;
    }

    /**
     * 处理系列品展示信息
     * @param categoryCommodityODTO
     * @param serialCommodityInfoList
     * @return
     */
    public void processXdaSerialCommodityInfo(XdaCategoryCommodityV3ODTO categoryCommodityODTO, List<XdaCommodityAppV3ODTO> serialCommodityInfoList){
        if(CollectionUtils.isEmpty(serialCommodityInfoList)){
            return;
        }
        List<XdaCommodityAppV3ODTO> sortList = serialCommodityInfoList.stream().sorted(Comparator.comparing(XdaCommodityAppV3ODTO::getSortPrice)).collect(Collectors.toList());
        Set<String> specList = new LinkedHashSet<>();
        Set<String> priceList = new LinkedHashSet<>();
        List<XdaSerialCommodityDetailV3ODTO> serialDetailList = new ArrayList<>();
        for (int i = 0; i < sortList.size(); i ++) {
            specList.add(sortList.get(i).getCommoditySpec());
            if(i == 0 || i == sortList.size() - 1) {
                priceList.add(NumberUtil.format_2_roundHalfUp(sortList.get(i).getSortPrice()));
            }
            serialDetailList.add(XdaSerialCommodityDetailV3ODTO.convert(sortList.get(i)));
        }
        categoryCommodityODTO.setSerialCommoditySpec(String.join("|", specList));
        categoryCommodityODTO.setSerialCommodityPrice(String.join("-", priceList));
        categoryCommodityODTO.setSerialCommodityDetailList(serialDetailList);
    }
    
    /**
     * get 推荐商品
     * @param firstIdList
     * @param storeId
     * @return
     */
    private Map<Long,List<Long>> getRecommendMap(List<Long> firstIdList,Long storeId){
        Map<Long,List<Long>> recommendMap = new HashMap<>();
        List<XdaReCommendAppODTO> recommendList = xdaCategoryFrontMapper.queryXdaReCommendFront(firstIdList,storeId);
        if (CollectionUtils.isNotEmpty(recommendList)){
            recommendList.stream().collect(groupingBy(XdaReCommendAppODTO::getFirstCateId)).forEach((k,v)->{
                recommendMap.put(k,v.stream().map(XdaReCommendAppODTO::getCommodityId).collect(toList()));
            });
        }
        return recommendMap;
    }

    /**
     * 验证特惠分类
     * @param storeId
     * @param orderTime
     */
    public BigDecimal validThCategory(Long storeId, Date orderTime){
        XdaOrderTargetSet orderTarget = xdaOrderTargetSetService.findOrderTargetByStoreIdAndOrderTime(storeId, orderTime);
        if(null != orderTarget){
            //查询符合条件的特惠商品集合
            List<XdaSpecialsCommoditySet> commodityIdList = xdaSpecialsCommoditySetService.findCommodityListByStoreAndOrderTime(storeId);
            if(SpringUtil.isNotEmpty(commodityIdList)){
                return orderTarget.getOrderTargetToDay();
            }
        }
        return null;
    }

    /**
     * 查询特惠分类下商品信息
     * @param searchIDTO
     * @return
     */
    private List<XdaCategoryCommodityV3ODTO> processXdaThCategoryCommodity(XdaSearchAppIDTO searchIDTO){
        List<XdaCommodityAppV3ODTO> appODTOList = xdaCategoryFrontMapper.queryXdaThCategoryCommodityV3(searchIDTO);
        if(SpringUtil.isEmpty(appODTOList)){
            return Collections.emptyList();
        }

        //设置参数
        XdaCommodityAppV3IDTO appIDTO = XdaCommodityAppV3IDTO.builder().orderTime(searchIDTO.getOrderTime()).storeId(searchIDTO.getStoreId())
                .defaultImageSize(PicSizeEnums.PIC_120x120).build();
        if (searchIDTO.getXdaFirstCategoryId() != null) {
            appIDTO.setNeedCartQuantity(true);
        }
        xdaCommodityFrontV3Service.setXdaThCommodityInfo(appODTOList, appIDTO);

        List<XdaCategoryCommodityV3ODTO> commodityODTOList = new ArrayList<>();
        for (XdaCommodityAppV3ODTO appODTO : appODTOList) {
            XdaCategoryCommodityV3ODTO categoryCommodityODTO = XdaCategoryCommodityV3ODTO.convert(appODTO);
            // 非系列, 是特惠
            categoryCommodityODTO.setIsSerial(0);
            categoryCommodityODTO.setIsThPrice(1);
            commodityODTOList.add(categoryCommodityODTO);
        }
        return commodityODTOList;
    }

    public XdaThTipsV3ODTO getXdaThTipsV3ODTO(String orderTime,Long storeId){
        if(null == orderTime || null == storeId){
            return XdaThTipsV3ODTO.buildSpecialLabel();
        }
        //查询当天是否有特惠商品
        BigDecimal orderTargetToDay = validThCategory(storeId, DateUtil.parseDate(orderTime, "yyyy-MM-dd"));
        if(null != orderTargetToDay){
            String thCategoryTips = "";
            Boolean isThInvalidate = Boolean.FALSE;
            //查询订货目标金额-判断是否能订货特惠商品
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            BigDecimal normalGroupAmountV3 = xdaShoppingCartClient.getNormalGroupAmountV3(orderTime, storeId);
            if(normalGroupAmountV3.compareTo(orderTargetToDay) < 0){
                //订货金额< 目标金额
                BigDecimal subtract = orderTargetToDay.subtract(normalGroupAmountV3).setScale(2, BigDecimal.ROUND_HALF_UP);
                thCategoryTips = "订货目标"+ orderTargetToDay + "元, 还差"+ subtract +"元可选购特惠商品";

            }else {
                thCategoryTips = "订货目标已满"+ orderTargetToDay + "元, 可选购特惠商品";
                isThInvalidate = Boolean.TRUE;
            }
            return XdaThTipsV3ODTO.buildSpecialLabelALL(thCategoryTips,isThInvalidate);
        }
        return XdaThTipsV3ODTO.buildSpecialLabel();
    }
}
