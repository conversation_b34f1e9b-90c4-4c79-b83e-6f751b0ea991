package com.pinshang.qingyun.xda.product.service.front.v3;

import static java.util.stream.Collectors.groupingBy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import tk.mybatis.mapper.entity.Example;

import com.pinshang.qingyun.base.enums.TagEnums;
import com.pinshang.qingyun.base.enums.marketing.MarketSourceTypeEnum;
import com.pinshang.qingyun.base.enums.pic.PicCutTypeEnum;
import com.pinshang.qingyun.base.enums.pic.PicSizeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.ImageUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.ImageLibraryODTO;
import com.pinshang.qingyun.common.service.ImageLibraryClient;
import com.pinshang.qingyun.marketing.dto.app.CommodityCategoryIDTO;
import com.pinshang.qingyun.marketing.dto.app.CommodityDetailIDTO;
import com.pinshang.qingyun.marketing.dto.app.CommodityDetailODTO;
import com.pinshang.qingyun.marketing.dto.app.CommodityODTO;
import com.pinshang.qingyun.marketing.dto.app.CommodityPromotionIDTO;
import com.pinshang.qingyun.marketing.dto.app.TagODTO;
import com.pinshang.qingyun.marketing.service.MtPromotionClient;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextPicListIDTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextTagInfoListIDTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDeliveryTimeODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityLimitODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityLongPicODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaSerialCommodityODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaShoppingCartODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.CommodityPromotionODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.FromPageEnums;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityAppV3IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityAppV3ODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityDetailAppV3IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityDetailAppV3ODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaSerialCommodityDetailV3ODTO;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityCollectMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityFrontMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityTextMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaCommodityTextPicMapper;
import com.pinshang.qingyun.xda.product.mapper.XdaSerialCommodityMapper;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityFreezeGroupMapper;
import com.pinshang.qingyun.xda.product.model.XdaCommodityCollect;
import com.pinshang.qingyun.xda.product.model.XdaCommodityTextPic;
import com.pinshang.qingyun.xda.product.model.common.CommodityFreezeGroup;
import com.pinshang.qingyun.xda.product.model.specialsCommoditySet.XdaSpecialsCommoditySet;
import com.pinshang.qingyun.xda.product.service.XdaOrderTargetSetService;
import com.pinshang.qingyun.xda.product.service.XdaSpecialsCommoditySetService;
import com.pinshang.qingyun.xda.product.service.front.XdaCommodityFrontService;

@Slf4j
@Service
public class XdaCommodityFrontV3Service {

	@Autowired
    private MtPromotionClient mtPromotionClient;
	
	@Autowired
    private ImageLibraryClient imageLibraryClient;
	
	@Autowired
    private XdaCommodityTextMapper xdaCommodityTextMapper;
	
	@Autowired
    private XdaCommodityFrontMapper xdaCommodityFrontMapper;
	
    @Autowired
    private XdaCommodityFrontService xdaCommodityFrontService;

    @Autowired
    private XdaOrderTargetSetService xdaOrderTargetSetService;
    
    @Autowired
    private XdaSerialCommodityMapper xdaSerialCommodityMapper;
    
    @Autowired
    private XdaCommodityCollectMapper xdaCommodityCollectMapper;
    
    @Autowired
    private XdaCommodityTextPicMapper xdaCommodityTextPicMapper;
    
    @Autowired
    private CommodityFreezeGroupMapper commodityFreezeGroupMapper;

    @Autowired
    private XdaSpecialsCommoditySetService xdaSpecialsCommoditySetService;

    @Value("${pinshang.img-xd-server-url}")
    private String imgServerUrl;

    /**
     * set商品信息
     * @param appODTOList
     * @param appIDTO
     */
    public void setXdaCommodityInfo(List<XdaCommodityAppV3ODTO> appODTOList, XdaCommodityAppV3IDTO appIDTO, FromPageEnums fromPage){
        if(CollectionUtils.isEmpty(appODTOList)){
            return;
        }
        Long storeId = appIDTO.getStoreId();
        Date orderTime = appIDTO.getOrderTime();
        List<Long> commodityIdList = appODTOList.stream().map(XdaCommodityAppV3ODTO::getCommodityId).collect(Collectors.toList());
        
//        // 特价
//        Map<Long, BigDecimal> priceMap = Collections.emptyMap();
//        if(appIDTO.getNeedSpecialPrice()){
//            priceMap = xdaCommodityFrontService.queryXdaSpecialPrice(orderTime,storeId,new ArrayList<>(commodityIdList));
//        }

//        // 促销
//        Map<Long, List<XdaStorePromotionODTO>> promotionMap = Collections.emptyMap();
//        if(appIDTO.getNeedPromotion()){
//            promotionMap = xdaCommodityFrontService.queryXdaPromotion(orderTime,storeId,new ArrayList<>(commodityIdList));
//        }
        
        /**
         * 特价、促销（买赠、梯度满折，以及之后满减）
         * 
         * 鲜达APP分类商品页，存在按价格排序的功能，且SQL超级复杂，
         * 有鉴于此，以及特价尚未真正独立库，所以本期不准备改变SQL（即查询排序仍按原SQL）
         * 
         * 注：待特价、促销都迁移走之后，特价和促销将全部通过Client拉取，同时鲜达APP分类商品页，将不能按照价格排序。
         */
        Map<Long, CommodityODTO> commodityPromotionMap = Collections.emptyMap();
        if (FromPageEnums.XDA_APP_CATEGORY_COMMODITY_LIST.equals(fromPage)) {
        	commodityPromotionMap = this.getCommodityPromotionMap(appIDTO, appODTOList);
        }

        // 自定义标签
        Map<Long, List<CommodityTextTagInfoODTO>> tagMap = Collections.emptyMap();
        if(appIDTO.getNeedTag()){
            List<CommodityTextTagInfoODTO> tagList = xdaCommodityTextMapper.selectCommodityTextTagInfoList(new SelectCommodityTextTagInfoListIDTO(commodityIdList));
            tagMap = tagList.stream().collect(Collectors.groupingBy(CommodityTextTagInfoODTO::getCommodityId));
        }

        // 是否可订货
        Map<Long, XdaCommodityDeliveryTimeODTO> deliveryTimeMap = Collections.emptyMap();
        if(appIDTO.getNeedCanOrder()){
            deliveryTimeMap = xdaCommodityFrontService.queryXdaCommodityDeliveryTime(orderTime,storeId, new ArrayList<>(commodityIdList));
        }

        // 商品限量
        Map<Long, XdaCommodityLimitODTO> limitQuantityMap = Collections.emptyMap();
        if(appIDTO.getNeedLimit()){
            limitQuantityMap = xdaCommodityFrontService.queryXdaCommodityLimit(orderTime,new ArrayList<>(commodityIdList));
        }

        // 购物车数量
        Map<Long,BigDecimal> shopCartQuantityMap = null;
        if(appIDTO.getNeedCartQuantity()){
            shopCartQuantityMap = this.queryShopCartQuantityMapV2(appIDTO.getStoreId(),commodityIdList, 1);
        }

        // 凑整商品集合
        List<Long> freezeGroupList = this.queryFreezeGroupMap(commodityIdList);

        for (XdaCommodityAppV3ODTO appODTO : appODTOList) {
        	Long commodityId = appODTO.getCommodityId();
        	
            // 标签合集-列表页：1-自定义标签、2-特价、3-促销（、4-凑整、5-速冻）、6-限量
            List<CommodityTextTagInfoODTO> listTagList = new ArrayList<>();
        	// 标签合集-详情页：1-自定义标签
            List<CommodityTextTagInfoODTO> tagList = tagMap.get(commodityId);
            // 标签合集-详情页：2-特价、4-凑整、5-速冻、6-限量
            List<CommodityTextTagInfoODTO> tagV2List = new ArrayList<>();
            
            // 自定义标签
            if (SpringUtil.isNotEmpty(tagList)) {
            	listTagList.addAll(tagList);
            }
            
            // 图片
            if(appIDTO.getNeedDefaultImage() && StringUtils.isNotEmpty(appODTO.getImageUrl())){
                appODTO.setImageUrl(imgServerUrl+ ImageUtils.getXdImgUrlV2(appODTO.getImageUrl(),appIDTO.getDefaultImageSize()==null?null:appIDTO.getDefaultImageSize().getSize()));
            }
            
            // 特价
//            if(priceMap!=null && priceMap.get(commodityId)!=null){
//                BigDecimal specialPrice = priceMap.get(commodityId);
//                if(appODTO.getCommodityPrice()!=null && appODTO.getCommodityPrice().compareTo(specialPrice)>0){
//                    appODTO.setIsSpecialPrice(1);
//                    appODTO.setSpecialPrice(priceMap.get(commodityId));
//                    tagV2List.add(new CommodityTextTagInfoODTO("特价", "#FF5733", commodityId));
//
//                }
//            }
            
            // 促销
//   		if (promotionMap != null && CollectionUtils.isNotEmpty(promotionMap.get(commodityId))) {
//      		appODTO.setIsPromotion(1);
//        		appODTO.setPromotionList(promotionMap.get(commodityId));
//      	}
            
            // 特价、促销（买赠、梯度满折，以及之后满减）
            CommodityODTO promotionCommodity = commodityPromotionMap.get(commodityId);
            if (null != promotionCommodity) {
            	List<TagODTO> promotionTagList = promotionCommodity.getTagODTOList();
            	if (SpringUtil.isNotEmpty(promotionTagList)) {
            		for (TagODTO thisTag: promotionTagList) {
            			if (TagEnums.SPECIAL.getType().equals(thisTag.getType())) {
            				if (appODTO.getCommodityPrice() != null && null != promotionCommodity.getSpecialPrice() && appODTO.getCommodityPrice().compareTo(promotionCommodity.getSpecialPrice()) > 0) {
            					appODTO.setIsSpecialPrice(1);
                                appODTO.setSpecialPrice(promotionCommodity.getSpecialPrice());
                                CommodityTextTagInfoODTO specialTag = new CommodityTextTagInfoODTO("特价", "#FF5733", commodityId);
                                
                                listTagList.add(specialTag);
                                tagV2List.add(specialTag);
            				}
            			} else if (TagEnums.PROMOTION.getType().equals(thisTag.getType())) {
            				if (null != thisTag.getName()) {
//            					appODTO.setIsPromotion(1);
            					CommodityTextTagInfoODTO promotionTag = new CommodityTextTagInfoODTO(thisTag.getName(), "#FF5733", commodityId);
            					listTagList.add(promotionTag);
            				}
            			}
            		}
            	}
            }
            
            // 送货日期范围
            if(deliveryTimeMap != null && deliveryTimeMap.get(commodityId) != null){
                XdaCommodityDeliveryTimeODTO deliveryTimeODTO = deliveryTimeMap.get(commodityId);
                appODTO.setIsCanOrder(deliveryTimeODTO.getIsCanOrder());
                appODTO.setDeliveryTimeODTO(deliveryTimeODTO);
                List<Date> xdaDeliveryTimeList = deliveryTimeODTO.getDeliveryDateList();
                if(CollectionUtils.isNotEmpty(xdaDeliveryTimeList)){
                    appODTO.setBeginDeliveryTime(xdaDeliveryTimeList.get(0));
                    appODTO.setEndDeliveryTime(xdaDeliveryTimeList.get(xdaDeliveryTimeList.size()-1));
                }
            }
            
            // 凑整
            if(freezeGroupList.contains(commodityId)){
                appODTO.setIsFreezeRounding(1);
                appODTO.setIsFreezeRoundingMultiple(30);
                
                CommodityTextTagInfoODTO czTag = new CommodityTextTagInfoODTO("凑整", "#FF5733", commodityId);
//                listTagList.add(czTag);
                tagV2List.add(czTag);
            }

            // 限量
            if(limitQuantityMap!=null && limitQuantityMap.get(commodityId)!=null){
                XdaCommodityLimitODTO xdaCommodityLimitODTO = limitQuantityMap.get(commodityId);
                appODTO.setIsLimit(1);
                appODTO.setLimitNumber(xdaCommodityLimitODTO.getLimitNumber());
                appODTO.setLimitStartTime(xdaCommodityLimitODTO.getBeginTime());
                appODTO.setLimitEndTime(xdaCommodityLimitODTO.getEndTime());
                
                CommodityTextTagInfoODTO xlTag = new CommodityTextTagInfoODTO("限量", "#FF5733", commodityId);
                listTagList.add(xlTag);
                tagV2List.add(xlTag);
            }
            
            // 购物车数量
            if(shopCartQuantityMap!=null && shopCartQuantityMap.get(commodityId)!=null){
                appODTO.setShoppingCartQuantity(shopCartQuantityMap.get(commodityId));
            }
            
            // 设置标签
            appODTO.setListTagList(listTagList);
            appODTO.setTagList(tagList);
            appODTO.setTagV2List(tagV2List);
        }
    }
    
    public Map<Long, CommodityODTO> getCommodityPromotionMap(XdaCommodityAppV3IDTO appIDTO, List<XdaCommodityAppV3ODTO> appODTOList) {
    	/**
         * 特价、促销（买赠、梯度满折，以及之后满减）
         * 
         * 鲜达APP分类商品页，存在按价格排序的功能，且SQL超级复杂，
         * 有鉴于此，以及特价尚未真正独立库，所以本期不准备改变SQL（即查询排序仍按原SQL）
         * 
         * 注：待特价、促销都迁移走之后，特价和促销将全部通过Client拉取，同时鲜达APP分类商品页，将不能按照价格排序。
         */
    	List<CommodityCategoryIDTO> commodityCategoryIDTOList = appODTOList.stream().map(o-> {
    		CommodityCategoryIDTO cci = new CommodityCategoryIDTO();
    		cci.setCommodityId(o.getCommodityId());
    		cci.setCategoryId(o.getXdaSecondCategoryId());
    		cci.setIsWeight(o.getIsWeight());
    		return cci;
    	}).collect(Collectors.toList());
       
       CommodityPromotionIDTO cpIDTO = new CommodityPromotionIDTO();
       cpIDTO.setChannelType(MarketSourceTypeEnum.XDA.getCode());
       cpIDTO.setShopId(appIDTO.getStoreId());
       cpIDTO.setOrderTime(appIDTO.getOrderTime());
       // cpIDTO.setUserId(userId);
       cpIDTO.setCommodityCategoryIDTOList(commodityCategoryIDTOList);
       
       long beginTime = System.currentTimeMillis();
       Map<Long, CommodityODTO> commodityPromotionMap = mtPromotionClient.queryCommodityPromotion(cpIDTO);
       log.info("\n\n\n==========>>>鲜达APP.调用mtPromotionClient.queryCommodityPromotion：耗时={}毫秒，入参={}", (System.currentTimeMillis() - beginTime), cpIDTO);
       if (null == commodityPromotionMap) {
    	   return Collections.emptyMap();
       }
       
       return commodityPromotionMap;
    }

    /**
     * set鲜达特惠商品信息
     * @param appODTOList
     * @param appIDTO
     */
    public void setXdaThCommodityInfo(List<XdaCommodityAppV3ODTO> appODTOList, XdaCommodityAppV3IDTO appIDTO){
        if(CollectionUtils.isEmpty(appODTOList)){
            return;
        }
        Long storeId = appIDTO.getStoreId();
        Date orderTime = appIDTO.getOrderTime();
        List<Long> commodityIdList = appODTOList.stream().map(XdaCommodityAppV3ODTO::getCommodityId).collect(Collectors.toList());

        // 自定义标签
        Map<Long, List<CommodityTextTagInfoODTO>> tagMap = Collections.emptyMap();
        if(appIDTO.getNeedTag()){
            List<CommodityTextTagInfoODTO> tagList = xdaCommodityTextMapper.selectCommodityTextTagInfoList(new SelectCommodityTextTagInfoListIDTO(commodityIdList));
            tagMap = tagList.stream().collect(Collectors.groupingBy(CommodityTextTagInfoODTO::getCommodityId));
        }

        // 是否可订货
        Map<Long, XdaCommodityDeliveryTimeODTO> deliveryTimeMap = Collections.emptyMap();
        if(appIDTO.getNeedCanOrder()){
            deliveryTimeMap = xdaCommodityFrontService.queryXdaCommodityDeliveryTime(orderTime,storeId, new ArrayList<>(commodityIdList));
        }

        // 商品限量
        Map<Long, XdaCommodityLimitODTO> limitQuantityMap = Collections.emptyMap();
        if(appIDTO.getNeedLimit()){
            limitQuantityMap = xdaCommodityFrontService.queryXdaCommodityLimit(orderTime,new ArrayList<>(commodityIdList));
        }

        // 购物车数量
        Map<Long,BigDecimal> shopCartQuantityMap = Collections.emptyMap();
        if(appIDTO.getNeedCartQuantity()){
            shopCartQuantityMap = this.queryShopCartQuantityMapV2(appIDTO.getStoreId(),commodityIdList,2);
        }

        // 凑整商品集合
        List<Long> freezeGroupList = this.queryFreezeGroupMap(commodityIdList);

        for (XdaCommodityAppV3ODTO appODTO : appODTOList) {
        	Long commodityId = appODTO.getCommodityId();
        	
            // 标签合集-列表页：1-自定义标签、2-特价、3-促销（、4-凑整、5-速冻）、6-限量
            List<CommodityTextTagInfoODTO> listTagList = new ArrayList<>();
        	// 标签合集-详情页：1-自定义标签
            List<CommodityTextTagInfoODTO> tagList = tagMap.get(commodityId);
            // 标签合集-详情页：2-特价、4-凑整、5-速冻、6-限量
            List<CommodityTextTagInfoODTO> tagV2List = new ArrayList<>();
            
            // 自定义标签，合并到其他标签中
            if (SpringUtil.isNotEmpty(tagList)) {
            	listTagList.addAll(tagList);
            }
            
            CommodityTextTagInfoODTO thTag = new CommodityTextTagInfoODTO("特惠", "#FF5733", commodityId);
            listTagList.add(thTag);
            tagV2List.add(thTag);
            
            // 图片
            if(appIDTO.getNeedDefaultImage() && StringUtils.isNotEmpty(appODTO.getImageUrl())){
                appODTO.setImageUrl(imgServerUrl+ ImageUtils.getXdImgUrlV2(appODTO.getImageUrl(),appIDTO.getDefaultImageSize()==null?null:appIDTO.getDefaultImageSize().getSize()));
            }

            // 送货日期范围
            if(deliveryTimeMap!=null && deliveryTimeMap.get(commodityId)!=null){
                XdaCommodityDeliveryTimeODTO deliveryTimeODTO = deliveryTimeMap.get(commodityId);
                appODTO.setIsCanOrder(deliveryTimeODTO.getIsCanOrder());
                appODTO.setDeliveryTimeODTO(deliveryTimeODTO);
                List<Date> xdaDeliveryTimeList = deliveryTimeODTO.getDeliveryDateList();
                if(CollectionUtils.isNotEmpty(xdaDeliveryTimeList)){
                    appODTO.setBeginDeliveryTime(xdaDeliveryTimeList.get(0));
                    appODTO.setEndDeliveryTime(xdaDeliveryTimeList.get(xdaDeliveryTimeList.size()-1));
                }
            }
            
            // 凑整
            if(freezeGroupList.contains(commodityId)){
                appODTO.setIsFreezeRounding(1);
                appODTO.setIsFreezeRoundingMultiple(30);
                
                CommodityTextTagInfoODTO czTag = new CommodityTextTagInfoODTO("凑整", "#FF5733", commodityId);
                // listTagList.add(czTag); // 凑整有图标了，这里就不显示文字了
                tagV2List.add(czTag);
            }

            // 限量
            if(limitQuantityMap!=null && limitQuantityMap.get(commodityId)!=null){
                XdaCommodityLimitODTO xdaCommodityLimitODTO = limitQuantityMap.get(commodityId);
                appODTO.setIsLimit(1);
                appODTO.setLimitNumber(xdaCommodityLimitODTO.getLimitNumber());
                appODTO.setLimitStartTime(xdaCommodityLimitODTO.getBeginTime());
                appODTO.setLimitEndTime(xdaCommodityLimitODTO.getEndTime());
                
                CommodityTextTagInfoODTO xlTag = new CommodityTextTagInfoODTO("限量", "#FF5733", commodityId);
                listTagList.add(xlTag);
                tagV2List.add(xlTag);
            }
            
            // 购物车数量
            if(shopCartQuantityMap!=null && shopCartQuantityMap.get(commodityId)!=null){
                appODTO.setShoppingCartQuantity(shopCartQuantityMap.get(commodityId));
            }
            
            // 设置标签
            appODTO.setListTagList(listTagList);
            appODTO.setTagList(tagList);
            appODTO.setTagV2List(tagV2List);
        }
    }

    /**
     * 查询商品是否凑整商品
     * @param commodityIdList
     * @return
     */
    public List<Long> queryFreezeGroupMap(List<Long> commodityIdList){
        if(SpringUtil.isEmpty(commodityIdList)){
            return Collections.emptyList();
        }
        Example example = new Example(CommodityFreezeGroup.class);
        example.createCriteria().andIn("commodityId",commodityIdList);
        List<CommodityFreezeGroup> commodityFreezeGroups = commodityFreezeGroupMapper.selectByExample(example);

        return commodityFreezeGroups.stream().map(CommodityFreezeGroup::getCommodityId).collect(Collectors.toList());
    }

    /**
     * 鲜达商品详情
     * @param detailAppIDTO
     * @return
     */
    public XdaCommodityDetailAppV3ODTO queryXdaCommodityDetailForApp(XdaCommodityDetailAppV3IDTO detailAppIDTO){
    	long beginTime = System.currentTimeMillis();
        if (detailAppIDTO == null) {
            log.error("查询商品详情参数空异常");
            return null;
        }
        Long commodityId = detailAppIDTO.getCommodityId();
        if(commodityId == null || detailAppIDTO.getStoreId() == null || detailAppIDTO.getOrderTime() == null){
            log.error("查询商品详情参数空异常");
            return null;
        }

        // set查询商品详情参数
        XdaCommodityAppV3IDTO appIDTO = XdaCommodityAppV3IDTO.builder().orderTime(detailAppIDTO.getOrderTime())
                .storeId(detailAppIDTO.getStoreId()).commodityIdList(Collections.singletonList(commodityId))
                .defaultImageSize(PicSizeEnums.PIC_750x750).needCartQuantity(true).build();
        List<XdaCommodityAppV3ODTO> appODTOList = this.queryXdaCommodityDetailsListForApp(appIDTO, detailAppIDTO.getIsThPrice(), FromPageEnums.XDA_APP_COMMODITY_DETAIL);
        if (CollectionUtils.isEmpty(appODTOList)) {
            return null;
        }

        XdaCommodityAppV3ODTO appODTO = appODTOList.get(0);
        XdaCommodityDetailAppV3ODTO detailAppODTO = BeanCloneUtils.copyTo(appODTO, XdaCommodityDetailAppV3ODTO.class);
        // 判断商品是否特惠
        if (null !=detailAppIDTO.getIsThPrice() && detailAppIDTO.getIsThPrice().equals(1)) {
            // 设置特惠信息
           this.setXdaCommodityThPrice(detailAppODTO,commodityId);
        } else {
            // 非特惠商品处理系列品
            this.processSerialCommodityDetail(detailAppODTO, detailAppIDTO, FromPageEnums.XDA_APP_COMMODITY_DETAIL);
        }
        
        // 设置促销信息
        this.setCommodityPromotion(detailAppODTO, detailAppIDTO);

        // 获取收藏状态
        this.queryXdaCommodityCollect(detailAppODTO,commodityId,detailAppIDTO.getStoreId());

        // 设置banner列表和长图
        this.setXdaCommodityImage(detailAppODTO,commodityId);
        // 设置最快送达日期
        if(appODTO.getDeliveryTimeODTO()!=null){
            detailAppODTO.setDistributionTipList(appODTO.getDeliveryTimeODTO().getDistributionTipList());
        }
        log.info("\n\n\n==========>>>鲜达APP.商品详情：耗时={}毫秒，idto={}", (System.currentTimeMillis() - beginTime), detailAppIDTO);
        return detailAppODTO;
    }
    
    /**
     * 设置  详情页-促销信息
     * 
     * @param detailAppODTO
     */
    private void setCommodityPromotion(XdaCommodityDetailAppV3ODTO detailAppODTO, XdaCommodityDetailAppV3IDTO detailAppIDTO) {
    	CommodityDetailIDTO cpIDTO = new CommodityDetailIDTO();
    	cpIDTO.setChannelType(MarketSourceTypeEnum.XDA.getCode());
    	cpIDTO.setShopId(detailAppIDTO.getStoreId());
    	cpIDTO.setOrderTime(detailAppIDTO.getOrderTime());
     	// cpIDTO.setUserId(userId);
    	cpIDTO.setCommodityId(detailAppODTO.getCommodityId());
    	cpIDTO.setCategoryId(detailAppODTO.getXdaSecondCategoryId());
     	cpIDTO.setIsWeight(detailAppODTO.getIsWeight());
     	long beginTime = System.currentTimeMillis();
     	CommodityDetailODTO promotionODTO = mtPromotionClient.commodityDetail(cpIDTO);
        log.info("\n\n\n==========>>>鲜达APP.调用mtPromotionClient.commodityDetail：耗时={}毫秒，入参={}", (System.currentTimeMillis() - beginTime), cpIDTO);
     	if (null != promotionODTO) {
     		detailAppODTO.setPromotion(CommodityPromotionODTO.init(promotionODTO));
        	List<TagODTO> promotionTagList = promotionODTO.getTagODTOList();
        	if (SpringUtil.isNotEmpty(promotionTagList)) {
        		for (TagODTO thisTag: promotionTagList) {
        			if (TagEnums.SPECIAL.getType().equals(thisTag.getType())) {
        				if (detailAppODTO.getCommodityPrice() != null && null != promotionODTO.getSpecialPrice() && detailAppODTO.getCommodityPrice().compareTo(promotionODTO.getSpecialPrice()) > 0) {
        					detailAppODTO.setIsSpecialPrice(1);
        					detailAppODTO.setSpecialPrice(promotionODTO.getSpecialPrice());
                            CommodityTextTagInfoODTO specialTag = new CommodityTextTagInfoODTO("特价", "#FF5733", detailAppODTO.getCommodityId());
                            detailAppODTO.getTagV2List().add(specialTag);
        				}
        			} else if (TagEnums.PROMOTION.getType().equals(thisTag.getType())) {
        				
        			}
        		}
        	}
        }
    }

    /**
     * 鲜达商品列表
     * @param appIDTO
     * @return
     */
    public List<XdaCommodityAppV3ODTO> queryXdaCommodityDetailsListForApp(XdaCommodityAppV3IDTO appIDTO, Integer isThPrice, FromPageEnums fromPage){
        if(appIDTO.getStoreId()==null || appIDTO.getOrderTime()==null){
            return Collections.emptyList();
        }
        List<XdaCommodityAppV3ODTO> appODTOList = xdaCommodityFrontMapper.queryXdaCommodityDetailsForAppV3(appIDTO);
        if (CollectionUtils.isEmpty(appODTOList)) {
            log.warn("APP未获取到商品信息，请确认B端销售商品范围。查询参数：storeId={},commodityId={}",appIDTO.getStoreId(),appIDTO.getCommodityIdList());
            return Collections.emptyList();
        }
        if(null != isThPrice &&  isThPrice.equals(1)){
            this.setXdaThCommodityInfo(appODTOList, appIDTO);
        }else{
            this.setXdaCommodityInfo(appODTOList, appIDTO, fromPage);
        }
        return appODTOList;
    }

    /**
     * set特惠商品特惠价
     * @param detailAppODTO
     * @param commodityId
     */
    public void setXdaCommodityThPrice(XdaCommodityDetailAppV3ODTO detailAppODTO, Long commodityId){
        XdaSpecialsCommoditySet xdaSpecialsCommodityInfo = xdaSpecialsCommoditySetService.findXdaSpecialsCommodityInfoByCommodityId(commodityId);
        if(null != xdaSpecialsCommodityInfo){
            detailAppODTO.setIsThPrice(1);
            detailAppODTO.setThPrice(xdaSpecialsCommodityInfo.getCommoditySpecialsPrice());
            detailAppODTO.setThLimitNumber(new BigDecimal(xdaSpecialsCommodityInfo.getCommodityLimit()));
            //特惠提示信息
            String thCategoryTipsDetails = "已加购物车的正常商品的实付总金额≥所选送货日期的订货目标，则客户可享受特惠商品，以特惠的价格限量加购指定商品；其中，\n" +
                    "1.订货目标由销售部门设置，若当天未设置订货目标，则客户不能享受特惠商品加购，特惠商品分类不可见；\n" +
                    "2.正常商品，指特惠商品之外的商品，特惠商品的订货金额不能参与订货目标的计算；\n" +
                    "3.实付总金额，指特价、促销（比如满减活动）后实付总金额；\n" +
                    "4.特惠商品不参与任何特价活动、促销活动的计算，不能与任何活动叠加优惠；\n" +
                    "5.特惠商品不参与结算返利。";
            detailAppODTO.setThTipsDetails(thCategoryTipsDetails);
        }
    }


    /**
     * 根据类型区分: 特惠商品和普通商品购物车数量
     * @param storeId
     * @param commodityIdList
     * @return
     */
    public Map<Long,BigDecimal> queryShopCartQuantityMapV2(Long storeId,List<Long> commodityIdList,Integer commodityType){
        List<XdaShoppingCartODTO> shoppingCartODTOList = xdaCommodityFrontMapper.queryXdaShoppingCartQuantityV2(storeId,commodityIdList,commodityType);
        if(CollectionUtils.isEmpty(shoppingCartODTOList)){
            return Collections.emptyMap();
        }
        Map<Long, BigDecimal> quantityMap = new HashMap<>();
        shoppingCartODTOList.stream().collect(groupingBy(XdaShoppingCartODTO::getCommodityId)).forEach((k,v)->{
            if(CollectionUtils.isEmpty(v)){
                return;
            }
            quantityMap.put(k,v.get(0).getQuantity());
        });
        return quantityMap;
    }

    /**
     * 处理系列品
     * 
     * @param detailAppODTO
     * @param detailAppIDTO
     */
    private void processSerialCommodityDetail(XdaCommodityDetailAppV3ODTO detailAppODTO, XdaCommodityDetailAppV3IDTO detailAppIDTO, FromPageEnums fromPage){
        if(detailAppODTO.getXdaSecondCategoryId()==null){
            return;
        }
        List<XdaSerialCommodityODTO> serialCommodityList = xdaSerialCommodityMapper.querySerialCommodityListFront(Collections.singletonList(detailAppODTO.getCommodityId()), detailAppODTO.getXdaSecondCategoryId());
        if(CollectionUtils.isEmpty(serialCommodityList) || serialCommodityList.stream().noneMatch(item->item.getIsMain()==1) || serialCommodityList.size()<2){
            XdaSerialCommodityDetailV3ODTO serialDetail = XdaSerialCommodityDetailV3ODTO.convert(detailAppODTO);
            serialDetail.setIsCurrentCommodity(1);
            detailAppODTO.setSerialCommodityDetailList(Collections.singletonList(serialDetail));
            return;
        }
        List<XdaCommodityAppV3ODTO> appODTOList = new ArrayList<>();
        appODTOList.add(detailAppODTO);
        List<Long> serialCommodityIdList = serialCommodityList.stream()
                .filter(item->!item.getCommodityId().equals(detailAppODTO.getCommodityId()))
                .map(XdaSerialCommodityODTO::getCommodityId).collect(Collectors.toList());
        XdaCommodityAppV3IDTO appIDTO = XdaCommodityAppV3IDTO.builder().orderTime(detailAppIDTO.getOrderTime())
                .storeId(detailAppIDTO.getStoreId()).commodityIdList(serialCommodityIdList)
                .needDefaultImage(false).build();
        List<XdaCommodityAppV3ODTO> serialList = this.queryXdaCommodityDetailsListForApp(appIDTO, null, fromPage);
        if(CollectionUtils.isNotEmpty(serialList)){
            appODTOList.addAll(serialList);
            detailAppODTO.setIsSerial(1);
        }
        List<XdaSerialCommodityDetailV3ODTO> serialCommodityODTOList = appODTOList.stream().map(XdaSerialCommodityDetailV3ODTO::convert)
                .sorted(Comparator.comparing(XdaSerialCommodityDetailV3ODTO::getSortPrice)).collect(Collectors.toList());
        serialCommodityODTOList.forEach(item-> {
            if (item.getCommodityId().equals(detailAppODTO.getCommodityId())) {
                item.setIsCurrentCommodity(1);
            }else{
                item.setIsCurrentCommodity(0);
            }
        });
        detailAppODTO.setSerialCommodityDetailList(serialCommodityODTOList);
    }


    /**
     * 获取收藏状态
     * @param detailAppODTO
     * @param commodityId
     * @param storeId
     */
    private void queryXdaCommodityCollect(XdaCommodityDetailAppV3ODTO detailAppODTO,Long commodityId,Long storeId){
        Example example = new Example(XdaCommodityCollect.class);
        example.createCriteria().andEqualTo("commodityId",commodityId).andEqualTo("storeId",storeId);
        List<XdaCommodityCollect> collectList = xdaCommodityCollectMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(collectList)){
            detailAppODTO.setIsCollect(0);
        }else{
            detailAppODTO.setIsCollect(1);
        }
    }

    //设置商品详情页：banner、长图
    private void setXdaCommodityImage(XdaCommodityDetailAppV3ODTO detailAppODTO,Long commodityId){
        List<XdaCommodityTextPic> picList = xdaCommodityTextPicMapper.selectCommodityTextPicList(new SelectCommodityTextPicListIDTO(commodityId));
        if(CollectionUtils.isEmpty(picList)){
            return;
        }
        PicSizeEnums picSizeEnums = PicSizeEnums.PIC_750x750;
        picList.stream().collect(Collectors.groupingBy(XdaCommodityTextPic::getPicType)).forEach((picK,picV)->{
            if(CollectionUtils.isEmpty(picV)){
                return;
            }
            if(picK.intValue()== XdaCommodityTextPic.PicTypeEnums.PIC.getCode() ){
                List<String> imageUrlList = picV.stream().map(pic -> imgServerUrl +(ImageUtils.getXdImgUrlV2(pic.getPicUrl(),picSizeEnums.getSize()))).collect(Collectors.toList());
                detailAppODTO.setImageUrlList(imageUrlList);
            }
            if(picK.intValue()== XdaCommodityTextPic.PicTypeEnums.LONG_PIC.getCode() ){
                detailAppODTO.setLongPicList(this.querySplitLongPicUrlList(picV.get(0).getPicUrl()));
            }
        });
    }

    //查询长图
    private List<XdaCommodityLongPicODTO> querySplitLongPicUrlList(String longPic){
        //<长图原始路径，长图切割后的list>
        List<ImageLibraryODTO> imageODTOList = imageLibraryClient.findSingleImgAnyCondition(longPic, PicCutTypeEnum.CUT_BY_HEIGHT, PicSizeEnums.PIC_750);
        if(CollectionUtils.isNotEmpty(imageODTOList)) {
            return imageODTOList.stream().map(XdaCommodityLongPicODTO::convert).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

}
