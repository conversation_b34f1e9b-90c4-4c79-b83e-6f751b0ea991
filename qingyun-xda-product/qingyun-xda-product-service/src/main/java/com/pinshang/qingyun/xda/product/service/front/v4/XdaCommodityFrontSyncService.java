package com.pinshang.qingyun.xda.product.service.front.v4;

import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.base.enums.SalesStatusEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.order.dto.tob.TobCommodityStockIDTO;
import com.pinshang.qingyun.order.service.ToBCommodityStockClient;
import com.pinshang.qingyun.store.dto.customer.StoreSelectODTO;
import com.pinshang.qingyun.store.service.StoreManageClient;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityDetailAppV4IDTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityDetailAppV4ODTO;
import com.pinshang.qingyun.xda.search.dto.EsXdaUpdateDiffRecordIDTO;
import com.pinshang.qingyun.xda.search.service.EsXdaStoreCommodityClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @Author: sk
 * @Date: 2024/6/24
 */
@Slf4j
@Service
public class XdaCommodityFrontSyncService {

    @Autowired
    private EsXdaStoreCommodityClient esXdaStoreCommodityClient;
    @Autowired
    private StoreManageClient storeManageClient;
    @Autowired
    private ToBCommodityStockClient toBCommodityStockClient;

    /**
     * 异步解决详情有库存，但是ES(分类页，搜素页...)显示没有库存问题
     * @param detailAppV4ODTO
     * @param detailAppIDTO
     */
    @Async
    public void updateEsXdaStoreCommodity(XdaCommodityDetailAppV4IDTO detailAppIDTO, XdaCommodityDetailAppV4ODTO detailAppV4ODTO){
        EsXdaUpdateDiffRecordIDTO esXdaUpdateDiffRecordIDTO = new EsXdaUpdateDiffRecordIDTO();
        esXdaUpdateDiffRecordIDTO.setCommodityId(detailAppV4ODTO.getCommodityId());
        esXdaUpdateDiffRecordIDTO.setDate(detailAppIDTO.getOrderTime());
        esXdaUpdateDiffRecordIDTO.setStoreId(detailAppIDTO.getStoreId());
        esXdaUpdateDiffRecordIDTO.setAppStatus(detailAppV4ODTO.getAppStatus());

        // 调用xda-search 维护是否上下架
        // 因为库存查询pinshang库的t_dc_tob_commodity_stock 表，这里只维护是否上下架
        esXdaStoreCommodityClient.updateDiffRecord(esXdaUpdateDiffRecordIDTO);

        // 库存取的实时表，不用刷了
        /*if(detailAppV4ODTO.getSalesStatus() != null && !SalesStatusEnums.NOT_DATE.getCode().equals(detailAppV4ODTO.getSalesStatus())){
            // 调用qingyun-order维护库存信息
            StoreSelectODTO storeSelectODTO = storeManageClient.getStoreData(detailAppIDTO.getStoreId());
            TobCommodityStockIDTO tobCommodityStockIDTO = new TobCommodityStockIDTO();
            tobCommodityStockIDTO.setBusinessType(BusinessTypeEnums.TD_SALE.getCode().equals(storeSelectODTO.getBusinessType()) ? BusinessTypeEnums.TD_SALE.getCode() : BusinessTypeEnums.SALE.getCode());
            tobCommodityStockIDTO.setCommodityId(detailAppV4ODTO.getCommodityId());

            if(detailAppV4ODTO.getSalesStatus().equals(SalesStatusEnums.NORMAL.getCode())){
                tobCommodityStockIDTO.setSoldOut(YesOrNoEnums.YES.getCode());
            }
            if(detailAppV4ODTO.getSalesStatus().equals(SalesStatusEnums.SOLD_OUT.getCode())){
                tobCommodityStockIDTO.setSoldOut(YesOrNoEnums.NO.getCode());
            }
            toBCommodityStockClient.updateXdaCommodityStock(tobCommodityStockIDTO);
        }*/

    }
}
