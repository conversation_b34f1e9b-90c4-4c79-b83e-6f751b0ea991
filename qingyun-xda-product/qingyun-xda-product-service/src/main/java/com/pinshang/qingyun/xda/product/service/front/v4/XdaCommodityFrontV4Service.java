package com.pinshang.qingyun.xda.product.service.front.v4;

import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.base.enums.TagEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.marketing.MarketSourceTypeEnum;
import com.pinshang.qingyun.base.enums.pic.PicCutTypeEnum;
import com.pinshang.qingyun.base.enums.pic.PicSizeEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.ImageUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.common.dto.ImageLibraryODTO;
import com.pinshang.qingyun.common.service.ImageLibraryClient;
import com.pinshang.qingyun.marketing.dto.app.*;
import com.pinshang.qingyun.marketing.service.MtPromotionClient;
import com.pinshang.qingyun.store.dto.customer.StoreSelectODTO;
import com.pinshang.qingyun.store.service.StoreManageClient;
import com.pinshang.qingyun.xda.product.dto.StoreCommodityPriceODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextPicListIDTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextTagInfoListIDTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDeliveryTimeODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityLongPicODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaSerialCommodityODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaShoppingCartODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.CommodityPromotionODTO;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.FromPageEnums;
import com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.*;
import com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.XdaCommodityAppStatusODTO;
import com.pinshang.qingyun.xda.product.mapper.*;
import com.pinshang.qingyun.xda.product.mapper.common.CommodityFreezeGroupMapper;
import com.pinshang.qingyun.xda.product.model.XdaCommodityCollect;
import com.pinshang.qingyun.xda.product.model.XdaCommodityTextPic;
import com.pinshang.qingyun.xda.product.model.common.CommodityFreezeGroup;
import com.pinshang.qingyun.xda.product.model.specialsCommoditySet.XdaSpecialsCommoditySet;
import com.pinshang.qingyun.xda.product.service.BStockService;
import com.pinshang.qingyun.xda.product.service.StoreService;
import com.pinshang.qingyun.xda.product.service.XdaOrderTargetSetService;
import com.pinshang.qingyun.xda.product.service.XdaSpecialsCommoditySetService;
import com.pinshang.qingyun.xda.product.service.front.XdaCommodityFrontService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Slf4j
@Service
public class XdaCommodityFrontV4Service {

	@Autowired
    private MtPromotionClient mtPromotionClient;
	
	@Autowired
    private ImageLibraryClient imageLibraryClient;
	
	@Autowired
    private XdaCommodityTextMapper xdaCommodityTextMapper;
	
	@Autowired
    private XdaCommodityFrontMapper xdaCommodityFrontMapper;
	
	@Autowired
    private XdaCommodityFrontMapperV4 xdaCommodityFrontMapperV4;
	
    @Autowired
    private XdaCommodityFrontService xdaCommodityFrontService;

    @Autowired
    private XdaOrderTargetSetService xdaOrderTargetSetService;
    
    @Autowired
    private XdaSerialCommodityMapper xdaSerialCommodityMapper;
    
    @Autowired
    private XdaCommodityCollectMapper xdaCommodityCollectMapper;
    
    @Autowired
    private XdaCommodityTextPicMapper xdaCommodityTextPicMapper;
    
    @Autowired
    private CommodityFreezeGroupMapper commodityFreezeGroupMapper;

    @Autowired
    private XdaSpecialsCommoditySetService xdaSpecialsCommoditySetService;

    @Value("${pinshang.img-xd-server-url}")
    private String imgServerUrl;
    @Autowired
    private BStockService bStockService;

    @Autowired
    private StoreManageClient storeManageClient;
    @Autowired
    private XdaCommodityFrontSyncService xdaCommodityFrontSyncService;
    @Autowired
    private StoreService storeService;
    @Autowired
    private XdaCommodityAppStatusMapper xdaCommodityAppStatusMapper;

    /**
     * set商品信息
     * @param appODTOList
     * @param appIDTO
     */
    public void setXdaCommodityInfo(List<XdaCommodityAppV4ODTO> appODTOList, XdaCommodityAppV4IDTO appIDTO, FromPageEnums fromPage){
        if(CollectionUtils.isEmpty(appODTOList)){
            return;
        }
        Long storeId = appIDTO.getStoreId();
        Date orderTime = appIDTO.getOrderTime();
        List<Long> commodityIdList = appODTOList.stream().map(XdaCommodityAppV4ODTO::getCommodityId).collect(Collectors.toList());
        
//        // 特价
//        Map<Long, BigDecimal> priceMap = Collections.emptyMap();
//        if(appIDTO.getNeedSpecialPrice()){
//            priceMap = xdaCommodityFrontService.queryXdaSpecialPrice(orderTime,storeId,new ArrayList<>(commodityIdList));
//        }

//        // 促销
//        Map<Long, List<XdaStorePromotionODTO>> promotionMap = Collections.emptyMap();
//        if(appIDTO.getNeedPromotion()){
//            promotionMap = xdaCommodityFrontService.queryXdaPromotion(orderTime,storeId,new ArrayList<>(commodityIdList));
//        }
        
        /**
         * 特价、促销（买赠、梯度满折，以及之后满减）
         * 
         * 鲜达APP分类商品页，存在按价格排序的功能，且SQL超级复杂，
         * 有鉴于此，以及特价尚未真正独立库，所以本期不准备改变SQL（即查询排序仍按原SQL）
         * 
         * 注：待特价、促销都迁移走之后，特价和促销将全部通过Client拉取，同时鲜达APP分类商品页，将不能按照价格排序。
         */
        Map<Long, CommodityODTO> commodityPromotionMap = Collections.emptyMap();
        if (FromPageEnums.XDA_APP_CATEGORY_COMMODITY_LIST.equals(fromPage)) {
        	commodityPromotionMap = this.getCommodityPromotionMap(appIDTO, appODTOList);
        }

        // 自定义标签
        Map<Long, List<CommodityTextTagInfoODTO>> tagMap = Collections.emptyMap();
        if(appIDTO.getNeedTag()){
            List<CommodityTextTagInfoODTO> tagList = xdaCommodityTextMapper.selectCommodityTextTagInfoList(new SelectCommodityTextTagInfoListIDTO(commodityIdList));
            tagMap = tagList.stream().collect(Collectors.groupingBy(CommodityTextTagInfoODTO::getCommodityId));
        }

        // 是否可订货
        Map<Long, XdaCommodityDeliveryTimeODTO> deliveryTimeMap = Collections.emptyMap();
        if(appIDTO.getNeedCanOrder()){
            deliveryTimeMap = xdaCommodityFrontService.queryXdaCommodityDeliveryTime(orderTime,storeId, new ArrayList<>(commodityIdList));
        }

        // 购物车数量
        Map<Long,BigDecimal> shopCartQuantityMap = null;
        if(appIDTO.getNeedCartQuantity()){
            shopCartQuantityMap = this.queryShopCartQuantityMapV2(appIDTO.getStoreId(),commodityIdList, 1);
        }

        // 凑整商品集合
        List<Long> freezeGroupList = this.queryFreezeGroupMap(commodityIdList);

        for (XdaCommodityAppV4ODTO appODTO : appODTOList) {
        	Long commodityId = appODTO.getCommodityId();
        	
            // 标签合集-列表页：1-自定义标签、2-特价、3-促销（、4-凑整、5-速冻）、6-限量
            List<CommodityTextTagInfoODTO> listTagList = new ArrayList<>();
        	// 标签合集-详情页：1-自定义标签
            List<CommodityTextTagInfoODTO> tagList = tagMap.get(commodityId);
            // 标签合集-详情页：2-特价、4-凑整、5-速冻、6-限量
            List<CommodityTextTagInfoODTO> tagV2List = new ArrayList<>();
            
            // 自定义标签
            if (SpringUtil.isNotEmpty(tagList)) {
            	listTagList.addAll(tagList);
            }
            
            // 图片
            if(appIDTO.getNeedDefaultImage() && StringUtils.isNotEmpty(appODTO.getImageUrl())){
                appODTO.setImageUrl(imgServerUrl+ ImageUtils.getXdImgUrlV2(appODTO.getImageUrl(),appIDTO.getDefaultImageSize()==null?null:appIDTO.getDefaultImageSize().getSize()));
            }
            
            // 特价
//            if(priceMap!=null && priceMap.get(commodityId)!=null){
//                BigDecimal specialPrice = priceMap.get(commodityId);
//                if(appODTO.getCommodityPrice()!=null && appODTO.getCommodityPrice().compareTo(specialPrice)>0){
//                    appODTO.setIsSpecialPrice(1);
//                    appODTO.setSpecialPrice(priceMap.get(commodityId));
//                    tagV2List.add(new CommodityTextTagInfoODTO("特价", "#FF5733", commodityId));
//
//                }
//            }
            
            // 促销
//   		if (promotionMap != null && CollectionUtils.isNotEmpty(promotionMap.get(commodityId))) {
//      		appODTO.setIsPromotion(1);
//        		appODTO.setPromotionList(promotionMap.get(commodityId));
//      	}
            
            // 特价、促销（买赠、梯度满折，以及之后满减）
            CommodityODTO promotionCommodity = commodityPromotionMap.get(commodityId);
            if (null != promotionCommodity) {
            	List<TagODTO> promotionTagList = promotionCommodity.getTagODTOList();
            	if (SpringUtil.isNotEmpty(promotionTagList)) {
            		for (TagODTO thisTag: promotionTagList) {
            			if (TagEnums.SPECIAL.getType().equals(thisTag.getType())) {
            				if (appODTO.getCommodityPrice() != null && null != promotionCommodity.getSpecialPrice() && appODTO.getCommodityPrice().compareTo(promotionCommodity.getSpecialPrice()) > 0) {
            					appODTO.setIsSpecialPrice(1);
                                appODTO.setSpecialPrice(promotionCommodity.getSpecialPrice());
                                Integer specialLimit = promotionCommodity.getSpecialLimit();
                                String tagName;
                                if (99999 == specialLimit || 0 == specialLimit) {
                                    //不限购
                                    tagName = "特价";
                                } else {
                                    tagName = "特价|每天限" + specialLimit + "份";
                                }
                                CommodityTextTagInfoODTO specialTag = new CommodityTextTagInfoODTO(tagName, "#FF3D00", commodityId);
                                
                                listTagList.add(specialTag);
                                tagV2List.add(specialTag);
            				}
            			} else if (TagEnums.PROMOTION.getType().equals(thisTag.getType())) {
            				if (null != thisTag.getName()) {
//            					appODTO.setIsPromotion(1);
            					CommodityTextTagInfoODTO promotionTag = new CommodityTextTagInfoODTO(thisTag.getName(), "#FF5733", commodityId);
            					listTagList.add(promotionTag);
            				}
            			}
            		}
            	}
            }
            
            // 送货日期范围
            if(deliveryTimeMap != null && deliveryTimeMap.get(commodityId) != null){
                XdaCommodityDeliveryTimeODTO deliveryTimeODTO = deliveryTimeMap.get(commodityId);
                appODTO.setIsCanOrder(deliveryTimeODTO.getIsCanOrder());
                appODTO.setDeliveryTimeODTO(deliveryTimeODTO);
                List<Date> xdaDeliveryTimeList = deliveryTimeODTO.getDeliveryDateList();
                if(CollectionUtils.isNotEmpty(xdaDeliveryTimeList)){
                    appODTO.setBeginDeliveryTime(xdaDeliveryTimeList.get(0));
                    appODTO.setEndDeliveryTime(xdaDeliveryTimeList.get(xdaDeliveryTimeList.size()-1));
                }
            }
            
            // 凑整
            if(freezeGroupList.contains(commodityId)){
                appODTO.setIsFreezeRounding(1);
                appODTO.setIsFreezeRoundingMultiple(30);
                
                CommodityTextTagInfoODTO czTag = new CommodityTextTagInfoODTO("凑整", "#FF5733", commodityId);
//                listTagList.add(czTag);
                tagV2List.add(czTag);
            }
            
            // 购物车数量
            if(shopCartQuantityMap!=null && shopCartQuantityMap.get(commodityId)!=null){
                appODTO.setShoppingCartQuantity(shopCartQuantityMap.get(commodityId));
            }
            
            // 设置标签
            appODTO.setListTagList(listTagList);
            appODTO.setTagList(tagList);
            appODTO.setTagV2List(tagV2List);

            // 当且仅当
            //商品保质期字段为X天，且文描管理中该商品“是否显示保质期=显示”，才在APP前台商详页显示保质期为X天
            if(!YesOrNoEnums.YES.getCode().equals(appODTO.getQualityStatus())){
                appODTO.setQualityDays(null);
            }
        }
    }
    
    public Map<Long, CommodityODTO> getCommodityPromotionMap(XdaCommodityAppV4IDTO appIDTO, List<XdaCommodityAppV4ODTO> appODTOList) {
    	/**
         * 特价、促销（买赠、梯度满折，以及之后满减）
         * 
         * 鲜达APP分类商品页，存在按价格排序的功能，且SQL超级复杂，
         * 有鉴于此，以及特价尚未真正独立库，所以本期不准备改变SQL（即查询排序仍按原SQL）
         * 
         * 注：待特价、促销都迁移走之后，特价和促销将全部通过Client拉取，同时鲜达APP分类商品页，将不能按照价格排序。
         */
    	List<CommodityCategoryIDTO> commodityCategoryIDTOList = appODTOList.stream().map(o-> {
    		CommodityCategoryIDTO cci = new CommodityCategoryIDTO();
    		cci.setCommodityId(o.getCommodityId());
    		cci.setCategoryId(o.getCommodityThirdId());
    		cci.setIsWeight(o.getIsWeight());
    		return cci;
    	}).collect(Collectors.toList());
       
       CommodityPromotionIDTO cpIDTO = new CommodityPromotionIDTO();
       cpIDTO.setChannelType(MarketSourceTypeEnum.XDA.getCode());
       cpIDTO.setShopId(appIDTO.getStoreId());
       cpIDTO.setOrderTime(appIDTO.getOrderTime());
       // cpIDTO.setUserId(userId);
       cpIDTO.setCommodityCategoryIDTOList(commodityCategoryIDTOList);
       
       long beginTime = System.currentTimeMillis();
       Map<Long, CommodityODTO> commodityPromotionMap = mtPromotionClient.queryCommodityPromotion(cpIDTO);
       log.info("\n\n\n==========>>>鲜达APP.调用mtPromotionClient.queryCommodityPromotion：耗时={}毫秒，入参={}", (System.currentTimeMillis() - beginTime), cpIDTO);
       if (null == commodityPromotionMap) {
    	   return Collections.emptyMap();
       }
       
       return commodityPromotionMap;
    }

    /**
     * set鲜达特惠商品信息
     * @param appODTOList
     * @param appIDTO
     */
    public void setXdaThCommodityInfo(List<XdaCommodityAppV4ODTO> appODTOList, XdaCommodityAppV4IDTO appIDTO){
        if(CollectionUtils.isEmpty(appODTOList)){
            return;
        }
        Long storeId = appIDTO.getStoreId();
        Date orderTime = appIDTO.getOrderTime();
        List<Long> commodityIdList = appODTOList.stream().map(XdaCommodityAppV4ODTO::getCommodityId).collect(Collectors.toList());

        // 自定义标签
        Map<Long, List<CommodityTextTagInfoODTO>> tagMap = Collections.emptyMap();
        if(appIDTO.getNeedTag()){
            List<CommodityTextTagInfoODTO> tagList = xdaCommodityTextMapper.selectCommodityTextTagInfoList(new SelectCommodityTextTagInfoListIDTO(commodityIdList));
            tagMap = tagList.stream().collect(Collectors.groupingBy(CommodityTextTagInfoODTO::getCommodityId));
        }

        // 是否可订货
        Map<Long, XdaCommodityDeliveryTimeODTO> deliveryTimeMap = Collections.emptyMap();
        if(appIDTO.getNeedCanOrder()){
            deliveryTimeMap = xdaCommodityFrontService.queryXdaCommodityDeliveryTime(orderTime,storeId, new ArrayList<>(commodityIdList));
        }


        // 购物车数量
        Map<Long,BigDecimal> shopCartQuantityMap = Collections.emptyMap();
        if(appIDTO.getNeedCartQuantity()){
            shopCartQuantityMap = this.queryShopCartQuantityMapV2(appIDTO.getStoreId(),commodityIdList,2);
        }

        // 凑整商品集合
        List<Long> freezeGroupList = this.queryFreezeGroupMap(commodityIdList);

        for (XdaCommodityAppV4ODTO appODTO : appODTOList) {
        	Long commodityId = appODTO.getCommodityId();
        	
            // 标签合集-列表页：1-自定义标签、2-特价、3-促销（、4-凑整、5-速冻）、6-限量
            List<CommodityTextTagInfoODTO> listTagList = new ArrayList<>();
        	// 标签合集-详情页：1-自定义标签
            List<CommodityTextTagInfoODTO> tagList = tagMap.get(commodityId);
            // 标签合集-详情页：2-特价、4-凑整、5-速冻、6-限量
            List<CommodityTextTagInfoODTO> tagV2List = new ArrayList<>();
            
            // 自定义标签，合并到其他标签中
            if (SpringUtil.isNotEmpty(tagList)) {
            	listTagList.addAll(tagList);
            }
            
            CommodityTextTagInfoODTO thTag = new CommodityTextTagInfoODTO("特惠", "#FF5733", commodityId);
            listTagList.add(thTag);
            tagV2List.add(thTag);
            
            // 图片
            if(appIDTO.getNeedDefaultImage() && StringUtils.isNotEmpty(appODTO.getImageUrl())){
                appODTO.setImageUrl(imgServerUrl+ ImageUtils.getXdImgUrlV2(appODTO.getImageUrl(),appIDTO.getDefaultImageSize()==null?null:appIDTO.getDefaultImageSize().getSize()));
            }

            // 送货日期范围
            if(deliveryTimeMap!=null && deliveryTimeMap.get(commodityId)!=null){
                XdaCommodityDeliveryTimeODTO deliveryTimeODTO = deliveryTimeMap.get(commodityId);
                appODTO.setIsCanOrder(deliveryTimeODTO.getIsCanOrder());
                appODTO.setDeliveryTimeODTO(deliveryTimeODTO);
                List<Date> xdaDeliveryTimeList = deliveryTimeODTO.getDeliveryDateList();
                if(CollectionUtils.isNotEmpty(xdaDeliveryTimeList)){
                    appODTO.setBeginDeliveryTime(xdaDeliveryTimeList.get(0));
                    appODTO.setEndDeliveryTime(xdaDeliveryTimeList.get(xdaDeliveryTimeList.size()-1));
                }
            }
            
            // 凑整
            if(freezeGroupList.contains(commodityId)){
                appODTO.setIsFreezeRounding(1);
                appODTO.setIsFreezeRoundingMultiple(30);
                
                CommodityTextTagInfoODTO czTag = new CommodityTextTagInfoODTO("凑整", "#FF5733", commodityId);
                // listTagList.add(czTag); // 凑整有图标了，这里就不显示文字了
                tagV2List.add(czTag);
            }
            
            // 购物车数量
            if(shopCartQuantityMap!=null && shopCartQuantityMap.get(commodityId)!=null){
                appODTO.setShoppingCartQuantity(shopCartQuantityMap.get(commodityId));
            }
            
            // 设置标签
            appODTO.setListTagList(listTagList);
            appODTO.setTagList(tagList);
            appODTO.setTagV2List(tagV2List);

            // 当且仅当
            //商品保质期字段为X天，且文描管理中该商品“是否显示保质期=显示”，才在APP前台商详页显示保质期为X天
            if(!YesOrNoEnums.YES.getCode().equals(appODTO.getQualityStatus())){
                appODTO.setQualityDays(null);
            }
        }
    }

    /**
     * 查询商品是否凑整商品
     * @param commodityIdList
     * @return
     */
    public List<Long> queryFreezeGroupMap(List<Long> commodityIdList){
        if(SpringUtil.isEmpty(commodityIdList)){
            return Collections.emptyList();
        }
        List<CommodityFreezeGroup> commodityFreezeGroups = getCommodityFreezeGroups(commodityIdList);

        return commodityFreezeGroups.stream().map(CommodityFreezeGroup::getCommodityId).collect(Collectors.toList());
    }

    /**
     * 鲜达商品详情
     * @param detailAppIDTO
     * @return
     */
    public XdaCommodityDetailAppV4ODTO queryXdaCommodityDetailForApp(XdaCommodityDetailAppV4IDTO detailAppIDTO){
    	long beginTime = System.currentTimeMillis();
        if (detailAppIDTO == null) {
            log.error("查询商品详情参数空异常");
            return null;
        }
        Long commodityId = detailAppIDTO.getCommodityId();
        if(commodityId == null || detailAppIDTO.getStoreId() == null || detailAppIDTO.getOrderTime() == null){
            log.error("查询商品详情参数空异常");
            return null;
        }

        // set查询商品详情参数
        XdaCommodityAppV4IDTO appIDTO = XdaCommodityAppV4IDTO.builder().orderTime(detailAppIDTO.getOrderTime())
                .storeId(detailAppIDTO.getStoreId()).commodityIdList(Collections.singletonList(commodityId))
                .defaultImageSize(PicSizeEnums.PIC_750x750).needCartQuantity(true).build();

        // 获取客户业务类型
        StoreSelectODTO storeSelectODTO = storeManageClient.getStoreData(appIDTO.getStoreId());
        Integer tdaStoreStatus = BusinessTypeEnums.TD_SALE.getCode().equals(storeSelectODTO.getBusinessType()) ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode();
        appIDTO.setTdaStoreStatus(tdaStoreStatus);
        Integer businessType = getSafeBusinessType(storeSelectODTO.getBusinessType());
        appIDTO.setBusinessType(businessType);
        Boolean isPfsStore = storeService.isPfsStore(appIDTO.getStoreId());
        appIDTO.setIsPfsStore(isPfsStore);

        List<XdaCommodityAppV4ODTO> appODTOList = this.queryXdaCommodityDetailsListForApp(appIDTO, detailAppIDTO.getIsThPrice(), FromPageEnums.XDA_APP_COMMODITY_DETAIL);
        if (CollectionUtils.isEmpty(appODTOList)) {

            // 如果查询出来的商品为空,则判断商品是否已经下架。如果已经下架则发送下架消息
            Long appStatus = null;
            List<XdaCommodityAppStatusODTO> commodityStatusList = xdaCommodityAppStatusMapper.batchSelectCommodityAppStatus(Collections.singletonList(commodityId));
            XdaCommodityAppStatusODTO commodityStatus = commodityStatusList.get(0);
            if(isPfsStore) {
                appStatus = commodityStatus.getPfAppState();
            }else {
                appStatus = commodityStatus.getAppState();
            }

            // 如果下架。则更新
            if(Long.valueOf("1").equals(appStatus)) {
                XdaCommodityDetailAppV4ODTO detailAppODTO = new XdaCommodityDetailAppV4ODTO();
                detailAppODTO.setCommodityId(commodityId);
                detailAppODTO.setAppStatus(1);
                //刷新es
                xdaCommodityFrontSyncService.updateEsXdaStoreCommodity(detailAppIDTO,detailAppODTO);
                throw new BizLogicException("该商品已下架");
            }else {
                throw new BizLogicException("该商品已下架");
            }
        }

        XdaCommodityAppV4ODTO appODTO = appODTOList.get(0);

        // 鲜达详情页设置是否有库存,实时查询大仓
        appODTO.setSalesStatus(bStockService.setCommodityDetailSoldOut(detailAppIDTO.getOrderTime(), commodityId, appODTO.getSalesStatus(), businessType, detailAppIDTO.getLogisticscenterid()));

        XdaCommodityDetailAppV4ODTO detailAppODTO = BeanCloneUtils.copyTo(appODTO, XdaCommodityDetailAppV4ODTO.class);
        // 判断商品是否特惠
        if (null !=detailAppIDTO.getIsThPrice() && detailAppIDTO.getIsThPrice().equals(1)) {
            // 设置特惠信息
           this.setXdaCommodityThPrice(detailAppODTO,commodityId);
        } else {
            // 非特惠商品处理系列品
            this.processSerialCommodityDetail(detailAppODTO, detailAppIDTO, FromPageEnums.XDA_APP_COMMODITY_DETAIL);
        }
        
        // 设置促销信息
        this.setCommodityPromotion(detailAppODTO, detailAppIDTO);

        // 获取收藏状态
        this.queryXdaCommodityCollect(detailAppODTO,commodityId,detailAppIDTO.getStoreId());

        // 设置banner列表和长图
        this.setXdaCommodityImage(detailAppODTO,commodityId);
        // 设置最快送达日期
        if(appODTO.getDeliveryTimeODTO()!=null){
            detailAppODTO.setDistributionTipList(appODTO.getDeliveryTimeODTO().getDistributionTipList());
        }
        log.info("\n\n\n==========>>>鲜达APP.商品详情：耗时={}毫秒，idto={}", (System.currentTimeMillis() - beginTime), detailAppIDTO);

        // 库存是实时查的mysql,不用去刷新ES了。
        // 产品价格方案也是实时查询的，上下架维护会导致吧其他门店的当前商品给下架掉。所以上下架也不刷新ES了
        //xdaCommodityFrontSyncService.updateEsXdaStoreCommodity(detailAppIDTO,detailAppODTO);
        return detailAppODTO;
    }
    
    /**
     * 设置  详情页-促销信息
     * 
     * @param detailAppODTO
     */
    private void setCommodityPromotion(XdaCommodityDetailAppV4ODTO detailAppODTO, XdaCommodityDetailAppV4IDTO detailAppIDTO) {
    	CommodityDetailIDTO cpIDTO = new CommodityDetailIDTO();
    	cpIDTO.setChannelType(MarketSourceTypeEnum.XDA.getCode());
    	cpIDTO.setShopId(detailAppIDTO.getStoreId());
    	cpIDTO.setOrderTime(detailAppIDTO.getOrderTime());
     	// cpIDTO.setUserId(userId);
    	cpIDTO.setCommodityId(detailAppODTO.getCommodityId());
    	cpIDTO.setCategoryId(detailAppODTO.getXdaSecondCategoryId());
     	cpIDTO.setIsWeight(detailAppODTO.getIsWeight());
        cpIDTO.setNeedAvailableLimit(1);
     	long beginTime = System.currentTimeMillis();
     	CommodityDetailODTO promotionODTO = mtPromotionClient.commodityDetail(cpIDTO);
        log.info("\n\n\n==========>>>鲜达APP.调用mtPromotionClient.commodityDetail：耗时={}毫秒，入参={}", (System.currentTimeMillis() - beginTime), cpIDTO);
     	if (null != promotionODTO) {
            detailAppODTO.setEnableCoupon(promotionODTO.getEnableCoupon());
     		detailAppODTO.setPromotion(CommodityPromotionODTO.init(promotionODTO));
        	List<TagODTO> promotionTagList = promotionODTO.getTagODTOList();
        	if (SpringUtil.isNotEmpty(promotionTagList)) {
        		for (TagODTO thisTag: promotionTagList) {
        			if (TagEnums.SPECIAL.getType().equals(thisTag.getType())) {
        				if (detailAppODTO.getCommodityPrice() != null && null != promotionODTO.getSpecialPrice() && detailAppODTO.getCommodityPrice().compareTo(promotionODTO.getSpecialPrice()) > 0) {
        					detailAppODTO.setIsSpecialPrice(1);
        					detailAppODTO.setSpecialPrice(promotionODTO.getSpecialPrice());
                            Integer specialLimit = promotionODTO.getSpecialLimit();
                            String tagName;
                            if (99999 == specialLimit || 0 == specialLimit) {
                                //不限购
                                tagName = "特价";
                            } else {
                                tagName = "特价|每天限" + specialLimit + "份";
                            }
                            CommodityTextTagInfoODTO specialTag = new CommodityTextTagInfoODTO(tagName, "#FF3D00", detailAppODTO.getCommodityId());
                            detailAppODTO.getTagV2List().add(0,specialTag);
        				}
        			} else if (TagEnums.PROMOTION.getType().equals(thisTag.getType())) {
        				
        			}
        		}
        	}
        }
    }

    /**
     * 鲜达商品列表
     * @param appIDTO
     * @return
     */
    public List<XdaCommodityAppV4ODTO> queryXdaCommodityDetailsListForApp(XdaCommodityAppV4IDTO appIDTO, Integer isThPrice, FromPageEnums fromPage){
        if(appIDTO.getStoreId()==null || appIDTO.getOrderTime()==null){
            return Collections.emptyList();
        }

        List<XdaCommodityAppV4ODTO> appODTOList = xdaCommodityFrontMapperV4.queryXdaCommodityDetailsForAppV4(appIDTO);
        if (CollectionUtils.isEmpty(appODTOList)) {
            log.warn("APP未获取到商品信息，请确认B端销售商品范围。查询参数：storeId={},commodityId={}",appIDTO.getStoreId(),appIDTO.getCommodityIdList());
            return Collections.emptyList();
        }
       /* // 获取已售罄商品
        List<XdaCommodityAppV4ODTO> soldOutList = appODTOList.stream().filter(p -> SalesStatusEnums.SOLD_OUT.getCode().equals(p.getSalesStatus())).collect(Collectors.toList());
        //if(CollectionUtils.isNotEmpty(soldOutList)){
        if(CollectionUtils.isNotEmpty(soldOutList)){
            bStockService.setSoldOutList(appIDTO.getOrderTime(), appODTOList);
        }
        //}*/

        if(null != isThPrice &&  isThPrice.equals(1)){
            this.setXdaThCommodityInfo(appODTOList, appIDTO);
        }else{
            this.setXdaCommodityInfo(appODTOList, appIDTO, fromPage);
        }
        return appODTOList;
    }


    /**
     * set特惠商品特惠价
     * @param detailAppODTO
     * @param commodityId
     */
    public void setXdaCommodityThPrice(XdaCommodityDetailAppV4ODTO detailAppODTO, Long commodityId){
        XdaSpecialsCommoditySet xdaSpecialsCommodityInfo = xdaSpecialsCommoditySetService.findXdaSpecialsCommodityInfoByCommodityId(commodityId);
        if(null != xdaSpecialsCommodityInfo){
            detailAppODTO.setIsThPrice(1);
            detailAppODTO.setThPrice(xdaSpecialsCommodityInfo.getCommoditySpecialsPrice());
            detailAppODTO.setThLimitNumber(new BigDecimal(xdaSpecialsCommodityInfo.getCommodityLimit()));
            //特惠提示信息
            String thCategoryTipsDetails = "已加购物车的正常商品的实付总金额≥所选送货日期的订货目标，则客户可享受特惠商品，以特惠的价格限量加购指定商品；其中，\n" +
                    "1.订货目标由销售部门设置，若当天未设置订货目标，则客户不能享受特惠商品加购，特惠商品分类不可见；\n" +
                    "2.正常商品，指特惠商品之外的商品，特惠商品的订货金额不能参与订货目标的计算；\n" +
                    "3.实付总金额，指特价、促销（比如满减活动）后实付总金额；\n" +
                    "4.特惠商品不参与任何特价活动、促销活动的计算，不能与任何活动叠加优惠；\n" +
                    "5.特惠商品不参与结算返利。";
            detailAppODTO.setThTipsDetails(thCategoryTipsDetails);
        }
    }


    /**
     * 根据类型区分: 特惠商品和普通商品购物车数量
     * @param storeId
     * @param commodityIdList
     * @return
     */
    public Map<Long,BigDecimal> queryShopCartQuantityMapV2(Long storeId,List<Long> commodityIdList,Integer commodityType){
        List<XdaShoppingCartODTO> shoppingCartODTOList = xdaCommodityFrontMapper.queryXdaShoppingCartQuantityV2(storeId,commodityIdList,commodityType);
        if(CollectionUtils.isEmpty(shoppingCartODTOList)){
            return Collections.emptyMap();
        }
        Map<Long, BigDecimal> quantityMap = new HashMap<>();
        shoppingCartODTOList.stream().collect(groupingBy(XdaShoppingCartODTO::getCommodityId)).forEach((k,v)->{
            if(CollectionUtils.isEmpty(v)){
                return;
            }
            quantityMap.put(k,v.get(0).getQuantity());
        });
        return quantityMap;
    }

    /**
     * 处理系列品
     * 
     * @param detailAppODTO
     * @param detailAppIDTO
     */
    private void processSerialCommodityDetail(XdaCommodityDetailAppV4ODTO detailAppODTO, XdaCommodityDetailAppV4IDTO detailAppIDTO, FromPageEnums fromPage){
        if(detailAppODTO.getXdaSecondCategoryId()==null){
            return;
        }
        List<XdaSerialCommodityODTO> serialCommodityList = xdaSerialCommodityMapper.querySerialCommodityListFront(Collections.singletonList(detailAppODTO.getCommodityId()), detailAppODTO.getXdaSecondCategoryId());
        if(CollectionUtils.isEmpty(serialCommodityList) || serialCommodityList.stream().noneMatch(item->item.getIsMain()==1) || serialCommodityList.size()<2){
            XdaSerialCommodityDetailV4ODTO serialDetail = XdaSerialCommodityDetailV4ODTO.convert(detailAppODTO);
            serialDetail.setIsCurrentCommodity(1);
            detailAppODTO.setSerialCommodityDetailList(Collections.singletonList(serialDetail));
            return;
        }
        List<XdaCommodityAppV4ODTO> appODTOList = new ArrayList<>();
        appODTOList.add(detailAppODTO);
        List<Long> serialCommodityIdList = serialCommodityList.stream()
                .filter(item->!item.getCommodityId().equals(detailAppODTO.getCommodityId()))
                .map(XdaSerialCommodityODTO::getCommodityId).collect(Collectors.toList());
        XdaCommodityAppV4IDTO appIDTO = XdaCommodityAppV4IDTO.builder().orderTime(detailAppIDTO.getOrderTime())
                .storeId(detailAppIDTO.getStoreId()).commodityIdList(serialCommodityIdList)
                .needDefaultImage(false).build();

        // 获取客户业务类型
        StoreSelectODTO storeSelectODTO = storeManageClient.getStoreData(appIDTO.getStoreId());
        Integer tdaStoreStatus = BusinessTypeEnums.TD_SALE.getCode().equals(storeSelectODTO.getBusinessType()) ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode();
        appIDTO.setTdaStoreStatus(tdaStoreStatus);

        Integer businessType = getSafeBusinessType(storeSelectODTO.getBusinessType());

        appIDTO.setBusinessType(businessType);

        Boolean isPfsStore = storeService.isPfsStore(appIDTO.getStoreId());
        appIDTO.setIsPfsStore(isPfsStore);

        List<XdaCommodityAppV4ODTO> serialList = this.queryXdaCommodityDetailsListForApp(appIDTO, null, fromPage);
        if(CollectionUtils.isNotEmpty(serialList)){
            appODTOList.addAll(serialList);
            detailAppODTO.setIsSerial(1);
        }
        List<XdaSerialCommodityDetailV4ODTO> serialCommodityODTOList = appODTOList.stream().map(XdaSerialCommodityDetailV4ODTO::convert)
                .sorted(Comparator.comparing(XdaSerialCommodityDetailV4ODTO::getSortPrice)).collect(Collectors.toList());
        serialCommodityODTOList.forEach(item-> {
            if (item.getCommodityId().equals(detailAppODTO.getCommodityId())) {
                item.setIsCurrentCommodity(1);
            }else{
                item.setIsCurrentCommodity(0);
            }
        });
        detailAppODTO.setSerialCommodityDetailList(serialCommodityODTOList);
    }


    /**
     * 获取收藏状态
     * @param detailAppODTO
     * @param commodityId
     * @param storeId
     */
    private void queryXdaCommodityCollect(XdaCommodityDetailAppV4ODTO detailAppODTO,Long commodityId,Long storeId){
        List<XdaCommodityCollect> collectList = getXdaCommodityCollects(commodityId, storeId);
        if(CollectionUtils.isEmpty(collectList)){
            detailAppODTO.setIsCollect(0);
        }else{
            detailAppODTO.setIsCollect(1);
        }
    }

    //设置商品详情页：banner、长图
    private void setXdaCommodityImage(XdaCommodityDetailAppV4ODTO detailAppODTO,Long commodityId){
        List<XdaCommodityTextPic> picList = xdaCommodityTextPicMapper.selectCommodityTextPicList(new SelectCommodityTextPicListIDTO(commodityId));
        if(CollectionUtils.isEmpty(picList)){
            return;
        }
        PicSizeEnums picSizeEnums = PicSizeEnums.PIC_750x750;
        picList.stream().collect(Collectors.groupingBy(XdaCommodityTextPic::getPicType)).forEach((picK,picV)->{
            if(CollectionUtils.isEmpty(picV)){
                return;
            }
            if(picK.intValue()== XdaCommodityTextPic.PicTypeEnums.PIC.getCode() ){
                List<String> imageUrlList = picV.stream().map(pic -> imgServerUrl +(ImageUtils.getXdImgUrlV2(pic.getPicUrl(),picSizeEnums.getSize()))).collect(Collectors.toList());
                detailAppODTO.setImageUrlList(imageUrlList);
            }
            if(picK.intValue()== XdaCommodityTextPic.PicTypeEnums.LONG_PIC.getCode() ){
                detailAppODTO.setLongPicList(this.querySplitLongPicUrlList(picV.get(0).getPicUrl()));
            }
        });
    }

    //查询长图
    private List<XdaCommodityLongPicODTO> querySplitLongPicUrlList(String longPic){
        //<长图原始路径，长图切割后的list>
        List<ImageLibraryODTO> imageODTOList = imageLibraryClient.findSingleImgAnyCondition(longPic, PicCutTypeEnum.CUT_BY_HEIGHT, PicSizeEnums.PIC_750);
        if(CollectionUtils.isNotEmpty(imageODTOList)) {
            return imageODTOList.stream().map(XdaCommodityLongPicODTO::convert).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }
    
    /**
     * APP查询商品列表信息
     * @param appIDTO
     * @return
     */
    public List<XdaCommodityAppV4ODTO> queryXdaCommodityListForAppV4(XdaCommodityAppV4IDTO appIDTO){
        if(appIDTO.getStoreId()==null || appIDTO.getOrderTime()==null){
            return Collections.emptyList();
        }

        StoreSelectODTO storeSelectODTO = storeManageClient.getStoreData(appIDTO.getStoreId());
        Integer tdaStoreStatus = BusinessTypeEnums.TD_SALE.getCode().equals(storeSelectODTO.getBusinessType()) ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode();
        appIDTO.setTdaStoreStatus(tdaStoreStatus);

        Integer businessType = getSafeBusinessType(storeSelectODTO.getBusinessType());
        appIDTO.setBusinessType(businessType);

        Boolean isPfsStore = storeService.isPfsStore(appIDTO.getStoreId());
        appIDTO.setIsPfsStore(isPfsStore);
        List<XdaCommodityAppV4ODTO> appODTOList = xdaCommodityFrontMapperV4.queryXdaCommodityListForAppV4(appIDTO);
        if (CollectionUtils.isEmpty(appODTOList)) {
            log.warn("APP未获取到商品信息，请确认B端销售商品范围。查询参数：storeId={},commodityId={}",appIDTO.getStoreId(),appIDTO.getCommodityIdList());
            return Collections.emptyList();
        }

        // 获取已售罄商品  2024-05-10去除此处校验逻辑，影响app组合品库存展示
//        List<XdaCommodityAppV4ODTO> soldOutList = appODTOList.stream().filter(p -> SalesStatusEnums.SOLD_OUT.getCode().equals(p.getSalesStatus())).collect(Collectors.toList());
//        if(CollectionUtils.isNotEmpty(soldOutList)){
//            bStockService.setSoldOutList(appIDTO.getOrderTime(), soldOutList);
//        }

        setXdaCommodityInfo(appODTOList,appIDTO, FromPageEnums.XDA_APP_CATEGORY_COMMODITY_LIST);

        return appODTOList;
    }


    public List<XdaShoppingCartODTO> queryXdaShoppingCartQuantityV2(Long storeId, List<Long> commodityIdList, Integer commodityType) {
        return xdaCommodityFrontMapper.queryXdaShoppingCartQuantityV2(storeId, commodityIdList, commodityType);
    }


    public List<XdaCommodityAppV4ODTO> queryXdaCommodityDetailsForApp(XdaCommodityAppV4IDTO idto){
        StoreSelectODTO storeSelectODTO = storeManageClient.getStoreData(idto.getStoreId());
        Integer tdaStoreStatus = BusinessTypeEnums.TD_SALE.getCode().equals(storeSelectODTO.getBusinessType()) ? YesOrNoEnums.YES.getCode() : YesOrNoEnums.NO.getCode();
        idto.setTdaStoreStatus(tdaStoreStatus);
        Integer businessType = getSafeBusinessType(storeSelectODTO.getBusinessType());
        idto.setBusinessType(businessType);
        Boolean isPfsStore = storeService.isPfsStore(idto.getStoreId());
        idto.setIsPfsStore(isPfsStore);
        return xdaCommodityFrontMapperV4.queryXdaCommodityDetailsForAppV4(idto);
    }

    public List<XdaCommodityAppV4ODTO> queryXdaCommodityListForApp(XdaCommodityAppV4IDTO idto){
        return xdaCommodityFrontMapperV4.queryXdaCommodityListForAppV4(idto);
    }

    public List<XdaCommodityCollect> getXdaCommodityCollects(Long commodityId, Long storeId) {
        Example example = new Example(XdaCommodityCollect.class);
        example.createCriteria().andEqualTo("commodityId", commodityId).andEqualTo("storeId", storeId);
        return xdaCommodityCollectMapper.selectByExample(example);
    }

    public List<CommodityFreezeGroup> getCommodityFreezeGroups(List<Long> commodityIdList) {
        if (CollectionUtils.isEmpty(commodityIdList)){
            return Collections.emptyList();
        }
        Example example = new Example(CommodityFreezeGroup.class);
        example.createCriteria().andIn("commodityId", commodityIdList);
        return commodityFreezeGroupMapper.selectByExample(example);
    }


    /**
     * 根据storeId查询客户的产品价格方案下面的商品及价格
     * @param storeId
     * @return
     */
    public List<StoreCommodityPriceODTO> getStoreCommodityPrice(Long storeId) {
        return xdaCommodityFrontMapperV4.getStoreCommodityPrice(storeId);
    }


    public Integer getSafeBusinessType(Integer businessType) {
        // 定义允许直接返回的业务类型列表
        Set<Integer> specialBusinessTypes = new HashSet<>(Arrays.asList(
                BusinessTypeEnums.TD_SALE.getCode(),
                BusinessTypeEnums.BIGSHOP_SALE.getCode(),
                BusinessTypeEnums.PLAN_SALE.getCode(),
                BusinessTypeEnums.B_COUNTRY.getCode()
        ));

        if (businessType != null && specialBusinessTypes.contains(businessType)) {
            return businessType;
        }

        return BusinessTypeEnums.SALE.getCode();
    }
}
