package com.pinshang.qingyun.xda.product.utils;

import com.pinshang.qingyun.box.utils.ConcurrentDateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;

import java.text.DateFormat;
import java.text.ParseException;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 23/6/2/002 10:07
 */

public class DateUtils {
    /**
     * 循环时间 Integer[]{1,2,3,4,5,6,7}
     * 订货时间
     * @param week
     * @param date
     * @return
     */
    public static Boolean isContainWeek(Integer[] week, Date date){
        if(SpringUtil.isEmpty(week))
            return Boolean.FALSE;
        return Arrays.asList(week).stream().anyMatch(item-> item.equals(getWeekInt(date)));
    }

    /**
     * 循环时间 String[]{"一", "二", "三", "四", "五", "六", "七"}
     * 订货时间
     * @param week
     * @param date
     * @return
     */
    public static Boolean isContainWeek(String[] week, Date date){
        if(SpringUtil.isEmpty(week))
            return Boolean.FALSE;
        return Arrays.asList(week).stream().anyMatch(item-> item.equals(getWeekStr(date)));
    }

    /**
     * 根据时间获取周几
     * @param date
     * @return
     */
    public static Integer getWeekInt(Date date){
        Integer[] weekDays = {7, 1, 2, 3, 4, 5, 6};
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        return weekDays[calendar.get(Calendar.DAY_OF_WEEK)-1];
    }

    /**
     * 根据时间获取周几
     * @param date
     * @return
     */
    public static String getWeekStr(Date date){
        String[] weekDays = {"七","一", "二", "三", "四", "五", "六"};
        Calendar calendar=Calendar.getInstance();
        calendar.setTime(date);
        return weekDays[calendar.get(Calendar.DAY_OF_WEEK)-1];
    }

    /**
     * @param args
     * @throws ParseException
     */
    public static void main(String[] args) throws ParseException {
        String date = "2023-06-03 21:30:21";
        DateFormat dateFormat = ConcurrentDateUtil.SDF_FULL_DATE_TIME.get();
        Date parse = dateFormat.parse(date);

        Integer[] integers = {4,5,6};
        System.out.println(isContainWeek(integers,parse));

        String s = "一;二;三;五;六";
        String[] strings = s.split(";");
        if(isContainWeek(strings,parse)){
            System.out.println("-------------符合----------");
        }

    }
}
