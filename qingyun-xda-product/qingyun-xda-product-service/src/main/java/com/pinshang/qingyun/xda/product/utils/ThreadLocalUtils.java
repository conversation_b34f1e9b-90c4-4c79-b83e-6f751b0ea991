package com.pinshang.qingyun.xda.product.utils;


/**
 * summary.
 * <p>
 * detailed description
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019-05-10
 */
public class ThreadLocalUtils {
    private static ThreadLocal<Boolean> threadLocal = new ThreadLocal<>();
private static ThreadLocal<Long> logisticsCenterIdThreadLocal = new ThreadLocal<>(); // 物流中心

    public static Boolean get() {
        return threadLocal.get() == null ? false : threadLocal.get();
    }

    public static void set(Boolean b) {
        threadLocal.set(b);
    }


    public static void setLogisticsCenterId(Long logisticsCenterId) {
        logisticsCenterIdThreadLocal.set(logisticsCenterId);
    }

    public static Long getLogisticsCenterId() {
        return logisticsCenterIdThreadLocal.get();
    }

    public static void remove(){
        threadLocal.remove();
        logisticsCenterIdThreadLocal.remove();
    }

}
