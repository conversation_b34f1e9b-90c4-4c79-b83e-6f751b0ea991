#security:
#  ignored: /chk.html,/robots.txt
server:
  tomcat:
    uri-encoding: UTF-8
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
mybatis:
    type-aliases-package: com.pinshang.qingyun.xda.*.model
    mapper-locations: classpath*:mappers/*.xml
    configuration:
      map-underscore-to-camel-case: true
      use-generated-keys: true

mapper:
    mappers:
        - com.pinshang.qingyun.base.mybatis.MyMapper
    not-empty: false
    identity: MYSQL

pagehelper:
    helperDialect: mysql
    reasonable: false
    supportMethodsArguments: true
    params: count=countSql
spring:
  http:
    encoding:
      charset: UTF-8
  jackson:
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: true
feign:
  httpclient:
    enabled: true
  hystrix:
    enabled: true
management:
  endpoints:
    web:
      exposure:
        include: '*'
eureka:
  client:
    healthcheck:
      enabled: true
  instance:
    metadata-map:
      instanceVersion: V3.0.0