pinshang:
  application-prefix:
  application-name: qingyun-xda-product-service
spring:
  application:
    name: ${application.name.switch:${pinshang.application-prefix}}${pinshang.application-name}
  profiles:
    active: dev
  freemarker:
    suffix: .ftl
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
server:
  tomcat:
    uri-encoding: UTF-8
    basedir: ./logs/${spring.application.name}
  port: 9037
logging:
  file:
    name: ${pinshang.application-name}
    path: ./logs/${spring.application.name}
app:
  id: ${pinshang.application-name}
apollo:
  cluster: ${application.name.switch:default}
  bootstrap:
    enabled: true
#-----------------------------------  me  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  config:
    activate:
      on-profile: me
apollo:
  meta: http://192.168.20.104:8080
#-----------------------------------  dev 各环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  config:
    activate:
      on-profile: dev
  cloud:
    config:
      enabled: false
apollo:
  meta: http://192.168.20.104:8080
#-----------------------------------  test  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  config:
    activate:
      on-profile: test
  cloud:
    config:
      enabled: false
apollo:
  meta: http://192.168.20.34:8080
#-----------------------------------  wg-prod  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  config:
    activate:
      on-profile: wg-prod
  cloud:
    config:
      enabled: false
apollo:
  meta: http://192.168.103.103:8080
#-----------------------------------  wg-hd-prod  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  config:
    activate:
      on-profile: wg-hd-prod
  cloud:
    config:
      enabled: false
apollo:
  meta: http://192.168.100.13:8080