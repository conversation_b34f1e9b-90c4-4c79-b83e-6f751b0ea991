<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.deliveryDate.PfOrderCommodityLogDao">
    <!-- PfOrderCommodityLog的resultMap,column是给数据库列起的别名,它对应property类的属性-->
    <resultMap id="result_PfOrderCommodityLog_Map" type="com.pinshang.qingyun.xda.product.model.deliveryDate.PfOrderCommodityLog">
        <id column="id" property="id" />
        <result column="commodity_code" property="commodityCode" />
        <result column="create_time" property="createTime" />
        <result column="create_id" property="createId" />
        <result column="delivery_date_range_code" property="deliveryDateRangeCode" />
        <result column="delivery_date_range_value" property="deliveryDateRangeValue" />
        <result column="op_type" property="opType" />
        <result column="change_price" property="changePrice" />
        <result column="spec" property="spec" />
        <result column="commodity_name" property="commodityName" />
        <result column="create_name" property="createName" />
    </resultMap>

    <!-- 数据库中表名为:t_pf_order_commodity_log的列名,as前是数据库的列明,as后是列的别名用于映射成实体类中的属性,需要注意的是别名必须与resultMap中的column别名一致 -->
    <sql id="t_pf_order_commodity_log_Column">
        t_pf_order_commodity_log.id as id
        ,t_pf_order_commodity_log.commodity_code as commodity_code
        ,t_pf_order_commodity_log.create_time as create_time
        ,t_pf_order_commodity_log.create_id as create_id
        ,t_pf_order_commodity_log.delivery_date_range_code as delivery_date_range_code
        ,t_pf_order_commodity_log.delivery_date_range_value as delivery_date_range_value
        ,t_pf_order_commodity_log.op_type as op_type
        ,t_pf_order_commodity_log.change_price as change_price
        ,t_pf_order_commodity_log.spec as spec
        ,t_pf_order_commodity_log.commodity_name as commodity_name
        ,t_pf_order_commodity_log.create_name as create_name
    </sql>


    <select id="getPfOrderCommodityLogList" parameterType="com.pinshang.qingyun.xda.product.mapper.deliveryDate.XdaOrderCommodityLogDao"
            resultType="com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityLogDTO">
        SELECT
        log.id,
        log.op_type,
        log.commodity_name,
        log.commodity_code,
        log.spec,
        log.delivery_date_range_code,
        log.delivery_date_range_value,
        log.change_price,
        log.create_time,
        log.create_id,
        log.create_name
        FROM
        t_pf_order_commodity_log AS log
        <where>
            <if test="operateType != null and operateType!=''">
                and log.op_type=#{operateType}
            </if>
            <if test="commodityId != null and commodityId!=''">
                and log.commodity_code=#{commodityId}
            </if>

        </where>
        order by log.create_time desc
    </select>

    <!-- 将PfOrderCommodityLog插入到对应数据库的表中,包括属性值为null的数据-->

    <insert id="insertPfOrderCommodityLog" parameterType="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodityLog">
        insert into t_pf_order_commodity_log(id,commodity_code,create_time,create_id,delivery_date_range_code,delivery_date_range_value,op_type,change_price,spec,commodity_name,create_name)
        values(#{id},#{commodityCode},#{createTime},#{createId},#{deliveryDateRangeCode},#{deliveryDateRangeValue},#{opType},#{changePrice},#{spec},#{commodityName},#{createName})
    </insert>

</mapper>