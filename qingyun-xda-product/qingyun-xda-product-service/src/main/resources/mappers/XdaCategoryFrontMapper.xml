<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaCategoryFrontMapper">

	<sql id="queryStoreCondition">
		SELECT pm.id AS productPriceModelId,tss.settlement_customer_id AS settlementCustomerId,ts.id AS storeId
		FROM t_product_price_model pm
		INNER JOIN t_store_settlement tss ON pm.id = tss.product_price_model_id AND pm.price_model_state = 1
		INNER JOIN t_store ts ON ts.id = tss.store_id
		WHERE ts.id = #{storeId}
	</sql>
	<sql id="queryStoreCommodityCondition">
		SELECT ppml.commodity_id,ppml.commodity_price
		FROM t_product_price_model pm
		INNER JOIN t_product_price_model_list ppml ON ppml.product_price_model_id = pm.id AND pm.price_model_state = 1
		INNER JOIN t_store_settlement tss ON ppml.product_price_model_id = tss.product_price_model_id
		WHERE tss.store_id = #{storeId}
	</sql>

	<select id="queryXdaCategoryList" resultType="com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaCategoryBaseODTO">
		SELECT
			DISTINCT xc.id as xdaCategoryId,
			xc.cate_name as xdaCategoryName,
			xc.sort_num as xdaCategorySort,
			xc.parent_id
		FROM t_xda_commodity_text ct
		INNER JOIN t_xda_commodity_app_status cas ON ct.commodity_id = cas.commodity_id AND cas.app_status = 0
		INNER JOIN  t_xda_category xc ON
		<if test="level==1">ct.first_category_id = xc.id</if>
		<if test="level==2">ct.second_category_id = xc.id</if> AND xc.`status` = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"/>
		) tmp ON ct.commodity_id = tmp.commodity_id
		<where>
			<if test="null != xdaCategoryIdList and xdaCategoryIdList.size > 0 ">
				AND ct.first_category_id IN
				<foreach collection="xdaCategoryIdList" index="index" item="id" open="(" separator="," close=")">
					#{id}
				</foreach>
			</if>
		</where>
		ORDER BY IFNULL(xc.sort_num,100000) ASC,xc.update_time desc
	</select>

	<select id="queryOftenBuyCommodityIdList" resultType="java.lang.Long">
		SELECT fp.commodity_id
		FROM t_xda_frequent_purchase fp
		INNER JOIN t_xda_commodity_text ct ON fp.commodity_id = ct.commodity_id AND fp.store_id = #{storeId}
		INNER JOIN t_xda_commodity_app_status cas ON ct.commodity_id = cas.commodity_id AND cas.app_status = 0
		INNER JOIN (
		<include refid="queryStoreCommodityCondition"></include>
		)tmp ON ct.commodity_id = tmp.commodity_id
		WHERE fp.update_time BETWEEN DATE_SUB(NOW(), INTERVAL 30 DAY)  AND NOW()
		ORDER BY fp.quantity DESC,fp.update_time DESC
		LIMIT 30
	</select>

	<select id="queryCollectCommodityIdList" resultType="java.lang.Long">
		SELECT cc.commodity_id
		FROM t_xda_commodity_collect cc
		INNER JOIN t_xda_commodity_text ct ON cc.commodity_id = ct.commodity_id AND cc.store_id = #{storeId}
		INNER JOIN t_xda_commodity_app_status cas ON ct.commodity_id = cas.commodity_id AND cas.app_status = 0
		INNER JOIN (
		<include refid="queryStoreCommodityCondition"></include>
		)tmp ON ct.commodity_id = tmp.commodity_id
		ORDER BY cc.create_time DESC
	</select>

	<select id="queryXdaCategoryCommoditySearch" resultType="com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppODTO"
			parameterType="com.pinshang.qingyun.xda.product.dto.front.categoryFront.XdaSearchAppIDTO">
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			-- c.sales_box_capacity,
			CASE
			WHEN #{isPfsStore} = true THEN c.pf_box_capacity
			ELSE c.sales_box_capacity
			END as salesBoxCapacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			tmp.commodity_price,
			cas.app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			pic.pic_url AS imageUrl,
			ct.sort_num,
			sale.total_quantity as saleQuantity
			<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 and null != xdaSecondCategoryId">
				,secTmp.serial_commodity_id
			</if>
		FROM
			t_xda_commodity_text ct
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id AND cas.app_status = 0
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"></include>
		)tmp ON ct.commodity_id = tmp.commodity_id

		<if test="null != keyword and keyword != '' ">
			INNER JOIN (
			SELECT t.commodity_id,
			CASE WHEN t.commodity_app_name LIKE concat('%',#{keyword},'%') THEN 1
			WHEN cate.cate_name LIKE concat('%',#{keyword},'%') THEN 2 END AS keywordSort
			FROM t_xda_commodity_text t
			INNER JOIN t_xda_category cate ON t.second_category_id = cate.id
			WHERE (t.commodity_app_name LIKE concat('%',#{keyword},'%') OR cate.cate_name LIKE
			concat('%',#{keyword},'%'))
			) cate ON ct.commodity_id = cate.commodity_id
		</if>

		LEFT JOIN t_xda_commodity_sale_multi_day_statistics sale ON ct.commodity_id = sale.commodity_id
		LEFT JOIN (
			SELECT a.commodityId,min(a.price) AS price
			FROM (
				<include refid="com.pinshang.qingyun.xda.product.mapper.XdaCommodityFrontMapper.queryStoreCommoditySpecialPrice"/>
			)a GROUP BY a.commodityId
		) sp ON ct.commodity_id = sp.commodityId
		<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 and null != xdaSecondCategoryId">
			LEFT JOIN t_xda_serial_commodity sec ON sec.commodity_id = ct.commodity_id
			LEFT JOIN (
				SELECT  sc.serial_commodity_id,ct.second_category_id
				FROM t_xda_serial_commodity sc
				INNER JOIN t_xda_commodity_text ct ON sc.serial_commodity_id = ct.commodity_id
				GROUP BY sc.serial_commodity_id HAVING count(1)>1
			) secTmp ON sec.serial_commodity_id = secTmp.serial_commodity_id AND secTmp.second_category_id = ct.second_category_id
		</if>
		INNER JOIN (
			SELECT commodity_id,
			CASE WHEN DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1) THEN 1 ELSE 0 END AS isCanOrder
			FROM t_xda_order_commodity
		) xoc ON ct.commodity_id = xoc.commodity_id
		<where>
			AND c.status = 1
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
			<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 ">
				and ct.first_category_id = #{xdaFirstCategoryId}
				<if test="null != xdaSecondCategoryId and xdaSecondCategoryId > 0 ">
					and ct.second_category_id = #{xdaSecondCategoryId}
				</if>
			</if>
		</where>
		ORDER BY
		 xoc.isCanOrder DESC
		<choose>
			<when test="priceSort !=null and priceSort >0">
				<if test="priceSort == 1">
					,IFNULL(sp.price, tmp.commodity_price) ASC
				</if>
				<if test="priceSort == 2">
					,IFNULL(sp.price, tmp.commodity_price) DESC
				</if>
			</when>
			<otherwise>
				<if test="saleSort==1">
					,IFNULL(sale.total_quantity, 0) ASC
				</if>
				<if test="saleSort==2">
					,IFNULL(sale.total_quantity, 0) DESC
				</if>
			</otherwise>
		</choose>
		<choose>
			<when test="null != commodityIdList and commodityIdList.size > 0 ">
				,FIELD(ct.commodity_id,
				<foreach collection="commodityIdList" item="id" separator=",">
					#{id}
				</foreach>
				)
			</when>
			<when test="null != keyword and keyword != ''">
				,cate.keywordSort,ct.id
			</when>
			<otherwise>
				<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 ">
					,IFNULL(ct.sort_num,100000) ASC,ct.update_time DESC
				</if>
			</otherwise>
		</choose>
	</select>

	<select id="queryXdaReCommendFront" resultType="com.pinshang.qingyun.xda.product.dto.front.XdaReCommendAppODTO">
		SELECT cr.commodity_id,ct.first_category_id AS firstCateId
		FROM t_xda_commodity_recommend cr
		INNER JOIN t_xda_commodity_text ct ON cr.commodity_id = ct.commodity_id
		INNER JOIN t_xda_commodity_app_status cas ON ct.commodity_id = cas.commodity_id AND cas.app_status = 0
		INNER JOIN (
		<include refid="queryStoreCommodityCondition"></include>
		)tmp ON ct.commodity_id = tmp.commodity_id
		<where>
			<if test="null != firstIdList and firstIdList.size > 0 ">
				AND ct.first_category_id IN
				<foreach collection="firstIdList" index="index" item="id" open="(" separator="," close=")">
					#{id}
				</foreach>
			</if>
		</where>
		ORDER BY cr.create_time DESC
	</select>

	<!-- 查询鲜达特惠分类信息 -->
	<select id="queryXdaThCategoryCommodity" resultType="com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaCommodityAppV2ODTO">
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			c.sales_box_capacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			tmp.commodity_price,
			cas.app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			pic.pic_url AS imageUrl,
			ct.sort_num,
			scs.commodity_limit as thLimitNumber,
			scs.commodity_specials_price as thPrice,
			sale.total_quantity as saleQuantity
		FROM
			t_xda_specials_commodity_set scs
		INNER JOIN t_xda_commodity_text ct on scs.commodity_id = ct.commodity_id
		INNER JOIN t_xda_category ca on ca.id = ct.first_category_id
		INNER JOIN t_xda_category ca2 on ca2.id = ct.second_category_id
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id AND cas.app_status = 0
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"></include>
			)tmp ON ct.commodity_id = tmp.commodity_id

		LEFT JOIN t_xda_commodity_sale_multi_day_statistics sale ON ct.commodity_id = sale.commodity_id
		INNER JOIN (
			SELECT commodity_id,
			CASE WHEN DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1) THEN 1 ELSE 0 END AS isCanOrder
			FROM t_xda_order_commodity
			) xoc ON ct.commodity_id = xoc.commodity_id
		ORDER BY
			xoc.isCanOrder DESC,
		<choose>
			<when test="priceSort !=null and priceSort >0">
				<if test="priceSort == 1">
					IFNULL(scs.commodity_specials_price, tmp.commodity_price) ASC,
				</if>
				<if test="priceSort == 2">
					IFNULL(scs.commodity_specials_price, tmp.commodity_price) DESC,
				</if>
			</when>
			<otherwise>
				<if test="saleSort==1">
					IFNULL(sale.total_quantity, 0) ASC,
				</if>
				<if test="saleSort==2">
					IFNULL(sale.total_quantity, 0) DESC,
				</if>
			</otherwise>
		</choose>
		ca.sort_num asc, ca2.sort_num asc
	</select>
	<!-- 查询鲜达特惠分类信息 -->
	<select id="queryXdaThCategoryCommodityV3" resultType="com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityAppV3ODTO">
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			c.sales_box_capacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			tmp.commodity_price,
			cas.app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			pic.pic_url AS imageUrl,
			ct.sort_num,
			scs.commodity_limit as thLimitNumber,
			scs.commodity_specials_price as thPrice,
			sale.total_quantity as saleQuantity
		FROM
			t_xda_specials_commodity_set scs
		INNER JOIN t_xda_commodity_text ct on scs.commodity_id = ct.commodity_id
		INNER JOIN t_xda_category ca on ca.id = ct.first_category_id
		INNER JOIN t_xda_category ca2 on ca2.id = ct.second_category_id
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id AND cas.app_status = 0
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"></include>
			)tmp ON ct.commodity_id = tmp.commodity_id

		LEFT JOIN t_xda_commodity_sale_multi_day_statistics sale ON ct.commodity_id = sale.commodity_id
		INNER JOIN (
			SELECT commodity_id,
			CASE WHEN DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1) THEN 1 ELSE 0 END AS isCanOrder
			FROM t_xda_order_commodity
			) xoc ON ct.commodity_id = xoc.commodity_id
		WHERE c.status = 1
		ORDER BY
			xoc.isCanOrder DESC,
		<choose>
			<when test="priceSort !=null and priceSort >0">
				<if test="priceSort == 1">
					IFNULL(scs.commodity_specials_price, tmp.commodity_price) ASC,
				</if>
				<if test="priceSort == 2">
					IFNULL(scs.commodity_specials_price, tmp.commodity_price) DESC,
				</if>
			</when>
			<otherwise>
				<if test="saleSort==1">
					IFNULL(sale.total_quantity, 0) ASC,
				</if>
				<if test="saleSort==2">
					IFNULL(sale.total_quantity, 0) DESC,
				</if>
			</otherwise>
		</choose>
		ca.sort_num asc, ca2.sort_num asc
	</select>
	<select id="queryXdaCategoryCommoditySearchV3"
			resultType="com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityAppV3ODTO">
		SELECT
		c.id AS commodityId,
		c.commodity_code,
		ct.commodity_app_name AS commodityName,
		ct.commodity_sub_name AS commoditySubName,
		c.commodity_spec AS commoditySpec,
		d.option_name AS commodityUnitName,
		c.sales_box_capacity,
		c.commodity_is_quick_freeze AS isQuickFreeze,
		c.is_weight AS isWeight,
		c.commodity_package_spec AS commodityPackageSpec,
		c.commodity_weight AS commodityWeight,
		c.storage_condition AS storageCondition,
		c.quality_days AS qualityDays,
		tmp.commodity_price,
		cas.app_status,
		ct.first_category_id AS xdaFirstCategoryId,
		ct.second_category_id AS xdaSecondCategoryId,
		pic.pic_url AS imageUrl,
		ct.sort_num,
		sale.total_quantity as saleQuantity
		<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 and null != xdaSecondCategoryId">
			,secTmp.serial_commodity_id
		</if>
		FROM
		t_xda_commodity_text ct
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id AND cas.app_status = 0
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
		<include refid="queryStoreCommodityCondition"></include>
		)tmp ON ct.commodity_id = tmp.commodity_id

		<if test="null != keyword and keyword != '' ">
			INNER JOIN (
			SELECT t.commodity_id,
			CASE WHEN t.commodity_app_name LIKE concat('%',#{keyword},'%') THEN 1
			WHEN cate.cate_name LIKE concat('%',#{keyword},'%') THEN 2 END AS keywordSort
			FROM t_xda_commodity_text t
			INNER JOIN t_xda_category cate ON t.second_category_id = cate.id
			WHERE (t.commodity_app_name LIKE concat('%',#{keyword},'%') OR cate.cate_name LIKE
			concat('%',#{keyword},'%'))
			) cate ON ct.commodity_id = cate.commodity_id
		</if>

		LEFT JOIN t_xda_commodity_sale_multi_day_statistics sale ON ct.commodity_id = sale.commodity_id
		LEFT JOIN (
		SELECT a.commodityId,min(a.price) AS price
		FROM (
		<include refid="com.pinshang.qingyun.xda.product.mapper.XdaCommodityFrontMapper.queryStoreCommoditySpecialPrice"/>
		)a GROUP BY a.commodityId
		) sp ON ct.commodity_id = sp.commodityId
		<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 and null != xdaSecondCategoryId">
			LEFT JOIN t_xda_serial_commodity sec ON sec.commodity_id = ct.commodity_id
			LEFT JOIN (
			SELECT  sc.serial_commodity_id,ct.second_category_id
			FROM t_xda_serial_commodity sc
			INNER JOIN t_xda_commodity_text ct ON sc.serial_commodity_id = ct.commodity_id
			GROUP BY sc.serial_commodity_id HAVING count(1)>1
			) secTmp ON sec.serial_commodity_id = secTmp.serial_commodity_id AND secTmp.second_category_id = ct.second_category_id
		</if>
		INNER JOIN (
		SELECT commodity_id,
		CASE WHEN DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1) THEN 1 ELSE 0 END AS isCanOrder
		FROM t_xda_order_commodity
		) xoc ON ct.commodity_id = xoc.commodity_id
		<where>
			AND c.status = 1
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
			<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 ">
				and ct.first_category_id = #{xdaFirstCategoryId}
				<if test="null != xdaSecondCategoryId and xdaSecondCategoryId > 0 ">
					and ct.second_category_id = #{xdaSecondCategoryId}
				</if>
			</if>
		</where>
		ORDER BY
		xoc.isCanOrder DESC
		<choose>
			<when test="priceSort !=null and priceSort >0">
				<if test="priceSort == 1">
					,IFNULL(sp.price, tmp.commodity_price) ASC
				</if>
				<if test="priceSort == 2">
					,IFNULL(sp.price, tmp.commodity_price) DESC
				</if>
			</when>
			<otherwise>
				<if test="saleSort==1">
					,IFNULL(sale.total_quantity, 0) ASC
				</if>
				<if test="saleSort==2">
					,IFNULL(sale.total_quantity, 0) DESC
				</if>
			</otherwise>
		</choose>
		<choose>
			<when test="null != commodityIdList and commodityIdList.size > 0 ">
				,FIELD(ct.commodity_id,
				<foreach collection="commodityIdList" item="id" separator=",">
					#{id}
				</foreach>
				)
			</when>
			<when test="null != keyword and keyword != ''">
				,cate.keywordSort,ct.id
			</when>
			<otherwise>
				<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 ">
					,IFNULL(ct.sort_num,100000) ASC,ct.update_time DESC
				</if>
			</otherwise>
		</choose>
	</select>

</mapper>