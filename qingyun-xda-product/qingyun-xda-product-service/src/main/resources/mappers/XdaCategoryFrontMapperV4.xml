<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaCategoryFrontMapperV4">
	<sql id="queryStoreCommodityCondition">
		SELECT ppml.commodity_id,ppml.commodity_price
		FROM t_product_price_model pm
		INNER JOIN t_product_price_model_list ppml ON ppml.product_price_model_id = pm.id AND pm.price_model_state = 1
		INNER JOIN t_store_settlement tss ON ppml.product_price_model_id = tss.product_price_model_id
		WHERE tss.store_id = #{storeId}
	</sql>

	<!-- 查询鲜达特惠分类信息 -->
	<select id="queryXdaThCategoryCommodityV4" resultType="com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityAppV4ODTO">
		select outerT.* from (
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			c.sales_box_capacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			tmp.commodity_price,
			cas.app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			pic.pic_url AS imageUrl,
			c.product_type,
			ct.sort_num,
			scs.commodity_limit as thLimitNumber,
			scs.commodity_specials_price as thPrice,
			sale.total_quantity as saleQuantity
			,
			xoc.salesStatus,
			ca.sort_num AS cateSortNum1, ca2.sort_num AS cateSortNum2
		FROM t_xda_specials_commodity_set scs
		INNER JOIN t_xda_commodity_text ct on scs.commodity_id = ct.commodity_id
		INNER JOIN t_xda_category ca on ca.id = ct.first_category_id
		INNER JOIN t_xda_category ca2 on ca2.id = ct.second_category_id
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id AND cas.app_status = 0
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"></include>
			)tmp ON ct.commodity_id = tmp.commodity_id
			
		LEFT JOIN t_xda_commodity_sale_multi_day_statistics sale ON ct.commodity_id = sale.commodity_id
		INNER JOIN (
			SELECT tcs.commodity_id, 1 AS salesStatus
			FROM t_dc_tob_commodity_stock tcs 
			LEFT JOIN t_dc_tob_process_order_commodity_statistics tpocs ON tcs.commodity_id = tpocs.commodity_id AND tpocs.order_time = #{orderTime}
			INNER JOIN t_xda_order_commodity oc ON tcs.commodity_id = oc.commodity_id
			WHERE DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1)
			<if test="null != businessType and businessType == 10 ">
				AND tcs.logistics_center_id = 1
				AND (
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.tda_stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.tda_stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.tda_stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>
			<if test="null != businessType and businessType == 15 ">
				AND (
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.national_stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.national_stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.national_stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>
			<if test="null != businessType and businessType == 0 ">
				AND tcs.logistics_center_id = 1
				AND (
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>

		) xoc ON ct.commodity_id = xoc.commodity_id
		WHERE c.status = 1
		UNION ALL
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			c.sales_box_capacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			tmp.commodity_price,
			cas.app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			pic.pic_url AS imageUrl,
			c.product_type,
			ct.sort_num,
			scs.commodity_limit as thLimitNumber,
			scs.commodity_specials_price as thPrice,
			sale.total_quantity as saleQuantity
			,
			xoc.salesStatus,
			ca.sort_num AS cateSortNum1, ca2.sort_num AS cateSortNum2
		FROM t_xda_specials_commodity_set scs
		INNER JOIN t_xda_commodity_text ct on scs.commodity_id = ct.commodity_id
		INNER JOIN t_xda_category ca on ca.id = ct.first_category_id
		INNER JOIN t_xda_category ca2 on ca2.id = ct.second_category_id
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id AND cas.app_status = 0
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"></include>
		)tmp ON ct.commodity_id = tmp.commodity_id
		LEFT JOIN t_xda_commodity_sale_multi_day_statistics sale ON ct.commodity_id = sale.commodity_id
		INNER JOIN (
			SELECT tcs.commodity_id, 2 AS salesStatus
			FROM t_dc_tob_commodity_stock tcs 
			LEFT JOIN t_dc_tob_process_order_commodity_statistics tpocs ON tcs.commodity_id = tpocs.commodity_id AND tpocs.order_time = #{orderTime}
			INNER JOIN t_xda_order_commodity oc ON tcs.commodity_id = oc.commodity_id
			WHERE DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1)
			<if test="null != businessType and businessType == 10 ">
				AND tcs.logistics_center_id = 1
				AND !(
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.tda_stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.tda_stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.tda_stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>
			<if test="null != businessType and businessType == 15 ">
				AND !(
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.national_stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.national_stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.national_stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>
			<if test="null != businessType and businessType == 0 ">
				AND tcs.logistics_center_id = 1
				AND !(
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>

		) xoc ON ct.commodity_id = xoc.commodity_id
		WHERE c.status = 1
		UNION ALL
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			c.sales_box_capacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			tmp.commodity_price,
			cas.app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			pic.pic_url AS imageUrl,
			c.product_type,
			ct.sort_num,
			scs.commodity_limit as thLimitNumber,
			scs.commodity_specials_price as thPrice,
			sale.total_quantity as saleQuantity
			,
			xoc.salesStatus,
			ca.sort_num AS cateSortNum1, ca2.sort_num AS cateSortNum2
		FROM t_xda_specials_commodity_set scs
		INNER JOIN t_xda_commodity_text ct on scs.commodity_id = ct.commodity_id
		INNER JOIN t_xda_category ca on ca.id = ct.first_category_id
		INNER JOIN t_xda_category ca2 on ca2.id = ct.second_category_id
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id AND cas.app_status = 0
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"></include>
		) tmp ON ct.commodity_id = tmp.commodity_id
		LEFT JOIN t_xda_commodity_sale_multi_day_statistics sale ON ct.commodity_id = sale.commodity_id
		INNER JOIN (
			SELECT oc.commodity_id, 3 AS salesStatus
			FROM t_xda_order_commodity oc
			WHERE !(DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1))
		) xoc ON ct.commodity_id = xoc.commodity_id
		WHERE c.status = 1
        ) outerT
   		GROUP BY outerT.commodityId
        ORDER BY outerT.salesStatus, outerT.cateSortNum1 ASC, outerT.cateSortNum2 ASC
	</select>
	
	<select id="queryXdaCategoryCommoditySearchV4" 
		resultType="com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityAppV4ODTO">
		select outerT.* from (
		SELECT
		c.id AS commodityId,
		c.commodity_code,
		ct.commodity_app_name AS commodityName,
		ct.commodity_sub_name AS commoditySubName,
		c.commodity_spec AS commoditySpec,
		d.option_name AS commodityUnitName,
		c.sales_box_capacity,
		c.commodity_is_quick_freeze AS isQuickFreeze,
		c.is_weight AS isWeight,
		c.commodity_package_spec AS commodityPackageSpec,
		c.commodity_weight AS commodityWeight,
		c.storage_condition AS storageCondition,
		c.quality_days AS qualityDays,
		tmp.commodity_price,
		cas.app_status,
		ct.first_category_id AS xdaFirstCategoryId,
		ct.second_category_id AS xdaSecondCategoryId,
		pic.pic_url AS imageUrl,
		c.product_type,
		ct.sort_num,
		sale.total_quantity as saleQuantity
		<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 and null != xdaSecondCategoryId">
			,secTmp.serial_commodity_id
		</if>
		,xoc.salesStatus
		,ct.update_time
		<if test="null != keyword and keyword != ''">
			,cate.keywordSort AS keywordSort, ct.id AS ctId
		</if>
		FROM t_xda_commodity_text ct
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id AND cas.app_status = 0
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"></include>
		) tmp ON ct.commodity_id = tmp.commodity_id

		<if test="null != keyword and keyword != '' ">
			INNER JOIN (
			SELECT t.commodity_id,
			CASE WHEN t.commodity_app_name LIKE concat('%',#{keyword},'%') THEN 1
			WHEN cate.cate_name LIKE concat('%',#{keyword},'%') THEN 2 END AS keywordSort
			FROM t_xda_commodity_text t
			INNER JOIN t_xda_category cate ON t.second_category_id = cate.id
			WHERE (t.commodity_app_name LIKE concat('%',#{keyword},'%') OR cate.cate_name LIKE
			concat('%',#{keyword},'%'))
			) cate ON ct.commodity_id = cate.commodity_id
		</if>

		LEFT JOIN t_xda_commodity_sale_multi_day_statistics sale ON ct.commodity_id = sale.commodity_id
		<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 and null != xdaSecondCategoryId">
			LEFT JOIN t_xda_serial_commodity sec ON sec.commodity_id = ct.commodity_id
			LEFT JOIN (
			SELECT  sc.serial_commodity_id,ct.second_category_id
			FROM t_xda_serial_commodity sc
			INNER JOIN t_xda_commodity_text ct ON sc.serial_commodity_id = ct.commodity_id
			GROUP BY sc.serial_commodity_id HAVING count(1)>1
			) secTmp ON sec.serial_commodity_id = secTmp.serial_commodity_id AND secTmp.second_category_id = ct.second_category_id
		</if>
		INNER JOIN (
			SELECT tcs.commodity_id, 1 AS salesStatus
			FROM t_dc_tob_commodity_stock tcs 
			LEFT JOIN t_dc_tob_process_order_commodity_statistics tpocs ON tcs.commodity_id = tpocs.commodity_id AND tpocs.order_time = #{orderTime}
			INNER JOIN t_xda_order_commodity oc ON tcs.commodity_id = oc.commodity_id
			WHERE DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1)
			<if test="null != businessType and businessType == 10 ">
				AND tcs.logistics_center_id = 1
				AND (
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.tda_stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.tda_stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.tda_stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>
			<if test="null != businessType and businessType == 15 ">
				AND (
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.national_stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.national_stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.national_stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>
			<if test="null != businessType and businessType == 0 ">
				AND tcs.logistics_center_id = 1
				AND (
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>

		) xoc ON ct.commodity_id = xoc.commodity_id
		<where>
			AND c.status = 1
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
			<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 ">
				and ct.first_category_id = #{xdaFirstCategoryId}
				<if test="null != xdaSecondCategoryId and xdaSecondCategoryId > 0 ">
					and ct.second_category_id = #{xdaSecondCategoryId}
				</if>
			</if>
		</where>
		UNION ALL
		SELECT
		c.id AS commodityId,
		c.commodity_code,
		ct.commodity_app_name AS commodityName,
		ct.commodity_sub_name AS commoditySubName,
		c.commodity_spec AS commoditySpec,
		d.option_name AS commodityUnitName,
		c.sales_box_capacity,
		c.commodity_is_quick_freeze AS isQuickFreeze,
		c.is_weight AS isWeight,
		c.commodity_package_spec AS commodityPackageSpec,
		c.commodity_weight AS commodityWeight,
		c.storage_condition AS storageCondition,
		c.quality_days AS qualityDays,
		tmp.commodity_price,
		cas.app_status,
		ct.first_category_id AS xdaFirstCategoryId,
		ct.second_category_id AS xdaSecondCategoryId,
		pic.pic_url AS imageUrl,
		c.product_type,
		ct.sort_num,
		sale.total_quantity as saleQuantity
		<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 and null != xdaSecondCategoryId">
			,secTmp.serial_commodity_id
		</if>
		,xoc.salesStatus
		,ct.update_time
		<if test="null != keyword and keyword != ''">
			,cate.keywordSort AS keywordSort, ct.id AS ctId
		</if>
		FROM t_xda_commodity_text ct
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id AND cas.app_status = 0
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"></include>
		) tmp ON ct.commodity_id = tmp.commodity_id
		<if test="null != keyword and keyword != '' ">
			INNER JOIN (
			SELECT t.commodity_id,
			CASE WHEN t.commodity_app_name LIKE concat('%',#{keyword},'%') THEN 1
			WHEN cate.cate_name LIKE concat('%',#{keyword},'%') THEN 2 END AS keywordSort
			FROM t_xda_commodity_text t
			INNER JOIN t_xda_category cate ON t.second_category_id = cate.id
			WHERE (t.commodity_app_name LIKE concat('%',#{keyword},'%') OR cate.cate_name LIKE
			concat('%',#{keyword},'%'))
			) cate ON ct.commodity_id = cate.commodity_id
		</if>

		LEFT JOIN t_xda_commodity_sale_multi_day_statistics sale ON ct.commodity_id = sale.commodity_id
		<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 and null != xdaSecondCategoryId">
			LEFT JOIN t_xda_serial_commodity sec ON sec.commodity_id = ct.commodity_id
			LEFT JOIN (
			SELECT  sc.serial_commodity_id,ct.second_category_id
			FROM t_xda_serial_commodity sc
			INNER JOIN t_xda_commodity_text ct ON sc.serial_commodity_id = ct.commodity_id
			GROUP BY sc.serial_commodity_id HAVING count(1)>1
			) secTmp ON sec.serial_commodity_id = secTmp.serial_commodity_id AND secTmp.second_category_id = ct.second_category_id
		</if>
		INNER JOIN (
			SELECT tcs.commodity_id, 2 AS salesStatus
			FROM t_dc_tob_commodity_stock tcs 
			LEFT JOIN t_dc_tob_process_order_commodity_statistics tpocs ON tcs.commodity_id = tpocs.commodity_id AND tpocs.order_time = #{orderTime}
			INNER JOIN t_xda_order_commodity oc ON tcs.commodity_id = oc.commodity_id
			WHERE DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1)
			<if test="null != businessType and businessType == 10 ">
				AND tcs.logistics_center_id = 1
				AND !(
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.tda_stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.tda_stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.tda_stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>
			<if test="null != businessType and businessType == 15 ">
				AND !(
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.national_stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.national_stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.national_stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>
			<if test="null != businessType and businessType == 0 ">
				AND tcs.logistics_center_id = 1
				AND !(
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>

		) xoc ON ct.commodity_id = xoc.commodity_id
		<where>
			AND c.status = 1
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
			<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 ">
				and ct.first_category_id = #{xdaFirstCategoryId}
				<if test="null != xdaSecondCategoryId and xdaSecondCategoryId > 0 ">
					and ct.second_category_id = #{xdaSecondCategoryId}
				</if>
			</if>
		</where>
		UNION ALL
		SELECT
		c.id AS commodityId,
		c.commodity_code,
		ct.commodity_app_name AS commodityName,
		ct.commodity_sub_name AS commoditySubName,
		c.commodity_spec AS commoditySpec,
		d.option_name AS commodityUnitName,
		c.sales_box_capacity,
		c.commodity_is_quick_freeze AS isQuickFreeze,
		c.is_weight AS isWeight,
		c.commodity_package_spec AS commodityPackageSpec,
		c.commodity_weight AS commodityWeight,
		c.storage_condition AS storageCondition,
		c.quality_days AS qualityDays,
		tmp.commodity_price,
		cas.app_status,
		ct.first_category_id AS xdaFirstCategoryId,
		ct.second_category_id AS xdaSecondCategoryId,
		pic.pic_url AS imageUrl,
		c.product_type,
		ct.sort_num,
		sale.total_quantity as saleQuantity
		<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 and null != xdaSecondCategoryId">
			,secTmp.serial_commodity_id
		</if>
		,xoc.salesStatus
		,ct.update_time
		<if test="null != keyword and keyword != '' ">
			,cate.keywordSort AS keywordSort, ct.id AS ctId
		</if>
		FROM t_xda_commodity_text ct
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id AND cas.app_status = 0
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"></include>
		) tmp ON ct.commodity_id = tmp.commodity_id

		<if test="null != keyword and keyword != '' ">
			INNER JOIN (
			SELECT t.commodity_id,
			CASE WHEN t.commodity_app_name LIKE concat('%',#{keyword},'%') THEN 1
			WHEN cate.cate_name LIKE concat('%',#{keyword},'%') THEN 2 END AS keywordSort
			FROM t_xda_commodity_text t
			INNER JOIN t_xda_category cate ON t.second_category_id = cate.id
			WHERE (t.commodity_app_name LIKE concat('%',#{keyword},'%') OR cate.cate_name LIKE
			concat('%',#{keyword},'%'))
			) cate ON ct.commodity_id = cate.commodity_id
		</if>

		LEFT JOIN t_xda_commodity_sale_multi_day_statistics sale ON ct.commodity_id = sale.commodity_id
		<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 and null != xdaSecondCategoryId">
			LEFT JOIN t_xda_serial_commodity sec ON sec.commodity_id = ct.commodity_id
			LEFT JOIN (
			SELECT  sc.serial_commodity_id,ct.second_category_id
			FROM t_xda_serial_commodity sc
			INNER JOIN t_xda_commodity_text ct ON sc.serial_commodity_id = ct.commodity_id
			GROUP BY sc.serial_commodity_id HAVING count(1)>1
			) secTmp ON sec.serial_commodity_id = secTmp.serial_commodity_id AND secTmp.second_category_id = ct.second_category_id
		</if>
		INNER JOIN (
			SELECT oc.commodity_id, 3 AS salesStatus
			FROM t_xda_order_commodity oc
			WHERE !(DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1))
		) xoc ON ct.commodity_id = xoc.commodity_id
		<where>
			AND c.status = 1
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
			<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 ">
				and ct.first_category_id = #{xdaFirstCategoryId}
				<if test="null != xdaSecondCategoryId and xdaSecondCategoryId > 0 ">
					and ct.second_category_id = #{xdaSecondCategoryId}
				</if>
			</if>
		</where>
		) outerT
		GROUP BY outerT.commodityId
		ORDER BY outerT.salesStatus ASC
		<choose>
			<!-- <when test="null != commodityIdList and commodityIdList.size > 0 ">
				,FIELD(outerT.commodity_id,
				<foreach collection="commodityIdList" item="id" separator=",">
					#{id}
				</foreach>
				)
			</when> -->
			<when test="null != keyword and keyword != ''">
				,outerT.keywordSort, outerT.ctId
			</when>
			<otherwise>
				<if test="null != xdaFirstCategoryId and xdaFirstCategoryId > 0 ">
					,IFNULL(outerT.sort_num,100000) ASC,outerT.update_time DESC
				</if>
			</otherwise>
		</choose>
	</select>
	<select id="queryCategoryCommodityField"
			resultType="com.pinshang.qingyun.xda.product.dto.front.categoryFront.CategoryCommodityFieldODTO">
		SELECT
		c.id AS commodity_id,
		c.commodity_spec,
		c.commodity_code,
		-- c.sales_box_capacity,
		CASE
		WHEN #{isPfsStore} = true THEN c.pf_box_capacity
		ELSE c.sales_box_capacity
		END as salesBoxCapacity,
		c.commodity_is_quick_freeze as isQuickFreeze,
		c.is_weight,
		c.commodity_package_spec,
		c.commodity_weight,
		c.storage_condition,
		c.quality_days,
		d.option_name as commodityUnitName,
		ctp.pic_url as imageUrl,
		csmds.total_quantity as saleQuantity,
		secTmp.serial_commodity_id,
		ct.update_time
		FROM
		t_xda_commodity_text ct
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		LEFT JOIN t_xda_commodity_text_pic ctp ON c.id = ctp.commodity_id
		AND ctp.pic_type = 1
		AND ctp.is_default = 1
		LEFT JOIN t_xda_commodity_sale_multi_day_statistics csmds ON c.id = csmds.commodity_id
		LEFT JOIN t_xda_serial_commodity sec ON sec.commodity_id = ct.commodity_id
		LEFT JOIN (
		SELECT  sc.serial_commodity_id,ct.second_category_id
		FROM t_xda_serial_commodity sc
		INNER JOIN t_xda_commodity_text ct ON sc.serial_commodity_id = ct.commodity_id
		GROUP BY sc.serial_commodity_id HAVING count(1)>1
		) secTmp ON sec.serial_commodity_id = secTmp.serial_commodity_id AND secTmp.second_category_id = ct.second_category_id
		where c.id in
		<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</select>

</mapper>