<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaCategoryMapper">

    <!-- 查询：同一父节点下，最大排序值 -->
    <select id="selectMaxSortNoInParent" resultType="Integer" parameterType="Long">
		SELECT MAX(c.sort_num)
		FROM t_xda_category c
		WHERE c.parent_id = #{parentId}
		and c.status = 1
		;
	</select>

	<!--批量更新排序-->
	<update id="batchEditXdaCategorySortNum" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" separator=";" open="" close="">
			update
				t_xda_category
			set
				sort_num = #{item.sortNum},
				update_id = #{item.updateId},
				update_time = #{item.updateTime}
			where id = #{item.id}
		</foreach>
	</update>

	<!--查询所有二级分类 to 导出-->
	<select id="findAllSecondXdaCategoryListToExport" resultType="com.pinshang.qingyun.xda.product.dto.xdaCategory.XdaCategoryListODTO">
		select
			ca.id,
			ca.parent_id,
			xc.cate_name as firstCategoryName,
			ca.cate_name as secondCategoryName,
			ca.cate_level
		from t_xda_category ca
		left join t_xda_category xc on xc.id = ca.parent_id
		where ca.cate_level = 2 and ca.status = 1
		ORDER BY xc.sort_num asc ,ca.sort_num
	</select>

	<!--查询所有一级分类-->
	<select id="findAllFirstXdaCategoryList" resultType="com.pinshang.qingyun.xda.product.dto.xdaCategory.XdaCategoryListODTO">
		select
			ca.id,
			ca.parent_id,
			xc.cate_name as firstCategoryName,
			ca.cate_name as secondCategoryName,
			ca.cate_level
		from t_xda_category ca
		left join t_xda_category xc on xc.id = ca.parent_id
		where ca.cate_level = 1 and ca.status = 1
	</select>
	
	<!-- 查询  品类和父级信息  集合 -->
	<select id="selectXdaCategoryAndParentInfoList" resultType="com.pinshang.qingyun.xda.product.dto.xdaCategory.XdaCategoryAndParentInfoODTO">
		SELECT
			c.id,
			c.cate_name AS cateName,
			c.sort_num AS sortNum,
			c.cate_level AS cateLevel,
		
			c.parent_id AS parentId,
			parent.cate_name AS parentCateName,
			parent.sort_num AS parentSortNum,
			parent.cate_level AS parentCateLevel
		FROM t_xda_category c
		LEFT JOIN t_xda_category parent ON parent.id = c.parent_id
		WHERE c.status = 1
		AND c.id IN
		<foreach collection="categoryIdList" item="id" open="(" close=")" separator=",">
            ${id}
        </foreach>
		ORDER BY c.cate_level
	</select>

    <!-- 查询  品类和父级信息  集合 -->
    <select id="selectXdaCuponList" resultType="com.pinshang.qingyun.xda.product.dto.xdaCategory.XdaCuponODTO">
        SELECT c.id, c.parent_id AS parentId, c.cate_level AS cateLevel FROM t_xda_category c WHERE c.`status` = 1
    </select>
</mapper>