<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaCommodityAppStatusLogMapper">

    <select id="selectCommodityAppStatusLogList" resultType="com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.XdaCommodityAppStatusLogODTO">
        SELECT
        tc.commodity_code AS commodityCode,
        tc.commodity_name AS commodityName,
        txct.commodity_app_name AS commodityAppName,
        tc.commodity_spec AS commoditySpec,
        txcasl.app_status AS commodityAppState,
        txcasl.reason AS reason,
        teu.employee_name AS userName,
        txcasl.create_time AS createTime
        FROM
        t_xda_commodity_app_status_log txcasl
        LEFT JOIN t_commodity tc ON tc.id = txcasl.commodity_id
        LEFT JOIN t_xda_commodity_text txct ON txct.commodity_id = txcasl.commodity_id
        LEFT JOIN t_employee_user teu ON teu.user_id = txcasl.create_id
        <where>
            <if test="null != bDate and null != eDate and bDate != '' and eDate != '' ">
              AND txcasl.create_time BETWEEN #{bDate} AND #{eDate}
            </if>
            <if test="null != commodityId">
              AND txcasl.commodity_id = #{commodityId}
            </if>
            <if test="null != commodityName and commodityName != '' ">
                AND tc.commodity_name LIKE CONCAT('%',#{commodityName},'%')
            </if>
            <if test="null != commodityAppName and commodityAppName != '' ">
                AND txct.commodity_app_name LIKE CONCAT('%',#{commodityAppName},'%')
            </if>
            <if test="null != commodityAppState">
              AND txcasl.app_status = #{commodityAppState}
            </if>
        </where>
        ORDER BY txcasl.create_time DESC
    </select>
</mapper>