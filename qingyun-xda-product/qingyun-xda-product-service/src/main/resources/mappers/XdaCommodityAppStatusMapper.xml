<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaCommodityAppStatusMapper">
    <select id="selectCommodityAppStatusList" resultType="com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.XdaCommodityAppStatusODTO">
            SELECT
            tc.id AS commodityId,
            CASE
            WHEN #{appType} = 1 THEN IFNULL(tcas.app_status, 1)
            ELSE IFNULL(tcas.pf_app_status, 1)
            END AS commodityAppState,
            tc.commodity_state AS commodityState,
            tc.commodity_code AS commodityCode,
            tc.commodity_name AS commodityName,
            tc.commodity_spec AS commoditySpec,
            txct.commodity_app_name AS commodityAppName,
            CONCAT(txc1.cate_name,"/",txc2.cate_name) AS appCategoryName,
            txct.commodity_sub_name AS commoditySubName,
            case
            when #{appType}=1 then txoc.delivery_date_range_value
            else txoc.pf_delivery_date_range_value
            end as deliveryDateRangeValue,
            txt.tag_name AS commodityTag,
            tc.sales_box_capacity AS salesBoxCapacity,
            tc.sales_box_capacity AS pfBoxCapacity,
            d.option_name AS commodityUnitName,
            tc.is_weight AS isWeight,
            tc.commodity_weight AS commodityWeight,
            tc.storage_condition AS storageCondition,
            tc.quality_days AS qualityDays,
            tc.commodity_is_quick_freeze AS commodityIsQuickFreeze,
            CASE
            WHEN tcfg.commodity_id IS NOT NULL THEN 1
            ELSE 0
            END AS commodityFreezeGroup,
            CONCAT(c1.cate_name,"/",c2.cate_name,"/",c3.cate_name) AS commodityCategory,
            tc.bar_code AS barCode
            FROM
            t_commodity tc
            LEFT JOIN t_xda_commodity_app_status tcas ON tcas.commodity_id = tc.id
            LEFT JOIN t_xda_order_commodity txoc ON txoc.commodity_id = tc.id
            LEFT JOIN t_xda_commodity_text txct ON  txct.commodity_id = tc.id
            LEFT JOIN t_xda_category txc1 ON txc1.id = txct.first_category_id
            LEFT JOIN t_xda_category txc2 ON txc2.id = txct.second_category_id
            LEFT JOIN t_category c1 ON c1.id = tc.commodity_first_id
            LEFT JOIN t_category c2 ON c2.id = tc.commodity_second_id
            LEFT JOIN t_category c3 ON c3.id = tc.commodity_third_id
            LEFT JOIN t_xda_tag txt ON txt.id = txct.tag_id
            LEFT JOIN t_dictionary d ON d.id = tc.commodity_unit_id
            LEFT JOIN t_commodity_freeze_group tcfg ON tcfg.commodity_id = tc.id
            <where>
                <if test="null != commodityId">
                    AND tc.id = #{commodityId}
                </if>
                <if test="null != isWeight">
                    AND tc.is_weight = #{isWeight}
                </if>
                <if test="null != commodityIsQuickFreeze">
                    AND tc.commodity_is_quick_freeze = #{commodityIsQuickFreeze}
                </if>
                <if test="null != barCode and barCode != ''">
                    AND tc.bar_code = #{barCode}
                </if>
                <if test="null != commodityAppId and commodityAppId != '' ">
                    AND txct.commodity_app_name LIKE CONCAT('%',#{commodityAppId},'%')
                </if>
                <if test="null != appCategoryId">
                    AND (txc1.id = #{appCategoryId} OR txc2.id = #{appCategoryId})
                </if>
                <if test="null != commodityAppState and commodityAppState == 0 and appType ==1">
                    AND tcas.app_status = 0
                </if>
                <if test="null != commodityAppState and commodityAppState == 1 and appType ==1">
                    AND IFNULL(tcas.app_status,1) = 1
                </if>
                <if test="null != commodityAppState and commodityAppState == 0 and appType ==2">
                    AND tcas.pf_app_status = 0
                </if>
                <if test="null != commodityAppState and commodityAppState == 1 and appType ==2">
                    AND IFNULL(tcas.pf_app_status,1) = 1
                </if>
                <if test="null != commodityFreezeGroup and commodityFreezeGroup == 1 and appType ==2">
                    AND tcfg.commodity_id IS NOT NULL
                </if>
                <if test="null != commodityFreezeGroup and commodityFreezeGroup == 0 and appType ==2">
                    AND tcfg.commodity_id IS NULL
                </if>
                <if test="null != productPriceModelId">
                    AND tc.id IN(
                    SELECT
                    tppml.commodity_id
                    FROM
                    t_product_price_model_list tppml
                    WHERE
                    tppml.product_price_model_id = #{productPriceModelId}
                    )
                </if>
                and tc.status = 1
            </where>
    </select>

    <select id="selectCommodityTextByCommodityIdList" resultType="com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.XdaCommodityAppStatusUpODTO">
        SELECT
            tc.id AS commodityId,
            txct.commodity_app_name AS commodityAppName,
            CONCAT(txc1.cate_name,"/",txc2.cate_name) AS appCategoryName,
            tc.commodity_state AS commodityStatus,
            case
            when #{appType}=1 then txoc.delivery_date_range_value
            else txoc.pf_delivery_date_range_value
            end as deliveryDateRangeValue
		FROM
		t_commodity tc
		LEFT JOIN t_xda_commodity_text txct ON  txct.commodity_id = tc.id
		LEFT JOIN t_xda_category txc1 ON txc1.id = txct.first_category_id
		LEFT JOIN t_xda_category txc2 ON txc2.id = txct.second_category_id
        LEFT JOIN t_xda_order_commodity txoc ON txoc.commodity_id = tc.id
        <where>
            <if test="null != commodityIdList and commodityIdList.size > 0">
                AND tc.id IN
                <foreach collection="commodityIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectUpCommodityList" resultType="com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.XdaCommodityAppStatusUpODTO">
        SELECT
			tc.id AS commodityId,
			txct.commodity_app_name AS commodityAppName,
			CONCAT(txc1.cate_name,"/",txc2.cate_name) AS appCategoryName,
			tc.commodity_state AS commodityStatus,
			case
            when #{appType}=1 then txoc.delivery_date_range_value
            else txoc.pf_delivery_date_range_value
            end as deliveryDateRangeValue
        FROM
        t_commodity tc
        LEFT JOIN t_xda_commodity_text txct ON  txct.commodity_id = tc.id
        LEFT JOIN t_xda_category txc1 ON txc1.id = txct.first_category_id
        LEFT JOIN t_xda_category txc2 ON txc2.id = txct.second_category_id
        LEFT JOIN t_xda_order_commodity txoc ON txoc.commodity_id = tc.id
       <where>
           AND txct.commodity_app_name IS NOT NULL
           AND CONCAT(txc1.cate_name,"/",txc2.cate_name) IS NOT NULL
           AND tc.commodity_state = 1
           AND (
           (#{appType} = 1 AND txoc.delivery_date_range_value !='')
           OR
           (#{appType} != 1 AND txoc.pf_delivery_date_range_value != '')
           )
       </where>
    </select>
    <select id="selectProductPriceModelIdByStoreId" resultType="java.lang.Long">
        SELECT
        tss.product_price_model_id
        FROM t_store_settlement tss
        where
        tss.store_id = #{storeId}
    </select>
    <select id="batchSelectCommodityAppStatus"
            resultType="com.pinshang.qingyun.xda.product.dto.xdaCommodityAppStatus.XdaCommodityAppStatusODTO">
        select
            txcs.id,
            txcs.commodity_id,
            txcs.app_status commodityAppState,
            txcs.app_status appState,
            txcs.pf_app_status pfAppState,
            txcs.create_id,
            txcs.create_time
            from t_xda_commodity_app_status txcs
        where txcs.`commodity_id` IN
        <foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <update id="updateBatchByIds">
        <foreach collection="list" item="item" index="index" separator=";" open="" close="">
            update t_xda_commodity_app_status
            <set>
                <if test="null != item.appStatus">
                    app_status = #{item.appStatus},
                </if>
                <if test="null != item.pfAppStatus">
                    pf_app_status = #{item.pfAppStatus},
                </if>
                <if test="null != item.createId">
                    create_id = #{item.createId},
                </if>
                <if test="null != item.createTime">
                    create_time = #{item.createTime},
                </if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>

</mapper>