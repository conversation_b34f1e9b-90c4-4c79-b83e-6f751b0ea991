<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaCommodityFrontMapper">

	<sql id="queryStoreCondition">
		SELECT pm.id AS productPriceModelId,tss.settlement_customer_id AS settlementCustomerId,ts.id AS storeId
		FROM t_product_price_model pm
		INNER JOIN t_store_settlement tss ON pm.id = tss.product_price_model_id AND pm.price_model_state = 1
		INNER JOIN t_store ts ON ts.id = tss.store_id
		WHERE ts.id = #{storeId}
	</sql>
	<sql id="queryStoreCommodityCondition">
		SELECT ppml.commodity_id,ppml.commodity_price
		FROM t_product_price_model pm
		INNER JOIN t_product_price_model_list ppml ON ppml.product_price_model_id = pm.id AND pm.price_model_state = 1
		INNER JOIN t_store_settlement tss ON ppml.product_price_model_id = tss.product_price_model_id
		WHERE tss.store_id = #{storeId}
	</sql>
	<sql id="queryStoreCommoditySpecialPrice">
		SELECT
			p.id AS promotionId,c.id AS commodityId,pp.price,p.start_time,p.end_time
		FROM
			t_promotion p
		INNER JOIN t_promotion_scope ps ON p.id = ps.promotion_id
		INNER JOIN (<include refid="queryStoreCondition"></include>)tmp ON
		CASE WHEN ps.scope_type = 7 THEN ps.type_id = tmp.settlementCustomerId
		WHEN ps.scope_type = 8 THEN ps.type_id = tmp.productPriceModelId
		WHEN ps.scope_type = 9 THEN ps.type_id = tmp.storeId END
		INNER JOIN t_promotion_product pp ON p.id = pp.promotion_id
		INNER JOIN t_commodity c ON pp.product_code = c.commodity_code
		WHERE tmp.storeId = #{storeId}
		AND #{orderTime} BETWEEN p.start_time AND p.end_time
		<if test="null != commodityIdList and commodityIdList.size > 0 ">
			AND c.id in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		AND p.`status` = 0
	</sql>

    <select id="queryXdaCommodityListForApp" resultType="com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppODTO"
		parameterType="com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppIDTO">
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			-- c.sales_box_capacity,
			CASE
			WHEN #{isPfsStore} = true THEN c.pf_box_capacity
			ELSE c.sales_box_capacity
			END as salesBoxCapacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			tmp.commodity_price,
			cas.app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			pic.pic_url AS imageUrl,
			ct.sort_num
		FROM
			t_xda_commodity_text ct
			INNER JOIN t_commodity c ON ct.commodity_id = c.id
			INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
			INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id <if test="!needAppDown">AND cas.app_status = 0</if>
			LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
			INNER JOIN (
				<include refid="queryStoreCommodityCondition"></include>
			)tmp ON ct.commodity_id = tmp.commodity_id
		<where>
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
		<if test="null != commodityIdList and commodityIdList.size > 0 ">
			ORDER BY FIELD(ct.commodity_id,
			<foreach collection="commodityIdList" item="id" separator=",">
				#{id}
			</foreach>
			)
		</if>
    </select>

	<select id="queryXdaStoreSpecialPriceList" resultType="com.pinshang.qingyun.xda.product.dto.front.XdaStoreSpecialPriceODTO">
		<include refid="queryStoreCommoditySpecialPrice"></include>
	</select>

	<select id="queryXdaStorePromotionList" resultType="com.pinshang.qingyun.xda.product.dto.front.XdaStorePromotionODTO">
		SELECT
			gm.id AS giftModelId,gm.gift_model_name AS giftModelName,gm.remark AS giftModelDesc,gm.gift_model_type AS giftModelType,
			gmc.condition_type,gmc.condition_value,
			gp.commodity_id,gp.commodity_number AS giftNumber,gp.commodity_max_number AS giftMaxNumber
		FROM
			t_gift_model gm
		INNER JOIN t_gift_model_scope gms ON gm.id = gms.gift_model_id
		INNER JOIN (<include refid="queryStoreCondition"></include>)tmp ON
		CASE WHEN gms.scope_type = 1 THEN gms.type_id = tmp.settlementCustomerId
			 WHEN gms.scope_type = 2 THEN gms.type_id = tmp.productPriceModelId
			 WHEN gms.scope_type = 3 THEN gms.type_id = tmp.storeId END
		INNER JOIN t_gift_model_condition gmc ON gm.id = gmc.gift_model_id
		INNER JOIN t_gift_product gp ON gmc.id = gp.gift_model_condition_id
		WHERE tmp.storeId= #{storeId}
		AND #{orderTime} BETWEEN gm.begin_date AND gm.end_date
		<if test="null != commodityIdList and commodityIdList.size > 0 ">
			AND gp.commodity_id IN
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		AND gm.`status` = 1
		ORDER BY gm.create_time DESC
	</select>


	<select id="queryXdaCommodityLimitList" resultType="com.pinshang.qingyun.xda.product.dto.front.XdaCommodityLimitODTO">
		SELECT cl.commodity_id,cl.limit_number,cl.begin_time,cl.end_time
		FROM t_commodity_limit cl
		WHERE  #{orderTime} BETWEEN cl.begin_time AND cl.end_time
		<if test="null != commodityIdList and commodityIdList.size > 0 ">
			AND cl.commodity_id in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
		AND cl.limit_status = 0
	</select>

	<select id="queryXdaCommodityDeliveryTime" resultType="com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDeliveryTimeODTO">
		SELECT cd.commodity_id,
		<if test="isPfsStore == true" >
			cd.pf_delivery_date_range_code deliveryDateRangeCode,
			cd.pf_delivery_date_range_value deliveryDateRangeValue
		</if>
		<if test="isPfsStore == false" >
			cd.delivery_date_range_code,
			cd.delivery_date_range_value
		</if>

		FROM t_xda_order_commodity cd
		<where>
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				AND cd.commodity_id in
				<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
					#{commodityId}
				</foreach>
			</if>
			<if test="isPfsStore == true">
				AND cd.pf_delivery_date_range_code != ''
				AND cd.pf_delivery_date_range_value != ''
			</if>
			<if test="isPfsStore == false">
				AND cd.delivery_date_range_code != ''
				AND cd.delivery_date_range_value != ''
			</if>
		</where>
	</select>

	<select id="queryXdaShoppingCartQuantity" resultType="com.pinshang.qingyun.xda.product.dto.front.XdaShoppingCartODTO">
		SELECT sc.commodity_id,sc.quantity
		FROM t_xda_shopping_cart sc
		WHERE sc.commodity_type = 1 and sc.store_id = #{storeId}
		<if test="null != commodityIdList and commodityIdList.size > 0 ">
			AND sc.commodity_id in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
	</select>

	<!--根据类型区分: 特惠商品和普通商品购物车数量-->
	<select id="queryXdaShoppingCartQuantityV2" resultType="com.pinshang.qingyun.xda.product.dto.front.XdaShoppingCartODTO">
		SELECT sc.commodity_id,sc.quantity
		FROM t_xda_shopping_cart sc
		WHERE sc.store_id = #{storeId}
		  <if test="null != commodityType">
			  and sc.commodity_type = #{commodityType}
		  </if>
		<if test="null != commodityIdList and commodityIdList.size > 0 ">
			AND sc.commodity_id in
			<foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
				#{commodityId}
			</foreach>
		</if>
	</select>

    <select id="queryXdaCommodityAppStatus" resultType="com.pinshang.qingyun.xda.product.dto.XdaCommodityAppStatusODTO">
        SELECT commodity_id,app_status
        FROM t_xda_commodity_app_status
        <where>
            <if test="null != commodityIdList and commodityIdList.size > 0 ">
                AND commodity_id in
                <foreach collection="commodityIdList" index="index" item="commodityId" open="(" separator="," close=")">
                    #{commodityId}
                </foreach>
            </if>
        </where>
    </select>


   	<!--查询商品详情信息-->
	<select id="queryXdaCommodityDetailsForApp" resultType="com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaCommodityAppV2ODTO"
			parameterType="com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppIDTO">
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			c.sales_box_capacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			tmp.commodity_price,
			cas.app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			pic.pic_url AS imageUrl,
			ct.sort_num
		FROM
			t_xda_commodity_text ct
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id <if test="!needAppDown">AND cas.app_status = 0</if>
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"></include>
		)tmp ON ct.commodity_id = tmp.commodity_id
		<where>
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
		<if test="null != commodityIdList and commodityIdList.size > 0 ">
			ORDER BY FIELD(ct.commodity_id,
			<foreach collection="commodityIdList" item="id" separator=",">
				#{id}
			</foreach>
			)
		</if>
	</select>
	<!--查询商品详情信息-->
	<select id="queryXdaCommodityDetailsForAppV3" resultType="com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityAppV3ODTO"
			parameterType="com.pinshang.qingyun.xda.product.dto.front.commodityFront.v3.XdaCommodityAppV3IDTO">
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			c.sales_box_capacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			tmp.commodity_price,
			cas.app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			pic.pic_url AS imageUrl,
			ct.sort_num
		FROM
			t_xda_commodity_text ct
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id <if test="!needAppDown">AND cas.app_status = 0</if>
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"></include>
		)tmp ON ct.commodity_id = tmp.commodity_id
		<where>
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
		<if test="null != commodityIdList and commodityIdList.size > 0 ">
			ORDER BY FIELD(ct.commodity_id,
			<foreach collection="commodityIdList" item="id" separator=",">
				#{id}
			</foreach>
			)
		</if>
	</select>
	
	<select id="getXdaCommoditySalesBoxCapacity"
			resultType="com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3ODTO">
		SELECT
			c.id AS commodityId,
			-- c.sales_box_capacity AS salesBoxCapacity,
			CASE
				WHEN #{isPfsStore} = true THEN c.pf_box_capacity
				ELSE c.sales_box_capacity
				END as salesBoxCapacity,
			ct.commodity_app_name AS commodityName
		FROM
		t_xda_commodity_text ct
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		where
			c.id = #{commodityId}
	</select>
	<select id="queryXdaCommodityListForAppV3"
			resultType="com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppODTO">
		SELECT
		c.id AS commodityId,
		c.commodity_code,
		ct.commodity_app_name AS commodityName,
		ct.commodity_sub_name AS commoditySubName,
		c.commodity_spec AS commoditySpec,
		d.option_name AS commodityUnitName,
		-- c.sales_box_capacity,
		CASE
		WHEN #{isPfsStore} = true THEN c.pf_box_capacity
		ELSE c.sales_box_capacity
		END as salesBoxCapacity,
		c.commodity_is_quick_freeze AS isQuickFreeze,
		c.is_weight AS isWeight,
		c.product_type AS productType,
		c.commodity_package_spec AS commodityPackageSpec,
		c.commodity_weight AS commodityWeight,
		c.storage_condition AS storageCondition,
		c.quality_days AS qualityDays,
		tmp.commodity_price,
		CASE
			WHEN #{isPfsStore} = true THEN cas.pf_app_status
			ELSE cas.app_status
		END as app_status,
		ct.first_category_id AS xdaFirstCategoryId,
		c.commodity_third_id AS xdaSecondCategoryId,
		pic.pic_url AS imageUrl,
		ct.sort_num
		FROM
		t_xda_commodity_text ct
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id
		<if test="isShoppingCart">
			AND (
				CASE
					WHEN #{isPfsStore} = true THEN cas.pf_app_status = 0
					ELSE cas.app_status = 0
				END
			)
		</if>
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
		<include refid="queryStoreCommodityCondition"></include>
		)tmp ON ct.commodity_id = tmp.commodity_id
		<where>
		            <if test="isShoppingCart">
						AND c.status = 1
					</if>
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
		<if test="null != commodityIdList and commodityIdList.size > 0 ">
			ORDER BY FIELD(ct.commodity_id,
			<foreach collection="commodityIdList" item="id" separator=",">
				#{id}
			</foreach>
			)
		</if>

	</select>
</mapper>