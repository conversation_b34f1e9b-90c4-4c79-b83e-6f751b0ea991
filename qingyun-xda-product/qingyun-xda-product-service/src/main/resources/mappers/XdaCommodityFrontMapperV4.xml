<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaCommodityFrontMapperV4">
	<sql id="queryStoreCommodityCondition">
		SELECT ppml.commodity_id,ppml.commodity_price
		FROM t_product_price_model pm
		INNER JOIN t_product_price_model_list ppml ON ppml.product_price_model_id = pm.id AND pm.price_model_state = 1
		INNER JOIN t_store_settlement tss ON ppml.product_price_model_id = tss.product_price_model_id
		WHERE tss.store_id = #{storeId}
	</sql>

	<select id="getStoreCommodityPrice" resultType="com.pinshang.qingyun.xda.product.dto.StoreCommodityPriceODTO">
		<include refid="queryStoreCommodityCondition"></include>
	</select>

	<select id="queryXdaCommodityDetailsForAppV4" resultType="com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityAppV4ODTO"
			parameterType="com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityAppV4IDTO">
		select outerT.* from (
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			-- c.sales_box_capacity,
			CASE
			WHEN #{isPfsStore} = true THEN c.pf_box_capacity
			ELSE c.sales_box_capacity
			END as salesBoxCapacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			ct.quality_status,
			tmp.commodity_price,
			CASE 
				WHEN #{isPfsStore} = true THEN cas.pf_app_status
				ELSE cas.app_status
			END as app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			c.commodity_third_id AS commodityThirdId,
			pic.pic_url AS imageUrl,
			c.product_type,
			ct.sort_num
			,xoc.salesStatus
		FROM t_xda_commodity_text ct
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id 
		<if test="!needAppDown">
			AND (
				CASE 
					WHEN #{isPfsStore} = true THEN cas.pf_app_status = 0
					ELSE cas.app_status = 0
				END
			)
		</if>
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"></include>
		)tmp ON ct.commodity_id = tmp.commodity_id
		INNER JOIN (
			SELECT tcs.commodity_id, 1 AS salesStatus
			FROM t_dc_tob_commodity_stock tcs 
			LEFT JOIN t_dc_tob_process_order_commodity_statistics tpocs ON tcs.commodity_id = tpocs.commodity_id AND tpocs.order_time = #{orderTime}
			INNER JOIN t_xda_order_commodity oc ON tcs.commodity_id = oc.commodity_id
			WHERE DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1)
			<if test="null != businessType and businessType == 10 ">
				AND tcs.logistics_center_id = 1
				AND (
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.tda_stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.tda_stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.tda_stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>
			<if test="null != businessType and businessType == 15 ">
				AND (
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.national_stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.national_stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.national_stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>
			<if test="null != businessType and businessType == 0 ">
				AND tcs.logistics_center_id = 1
				AND (
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>

		) xoc ON ct.commodity_id = xoc.commodity_id
		<where>
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
		UNION ALL
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			c.sales_box_capacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			ct.quality_status,
			tmp.commodity_price,
			CASE 
				WHEN #{isPfsStore} = true THEN cas.pf_app_status
				ELSE cas.app_status
			END as app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			c.commodity_third_id AS commodityThirdId,
			pic.pic_url AS imageUrl,
			c.product_type,
			ct.sort_num
			,xoc.salesStatus
		FROM t_xda_commodity_text ct
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id 
		<if test="!needAppDown">
			AND (
				CASE 
					WHEN #{isPfsStore} = true THEN cas.pf_app_status = 0
					ELSE cas.app_status = 0
				END
			)
		</if>
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"></include>
		)tmp ON ct.commodity_id = tmp.commodity_id
		INNER JOIN (
			SELECT tcs.commodity_id, 2 AS salesStatus
			FROM t_dc_tob_commodity_stock tcs 
			LEFT JOIN t_dc_tob_process_order_commodity_statistics tpocs ON tcs.commodity_id = tpocs.commodity_id AND tpocs.order_time = #{orderTime}
			INNER JOIN t_xda_order_commodity oc ON tcs.commodity_id = oc.commodity_id
			WHERE DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1)
			<if test="null != businessType and businessType == 10 ">
				AND tcs.logistics_center_id = 1
				AND !(
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.tda_stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.tda_stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.tda_stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>
			<if test="null != businessType and businessType == 15 ">
				AND !(
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.national_stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.national_stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.national_stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>
			<if test="null != businessType and businessType == 0 ">
				AND tcs.logistics_center_id = 1
				AND !(
				<!-- 1、依据大仓库存 & 有库存 -->
				(tcs.stock_type = 1 AND tcs.stock_status = 1) OR
				<!-- 2、不限量订购 & 有库存 -->
				(tcs.stock_type = 2 AND tcs.stock_status = 1) OR
				<!-- 3.1、限量供应 & 限总量 & 有库存 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.stock_status = 1) OR
				<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
				(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
				)
			</if>

		) xoc ON ct.commodity_id = xoc.commodity_id
		<where>
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
		UNION ALL
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			c.sales_box_capacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			ct.quality_status,
			tmp.commodity_price,
			CASE 
				WHEN #{isPfsStore} = true THEN cas.pf_app_status
				ELSE cas.app_status
			END as app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			c.commodity_third_id AS commodityThirdId,
			pic.pic_url AS imageUrl,
			c.product_type,
			ct.sort_num
			,xoc.salesStatus
		FROM t_xda_commodity_text ct
		INNER JOIN t_commodity c ON ct.commodity_id = c.id
		INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
		INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id 
		<if test="!needAppDown">
			AND (
				CASE 
					WHEN #{isPfsStore} = true THEN cas.pf_app_status = 0
					ELSE cas.app_status = 0
				END
			)
		</if>
		LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
		INNER JOIN (
			<include refid="queryStoreCommodityCondition"></include>
		)tmp ON ct.commodity_id = tmp.commodity_id
		INNER JOIN (
			SELECT oc.commodity_id, 3 AS salesStatus
			FROM t_xda_order_commodity oc
			WHERE !(DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1))
		) xoc ON ct.commodity_id = xoc.commodity_id
		<where>
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
		) outerT
		GROUP BY outerT.commodityId
		<if test="null != commodityIdList and commodityIdList.size > 0 ">
			ORDER BY FIELD(outerT.commodityId,
			<foreach collection="commodityIdList" item="id" separator=",">
				#{id}
			</foreach>
			)
		</if>
	</select>
	
	
	<select id="queryXdaCommodityListForAppV4" resultType="com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityAppV4ODTO"
		parameterType="com.pinshang.qingyun.xda.product.dto.front.commodityFront.v4.XdaCommodityAppV4IDTO">
		select outerT.* from (
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			-- c.sales_box_capacity,
			CASE
			WHEN #{isPfsStore} = true THEN c.pf_box_capacity
			ELSE c.sales_box_capacity
			END as salesBoxCapacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			tmp.commodity_price,
			CASE 
				WHEN #{isPfsStore} = true THEN cas.pf_app_status
				ELSE cas.app_status
			END as app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			pic.pic_url AS imageUrl,
			c.product_type,
			ct.sort_num
			,xoc.salesStatus
		FROM
			t_xda_commodity_text ct
			INNER JOIN t_commodity c ON ct.commodity_id = c.id
			INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
			INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id 
			<if test="!needAppDown">
				AND (
					CASE 
						WHEN #{isPfsStore} = true THEN cas.pf_app_status = 0
						ELSE cas.app_status = 0
					END
				)
			</if>
			LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
			INNER JOIN (
				<include refid="queryStoreCommodityCondition"></include>
			)tmp ON ct.commodity_id = tmp.commodity_id
			INNER JOIN (
				SELECT tcs.commodity_id, 1 AS salesStatus
				FROM t_dc_tob_commodity_stock tcs 
				LEFT JOIN t_dc_tob_process_order_commodity_statistics tpocs ON tcs.commodity_id = tpocs.commodity_id AND tpocs.order_time = #{orderTime}
				INNER JOIN t_xda_order_commodity oc ON tcs.commodity_id = oc.commodity_id
				WHERE DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1)
				<if test="null != businessType and businessType == 10 ">
					AND tcs.logistics_center_id = 1
					AND (
					<!-- 1、依据大仓库存 & 有库存 -->
					(tcs.stock_type = 1 AND tcs.tda_stock_status = 1) OR
					<!-- 2、不限量订购 & 有库存 -->
					(tcs.stock_type = 2 AND tcs.tda_stock_status = 1) OR
					<!-- 3.1、限量供应 & 限总量 & 有库存 -->
					(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.tda_stock_status = 1) OR
					<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
					(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
					)
				</if>
				<if test="null != businessType and businessType == 15 ">
					AND (
					<!-- 1、依据大仓库存 & 有库存 -->
					(tcs.stock_type = 1 AND tcs.national_stock_status = 1) OR
					<!-- 2、不限量订购 & 有库存 -->
					(tcs.stock_type = 2 AND tcs.national_stock_status = 1) OR
					<!-- 3.1、限量供应 & 限总量 & 有库存 -->
					(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.national_stock_status = 1) OR
					<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
					(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
					)
				</if>
				<if test="null != businessType and businessType == 0 ">
					AND tcs.logistics_center_id = 1
					AND (
					<!-- 1、依据大仓库存 & 有库存 -->
					(tcs.stock_type = 1 AND tcs.stock_status = 1) OR
					<!-- 2、不限量订购 & 有库存 -->
					(tcs.stock_type = 2 AND tcs.stock_status = 1) OR
					<!-- 3.1、限量供应 & 限总量 & 有库存 -->
					(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.stock_status = 1) OR
					<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
					(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
					)
				</if>

			) xoc ON ct.commodity_id = xoc.commodity_id
		<where>
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
			
		</where>
		UNION ALL
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			c.sales_box_capacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			tmp.commodity_price,
			CASE 
				WHEN #{isPfsStore} = true THEN cas.pf_app_status
				ELSE cas.app_status
			END as app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			pic.pic_url AS imageUrl,
			c.product_type,
			ct.sort_num
			,xoc.salesStatus
		FROM
			t_xda_commodity_text ct
			INNER JOIN t_commodity c ON ct.commodity_id = c.id
			INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
			INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id 
			<if test="!needAppDown">
				AND (
					CASE 
						WHEN #{isPfsStore} = true THEN cas.pf_app_status = 0
						ELSE cas.app_status = 0
					END
				)
			</if>
			LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
			INNER JOIN (
				<include refid="queryStoreCommodityCondition"></include>
			)tmp ON ct.commodity_id = tmp.commodity_id
			INNER JOIN (
				SELECT tcs.commodity_id, 2 AS salesStatus
				FROM t_dc_tob_commodity_stock tcs 
				LEFT JOIN t_dc_tob_process_order_commodity_statistics tpocs ON tcs.commodity_id = tpocs.commodity_id AND tpocs.order_time = #{orderTime}
				INNER JOIN t_xda_order_commodity oc ON tcs.commodity_id = oc.commodity_id
				WHERE DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1)
				<if test="null != businessType and businessType == 10 ">
					AND tcs.logistics_center_id = 1
					AND !(
					<!-- 1、依据大仓库存 & 有库存 -->
					(tcs.stock_type = 1 AND tcs.tda_stock_status = 1) OR
					<!-- 2、不限量订购 & 有库存 -->
					(tcs.stock_type = 2 AND tcs.tda_stock_status = 1) OR
					<!-- 3.1、限量供应 & 限总量 & 有库存 -->
					(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.tda_stock_status = 1) OR
					<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
					(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
					)
				</if>
				<if test="null != businessType and businessType == 15 ">
					AND !(
					<!-- 1、依据大仓库存 & 有库存 -->
					(tcs.stock_type = 1 AND tcs.national_stock_status = 1) OR
					<!-- 2、不限量订购 & 有库存 -->
					(tcs.stock_type = 2 AND tcs.national_stock_status = 1) OR
					<!-- 3.1、限量供应 & 限总量 & 有库存 -->
					(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.national_stock_status = 1) OR
					<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
					(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
					)
				</if>
				<if test="null != businessType and businessType == 0 ">
					AND tcs.logistics_center_id = 1
					AND !(
					<!-- 1、依据大仓库存 & 有库存 -->
					(tcs.stock_type = 1 AND tcs.stock_status = 1) OR
					<!-- 2、不限量订购 & 有库存 -->
					(tcs.stock_type = 2 AND tcs.stock_status = 1) OR
					<!-- 3.1、限量供应 & 限总量 & 有库存 -->
					(tcs.stock_type = 3 AND tcs.effect_type = 1 AND tcs.stock_status = 1) OR
					<!-- 3.2、限量供应 & 每天循环限量 & 限量>已下单总份数 -->
					(tcs.stock_type = 3 AND tcs.effect_type = 2 AND tcs.limit_number - IFNULL(tpocs.order_number, 0) > 0)
					)
				</if>

			) xoc ON ct.commodity_id = xoc.commodity_id
		<where>
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
		UNION ALL
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			ct.commodity_sub_name AS commoditySubName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			c.sales_box_capacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			tmp.commodity_price,
			CASE 
				WHEN #{isPfsStore} = true THEN cas.pf_app_status
				ELSE cas.app_status
			END as app_status,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			pic.pic_url AS imageUrl,
			c.product_type,
			ct.sort_num
			,xoc.salesStatus
		FROM
			t_xda_commodity_text ct
			INNER JOIN t_commodity c ON ct.commodity_id = c.id
			INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
			INNER JOIN t_xda_commodity_app_status cas ON c.id = cas.commodity_id 
			<if test="!needAppDown">
				AND (
					CASE 
						WHEN #{isPfsStore} = true THEN cas.pf_app_status = 0
						ELSE cas.app_status = 0
					END
				)
			</if>
			LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
			INNER JOIN (
				<include refid="queryStoreCommodityCondition"></include>
			)tmp ON ct.commodity_id = tmp.commodity_id
			INNER JOIN (
				SELECT oc.commodity_id, 3 AS salesStatus
				FROM t_xda_order_commodity oc
				WHERE !(DATEDIFF(#{orderTime},CURDATE()) BETWEEN SUBSTRING_INDEX(delivery_date_range_code,'-',1) AND SUBSTRING_INDEX(delivery_date_range_code,'-',-1))
			) xoc ON ct.commodity_id = xoc.commodity_id
		<where>
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
		) outerT
		GROUP BY outerT.commodityId
		ORDER BY outerT.salesStatus ASC, outerT.commodity_code
    </select>
	
</mapper>