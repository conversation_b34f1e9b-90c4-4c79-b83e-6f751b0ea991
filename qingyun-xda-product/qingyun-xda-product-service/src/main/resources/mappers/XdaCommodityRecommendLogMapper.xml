<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaCommodityRecommendLogMapper">

    <select id="findList" resultType="com.pinshang.qingyun.xda.product.dto.recommend.XdaCommodityRecommendLogODTO"
    parameterType="com.pinshang.qingyun.xda.product.dto.recommend.XdaCommodityRecommendLogIDTO">
        <![CDATA[
          SELECT
            c.commodity_code,
            c.commodity_name,
            c.commodity_spec,
            xcpl.operate_type,
            (
            CASE
            WHEN xcpl.operate_type=1 THEN '新增'
            WHEN xcpl.operate_type=2 THEN '删除'
            ELSE '' END)as operatetypeName,
            xcpl.operate_type,
            eu.employee_name createName,
            xcpl.create_time createTime,
            xcpl.create_id,
            xcpl.id
            ]]>
        FROM
            t_xda_commodity_recommend_log  xcpl
            LEFT JOIN  t_commodity c ON xcpl.commodity_id=c.id
            LEFT JOIN t_employee_user eu ON xcpl.create_id=eu.user_id
        <where>
            <if test="commodityParam != null and commodityParam != ''">
                c.commodity_code =#{commodityParam}
            </if>
            <if test="operateType!=null">
                AND xcpl.operate_type=#{operateType}
            </if>
        </where>
        ORDER BY xcpl.create_time DESC
    </select>
</mapper>