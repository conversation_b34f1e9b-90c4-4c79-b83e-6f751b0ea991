<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaCommodityRecommendMapper">


    <delete id="deleteAll">
        delete  from t_xda_commodity_recommend
    </delete>

    <select id="findList" resultType="com.pinshang.qingyun.xda.product.dto.recommend.XdaCommodityRecommendODTO"
            parameterType="com.pinshang.qingyun.xda.product.dto.recommend.XdaCommodityRecommendIDTO">
        SELECT
            xcr.id AS id,
            c.commodity_code commodityCode,
            c.commodity_name commodityName,
            c.commodity_spec commoditySpec,
            c1.cate_name cateFirstName,
            c2.cate_name cateSecondName,
            eu.employee_name createName,
            xcr.create_id,
            ct.commodity_app_name,
            xcr.create_time createTime
        FROM
            t_xda_commodity_recommend  xcr
            LEFT JOIN  t_commodity c ON xcr.commodity_id=c.id
            LEFT JOIN t_xda_commodity_text ct ON ct.commodity_id=c.id
            LEFT JOIN t_employee_user eu ON xcr.create_id=eu.user_id
            LEFT JOIN t_xda_category c1 ON c1.id=ct.first_category_id
            LEFT JOIN t_xda_category c2 ON c2.id=ct.second_category_id
        <where>

            <if test="commodityParam != null and commodityParam != ''">
                c.commodity_code =#{commodityParam}
            </if>
            <if test=" cateId1s != null ">
                AND ct.first_category_id = #{cateId1s}
            </if>
            <if test=" cateId2s != null ">
                AND ct.second_category_id = #{cateId2s}
            </if>
            <if test="commodityAppName!=null and commodityAppName!=''">
                AND ct.commodity_app_name LIKE CONCAT('%',#{commodityAppName},'%')
            </if>
        </where>
        ORDER BY c.commodity_code ASC
    </select>
</mapper>