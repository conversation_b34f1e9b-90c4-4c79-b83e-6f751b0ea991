<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaCommodityTextMapper">

	<!-- 查询  客户-客户价格方案-商品ID 集合 -->
	<select id="selectCommodityIdListByStoreId" resultType="Long">
		SELECT ppml.commodity_id
		FROM t_store_settlement ss
		INNER JOIN t_product_price_model_list ppml ON ppml.product_price_model_id = ss.product_price_model_id
		WHERE ss.store_id = #{storeId}
	</select>

	<!-- 查询商品文描信息列表 -->
	<select id="selectCommodityTextInfoList"
		resultType="com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextInfoODTO" 
		parameterType="com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextInfoPageIDTO">
		SELECT
			c.id,
			c.id AS commodityId,
			
			c.commodity_code AS commodityCode,
			c.commodity_name AS commodityName,
			c.commodity_spec AS commoditySpec,
			c.commodity_unit_id AS commodityUnitId,
			<!-- unit.option_name AS commodityUnitName, -->

			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			ct.commodity_app_name AS commodityAppName,

			sc.serial_commodity_id AS serialCommodityId,
			sc.serial_commodity_code AS serialCommodityCode,
			
			ct.tag_id AS tagId,
			ct.commodity_sub_name AS commoditySubName,

			c.quality_days,
			ct.quality_status,
			
			c.is_weight AS isWeight,
			c.commodity_state AS commodityState,
			
			c.commodity_first_id AS firstCategoryId,
			c.commodity_second_id AS secondCategoryId,
			c.commodity_third_id AS thirdCategoryId,
			
			c.bar_code AS barCode,
			<!-- (SELECT GROUP_CONCAT(cbc.bar_code SEPARATOR ',') FROM t_commodity_bar_code cbc WHERE cbc.commodity_id = c.id AND cbc.default_state = 0) AS subBarCodes, -->
			
			IF(sc.commodity_id, 1, 0) AS serialCommoditySort
		FROM t_commodity c
		LEFT JOIN t_xda_commodity_text ct ON ct.commodity_id = c.id
		<!-- LEFT JOIN t_dictionary unit ON unit.id = c.commodity_unit_id -->
		LEFT JOIN t_xda_serial_commodity sc ON sc.commodity_id = c.id
		<where>
		<if test="commodityName != null and commodityName != ''">
			AND (c.commodity_code LIKE CONCAT('%', TRIM(#{commodityName}), '%') OR c.commodity_name LIKE CONCAT('%', TRIM(#{commodityName}), '%') OR c.commodity_spec LIKE CONCAT('%', TRIM(#{commodityName}), '%'))
		</if>
		<if test="commodityAppName != null and commodityAppName != ''">
			AND (ct.commodity_app_name LIKE CONCAT('%', TRIM(#{commodityAppName}), '%'))
		</if>
		<if test="barCode != null and barCode != ''">
			AND c.bar_code = TRIM(#{barCode})
		</if>
		<if test="commodityState != null">
			AND c.commodity_state = #{commodityState}
		</if>
		<if test="isWeight != null">
			AND c.is_weight = #{isWeight}
		</if>
		
		<if test="picStatus != null">
			<choose>
				<when test="picStatus == 0">
					AND c.id NOT IN (SELECT innerPic.commodity_id FROM t_xda_commodity_text_pic innerPic WHERE innerPic.pic_type = 1)
				</when>
				<when test="picStatus == 1">
					AND c.id IN (SELECT innerPic.commodity_id FROM t_xda_commodity_text_pic innerPic WHERE innerPic.pic_type = 1)
				</when>
				<otherwise>
					AND 1 = 2
				</otherwise>
			</choose>
		</if>
		<if test="longPicStatus != null">
			<choose>
				<when test="longPicStatus == 0">
					AND c.id NOT IN (SELECT innerLongPic.commodity_id FROM t_xda_commodity_text_pic innerLongPic WHERE innerLongPic.pic_type = 2)
				</when>
				<when test="longPicStatus == 1">
					AND c.id IN (SELECT innerLongPic.commodity_id FROM t_xda_commodity_text_pic innerLongPic WHERE innerLongPic.pic_type = 2)
				</when>
				<otherwise>
					AND 1 = 2
				</otherwise>
			</choose>
		</if>
		
		<!-- 子查询太慢，storeId转化为commodityIdListByStoreId -->
		<!-- <if test="storeId != null">
			AND c.id IN (
				SELECT ppml.commodity_id
				FROM t_store_settlement ss
				INNER JOIN t_product_price_model_list ppml ON ppml.product_price_model_id = ss.product_price_model_id
				WHERE ss.store_id = #{storeId}
			)
		</if> -->
		<choose>
			<when test="commodityIdListByStoreId != null and commodityIdListByStoreId.size != 0">
				AND c.id IN 
				<foreach collection="commodityIdListByStoreId" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</when>
			<when test="commodityIdListByStoreId != null and commodityIdListByStoreId.size == 0">
				AND 1 = 2
			</when>
		</choose>
		<if test="productPriceModelId != null">
			AND c.id IN (
                SELECT ppml.commodity_id
                FROM t_product_price_model_list ppml
                WHERE ppml.product_price_model_id = #{productPriceModelId}
       		)
		</if>
		
		<if test="commoditySubNameStatus != null">
			<choose>
				<when test="commoditySubNameStatus == 0">
					AND ct.commodity_sub_name IS NULL
				</when>
				<when test="commoditySubNameStatus == 1">
					AND ct.commodity_sub_name IS NOT NULL
				</when>
				<otherwise>
					AND 1 = 2
				</otherwise>
			</choose>
		</if>
		<if test="commodityAppNameStatus != null">
			<choose>
				<when test="commodityAppNameStatus == 0">
					AND ct.commodity_app_name IS NULL
				</when>
				<when test="commodityAppNameStatus == 1">
					AND ct.commodity_app_name IS NOT NULL
				</when>
				<otherwise>
					AND 1 = 2
				</otherwise>
			</choose>
		</if>
		<if test="xdaCategoryStatus != null">
			<choose>
				<when test="xdaCategoryStatus == 0">
					AND (ct.first_category_id IS NULL OR ct.second_category_id IS NULL)
				</when>
				<when test="xdaCategoryStatus == 1">
					AND (ct.first_category_id IS NOT NULL AND ct.second_category_id IS NOT NULL)
				</when>
				<otherwise>
					AND 1 = 2
				</otherwise>
			</choose>
		</if>
		<if test="serialCommodityStatus != null">
			<choose>
				<when test="serialCommodityStatus == 0">
					AND (sc.serial_commodity_id IS NULL OR sc.serial_commodity_code IS NULL)
				</when>
				<when test="serialCommodityStatus == 1">
					AND (sc.serial_commodity_id IS NOT NULL AND sc.serial_commodity_code IS NOT NULL)
				</when>
				<otherwise>
					AND 1 = 2
				</otherwise>
			</choose>
		</if>
		<if test="xdaCategoryId != null">
			AND (ct.first_category_id = #{xdaCategoryId} OR ct.second_category_id = #{xdaCategoryId})
		</if>
		<if test="qualityStatus != null">
			<choose>
				<when test="qualityStatus == 1">
					AND ct.quality_status = 1
				</when>
				<when test="qualityStatus == 0">
					AND (ct.quality_status is null or ct.quality_status = 0)
				</when>
			</choose>
		</if>
		</where>
		ORDER BY serialCommoditySort DESC, sc.serial_commodity_code ASC, c.commodity_code ASC
	</select>
	
	 <!-- 查询  商品排序信息  列表 -->
    <select id="selectCommoditySortNumInfoList"
    	parameterType="com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommoditySortNumInfoPageIDTO"
    	resultType="com.pinshang.qingyun.xda.product.dto.commodityText.CommoditySortNumInfoODTO">
    	SELECT
			t.*
		FROM (

    	SELECT
    		IFNULL(xdaCate1.sort_num, 100000) AS xdaCate1SortNum,
			IFNULL(xdaCate2.sort_num, 100000) AS xdaCate2SortNum,
			IFNULL(ct.sort_num, 100000) AS commoditySortNum,
			ct.sort_num_update_time AS sortNumUpdateTime,
			
			c.id AS commodityId,
			c.commodity_code AS commodityCode,
			ct.sort_num AS sortNum,
			c.commodity_name AS commodityName,
			c.commodity_spec AS commoditySpec,
			c.bar_code AS barCode,
			(SELECT GROUP_CONCAT(cbc.bar_code SEPARATOR ',') FROM t_commodity_bar_code cbc WHERE cbc.commodity_id = c.id AND cbc.default_state = 0) AS subBarCodes,
			ct.commodity_app_name AS commodityAppName,
			
			xdaCate1.cate_name AS xdaFirstCategoryName,
			xdaCate2.cate_name AS xdaSecondCategoryName,
			
			c.commodity_first_id AS firstCategoryId,
			c.commodity_second_id AS secondCategoryId,
			c.commodity_third_id AS thirdCategoryId
			-- CONCAT(c.commodity_first_kind_name, '/', c.commodity_second_kind_name, '/', c.commodity_third_kind_name) AS categoryName
		FROM t_commodity c
		LEFT JOIN t_xda_commodity_text ct ON ct.commodity_id = c.id
		LEFT JOIN t_xda_category xdaCate1 ON xdaCate1.id = ct.first_category_id
		LEFT JOIN t_xda_category xdaCate2 ON xdaCate2.id = ct.second_category_id
		<where>
		<if test="null != commodityId and commodityId > 0">
    		AND c.id = #{commodityId}
   		</if>
   		<if test="xdaCategoryId != null and xdaCategoryId > 0">
   			AND (ct.first_category_id = #{xdaCategoryId} OR ct.second_category_id = #{xdaCategoryId})
		</if>
		<if test="commodityAppName != null and commodityAppName != ''">
			AND ct.commodity_app_name LIKE CONCAT('%', TRIM(#{commodityAppName}), '%')
		</if>
		<if test="barCode != null and barCode != ''">
			AND c.bar_code = TRIM(#{barCode})
		</if>
		<if test="onlySortNumNull != null and onlySortNumNull == 1">
			AND ct.sort_num IS NULL
		</if>
		</where>

		) t
		ORDER BY t.xdaCate1SortNum ASC, t.xdaCate2SortNum ASC, t.commoditySortNum ASC, t.sortNumUpdateTime DESC
    </select>
	
    <!-- 查询  商品文描日志信息列表 -->
	<select id="selectCommodityTextLogInfoList"
		resultType="com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextLogInfoODTO" 
		parameterType="com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextLogInfoPageIDTO">
		SELECT
			c.commodity_code AS commodityCode,
			c.commodity_name AS commodityName,
			c.commodity_spec AS commoditySpec,
			ctl.operate_type AS operateType,
			ctl.old_value AS oldValue,
			ctl.new_value AS newValue,
			eu.employee_name AS createName,
			ctl.create_time AS createTime
		FROM t_xda_commodity_text_log ctl
		LEFT JOIN t_commodity c ON c.id = ctl.commodity_id
		LEFT JOIN t_employee_user eu ON eu.user_id = ctl.create_id
		<where>
		<if test="commodityName != null and commodityName != ''">
			AND (c.commodity_code LIKE CONCAT('%', TRIM(#{commodityName}), '%') OR c.commodity_name LIKE CONCAT('%', TRIM(#{commodityName}), '%') OR c.commodity_spec LIKE CONCAT('%', TRIM(#{commodityName}), '%'))
		</if>
		<if test="createName != null and createName != ''">
			AND (eu.employee_code LIKE CONCAT('%', TRIM(#{createName}), '%') OR eu.employee_name LIKE CONCAT('%', TRIM(#{createName}), '%'))
		</if>
		<if test="beginCreateDate != null and beginCreateDate != ''">
			AND ctl.create_time >= #{beginCreateDate}
		</if>
		<if test="endCreateDate != null and endCreateDate != ''">
			AND ctl.create_time <![CDATA[ <= ]]> #{endCreateDate}
		</if>
		<if test="operateType != null">
			AND ctl.operate_type = #{operateType}
		</if>
		</where>
		ORDER BY ctl.id DESC
	</select>
	
	<!-- 更新  商品文描-前台品名 -->
	<update id="updateCommodityAppName"
		parameterType="com.pinshang.qingyun.xda.product.model.XdaCommodityText">
        UPDATE t_xda_commodity_text
		SET
			commodity_app_name = #{commodityAppName},
			update_id = #{updateId},
			update_time = #{updateTime}
		WHERE commodity_id = #{commodityId}
    </update>
    
    <!-- 更新  商品文描-副标题 -->
    <update id="updateCommoditySubName"
		parameterType="com.pinshang.qingyun.xda.product.model.XdaCommodityText">
        UPDATE t_xda_commodity_text
		SET
			commodity_sub_name = #{commoditySubName},
			update_id = #{updateId},
			update_time = #{updateTime}
		WHERE commodity_id = #{commodityId}
    </update>
    
    <!-- 更新  商品文描-前台品类 -->
	<update id="updateCommodityCategory"
		parameterType="com.pinshang.qingyun.xda.product.model.XdaCommodityText">
        UPDATE t_xda_commodity_text
		SET
			first_category_id = #{firstCategoryId},
			second_category_id = #{secondCategoryId},
			update_id = #{updateId},
			update_time = #{updateTime}
		WHERE commodity_id = #{commodityId}
    </update>
    
    <!-- 更新  商品文描-标签 -->
	<update id="updateCommodityTag"
		parameterType="com.pinshang.qingyun.xda.product.model.XdaCommodityText">
        UPDATE t_xda_commodity_text
		SET
			tag_id = #{tagId},
			update_id = #{updateId},
			update_time = #{updateTime}
		WHERE commodity_id = #{commodityId}
    </update>
    
    <!-- 更新  商品文描-排序 -->
	<update id="updateCommoditySortNum"
		parameterType="com.pinshang.qingyun.xda.product.model.XdaCommodityText">
        UPDATE t_xda_commodity_text
		SET
			sort_num = #{sortNum},
			sort_num_update_time = #{sortNumUpdateTime},
			update_id = #{updateId},
			update_time = #{updateTime}
		WHERE commodity_id = #{commodityId}
    </update>

	<!--更新 商品文描-是否显示保质期-->
	<update id="updateCommodityQualityStatus" parameterType="com.pinshang.qingyun.xda.product.model.XdaCommodityText">
		UPDATE t_xda_commodity_text
		SET
			quality_status = #{qualityStatus},
			update_id = #{updateId},
			update_time = #{updateTime}
		WHERE commodity_id = #{commodityId}
	</update>
    
    <!-- 查询  鲜达价格体系-商品文描信息  列表 -->
    <select id="selectXdaPriceStructureCommodityTextInfoList" 
    	parameterType="com.pinshang.qingyun.xda.product.dto.commodityText.SelectXdaPriceStructureCommodityTextInfoListIDTO"
    	resultType="com.pinshang.qingyun.xda.product.dto.commodityText.XdaPriceStructureCommodityTextInfoODTO">
    	SELECT
			c.id AS commodityId,
			c.commodity_code AS commodityCode,
			ct.commodity_app_name AS commodityAppName,
			ct.commodity_sub_name AS commoditySubName,
			xdCate1.cate_name AS xdFirstCategoryName,
			xdCate2.cate_name AS xdSecondCategoryName,
			t.tag_name AS tagName,
			ct.sort_num AS sortNum,
			ct.quality_status AS qualityStatus
		FROM t_commodity c
		LEFT JOIN t_xda_commodity_text ct ON ct.commodity_id = c.id
		LEFT JOIN t_xda_category xdCate1 ON xdCate1.id = ct.first_category_id
		LEFT JOIN t_xda_category xdCate2 ON xdCate2.id = ct.second_category_id
		LEFT JOIN t_xda_tag t ON t.id = ct.tag_id
		WHERE c.commodity_code IN 
		<foreach collection="commodityCodeList" item="commodityCode" open="(" close=")" separator=",">
   			#{commodityCode}
    	</foreach>
		ORDER BY c.commodity_code ASC
    </select>
    
    <!-- 查询  鲜达-商品文描-前台品类信息 -->
    <select id="selectXdaCommodityTextCategoryInfo" 
    	parameterType="com.pinshang.qingyun.xda.product.dto.commodityText.SelectXdaCommodityTextCategoryInfoIDTO"
    	resultType="com.pinshang.qingyun.xda.product.dto.commodityText.XdaCommodityTextCategoryInfoODTO">
    	SELECT
			xdCate1.id AS xdaFirstCategoryId,
			xdCate1.cate_name AS xdaFirstCategoryName,
			xdCate2.id AS xdaSecondCategoryId,
			xdCate2.cate_name AS xdaSecondCategoryName
		FROM t_xda_category xdCate1, t_xda_category xdCate2
		WHERE xdCate1.id = xdCate2.parent_id
		AND xdCate1.status = 1 AND xdCate2.status = 1
		AND (xdCate1.cate_name = #{xdaFirstCategoryName} AND xdCate2.cate_name = #{xdaSecondCategoryName})
    </select>
    
    <!-- 查询  鲜达商品信息 列表 -->
    <select id="selectXdaCommodityInfoList"
    	parameterType="com.pinshang.qingyun.xda.product.dto.commodityText.SelectXdaCommodityInfoListIDTO"
    	resultType="com.pinshang.qingyun.xda.product.dto.commodityText.XdaCommodityInfoODTO">
    	SELECT
			c.id AS commodityId,
			ct.commodity_app_name AS commodityAppName,
			ct.commodity_sub_name AS commoditySubName,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			c.commodity_spec AS commoditySpec,
			unit.option_name AS commodityUnitName,
			c.is_weight AS isWeight,
			c.sales_box_capacity AS salesBoxCapacity,
			c.commodity_package_spec as commodityPackageSpec
		FROM t_commodity c
		LEFT JOIN t_xda_commodity_text ct ON ct.commodity_id = c.id
		LEFT JOIN t_dictionary unit ON unit.id = c.commodity_unit_id
		WHERE c.id IN 
		<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
   			#{id}
    	</foreach>
    </select>
    
    <!-- 查询  商品文描标签信息  列表 -->
    <select id="selectCommodityTextTagInfoList"
    	parameterType="com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextTagInfoListIDTO"
    	resultType="com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO">
    	SELECT
			t.tag_name AS tagName,
			t.tag_bg_color AS tagBgColor,
			ct.commodity_id AS commodityId
		FROM t_xda_commodity_text ct
		INNER JOIN t_xda_tag t ON t.id = ct.tag_id
		<where>
		<if test="null != commodityId">
    		AND ct.commodity_id = #{commodityId}
   		</if>
   		<if test="null != commodityIdList and commodityIdList.size > 0">
    		AND ct.commodity_id IN 
    		<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
   				#{id}
    		</foreach>
   		</if>
		</where>
    </select>

 	<!-- 查询   鲜达商品下拉信息  列表 -->
    <select id="selectXdaCommodityDropdownInfoList"
    	parameterType="com.pinshang.qingyun.xda.product.dto.commodityText.SelectXdaCommodityDropdownInfoListIDTO"
    	resultType="com.pinshang.qingyun.xda.product.dto.commodityText.XdaCommodityDropdownInfoODTO">
    	SELECT
			c.id AS commodityId,
			c.commodity_code AS commodityCode,
			c.commodity_name AS commodityName,
			c.commodity_spec AS commoditySpec,
			ct.commodity_app_name AS commodityAppName
		FROM t_commodity c
		LEFT JOIN t_xda_commodity_text ct ON ct.commodity_id = c.id
		<where>
		<if test="null != keyword">
			AND (c.commodity_code LIKE CONCAT('%', TRIM(#{keyword}), '%') OR c.commodity_name LIKE CONCAT('%', TRIM(#{keyword}), '%'))
		</if>
		<if test="null != commodityAppName">
			AND (c.commodity_code LIKE CONCAT('%', TRIM(#{commodityAppName}), '%') OR ct.commodity_app_name LIKE CONCAT('%', TRIM(#{commodityAppName}), '%'))
		</if>
		<if test="null != commodityId">
			AND c.id = #{commodityId}
		</if>
		</where>
		ORDER BY LENGTH(c.commodity_code) ASC, c.commodity_code ASC
		LIMIT #{limitQuantity}
    </select>
	<select id="selectCommodityTextByShoppingCartGiftList"
			resultType="com.pinshang.qingyun.xda.product.dto.front.commodityFront.v2.XdaShoppingCartV3ODTO">
		SELECT
			c.id AS commodityId,
			c.commodity_code,
			ct.commodity_app_name AS commodityName,
			c.commodity_spec AS commoditySpec,
			d.option_name AS commodityUnitName,
			c.sales_box_capacity,
			c.commodity_is_quick_freeze AS isQuickFreeze,
			c.is_weight AS isWeight,
			c.commodity_package_spec AS commodityPackageSpec,
			c.commodity_weight AS commodityWeight,
			c.storage_condition AS storageCondition,
			c.quality_days AS qualityDays,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			pic.pic_url AS imageUrl,
			ct.sort_num
		FROM
			t_xda_commodity_text ct
			INNER JOIN t_commodity c ON ct.commodity_id = c.id
			INNER JOIN t_dictionary d ON c.commodity_unit_id = d.id
			LEFT JOIN t_xda_commodity_text_pic pic ON ct.commodity_id = pic.commodity_id AND pic.pic_type=1 AND pic.is_default = 1
			INNER JOIN t_xda_commodity_app_status txas ON txas.id = null or (txas.commodity_id = ct.commodity_id  <if test="isShoppingCart">
			AND (
				CASE
					WHEN #{isPfsStore} = true THEN txas.pf_app_status = 0
					ELSE txas.app_status = 0
				END
				)
			</if>)
		<where>
			<if test="isShoppingCart">
				AND c.status = 1
			</if>
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and ct.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
	</select>

	<select id="queryAllXdaCommodityText" resultType="com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextInfoODTO">
		SELECT
		    ct.commodity_id AS id,
			ct.commodity_id AS commodityId,
			ct.commodity_app_name commodityAppName,
			ct.first_category_id AS xdaFirstCategoryId,
			ct.second_category_id AS xdaSecondCategoryId,
			ca.cate_name xdaFirstCategoryName,
			ca2.cate_name xdaSecondCategoryName,
			cas.app_status appStatus,
		    oc.delivery_date_range_code
		from t_xda_commodity_text ct
		INNER JOIN t_xda_category ca on ca.id = ct.first_category_id
		INNER JOIN t_xda_category ca2 on ca2.id = ct.second_category_id
		INNER  JOIN t_xda_commodity_app_status cas on cas.commodity_id = ct.commodity_id
		INNER JOIN t_xda_order_commodity oc on oc.commodity_id = ct.commodity_id
		WHERE 1=1
		<if test="null != commodityIdList and commodityIdList.size > 0 ">
			and ct.commodity_id IN
			<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
				#{id}
			</foreach>
		</if>
	</select>

	<select id="selectCommodityTextEsList" resultType="com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextEsODTO">
		select cas.commodity_id AS commodityId,ct.commodity_app_name, ct.commodity_sub_name, ct.sort_num,
			   oc.delivery_date_range_code, oc.delivery_date_range_value,ctp.pic_url as defaultImageUrl, cas.app_status,
		       ct.first_category_id AS xdaFirstCategoryId,
		       ct.second_category_id AS xdaSecondCategoryId,
				ca.cate_name xdaFirstCategoryName,
				ca2.cate_name xdaSecondCategoryName,
				cas.pf_app_status,
				oc.pf_delivery_date_range_code,
				oc.pf_delivery_date_range_value
		from t_xda_commodity_app_status cas
		 INNER JOIN t_xda_commodity_text ct on cas.commodity_id = ct.commodity_id
		INNER JOIN t_xda_category ca on ca.id = ct.first_category_id
		INNER JOIN t_xda_category ca2 on ca2.id = ct.second_category_id
		    left join t_xda_commodity_text_pic ctp on (ctp.commodity_id = cas.commodity_id and ctp.pic_type = 1 and ctp.is_default = 1)
		 left join t_xda_order_commodity oc on oc.commodity_id = cas.commodity_id
		<where>
			<if test="null != commodityIdList and commodityIdList.size > 0 ">
				and cas.commodity_id IN
				<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
					#{id}
				</foreach>
			</if>
		</where>
	</select>

	<select id="selectCommodityByCateId" resultType="com.pinshang.qingyun.xda.product.model.XdaCommodityText">
		SELECT
		*
		FROM
		t_xda_commodity_text
		WHERE first_category_id = #{cateId}
		OR second_category_id = #{cateId}
	</select>

</mapper>