<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaCommodityTextPicMapper">

	<!-- 查询  商品文描图片  列表 -->
	<select id="selectCommodityTextPicList"
		resultType="com.pinshang.qingyun.xda.product.model.XdaCommodityTextPic" 
		parameterType="com.pinshang.qingyun.xda.product.dto.commodityText.SelectCommodityTextPicListIDTO">
		SELECT *
		FROM t_xda_commodity_text_pic ctp
		<where>
		<if test="commodityId != null">
			AND ctp.commodity_id = #{commodityId}
		</if>
		<if test="commodityIdList != null">
			AND ctp.commodity_id IN 
			<foreach collection="commodityIdList" item="id" open="(" close=")" separator=",">
     			#{id}
      		</foreach>
		</if>
		<if test="picType != null">
			AND ctp.pic_type = #{picType}
		</if>
		<if test="isDefault != null">
			AND ctp.is_default = #{isDefault}
		</if>
		</where>
		ORDER BY ctp.commodity_id, ctp.pic_type, ctp.is_default DESC, ctp.id
	</select>

</mapper>