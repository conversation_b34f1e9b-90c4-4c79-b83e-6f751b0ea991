<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.deliveryDate.XdaOrderCommodityLogDao">
    <!-- XdaOrderCommodityLog的resultMap,column是给数据库列起的别名,它对应property类的属性-->
    <resultMap id="result_XdaOrderCommodityLog_Map" type="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodityLog">
        <id column="id" property="id" />
        <result column="commodity_code" property="commodityCode" />
        <result column="create_time" property="createTime" />
        <result column="create_id" property="createId" />
        <result column="delivery_date_range_code" property="deliveryDateRangeCode" />
        <result column="delivery_date_range_value" property="deliveryDateRangeValue" />
        <result column="op_type" property="opType" />
        <result column="change_price" property="changePrice" />
        <result column="spec" property="spec" />
        <result column="commodity_name" property="commodityName" />
        <result column="create_name" property="createName" />
    </resultMap>

    <!-- 数据库中表名为:t_xda_order_commodity_log的列名,as前是数据库的列明,as后是列的别名用于映射成实体类中的属性,需要注意的是别名必须与resultMap中的column别名一致 -->
    <sql id="t_xda_order_commodity_log_Column">
        t_xda_order_commodity_log.id as id
        ,t_xda_order_commodity_log.commodity_code as commodity_code
        ,t_xda_order_commodity_log.create_time as create_time
        ,t_xda_order_commodity_log.create_id as create_id
        ,t_xda_order_commodity_log.delivery_date_range_code as delivery_date_range_code
        ,t_xda_order_commodity_log.delivery_date_range_value as delivery_date_range_value
        ,t_xda_order_commodity_log.op_type as op_type
        ,t_xda_order_commodity_log.change_price as change_price
        ,t_xda_order_commodity_log.spec as spec
        ,t_xda_order_commodity_log.commodity_name as commodity_name
        ,t_xda_order_commodity_log.create_name as create_name
    </sql>

    <!--获得类名为:XdaOrderCommodityLog对应的数据库表的数据总行数 -->
    <select id="getXdaOrderCommodityLogRowCount" resultType="java.lang.Long">
        select count(id) from t_xda_order_commodity_log
    </select>
    <!-- 获得类名为:XdaOrderCommodityLog对应数据库中表的数据集合 -->
    <select id="selectXdaOrderCommodityLog" resultMap="result_XdaOrderCommodityLog_Map">
        select 
        <include refid="t_xda_order_commodity_log_Column" /> 
        from t_xda_order_commodity_log
    </select> 

    <!-- 获得一个XdaOrderCommodityLog对象,以参数XdaOrderCommodityLog对象中不为空的属性作为条件进行查询-->
    <select id="selectXdaOrderCommodityLogByObj" parameterType="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodityLog" resultMap="result_XdaOrderCommodityLog_Map">
        select 
            <include refid="t_xda_order_commodity_log_Column" /> 
        from t_xda_order_commodity_log
        <where>
            <if test="createName != null "> and t_xda_order_commodity_log.create_name = #{createName}</if>
            <if test="commodityName != null "> and t_xda_order_commodity_log.commodity_name = #{commodityName}</if>
            <if test="spec != null "> and t_xda_order_commodity_log.spec = #{spec}</if>
            <if test="changePrice != null "> and t_xda_order_commodity_log.change_price = #{changePrice}</if>
            <if test="opType != null "> and t_xda_order_commodity_log.op_type = #{opType}</if>
            <if test="deliveryDateRangeValue != null "> and t_xda_order_commodity_log.delivery_date_range_value = #{deliveryDateRangeValue}</if>
            <if test="deliveryDateRangeCode != null "> and t_xda_order_commodity_log.delivery_date_range_code = #{deliveryDateRangeCode}</if>
            <if test="createId != null "> and t_xda_order_commodity_log.create_id = #{createId}</if>
            <if test="createTime != null "> and t_xda_order_commodity_log.create_time = #{createTime}</if>
            <if test="commodityCode != null "> and t_xda_order_commodity_log.commodity_code = #{commodityCode}</if>
            <if test="id != null "> and t_xda_order_commodity_log.id = #{id}</if>
        </where>
    </select>
    <select id="getXdaOrderCommodityLogList" parameterType="com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityLogVO" resultType="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodityLog">
        SELECT
        log.id,
        log.op_type,
        log.commodity_name,
        log.commodity_code,
        log.spec,
        log.delivery_date_range_code,
        log.delivery_date_range_value,
        log.change_price,
        log.create_time,
        log.create_id,
        log.create_name
        FROM
        t_xda_order_commodity_log AS log
        <where>
            <if test="operateType != null and operateType!=''">
                and log.op_type=#{operateType}
            </if>
            <if test="commodityId != null and commodityId!=''">
                and log.commodity_code=#{commodityId}
            </if>

        </where>
        order by log.create_time desc


    </select>
    <!-- 通过XdaOrderCommodityLog的id获得对应数据库中表的数据对象-->
    <select id="selectXdaOrderCommodityLogById" parameterType="java.lang.Long" resultMap="result_XdaOrderCommodityLog_Map">
        select 
            <include refid="t_xda_order_commodity_log_Column" /> 
        from t_xda_order_commodity_log
        where t_xda_order_commodity_log.id = #{id}
    </select> 

    <!-- 将XdaOrderCommodityLog插入到对应数据库的表中,包括属性值为null的数据-->

    <insert id="insertXdaOrderCommodityLog" parameterType="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodityLog">
        insert into t_xda_order_commodity_log(id,commodity_code,create_time,create_id,delivery_date_range_code,delivery_date_range_value,op_type,change_price,spec,commodity_name,create_name) 
        values(#{id},#{commodityCode},#{createTime},#{createId},#{deliveryDateRangeCode},#{deliveryDateRangeValue},#{opType},#{changePrice},#{spec},#{commodityName},#{createName})
    </insert>

    <!-- 将XdaOrderCommodityLog中属性值不为null的数据,插入到对应数据库的表中-->
    <insert id="insertNonEmptyXdaOrderCommodityLog" parameterType="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodityLog">
        insert into t_xda_order_commodity_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="commodityCode != null">commodity_code,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createId != null">create_id,</if>
            <if test="deliveryDateRangeCode != null">delivery_date_range_code,</if>
            <if test="deliveryDateRangeValue != null">delivery_date_range_value,</if>
            <if test="opType != null">op_type,</if>
            <if test="changePrice != null">change_price,</if>
            <if test="spec != null">spec,</if>
            <if test="commodityName != null">commodity_name,</if>
            <if test="createName != null">create_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null"> #{id},</if>
            <if test="commodityCode != null"> #{commodityCode},</if>
            <if test="createTime != null"> #{createTime},</if>
            <if test="createId != null"> #{createId},</if>
            <if test="deliveryDateRangeCode != null"> #{deliveryDateRangeCode},</if>
            <if test="deliveryDateRangeValue != null"> #{deliveryDateRangeValue},</if>
            <if test="opType != null"> #{opType},</if>
            <if test="changePrice != null"> #{changePrice},</if>
            <if test="spec != null"> #{spec},</if>
            <if test="commodityName != null"> #{commodityName},</if>
            <if test="createName != null"> #{createName},</if>
        </trim>
    </insert>

    <!-- 将XdaOrderCommodityLog批量插入到对应数据库的表中-->
    <insert id="insertXdaOrderCommodityLogByBatch" parameterType="ArrayList">
        insert into t_xda_order_commodity_log(id,commodity_code,create_time,create_id,delivery_date_range_code,delivery_date_range_value,op_type,change_price,spec,commodity_name,create_name) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.commodityCode},#{item.createTime},#{item.createId},#{item.deliveryDateRangeCode},#{item.deliveryDateRangeValue},#{item.opType},#{item.changePrice},#{item.spec},#{item.commodityName},#{item.createName})
        </foreach>
    </insert>

    <!-- 通过XdaOrderCommodityLog的id将数据库表中对应的数据删除-->
    <delete id="deleteXdaOrderCommodityLogById" parameterType="java.lang.Long">
        delete from t_xda_order_commodity_log
        where id = #{id}
    </delete>

    <!-- 通过XdaOrderCommodityLog的id将XdaOrderCommodityLog的数据更新到数据库中对应的表,包括值null的数据-->
    <update id="updateXdaOrderCommodityLogById" parameterType="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodityLog">
        update t_xda_order_commodity_log set
            commodity_code=#{commodityCode}
            ,create_time=#{createTime}
            ,create_id=#{createId}
            ,delivery_date_range_code=#{deliveryDateRangeCode}
            ,delivery_date_range_value=#{deliveryDateRangeValue}
            ,op_type=#{opType}
            ,change_price=#{changePrice}
            ,spec=#{spec}
            ,commodity_name=#{commodityName}
            ,create_name=#{createName}
        where id=#{id}
    </update>

    <!-- 通过XdaOrderCommodityLog的id将XdaOrderCommodityLog中属性值不为null的数据更新到数据库对应的表中-->
    <update id="updateNonEmptyXdaOrderCommodityLogById" parameterType="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodityLog">
        update t_xda_order_commodity_log
        <set>
            <if test="commodityCode != null">
                commodity_code=#{commodityCode},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="createId != null">
                create_id=#{createId},
            </if>
            <if test="deliveryDateRangeCode != null">
                delivery_date_range_code=#{deliveryDateRangeCode},
            </if>
            <if test="deliveryDateRangeValue != null">
                delivery_date_range_value=#{deliveryDateRangeValue},
            </if>
            <if test="opType != null">
                op_type=#{opType},
            </if>
            <if test="changePrice != null">
                change_price=#{changePrice},
            </if>
            <if test="spec != null">
                spec=#{spec},
            </if>
            <if test="commodityName != null">
                commodity_name=#{commodityName},
            </if>
            <if test="createName != null">
                create_name=#{createName},
            </if>
        </set>
        where id=#{id}
    </update>

</mapper>