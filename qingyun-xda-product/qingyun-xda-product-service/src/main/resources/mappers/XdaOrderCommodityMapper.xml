<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.deliveryDate.XdaOrderCommodityDao">
    <!-- XdaOrderCommodity的resultMap,column是给数据库列起的别名,它对应property类的属性-->
    <resultMap id="result_XdaOrderCommodity_Map" type="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodity">
        <id column="id" property="id" />
        <result column="update_id" property="updateId" />
        <result column="commodity_code" property="commodityCode" />
        <result column="create_time" property="createTime" />
        <result column="update_name" property="updateName" />
        <result column="spec" property="spec" />
        <result column="update_time" property="updateTime" />
        <result column="commodity_id" property="commodityId" />
        <result column="create_id" property="createId" />
        <result column="delivery_date_range_code" property="deliveryDateRangeCode" />
        <result column="delivery_date_range_value" property="deliveryDateRangeValue" />
        <result column="pf_delivery_date_range_code" property="pfDeliveryDateRangeCode" />
        <result column="pf_delivery_date_range_value" property="pfDeliveryDateRangeValue" />
        <result column="commodity_name" property="commodityName" />
        <result column="create_name" property="createName" />
    </resultMap>

    <!-- 数据库中表名为:t_xda_order_commodity的列名,as前是数据库的列明,as后是列的别名用于映射成实体类中的属性,需要注意的是别名必须与resultMap中的column别名一致 -->
    <sql id="t_xda_order_commodity_Column">
        t_xda_order_commodity.id as id
        ,t_xda_order_commodity.update_id as update_id
        ,t_xda_order_commodity.commodity_code as commodity_code
        ,t_xda_order_commodity.create_time as create_time
        ,t_xda_order_commodity.update_name as update_name
        ,t_xda_order_commodity.spec as spec
        ,t_xda_order_commodity.update_time as update_time
        ,t_xda_order_commodity.commodity_id as commodity_id
        ,t_xda_order_commodity.create_id as create_id
        ,t_xda_order_commodity.delivery_date_range_code as delivery_date_range_code
        ,t_xda_order_commodity.delivery_date_range_value as delivery_date_range_value
        ,t_xda_order_commodity.pf_delivery_date_range_code as pf_delivery_date_range_code
        ,t_xda_order_commodity.pf_delivery_date_range_value as pf_delivery_date_range_value
        ,t_xda_order_commodity.commodity_name as commodity_name
        ,t_xda_order_commodity.create_name as create_name
    </sql>
    <select id="getXdaOrderCommodityList" parameterType="com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityVO" resultType="com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityDTO">
        SELECT
        xoc.id,
        tc.id AS commodity_id,
        tc.commodity_code,
        tc.commodity_name,
        tc.commodity_spec AS spec,
        case
        when #{appType}=1 then xoc.delivery_date_range_code
        else xoc.pf_delivery_date_range_code
        end as deliveryDateRangeCode,
        case
        when #{appType}=1 then xoc.delivery_date_range_value
        else xoc.pf_delivery_date_range_value
        end as deliveryDateRangeValue,
        xoc.create_time,
        xoc.update_time,
        xoc.create_id,
        xoc.update_id,
        xoc.create_name,
        xoc.update_name
        FROM
        t_commodity AS tc
        LEFT JOIN t_xda_order_commodity xoc ON xoc.commodity_id = tc.id
        <if test="prizeModelId != null and prizeModelId!=''">
            LEFT JOIN t_product_price_model_list tppml ON tc.id = tppml.commodity_id
        </if>
        <where>
            <if test="supplierId != null ">
                and  xoc.commodity_id IN
                <foreach collection="commodityIds" item="id" index="index"
                         open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
            <if test="deliveryDateRangeCode != null and deliveryDateRangeCode!='' and appType == 1">
                and xoc.delivery_date_range_code=#{deliveryDateRangeCode}
            </if>
            <if test="deliveryDateRangeCode != null and deliveryDateRangeCode!='' and appType == 2">
                and xoc.pf_delivery_date_range_code=#{deliveryDateRangeCode}
            </if>
            <if test="setDate=='0'.toString()  and appType == 1">
                and ISNULL(xoc.delivery_date_range_value)=1
            </if>
            <if test="setDate=='0'.toString()  and appType == 2">
                and ISNULL(xoc.pf_delivery_date_range_value)=1
            </if>
            <if test="commodityName != null and commodityName!=''">
                and (tc.commodity_name=#{commodityName} or tc.commodity_code=#{commodityName})
            </if>
            <if test="prizeModelId != null ">
                and  tppml.product_price_model_id = #{prizeModelId}
            </if>
            <if test="categoryId != null ">
                and  (tc.commodity_first_id = #{categoryId} or tc.commodity_second_id = #{categoryId} or tc.commodity_third_id = #{categoryId}  )
            </if>
            and tc.status = 1
        </where>
        order by 
            case 
                when #{appType}=1 then ISNULL(xoc.delivery_date_range_value)
                else ISNULL(xoc.pf_delivery_date_range_value)
            end desc,
            LENGTH(tc.commodity_code) asc,
            tc.commodity_code asc

    </select>

    <select id="getDistinctXdaOrderCommodityList" parameterType="com.pinshang.qingyun.xda.product.dto.deliveryDate.XdaOrderCommodityVO" resultType="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodity">
        SELECT
        DISTINCT
        xoc.delivery_date_range_code
        FROM
        t_xda_order_commodity AS xoc
        order by xoc.create_time desc
    </select>

    <!-- 通过XdaOrderCommodity的id获得对应数据库中表的数据对象-->
    <select id="selectCommodityById" parameterType="java.lang.Long" resultMap="result_XdaOrderCommodity_Map">
          SELECT
         id as commodity_id,
         commodity_code,
         commodity_name,
         commodity_spec as spec
        FROM
            t_commodity
        WHERE
            t_commodity.id = #{id}
    </select>
    <!-- 通过XdaOrderCommodity的id获得对应数据库中表的数据对象-->
    <select id="selectXdaOrderCommodityByCommodityId" parameterType="java.lang.Long" resultMap="result_XdaOrderCommodity_Map">
        select
        <include refid="t_xda_order_commodity_Column" />
        from t_xda_order_commodity
        where t_xda_order_commodity.commodity_id = #{commodityId}
    </select>



    <!--获得类名为:XdaOrderCommodity对应的数据库表的数据总行数 -->
    <select id="getXdaOrderCommodityRowCount" resultType="java.lang.Long">
        select count(id) from t_xda_order_commodity
    </select>
    <!-- 获得类名为:XdaOrderCommodity对应数据库中表的数据集合 -->
    <select id="selectXdaOrderCommodity" resultMap="result_XdaOrderCommodity_Map">
        select 
        <include refid="t_xda_order_commodity_Column" /> 
        from t_xda_order_commodity
    </select> 

    <!-- 获得一个XdaOrderCommodity对象,以参数XdaOrderCommodity对象中不为空的属性作为条件进行查询-->
    <select id="selectXdaOrderCommodityByObj" parameterType="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodity" resultMap="result_XdaOrderCommodity_Map">
        select 
            <include refid="t_xda_order_commodity_Column" /> 
        from t_xda_order_commodity
        <where>
            <if test="createName != null "> and t_xda_order_commodity.create_name = #{createName}</if>
            <if test="commodityName != null "> and t_xda_order_commodity.commodity_name = #{commodityName}</if>
            <if test="deliveryDateRangeValue != null "> and t_xda_order_commodity.delivery_date_range_value = #{deliveryDateRangeValue}</if>
            <if test="deliveryDateRangeCode != null "> and t_xda_order_commodity.delivery_date_range_code = #{deliveryDateRangeCode}</if>
            <if test="createId != null "> and t_xda_order_commodity.create_id = #{createId}</if>
            <if test="commodityId != null "> and t_xda_order_commodity.commodity_id = #{commodityId}</if>
            <if test="updateTime != null "> and t_xda_order_commodity.update_time = #{updateTime}</if>
            <if test="spec != null "> and t_xda_order_commodity.spec = #{spec}</if>
            <if test="updateName != null "> and t_xda_order_commodity.update_name = #{updateName}</if>
            <if test="createTime != null "> and t_xda_order_commodity.create_time = #{createTime}</if>
            <if test="commodityCode != null "> and t_xda_order_commodity.commodity_code = #{commodityCode}</if>
            <if test="updateId != null "> and t_xda_order_commodity.update_id = #{updateId}</if>
            <if test="id != null "> and t_xda_order_commodity.id = #{id}</if>
        </where>
    </select> 

    <!-- 通过XdaOrderCommodity的id获得对应数据库中表的数据对象-->
    <select id="selectXdaOrderCommodityById" parameterType="java.lang.Long" resultMap="result_XdaOrderCommodity_Map">
        select 
            <include refid="t_xda_order_commodity_Column" /> 
        from t_xda_order_commodity
        where t_xda_order_commodity.id = #{id}
    </select> 

    <!-- 将XdaOrderCommodity插入到对应数据库的表中,包括属性值为null的数据-->

    <insert id="insertXdaOrderCommodity" parameterType="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodity">
        insert into t_xda_order_commodity(id,update_id,commodity_code,create_time,update_name,spec,update_time,commodity_id,create_id,delivery_date_range_code,delivery_date_range_value,commodity_name,create_name) 
        values(#{id},#{updateId},#{commodityCode},#{createTime},#{updateName},#{spec},#{updateTime},#{commodityId},#{createId},#{deliveryDateRangeCode},#{deliveryDateRangeValue},#{commodityName},#{createName})
    </insert>

    <!-- 将XdaOrderCommodity中属性值不为null的数据,插入到对应数据库的表中-->
    <insert id="insertNonEmptyXdaOrderCommodity" useGeneratedKeys="true" keyProperty="id" parameterType="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodity">
        insert into t_xda_order_commodity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="updateId != null">update_id,</if>
            <if test="commodityCode != null">commodity_code,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateName != null">update_name,</if>
            <if test="spec != null">spec,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="commodityId != null">commodity_id,</if>
            <if test="createId != null">create_id,</if>
            <if test="deliveryDateRangeCode != null">delivery_date_range_code,</if>
            <if test="deliveryDateRangeValue != null">delivery_date_range_value,</if>
            <if test="pfDeliveryDateRangeCode != null">pf_delivery_date_range_code,</if>
            <if test="pfDeliveryDateRangeValue != null">pf_delivery_date_range_value,</if>
            <if test="commodityName != null">commodity_name,</if>
            <if test="createName != null">create_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null"> #{id},</if>
            <if test="updateId != null"> #{updateId},</if>
            <if test="commodityCode != null"> #{commodityCode},</if>
            <if test="createTime != null"> #{createTime},</if>
            <if test="updateName != null"> #{updateName},</if>
            <if test="spec != null"> #{spec},</if>
            <if test="updateTime != null"> #{updateTime},</if>
            <if test="commodityId != null"> #{commodityId},</if>
            <if test="createId != null"> #{createId},</if>
            <if test="deliveryDateRangeCode != null"> #{deliveryDateRangeCode},</if>
            <if test="deliveryDateRangeValue != null"> #{deliveryDateRangeValue},</if>
            <if test="pfDeliveryDateRangeCode != null"> #{pfDeliveryDateRangeCode},</if>
            <if test="pfDeliveryDateRangeValue != null"> #{pfDeliveryDateRangeValue},</if>
            <if test="commodityName != null"> #{commodityName},</if>
            <if test="createName != null"> #{createName},</if>
        </trim>
    </insert>

    <!-- 将XdaOrderCommodity批量插入到对应数据库的表中-->
    <insert id="insertXdaOrderCommodityByBatch" parameterType="ArrayList">
        insert into t_xda_order_commodity(id,update_id,commodity_code,create_time,update_name,spec,update_time,commodity_id,create_id,delivery_date_range_code,delivery_date_range_value,commodity_name,create_name) values
        <foreach collection="list" item="item" index="index" separator="," >
            (#{item.id},#{item.updateId},#{item.commodityCode},#{item.createTime},#{item.updateName},#{item.spec},#{item.updateTime},#{item.commodityId},#{item.createId},#{item.deliveryDateRangeCode},#{item.deliveryDateRangeValue},#{item.commodityName},#{item.createName})
        </foreach>
    </insert>

    <!-- 通过XdaOrderCommodity的id将数据库表中对应的数据删除-->
    <delete id="deleteXdaOrderCommodityById" parameterType="java.lang.Long">
        delete from t_xda_order_commodity
        where id = #{id}
    </delete>

    <!-- 通过XdaOrderCommodity的id将XdaOrderCommodity的数据更新到数据库中对应的表,包括值null的数据-->
    <update id="updateXdaOrderCommodityById" parameterType="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodity">
        update t_xda_order_commodity set
            update_id=#{updateId}
            ,commodity_code=#{commodityCode}
            ,create_time=#{createTime}
            ,update_name=#{updateName}
            ,spec=#{spec}
            ,update_time=#{updateTime}
            ,commodity_id=#{commodityId}
            ,create_id=#{createId}
            ,delivery_date_range_code=#{deliveryDateRangeCode}
            ,delivery_date_range_value=#{deliveryDateRangeValue}
            ,commodity_name=#{commodityName}
            ,create_name=#{createName}
        where id=#{id}
    </update>

    <!-- 通过XdaOrderCommodity的id将XdaOrderCommodity中属性值不为null的数据更新到数据库对应的表中-->
    <update id="updateNonEmptyXdaOrderCommodityById" parameterType="com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodity">
        update t_xda_order_commodity
        <set>
            <if test="updateId != null">
                update_id=#{updateId},
            </if>
            <if test="commodityCode != null">
                commodity_code=#{commodityCode},
            </if>
            <if test="createTime != null">
                create_time=#{createTime},
            </if>
            <if test="updateName != null">
                update_name=#{updateName},
            </if>
            <if test="spec != null">
                spec=#{spec},
            </if>
            <if test="updateTime != null">
                update_time=#{updateTime},
            </if>
            <if test="commodityId != null">
                commodity_id=#{commodityId},
            </if>
            <if test="createId != null">
                create_id=#{createId},
            </if>
            <if test="deliveryDateRangeCode != null">
                delivery_date_range_code=#{deliveryDateRangeCode},
            </if>
            <if test="deliveryDateRangeValue != null">
                delivery_date_range_value=#{deliveryDateRangeValue},
            </if>
            <if test="pfDeliveryDateRangeCode != null">
                pf_delivery_date_range_code=#{pfDeliveryDateRangeCode},
            </if>
            <if test="pfDeliveryDateRangeValue != null">
                pf_delivery_date_range_value=#{pfDeliveryDateRangeValue},
            </if>
            <if test="commodityName != null">
                commodity_name=#{commodityName},
            </if>
            <if test="createName != null">
                create_name=#{createName},
            </if>
        </set>
        where id=#{id}
    </update>

</mapper>