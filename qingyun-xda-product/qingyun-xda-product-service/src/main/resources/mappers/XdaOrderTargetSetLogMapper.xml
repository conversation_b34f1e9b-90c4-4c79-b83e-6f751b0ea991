<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaOrderTargetSetLogMapper">

    <select id="findXdaOrderTargetSetLogList" resultType="com.pinshang.qingyun.xda.product.dto.orderTargetSet.XdaOrderTargetSetLogQueryODTO">
        SELECT
        <![CDATA[
          o.operation_type,
          o.operation_type_name,
          o.store_id,
          s.store_code,
          s.store_name,
          CONCAT_WS('_',sl.customer_code,sl.customer_name)AS settlementCodeName,
          d.option_name AS storeTypeName,
          CONCAT_WS('~',DATE_FORMAT(o.delivery_start_date,'%Y-%m-%d'),DATE_FORMAT(o.delivery_end_date,'%Y-%m-%d')) AS deliveryDate,
          o.order_target_to_day,
          o.loop_set,
          o.order_target_status,
          (CASE  WHEN o.order_target_status=0 THEN '停用'
                 WHEN o.order_target_status=1 THEN '启用'
                 ELSE '' END
           )AS orderTargetStatusName,
          o.order_target_code,
          DATE_FORMAT(o.create_time,'%Y-%m-%d %H:%i:%s') AS operateTime,
          CONCAT_WS('_',e.employee_code,o.create_name,IF(e.employee_state=4,'已离职',NULL)) AS operateUser
          ]]>
        FROM
        t_xda_order_target_set_log o
        LEFT JOIN t_store s ON s.id = o.store_id
        LEFT JOIN t_store_settlement se ON se.store_id=s.id
        LEFT JOIN t_settlement sl ON sl.id=se.settlement_customer_id
        LEFT JOIN t_dictionary d ON d.id=s.store_type_id
        LEFT JOIN t_employee_user e ON e.user_id=o.create_id
        WHERE o.store_id= #{vo.storeId}
        ORDER BY o.create_time DESC
    </select>

</mapper>
