<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaOrderTargetSetMapper">

    <!--根据客户id和送货日期查询当天是否有符合的订货目标-->
    <select id="findOrderTargetByStoreIdAndOrderTime" resultType="com.pinshang.qingyun.xda.product.model.orderTargetSet.XdaOrderTargetSet">
        SELECT
            ots.id,
            ots.order_target_code,
            ots.store_id,
            ots.order_target_to_day,
            ots.delivery_start_date,
            ots.delivery_end_date,
            ots.loop_set,
            ots.delivery_date_range_type,
            ots.order_target_status
        FROM
            t_xda_order_target_set ots
        <where>
            and ots.order_target_status = 1
            <if test="null != storeId ">
                and ots.store_id = #{storeId}
            </if>
            <if test="null != orderTime">
                and #{orderTime} BETWEEN ots.delivery_start_date AND ots.delivery_end_date
            </if>
        </where>
        ORDER BY ots.create_time DESC,ots.id DESC
        limit 1
    </select>


    <select id="findXdaOrderTargetSetList" resultType="com.pinshang.qingyun.xda.product.dto.orderTargetSet.XdaOrderTargetSetQueryODTO">
        SELECT
         <![CDATA[
          o.id,
          o.store_id,
          s.store_code,
          s.store_name,
          CONCAT_WS('_',sl.customer_code,sl.customer_name)AS settlementCodeName,
          d.option_name AS storeTypeName,
          CONCAT_WS('~',DATE_FORMAT(o.delivery_start_date,'%Y-%m-%d'),DATE_FORMAT(o.delivery_end_date,'%Y-%m-%d')) AS deliveryDate,
          o.order_target_to_day,
          o.loop_set,
          o.order_target_status,
          (CASE  WHEN o.order_target_status=0 THEN '停用'
                 WHEN o.order_target_status=1 THEN '启用'
                 ELSE '' END
           )AS orderTargetStatusName,
          o.order_target_code,
          DATE_FORMAT(o.update_time,'%Y-%m-%d %H:%i:%s') AS operateTime,
          CONCAT_WS('_',e.employee_code,o.update_name,IF(e.employee_state=4,'已离职',NULL)) AS operateUser
          ]]>
        FROM
            t_xda_order_target_set o
        LEFT JOIN t_store s ON s.id = o.store_id
        LEFT JOIN t_store_settlement se ON se.store_id=s.id
        LEFT JOIN t_settlement sl ON sl.id=se.settlement_customer_id
        LEFT JOIN t_dictionary d ON d.id=s.store_type_id
        LEFT JOIN t_employee_user e ON e.user_id=o.update_id
        <where>
        <![CDATA[ (o.delivery_start_date<= #{vo.deliveryDate} AND o.delivery_end_date>= #{vo.deliveryDate}) ]]>
         <if test="vo.isExpire!=null">
             <choose>
                 <when test="vo.isExpire==0">
                     <![CDATA[ AND o.delivery_end_date< DATE_FORMAT(now(),'%Y-%m-%d') ]]>
                 </when>
                <otherwise>
                     <![CDATA[ AND o.delivery_end_date>= DATE_FORMAT(now(),'%Y-%m-%d') ]]>
                 </otherwise>
             </choose>
         </if>
         <if test="vo.orderTargetStatus!=null">
             AND o.order_target_status= #{vo.orderTargetStatus}
         </if>

        <if test="vo.orderTargetCode!=null and vo.orderTargetCode!=''">
            AND o.order_target_code= #{vo.orderTargetCode}
        </if>

        <if test="vo.storeId!=null">
            AND o.store_id= #{vo.storeId}
        </if>
        </where>
        ORDER BY o.create_time DESC ,o.id DESC
    </select>

    <select id="findXdaOrderTargetSetDetailById" resultType="com.pinshang.qingyun.xda.product.dto.orderTargetSet.XdaOrderTargetSetDetailODTO">
        SELECT
          <![CDATA[
          o.id,
          o.store_id,
          o.order_target_to_day,
          o.delivery_date_range_type,
          o.loop_set,
          DATE_FORMAT(o.delivery_start_date,'%Y-%m-%d') AS deliveryStartDate,
          DATE_FORMAT(o.delivery_end_date,'%Y-%m-%d') AS deliveryEndDate,
          CONCAT_WS('_',s.store_code,s.store_name) AS storeCodeName,
          d.option_name AS storeTypeName
          ]]>
        FROM
            t_xda_order_target_set o
        LEFT JOIN t_store s ON s.id = o.store_id
        LEFT JOIN t_dictionary d ON d.id=s.store_type_id
        WHERE o.id=#{id}
    </select>
</mapper>
