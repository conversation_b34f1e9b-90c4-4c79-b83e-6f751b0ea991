<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaSerialCommodityMapper">

	<!-- 分页查询  系列品日志信息  列表 -->
	<select id="selectSerialCommodityLogInfoList"
		resultType="com.pinshang.qingyun.xda.product.dto.serialCommodity.SerialCommodityLogInfoODTO" 
		parameterType="com.pinshang.qingyun.xda.product.dto.serialCommodity.SelectSerialCommodityLogInfoPageIDTO">
		SELECT
			scl.operate_type AS operateType,
			scl.commodity_id AS commodityId,
			c.commodity_code AS commodityCode,
			c.commodity_name AS commodityName,
			c.commodity_spec AS commoditySpec,
			
			ct.commodity_app_name AS commodityAppName,
			scl.first_category_id AS xdaFirstCategoryId,
			scl.second_category_id AS xdaSecondCategoryId,
			scl.serial_commodity_id AS serialCommodityId,
			scl.serial_commodity_code AS serialCommodityCode,
			u.employee_name AS createName,
			scl.create_time AS createTime
		FROM t_xda_serial_commodity_log scl
		LEFT JOIN t_commodity c ON c.id = scl.commodity_id
		LEFT JOIN t_xda_commodity_text ct ON ct.commodity_id = scl.commodity_id
		LEFT JOIN t_employee_user u ON u.user_id = scl.create_id
		<where>
		<if test="serialCommodityCode != null and serialCommodityCode != ''">
			AND scl.serial_commodity_code = #{serialCommodityCode}
		</if>
		<if test="commodityId != null">
			AND scl.commodity_id = #{commodityId}
		</if>
		<if test="barCode != null and barCode != ''">
			AND c.bar_code = #{barCode}
		</if>
		</where>
		ORDER BY scl.id DESC
	</select>

	<!-- 查询  系列品 -->
	<select id="selectSerialCommodity" 
		resultType="com.pinshang.qingyun.xda.product.model.XdaSerialCommodity" 
		parameterType="com.pinshang.qingyun.xda.product.dto.serialCommodity.SelectSerialCommodityIDTO">
		SELECT
			sc.commodity_id AS commodityId,
			sc.serial_commodity_id AS serialCommodityId,
			sc.serial_commodity_code AS serialCommodityCode
		FROM t_xda_serial_commodity sc 
		INNER JOIN t_commodity c ON c.id = sc.commodity_id
		<where>
		<if test="serialCommodityCode != null and serialCommodityCode != ''">
			AND sc.serial_commodity_code = #{serialCommodityCode}
		</if>
		<if test="commodityId != null and commodityId != ''">
			AND sc.commodity_id = #{commodityId}
		</if>
		<if test="barCode != null and barCode != ''">
			AND c.bar_code = #{barCode}
		</if>
		</where>
		LIMIT 1
	</select>
	
	<!-- 查询  系列品信息  列表 -->
	<select id="selectSerialCommodityInfoList" 
		resultType="com.pinshang.qingyun.xda.product.dto.serialCommodity.SerialCommodityInfoODTO">
		SELECT
			c.id AS commodityId,
			c.commodity_code AS commodityCode,
			c.commodity_name AS commodityName,
			c.commodity_spec AS commoditySpec,
		
			ct.commodity_app_name AS commodityAppName,
			cate1.cate_name AS xdaFirstCategoryName,
			cate2.cate_name AS xdaSecondCategoryName,
		
			sc.serial_commodity_id AS serialCommodityId,
			sc.serial_commodity_code AS serialCommodityCode
		FROM t_xda_serial_commodity sc 
		LEFT JOIN t_xda_commodity_text ct ON ct.commodity_id = sc.commodity_id
		LEFT JOIN t_commodity c ON c.id = sc.commodity_id
		LEFT JOIN t_xda_category cate1 ON cate1.id = ct.first_category_id
		LEFT JOIN t_xda_category cate2 ON cate2.id = ct.second_category_id
		WHERE sc.serial_commodity_id = #{serialCommodityId}
		ORDER BY c.commodity_code ASC
	</select>
	
	<!-- 查询  商品下拉信息  列表 （for系列品） -->
	<select id="selectCommodityDropdownInfoList4SerialCommodity" 
		resultType="com.pinshang.qingyun.xda.product.dto.serialCommodity.SerialCommodityInfoODTO"
		parameterType="com.pinshang.qingyun.xda.product.dto.serialCommodity.SelectCommodityDropdownInfoList4SerialCommodityIDTO">
		SELECT
			c.id AS commodityId,
			c.commodity_code AS commodityCode,
			c.commodity_name AS commodityName,
			c.commodity_spec AS commoditySpec,
		
			ct.commodity_app_name AS commodityAppName,
			cate1.cate_name AS xdaFirstCategoryName,
			cate2.cate_name AS xdaSecondCategoryName
		FROM t_commodity c
		INNER JOIN t_xda_commodity_text ct ON ct.commodity_id = c.id
		LEFT JOIN t_xda_category cate1 ON cate1.id = ct.first_category_id
		LEFT JOIN t_xda_category cate2 ON cate2.id = ct.second_category_id
		<where>
		<if test="keyword != null and keyword != ''">
			AND (c.commodity_code LIKE CONCAT('%',TRIM(#{keyword}),'%') OR ct.commodity_app_name LIKE CONCAT('%',TRIM(#{keyword}),'%'))
		</if>
		AND c.id NOT IN (SELECT sc.commodity_id FROM t_xda_serial_commodity sc)
		AND ct.first_category_id IS NOT NULL
		AND ct.second_category_id IS NOT NULL
		</where>
		ORDER BY c.commodity_code ASC
		<if test="limitQuantity != null">
			LIMIT #{limitQuantity}
		</if>
	</select>

	<select id="querySerialCommodityListFront" resultType="com.pinshang.qingyun.xda.product.dto.front.XdaSerialCommodityODTO">
		SELECT sc.commodity_id,sc.serial_commodity_id
		FROM t_xda_serial_commodity sc
		INNER JOIN (
			SELECT DISTINCT sc1.serial_commodity_id FROM t_xda_serial_commodity sc1
			WHERE sc1.commodity_id IN
			<foreach collection="commodityIdList" index="index" item="id" open="(" separator="," close=")">
				#{id}
			</foreach>
		)tmp ON sc.serial_commodity_id = tmp.serial_commodity_id
		INNER JOIN t_xda_commodity_text ct ON sc.commodity_id = ct.commodity_id
		AND ct.second_category_id = #{xdaSecondCategoryId}
		INNER JOIN t_xda_commodity_app_status cas ON ct.commodity_id = cas.commodity_id AND cas.app_status = 0
	</select>

</mapper>