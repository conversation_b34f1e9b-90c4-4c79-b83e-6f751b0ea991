<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaSpecialsCommoditySetLogMapper">
    <select id="findSpecialsCommoditySetLogList" resultType="com.pinshang.qingyun.xda.product.dto.specialsCommoditySet.XdaSpecialsCommoditySetLogQueryODTO">
        SELECT
        <![CDATA[
            s.id,
            s.operation_type,
            s.operation_type_name,
            s.commodity_id,
            s.commodity_specials_price,
            s.commodity_limit,
            c.commodity_code,
            c.commodity_name,
            c.commodity_spec,
            c.bar_code,
                    (
            CASE
            WHEN c.is_weight=0 THEN '否'
            WHEN c.is_weight=1 THEN '是'
            ELSE '' end
                  ) AS isWeightName,
            d.option_name AS commodityUnitName,
            xc.commodity_app_name,
            DATE_FORMAT(s.create_time,'%Y-%m-%d %H:%i:%s') AS operateTime,
            CONCAT_WS('_',e.employee_code,s.create_name,IF(e.employee_state=4,'已离职',NULL)) AS operateUser
          ]]>
        FROM
        t_xda_specials_commodity_set_log s
        LEFT JOIN t_commodity c ON c.id=s.commodity_id
        LEFT JOIN t_xda_commodity_text xc ON xc.commodity_id=c.id
        LEFT JOIN t_dictionary d ON d.id=c.commodity_unit_id
        LEFT JOIN t_employee_user e ON e.user_id=s.create_id
        <where>
            <if test="(vo.operateStartDate!=null and vo.operateStartDate!='') and (vo.operateEndDate!=null and vo.operateEndDate!='')">
                AND s.create_time between STR_TO_DATE(concat(#{vo.operateStartDate},' ','00:00:00'),'%Y-%m-%d %H:%i:%s') AND STR_TO_DATE(concat(#{vo.operateEndDate},' ','23:59:59'),'%Y-%m-%d %H:%i:%s')
            </if>
            <if test="vo.commodityId!=null">
                AND s.commodity_id=#{vo.commodityId}
            </if>

            <if test="vo.barCode!=null and vo.barCode!=''">
                AND c.bar_code=#{vo.barCode}
            </if>

            <if test="vo.operateUserId!=null">
                AND s.create_id=#{vo.operateUserId}
            </if>
        </where>
        ORDER BY s.create_time DESC
    </select>
</mapper>