<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaSpecialsCommoditySetMapper">

    <select id="findSpecialsCommoditySetList" resultType="com.pinshang.qingyun.xda.product.dto.specialsCommoditySet.XdaSpecialsCommoditySetQueryODTO">
        SELECT
          <![CDATA[
          s.id,
          s.commodity_id,
          s.commodity_specials_price,
          s.commodity_limit,
          c.commodity_code,
          c.commodity_name,
          c.commodity_spec,
          c.bar_code,
          (
           CASE
             WHEN c.is_weight=0 THEN '否'
             WHEN c.is_weight=1 THEN '是'
           ELSE '' end
          ) AS isWeightName,
          d.option_name AS commodityUnitName,
          xc.commodity_app_name
          ]]>
    FROM
        t_xda_specials_commodity_set s
      LEFT JOIN t_commodity c ON c.id=s.commodity_id
      LEFT JOIN t_xda_commodity_text xc ON xc.commodity_id=c.id
      LEFT JOIN t_dictionary d ON d.id=c.commodity_unit_id
      <where>
          <if test="vo.commodityId!=null">
             AND s.commodity_id=#{vo.commodityId}
          </if>

          <if test="vo.barCode!=null and vo.barCode!=''">
              AND c.bar_code=#{vo.barCode}
          </if>

          <if test="vo.commodityAppName!=null and vo.commodityAppName!=''">
              AND xc.commodity_app_name LIKE CONCAT('%',#{vo.commodityAppName},'%')
          </if>
      </where>
      ORDER BY s.create_time DESC
    </select>

    <select id="findSpecialsCommoditySetDetails" resultType="com.pinshang.qingyun.xda.product.dto.specialsCommoditySet.XdaSpecialsCommoditySetDetailODTO">
      SELECT
       <![CDATA[
          s.id,
          s.commodity_id,
          s.commodity_specials_price,
          s.commodity_limit,
          c.commodity_code,
          c.commodity_name,
          CONCAT_WS('_',c.commodity_code,c.commodity_name) AS commodityCodeName,
          c.commodity_spec,
          c.sales_box_capacity,
          (
           CASE
             WHEN c.is_weight=0 THEN '否'
             WHEN c.is_weight=1 THEN '是'
           ELSE '' end
          ) AS isWeightName,
          d.option_name AS commodityUnitName
          ]]>
    FROM
        t_xda_specials_commodity_set s
      LEFT JOIN t_commodity c ON c.id=s.commodity_id
      LEFT JOIN t_dictionary d ON d.id=c.commodity_unit_id
      WHERE s.id=#{id}
    </select>

    <select id="selectCommodityDropDownList" resultType="com.pinshang.qingyun.xda.product.dto.specialsCommoditySet.XdaSpecialsCommoditydropDownODTO">
         SELECT
       <![CDATA[
          c.id AS commodityId,
          CONCAT_WS('_',c.commodity_code,c.commodity_name) AS commodityCodeName,
          c.commodity_spec,
          c.sales_box_capacity,
          (
           CASE
             WHEN c.is_weight=0 THEN '否'
             WHEN c.is_weight=1 THEN '是'
           ELSE '' end
          ) AS isWeightName,
          d.option_name AS commodityUnitName
          ]]>
    FROM t_commodity c
      LEFT JOIN t_dictionary d ON d.id=c.commodity_unit_id
      <where>
          <if test="vo.status!=null">
             AND c.commodity_state=#{vo.status}
          </if>
          <if test="vo.content!=null and vo.content!=''">
              AND (c.commodity_code LIKE CONCAT('%',#{vo.content},'%')
                   OR
                   c.commodity_name LIKE CONCAT('%',#{vo.content},'%')
                   )
          </if>
      </where>
      <if test="vo.quantity!=null">
          LIMIT #{vo.quantity}
      </if>
    </select>

    <update id="batchUpdateSpecialsCommoditySet">
        <foreach collection="list" item="vo"  open="" close="" separator=";">
            UPDATE t_xda_specials_commodity_set
            SET
             commodity_specials_price = #{vo.commoditySpecialsPrice},
             commodity_limit = #{vo.commodityLimit},
             import_no = #{vo.importNo},
             update_id = #{vo.updateId},
             update_name = #{vo.updateName},
             update_time = #{vo.updateTime}
            WHERE commodity_id = #{vo.commodityId}
        </foreach>
    </update>

    <!--客户id和送货日期查询符合条件的商品集合-->
    <select id="findCommodityListByStoreAndOrderTime" resultType="com.pinshang.qingyun.xda.product.model.specialsCommoditySet.XdaSpecialsCommoditySet">
        SELECT
            scs.id,
            scs.commodity_limit,
            scs.commodity_id,
            scs.commodity_specials_price
        FROM
            t_xda_specials_commodity_set scs
            INNER JOIN t_product_price_model_list ppml on ppml.commodity_id = scs.commodity_id
            INNER JOIN t_store_settlement ss on ss.product_price_model_id = ppml.product_price_model_id
            INNER JOIN t_xda_commodity_app_status cas ON scs.commodity_id = cas.commodity_id
                AND (
                     CASE
                         WHEN #{isPfsStore} = true THEN cas.pf_app_status = 0
                         ELSE cas.app_status = 0
                     END
                    )
        where
            ss.store_id = #{storeId};
    </select>
</mapper>