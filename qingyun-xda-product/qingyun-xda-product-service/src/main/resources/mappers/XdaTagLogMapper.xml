<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaTagLogMapper">

    <select id="selectXdaCommodityTagLogList" resultType="com.pinshang.qingyun.xda.product.dto.commodityTag.XdaCommodityTagLogODTO">
        SELECT
        xtl.operate_type AS operateType,
        xt.tag_name AS commodityTagName,
        xt.tag_bg_color AS tagBgColor,
        eu.employee_name AS employeeName,
        xtl.create_time AS createTime
        FROM t_xda_tag_log xtl
        LEFT JOIN t_xda_tag xt ON xtl.tag_id = xt.id
        LEFT JOIN t_employee_user eu ON xtl.create_id = eu.user_id
        <where>
            <if test="null != commodityTagName and commodityTagName != '' ">
                AND xt.tag_name LIKE CONCAT('%', #{commodityTagName}, '%')
            </if>
        </where>
        ORDER BY xtl.create_time DESC
    </select>
</mapper>