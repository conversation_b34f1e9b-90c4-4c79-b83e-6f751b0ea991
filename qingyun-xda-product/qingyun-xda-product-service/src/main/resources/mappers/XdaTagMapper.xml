<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.product.mapper.XdaTagMapper">

    <select id="selectXdaCommodityTagList" resultType="com.pinshang.qingyun.xda.product.dto.commodityTag.XdaCommodityTagODTO">
        SELECT
        xt.id AS id,
        xt.tag_name AS commodityTagName,
        xt.tag_bg_color AS tagBgColor,
        xt.`status` AS `status`
        FROM t_xda_tag xt
        <where>
            <if test="null != commodityTagName and commodityTagName != '' ">
                AND xt.tag_name LIKE CONCAT('%', #{commodityTagName}, '%')
            </if>
            <if test=" null != status">
                AND xt.`status` = #{status}
            </if>
        </where>
    </select>
</mapper>