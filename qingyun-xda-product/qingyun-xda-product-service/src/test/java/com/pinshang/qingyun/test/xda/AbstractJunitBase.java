//package com.pinshang.qingyun.test.xda;
//
//import org.junit.BeforeClass;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.contexti.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//import org.springframework.test.context.web.WebAppConfiguration;
//import org.springframework.transaction.annotation.Transactional;
//
//import com.pinshang.qingyun.ApplicationXdaProductService;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = ApplicationXdaProductService.class,properties = {
//        "spring.profiles.active=dev",
//        "application.name.switch=chenqiang-"
//})
//@WebAppConfiguration
//@Transactional
//public  abstract class AbstractJunitBase {
//    @BeforeClass
//    public static void beforeClass(){
//        //System.setProperty("application.name.switch","weican-");
//    }
//}