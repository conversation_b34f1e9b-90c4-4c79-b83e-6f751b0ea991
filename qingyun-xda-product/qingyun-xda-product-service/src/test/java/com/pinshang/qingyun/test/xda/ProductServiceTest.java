//package com.pinshang.qingyun.test.xda;
//
//import com.github.pagehelper.PageInfo;
//import com.pinshang.qingyun.base.api.TokenInfo;
//import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
//import com.pinshang.qingyun.xda.product.dto.deliveryDate.*;
//import com.pinshang.qingyun.xda.product.mapper.deliveryDate.XdaOrderCommodityDao;
//import com.pinshang.qingyun.xda.product.mapper.deliveryDate.XdaOrderCommodityLogDao;
//import com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodity;
//import com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodityLog;
//import com.pinshang.qingyun.xda.product.service.deliveryDate.XdaOrderCommodityLogService;
//import com.pinshang.qingyun.xda.product.service.deliveryDate.XdaOrderCommodityService;
//import org.junit.Test;
//import org.springframework.test.annotation.Rollback;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//import java.util.Arrays;
//import java.util.List;
//
//public class ProductServiceTest extends AbstractJunitBase {
//
//    @Resource
//    XdaOrderCommodityLogDao xdaOrderCommodityLogDao;
//
//    @Resource
//    XdaOrderCommodityLogService xdaOrderCommodityLogService;
//
//    @Resource
//    XdaOrderCommodityService xdaOrderCommodityService;
//
//    @Resource
//    XdaOrderCommodityDao xdaOrderCommodityDao;
//
//
//
//    @Test
//    public void test(){
//        System.out.println("test_____________________________");
//    }
//
//    @Test
//    public void testList(){
//        List<XdaOrderCommodityLog> xdaOrderCommodityLogList = xdaOrderCommodityLogDao.selectXdaOrderCommodityLog();
//        System.out.println(xdaOrderCommodityLogList);
//    }
//
//    @Test
//    public void testQuery(){
//        XdaOrderCommodityLogVO xdaOrderCommodityLogVO = new XdaOrderCommodityLogVO();
//        xdaOrderCommodityLogVO.setPageNo(1);
//        xdaOrderCommodityLogVO.setPageSize(20);
//        PageInfo<XdaOrderCommodityLogDTO> xdaOrderCommodityLogPageInfo = xdaOrderCommodityLogService.getXdaOrderCommodityLog(xdaOrderCommodityLogVO);
//        System.out.println(xdaOrderCommodityLogPageInfo);
//    }
//
//    @Test
//    public void testQuery2(){
//        XdaOrderCommodityVO xdaOrderCommodityVO = new XdaOrderCommodityVO();
//        xdaOrderCommodityVO.setPageNo(1);
//        xdaOrderCommodityVO.setPageSize(10);
////        xdaOrderCommodityVO.setCategoryId("522145376802696448");
////        xdaOrderCommodityVO.setPrizeModelId("1");
//////        xdaOrderCommodityVO.setCommodityIds(Arrays.asList(1L, 2L, 3L));
////        xdaOrderCommodityVO.setSupplierId(344144L);
////        xdaOrderCommodityVO.setDeliveryDateRangeCode("1-1");
////        xdaOrderCommodityVO.setCommodityName("xxx");
//
//        PageInfo<XdaOrderCommodityDTO> xdaOrderCommodityDTOPageInfo = xdaOrderCommodityService.selectOrderCommodityList(xdaOrderCommodityVO);
//        System.out.println(xdaOrderCommodityDTOPageInfo);
//    }
//    @Test
//    @Transactional
//    @Rollback(false)
//    public void testBatchSet(){
//        TokenInfo tokenInfo = new TokenInfo();
//        tokenInfo.setRealName("xxx");
//        tokenInfo.setUserId(1l);
//        FastThreadLocalUtil.setQY(tokenInfo);
//        XdaOrderCommodityBatchAddVO xdaOrderCommodityBatchAddVO = new XdaOrderCommodityBatchAddVO();
//        xdaOrderCommodityBatchAddVO.setCommodityDateCode("code2");
//        xdaOrderCommodityBatchAddVO.setCommodityDateValue("value2");
//        xdaOrderCommodityBatchAddVO.setOrderCommodityIds(Arrays.asList(468788174651305600L));
//        xdaOrderCommodityService.batchAddOrderCommodityDate(xdaOrderCommodityBatchAddVO);
//    }
//
//    @Test
//    @Transactional
//    @Rollback(false)
//    public void testAddLog(){
//        TokenInfo tokenInfo = new TokenInfo();
//        tokenInfo.setRealName("xxx");
//        tokenInfo.setUserId(1l);
//        FastThreadLocalUtil.setQY(tokenInfo);
//        XdaOrderCommodityLogAddVO logAddVO = new XdaOrderCommodityLogAddVO();
//        logAddVO.setOpType("B");
//        logAddVO.setDeliveryOrderId(1L);
//        xdaOrderCommodityLogService.addXdaOrderCommodityLog(logAddVO);
//    }
//
//    @Test
//    public void testDistinct(){
//        List<XdaOrderCommodity> xdaOrderCommodities = xdaOrderCommodityDao.getDistinctXdaOrderCommodityList();
//        System.out.println(xdaOrderCommodities);
//    }
//
//    @Test
//    public void testDistinctService(){
//        List<Integer> xdaList = xdaOrderCommodityService.getDeliveryDateRangeCode();
//        System.out.println(xdaList);
//    }
//}
