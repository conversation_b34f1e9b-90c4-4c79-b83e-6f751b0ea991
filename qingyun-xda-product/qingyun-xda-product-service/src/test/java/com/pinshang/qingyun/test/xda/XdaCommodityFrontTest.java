//package com.pinshang.qingyun.test.xda;
//
//import com.github.pagehelper.PageInfo;
//import com.pinshang.qingyun.base.api.TokenInfo;
//import com.pinshang.qingyun.base.enums.pic.PicSizeEnums;
//import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
//import com.pinshang.qingyun.box.utils.DateUtil;
//import com.pinshang.qingyun.xda.product.dto.deliveryDate.*;
//import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppIDTO;
//import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityAppODTO;
//import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDetailAppIDTO;
//import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDetailAppODTO;
//import com.pinshang.qingyun.xda.product.mapper.deliveryDate.XdaOrderCommodityDao;
//import com.pinshang.qingyun.xda.product.mapper.deliveryDate.XdaOrderCommodityLogDao;
//import com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodity;
//import com.pinshang.qingyun.xda.product.model.deliveryDate.XdaOrderCommodityLog;
//import com.pinshang.qingyun.xda.product.service.deliveryDate.XdaOrderCommodityLogService;
//import com.pinshang.qingyun.xda.product.service.deliveryDate.XdaOrderCommodityService;
//import com.pinshang.qingyun.xda.product.service.front.XdaCommodityFrontService;
//import org.junit.Test;
//import org.springframework.test.annotation.Rollback;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//import java.util.Arrays;
//import java.util.List;
//
//public class XdaCommodityFrontTest extends AbstractJunitBase {
//
//    @Resource
//    XdaCommodityFrontService xdaCommodityFrontService;
//
//
//    @Test
//    public void queryXdaCommodityListForApp(){
//        XdaCommodityAppIDTO appIDTO = XdaCommodityAppIDTO.builder().orderTime(DateUtil.addDay(DateUtil.getNowDate(),2))
//                .storeId(752908786325622016L).defaultImageSize(PicSizeEnums.PIC_120x120)
//                .needAppDown(true)
//                .needSerial(true)
//                .needCartQuantity(true).build();
//        List<XdaCommodityAppODTO> commodityAppODTOList = xdaCommodityFrontService.queryXdaCommodityListForApp(appIDTO);
//        System.out.println("====APP商品size="+commodityAppODTOList.size());
//        commodityAppODTOList.forEach(item->{
//            System.out.println("=======APP商品："+item.toString());
//        });
//    }
//
//    @Test
//    public void testQuery(){
//        XdaCommodityDetailAppIDTO detailAppIDTO = new XdaCommodityDetailAppIDTO();
//        detailAppIDTO.setStoreId(752908786325622016L);
//        detailAppIDTO.setCommodityId(56095697583367000L);
//        detailAppIDTO.setOrderTime(DateUtil.addDay(DateUtil.getNowDate(),2));
//        XdaCommodityDetailAppODTO detailAppODTO = xdaCommodityFrontService.queryXdaCommodityDetailForApp(detailAppIDTO);
//        System.out.println("=====商品详情："+detailAppODTO);
//    }
//
//
//}
