package com.pinshang.qingyun.xda.search.hystrix;

import com.pinshang.qingyun.xda.search.service.EsXdaSearchClient;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 鲜达-搜索
 */
@Component
public class EsXdaSearchClientHystrix implements FallbackFactory<EsXdaSearchClient> {
    @Override
    public EsXdaSearchClient create(Throwable throwable) {
        return new EsXdaSearchClient() {

            @Override
            public Boolean refreshStock() {
                return null;
            }
        };
    }

}
