package com.pinshang.qingyun.xda.search.hystrix;

import com.pinshang.qingyun.xda.search.dto.EsXdaUpdateDiffRecordIDTO;
import com.pinshang.qingyun.xda.search.service.EsXdaStoreCommodityClient;
import feign.hystrix.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/04/17
 * @Version 1.0
 */
@Component
public class EsXdaStoreCommodityHystrix implements FallbackFactory<EsXdaStoreCommodityClient> {
    @Override
    public EsXdaStoreCommodityClient create(Throwable throwable) {
        return new EsXdaStoreCommodityClient() {
            @Override
            public void updateDiffRecord(EsXdaUpdateDiffRecordIDTO idto) {
                return;
            }
        };
    }
}
