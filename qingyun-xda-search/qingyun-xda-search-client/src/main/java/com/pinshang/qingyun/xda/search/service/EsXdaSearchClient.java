package com.pinshang.qingyun.xda.search.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.xda.search.hystrix.EsXdaSearchClientHystrix;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;


/**
 * es搜索
 * <AUTHOR>
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_XDA_SEARCH_SERVICE, fallbackFactory = EsXdaSearchClientHystrix.class, configuration = FeignClientConfiguration.class)
public interface EsXdaSearchClient {

    /**
     * 刷新限量商品库存
     */
    @PostMapping("/xda/refresh/stock")
    Boolean refreshStock();

}
