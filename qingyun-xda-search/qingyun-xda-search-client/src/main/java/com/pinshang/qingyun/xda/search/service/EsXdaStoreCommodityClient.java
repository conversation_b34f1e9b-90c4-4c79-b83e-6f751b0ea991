package com.pinshang.qingyun.xda.search.service;

import com.pinshang.qingyun.base.configure.FeignClientConfiguration;
import com.pinshang.qingyun.base.constant.ApplicationNameConstant;
import com.pinshang.qingyun.xda.search.dto.EsXdaUpdateDiffRecordIDTO;
import com.pinshang.qingyun.xda.search.hystrix.EsXdaStoreCommodityHystrix;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/04/17
 * @Version 1.0
 */
@FeignClient(value = ApplicationNameConstant.QINGYUN_XDA_SEARCH_SERVICE, fallbackFactory = EsXdaStoreCommodityHystrix.class, configuration = FeignClientConfiguration.class)
public interface EsXdaStoreCommodityClient {
    @RequestMapping(value = "/xda/storeCommodity/updateDiffRecord" , method = RequestMethod.POST)
    @ApiOperation(value = "更新异常数据")
    void updateDiffRecord(@RequestBody EsXdaUpdateDiffRecordIDTO idto);
}
