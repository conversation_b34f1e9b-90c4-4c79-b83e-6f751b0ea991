package com.pinshang.qingyun;

import com.ctrip.framework.apollo.spring.annotation.EnableApolloConfig;
import com.pinshang.qingyun.infrastructure.apm.cat.springboot.EnableCatMetrics;
import com.pinshang.qingyun.infrastructure.cache.starter.EnableCacheComponent;
import com.pinshang.qingyun.infrastructure.loadBalancer.starter.EnableQyLoadBalancer;
import com.pinshang.qingyun.infrastructure.mq.starter.EnableMqComponent;
import com.pinshang.qingyun.infrastructure.springcloud.common.MainArgsPreHandler;
import com.pinshang.qingyun.infrastructure.switcher.starter.EnableOnlineSwitchComponent;
import com.pinshang.qinyun.cache.service.RedisServiceDefinition;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.solr.SolrAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import tk.mybatis.spring.annotation.MapperScan;

@Controller
@SpringBootApplication(exclude = {SolrAutoConfiguration.class,DataSourceAutoConfiguration.class})
@EnableDiscoveryClient
@EnableFeignClients
@Import(value = {RedisServiceDefinition.class})
@ComponentScan(basePackages = {"com.pinshang.qingyun","com.pinshang.qingyun.xda.search"})
@MapperScan(basePackages = { "com.pinshang.qingyun.xda.*.mapper"})
@EnableAsync
@EnableApolloConfig
@EnableMqComponent
@EnableCatMetrics
@EnableQyLoadBalancer
@EnableOnlineSwitchComponent
@EnableCacheComponent
public class ApplicationXdaSearchService extends WebMvcConfigurerAdapter {
    public static void main(String[] args) {
//        System.setProperty("es.set.netty.runtime.available.processors","false");
        SpringApplication.run(ApplicationXdaSearchService.class, MainArgsPreHandler.argsHandle(args));
    }

    @GetMapping(value={"","/"})
    public String index(){
        System.out.println("hello world");
        return "redirect:/chk.html";
    }
}
