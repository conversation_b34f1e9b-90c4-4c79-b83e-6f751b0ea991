package com.pinshang.qingyun.xda.search.conf;

import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.search.enums.XdaRedisEnums;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class RedisDB {

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 鲜达基本信息缓存  --获取
     * @param xdaRedisEnums
     * @param orderTime
     * @param storeId
     * @param commodityIdList
     * @return
     */
    public Map<Long, Object> getRedisXdaSpecialPrice(XdaRedisEnums xdaRedisEnums, Date orderTime, Long storeId, List<Long> commodityIdList){
        if(SpringUtil.isEmpty(commodityIdList)|| null == xdaRedisEnums){
            return null;
        }
        Map<Long,String> keys = new HashMap<>();
        String key = xdaRedisEnums.getName();

        if(null != orderTime ){
            key = key + DateUtil.get4yMdNoDash(orderTime) +":";
        }

        if(null != storeId){
            key = key + storeId +":";
        }

        String finalKey = key;

        commodityIdList.forEach(item-> keys.put(item, finalKey + item));

        return getRedisData(keys);
    }

    /**
     * 鲜达基本信息缓存  --保存
     * 这里不考虑安全问题，在获取的时候相同的key，后面的数据覆盖前面的数据
     * @param xdaRedisEnums
     * @param orderTime
     * @param storeId
     * @param map
     */
    public void setRedisXdaSpecialPrice(XdaRedisEnums xdaRedisEnums,Date orderTime, Long storeId, Map<Long, Object> map){
        String key = "";
        if(null != orderTime ){
            key = key + DateUtil.get4yMdNoDash(orderTime) +":";
        }

        if(null != storeId){
            key = key + storeId +":";
        }

        String finalKey = key;
        map.forEach((item, value)->{
            RBucket<Object> bucket = redissonClient.getBucket(xdaRedisEnums.getName() + finalKey + item);
            bucket.set(value);
            bucket.expire(5L, TimeUnit.MINUTES);
        });

    }

    /**
     *
     * @param keyMap Map<商品ID,对应key></>
     * @return
     */
    public Map<Long,Object> getRedisData(Map<Long,String> keyMap){
        //future列表
        LinkedList<Map<Long,RFuture<Object>>> futures = new LinkedList<>();
        //结果集
        Map<Long,Object> result = new HashMap<>();
        RBatch batch = redissonClient.createBatch();
        keyMap.forEach((key,value)->{
            RBucketAsync<Object> bucket = batch.getBucket(value);
            RFuture<Object> async = bucket.getAsync();
            Map<Long,RFuture<Object>> map = new HashMap<>();
            map.put(key,async);
            futures.add(map);
        });
        //批量执行
        batch.execute();
        while (!futures.isEmpty()){
            Map<Long,RFuture<Object>> first = futures.removeFirst();
            first.forEach((key,value)->{
                Object o = value.getNow();
                if (o!=null){
                    result.put(key,value);
                }else {
                    if (!value.isDone()){
                        futures.addLast(first);
                    }
                }
            });
        }
        return result;
    }
}
