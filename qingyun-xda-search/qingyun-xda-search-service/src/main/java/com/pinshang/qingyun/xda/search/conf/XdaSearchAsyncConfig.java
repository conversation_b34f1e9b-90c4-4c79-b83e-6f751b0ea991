package com.pinshang.qingyun.xda.search.conf;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 类描述：异步执行的线程池配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/20 下午4:01
 */
@Configuration
public class XdaSearchAsyncConfig implements AsyncConfigurer {

    @Value("${threadPool.coreSize:50}")
    private Integer coreSize;
    @Value("${threadPool.maxSize:100}")
    private Integer maxSize;

    @Bean(name = "dynamicTaskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreSize);
        executor.setMaxPoolSize(maxSize);
        executor.setQueueCapacity(1024);
        executor.setThreadNamePrefix("AsyncExecutorThread-");
        executor.setKeepAliveSeconds(5);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.initialize();
        return executor;
    }
}
