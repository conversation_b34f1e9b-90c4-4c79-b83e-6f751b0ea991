package com.pinshang.qingyun.xda.search.conf;

import com.pinshang.qingyun.base.interceptor.QYServiceInterceptor;
import com.pinshang.qingyun.base.interceptor.XdaServiceInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import java.util.concurrent.ScheduledThreadPoolExecutor;


@Configuration
public class XdaSearchConfiguration {
    @Bean("xdaServiceInterceptor")
    public HandlerInterceptorAdapter xdaServiceInterceptor(){
        return new XdaServiceInterceptor();
    }

    @Bean("qyServiceInterceptor")
    public HandlerInterceptorAdapter qyServiceInterceptor(){
        return new QYServiceInterceptor();
    }


    public static final ScheduledThreadPoolExecutor POOL_EXECUTOR_DELAY =
            new ScheduledThreadPoolExecutor(
                    20,
                    (r) -> new Thread(r, "XDA-Delay-Pool-" + r.hashCode())
            );

}
