/*
package com.pinshang.qingyun.xd.search.controller;

import com.pinshang.qingyun.base.util.IdWorker;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.xd.search.document.EsDemo;
import com.pinshang.qingyun.xd.search.repository.EsDemoRepository;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.range.InternalRange;
import org.elasticsearch.search.aggregations.bucket.terms.DoubleTerms;
import org.elasticsearch.search.aggregations.bucket.terms.LongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.StringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.web.bind.annotation.*;

import java.util.*;

*/
/**
 * https://www.elastic.co/guide/en/elasticsearch/reference/0.90/query-dsl-span-term-query.html
 * https://blog.csdn.net/wenwen513/article/details/85163168
 * http://localhost:9061/search/es/demo/insertInit
 * http://localhost:9061/search/es/demo/deleteAll
 * Created by weican on 2017-08-04.
 *//*

@RequestMapping("/search/es/demo")
@RestController
@Slf4j
public class ESDemoController {
    @Autowired
    private EsDemoRepository esDemoRepository;
    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;

    */
/** ----------------------------------------------------------------------------------------- 插入代码 ----------------------------------------------------------------------------------------- **//*


    @GetMapping("/insert")
    public Boolean insert(){
        EsDemo esDemo = getEsDemo() ;
        esDemoRepository.save( esDemo );
        return true;
    }
    @GetMapping("/insertAll")
    public Boolean insertAll(){
        List<EsDemo> list = Arrays.asList(getEsDemo(),getEsDemo(),getEsDemo()) ;
        esDemoRepository.saveAll(list);
        return true;
    }
    */
/** ----------------------------------------------------------------------------------------- 基本查询代码 ----------------------------------------------------------------------------------------- **//*

    @GetMapping("/findById/{id}")
    public EsDemo findById(@PathVariable("id") String id){
        Optional<EsDemo> optionalEsDemo =  esDemoRepository.findById(id);

        return  optionalEsDemo.isPresent()?optionalEsDemo.get():null;
    }
    @GetMapping("/findAll")
    public List<EsDemo> findAll(){
        List<EsDemo> list = new ArrayList<>();
        Iterable<EsDemo> iterable =  esDemoRepository.findAll();
        iterable.forEach(iter->list.add(iter));
        return list;
    }
    @GetMapping("/findAndSort")
    public List<EsDemo> findAndSort(){
        List<EsDemo> list = new ArrayList<>();
       */
/* // 1
        Sort sort1 = new Sort(Sort.Direction.ASC,"age","birthday");
        //2
        List<String> sortFieldList = new ArrayList<>(2) ;
        sortFieldList.add("age");   sortFieldList.add("birthday");
        Sort sort2 = new Sort(Sort.Direction.ASC,sortFieldList);*//*

        // 3
        List<Sort.Order> orderList = new ArrayList<>(2);
        orderList.add(new Sort.Order(Sort.Direction.ASC,"age"));
        orderList.add(new Sort.Order(Sort.Direction.DESC,"birthday"));
        Sort sort3 = Sort.by(orderList);

        // 4
        List<Sort.Order> orderList2 = new ArrayList<>(2);
        orderList2.add(new Sort.Order(Sort.Direction.ASC,"age"));
        // 字段NULL的设置不启作用
        orderList2.add(new Sort.Order(Sort.Direction.DESC,"birthday", Sort.NullHandling.NULLS_FIRST));
        Sort sort4 = Sort.by(orderList2);

        Iterable<EsDemo> iterable = esDemoRepository.findAll(sort4);
        iterable.forEach(iter->list.add(iter));
        return  list ;
    }
    @PostMapping("/findAllById")
    public List<EsDemo> findAllById(@RequestBody List<String> ids){
        Iterable<EsDemo> iterable = esDemoRepository.findAllById(ids);
        List<EsDemo> list = new ArrayList<>();
        iterable.forEach(iter->list.add(iter));
        return list;
    }

    */
/**
     * 这个方法不可使用，底层有问题，实际上他也是根据ID查询一下，判断有没有找到，但代码实现有问题
     * findById(id) != null    findById(id)返回的是一个 Optional 所以不可能为null,
     * @param id
     * @return
     *//*

    @GetMapping("/existsById/{id}")
    public Boolean existsById(@PathVariable("id") String id){
        return esDemoRepository.existsById(id) ;
    }
    @GetMapping("/findByPage")
    public Page<EsDemo> findByPage(){
        Sort sort = Sort.by(new Sort.Order(Sort.Direction.DESC,"age"));
        //Sort sort = new Sort(Sort.Direction.DESC, "age");
        Pageable pageable = PageRequest.of(0,12,sort);
        Page<EsDemo> page =  esDemoRepository.findAll(pageable) ;
        return page;
    }

    @GetMapping("/count")
    public Long count(){
        return esDemoRepository.count() ;
    }
    */
/** ----------------------------------------------------------------------------------------- 高级查询代码 ----------------------------------------------------------------------------------------- **//*

    */
/**
     * 模糊查询
     * @return
     *//*

    @GetMapping("/search/fuzzy")
    public  List<EsDemo>  search(){
        //1 模糊查询  QueryBuilder
        QueryBuilder query1 = QueryBuilders.fuzzyQuery("name","weican20") ;
        WildcardQueryBuilder query2 =  QueryBuilders.wildcardQuery("name","刘阿姨");
        WildcardQueryBuilder query3 =  QueryBuilders.wildcardQuery("aliasName","can");
        Iterable<EsDemo> iterable =esDemoRepository.search(query3) ;
        List<EsDemo> list = print(iterable,"模糊查询");
        return list;
    }

    */
/**
     * 主键查询
     * @return
     *//*

    @GetMapping("/search/ids")
    public List<EsDemo>  searchIds(){
        IdsQueryBuilder query1 = QueryBuilders.idsQuery() ;
        query1.addIds("1176765097524838401","1176765102595751937") ;

        Iterable<EsDemo> iterable =esDemoRepository.search(query1) ;
        List<EsDemo> list = print(iterable,"主键查询");
        return list;
    }

    */
/**
     * 精确查询
     * @return
     *//*

    @GetMapping("/search/term")
    public List<EsDemo> searchTerm(){
        TermQueryBuilder query1 =  QueryBuilders.termQuery("sex",true);
        TermQueryBuilder query2 =  QueryBuilders.termQuery("name","weican94");
        TermQueryBuilder query3 =  QueryBuilders.termQuery("name","weican");
        TermsQueryBuilder query4 =  QueryBuilders.termsQuery("name","weican1","weican2") ;

        Iterable<EsDemo> iterable =esDemoRepository.search(query4) ;
        List<EsDemo> list =  print(iterable,"精确查询");
        return list;
    }
    @GetMapping("/search/range")
    public List<EsDemo> searchRange(){
        QueryBuilder query =  QueryBuilders.rangeQuery("age").from(10).to(20);
        Iterable<EsDemo> iterable =esDemoRepository.search(query) ;
        print(iterable,"闭区间查询");

        query =  QueryBuilders.rangeQuery("age").from(10).to(20,false);
        iterable =esDemoRepository.search(query) ;
        print(iterable,"开区间查询");

        query =  QueryBuilders.rangeQuery("age").gt(20);
        iterable =esDemoRepository.search(query) ;
        print(iterable,"大于查询");

        query =  QueryBuilders.rangeQuery("age").gte(20);
        iterable =esDemoRepository.search(query) ;
        print(iterable,"大于查询");

        query =  QueryBuilders.rangeQuery("age").lt(20);
        iterable =esDemoRepository.search(query) ;
        print(iterable,"小于查询");

        query =  QueryBuilders.rangeQuery("age").lte(20);
        iterable =esDemoRepository.search(query) ;
        print(iterable,"小于等于查询");

        return Collections.emptyList();
    }
    @GetMapping("/search/phrase")
    public List<EsDemo> searchPrase(){
        QueryBuilder query =  QueryBuilders.matchPhraseQuery("name","张四");
        Iterable<EsDemo> iterable =esDemoRepository.search(query) ;
        print(iterable,"析语查询");

        query = QueryBuilders.matchPhrasePrefixQuery("name","张");
        iterable =esDemoRepository.search(query) ;
        print(iterable,"析语前缀查询");

        return Collections.emptyList();
    }
    @GetMapping("/search/multi")
    public List<EsDemo> searchMulti(){
//        QueryBuilder query1 = QueryBuilders.moreLikeThisQuery(new String[]{"aliasName"}, new String[]{"O3gyGfrMVO"},null);
        TermQueryBuilder query1 =  QueryBuilders.termQuery("sex",true);
        QueryBuilder query2 = QueryBuilders.rangeQuery("age").gt("50");
        QueryBuilder query3 = QueryBuilders.termQuery("name","姚明");

        QueryBuilder query4 = QueryBuilders.boolQuery().must(query1).must(query2) ;

        QueryBuilder query = QueryBuilders.boolQuery()
                // 1. 查询 sex=true and age>50
//                .must(query1)
//                .must(query2)

                // 2. 查询 age>50 or name="姚明"
//                .should(query2)
//                .should(query3)

                // 3. 查询  ( name="姚明" ) or (  sex=true and age>50   )
//                .should(query3)
//                .should(query4)

                // 4.查询  name != "姚明"
                .mustNot(query3)
                ;
        Iterable<EsDemo> iterable =esDemoRepository.search(query) ;
        print(iterable,"多条件查询－－QueryBuilder");
        return Collections.emptyList();
    }
    @GetMapping("/search/query")
    public void searchQuery(){

        SearchQuery searchQuery = new NativeSearchQuery(QueryBuilders.termQuery("sex",true));
        Iterable<EsDemo> iterable = esDemoRepository.search(searchQuery) ;
        print(iterable,"多条件查询－－SearchQuery");
    }
    @GetMapping("/search/query/standard")
    public String searchQueryStandard(@RequestParam("shopIds") String shopIds){

        SearchQuery searchQuery = new NativeSearchQuery(QueryBuilders.termQuery("shopIds",shopIds));
        Iterable<EsDemo> iterable = esDemoRepository.search(searchQuery) ;
        print(iterable,"多条件查询－－SearchQuery");
        return JsonUtil.java2json(iterable);
    }
    @GetMapping("/search/page")
    public void searchPage(){
        TermQueryBuilder query1 =  QueryBuilders.termQuery("sex",true);
        Sort sort = new Sort(Sort.Direction.DESC, "age");
        Pageable pageable = PageRequest.of(0,12,sort);
        Iterable<EsDemo> iterable = esDemoRepository.search(query1,pageable);
        print(iterable,"高级分页查询－－Pageable");
    }
    @GetMapping("/search/native")
    public void searchNative(){
        Sort sort = new Sort(Sort.Direction.DESC, "age");
        Pageable pageable =
//                new PageRequest(0, 3, sort);
        PageRequest.of(0,3,sort);
        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                */
/** 自定义查询（这是不同的地方） **//*

                .withQuery(QueryBuilders.matchAllQuery())
                */
/** 自定义分页 *//*

                .withPageable(pageable)
                .build();

        AggregatedPage<EsDemo> page = elasticsearchTemplate.queryForPage(searchQuery,EsDemo.class);

        print(page,"高级查询");
    }

    */
/**
     * https://blog.csdn.net/donghaixiaolongwang/article/details/58597058
     * https://blog.csdn.net/wenwen513/article/details/85163168
     * https://www.cnblogs.com/chenyuanbo/p/9973311.html
     *//*

    @GetMapping("/search/aggregation/metrics")
    public void aggregationMetrics(){
        Sort sort = new Sort(Sort.Direction.DESC, "age");
        Pageable pageable =
//                new PageRequest(0, 3, sort);
                PageRequest.of(0,3,sort);
        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                */
/** 自定义查询（这是不同的地方） **//*

                .withQuery(QueryBuilders.matchAllQuery())
                */
/** 自定义分页 *//*

                .withPageable(pageable)
                .addAggregation(AggregationBuilders.sum("sum_money").field("money"))
                .addAggregation(AggregationBuilders.max("max_money").field("money"))
                .addAggregation(AggregationBuilders.min("min_money").field("money"))
                .addAggregation(AggregationBuilders.count("count_money").field("money"))
                .addAggregation(AggregationBuilders.count("count_sex").field("sex"))
                .addAggregation(AggregationBuilders.cardinality("distinct_count_sex").field("sex"))
                .addAggregation(AggregationBuilders.avg("avg_money").field("money"))
                //TODO  range
                .addAggregation(AggregationBuilders.range("range_money").field("money").addRange(6000.0,80000.0))
//                .withHighlightFields()
                .build();
        AggregatedPage<EsDemo> page = elasticsearchTemplate.queryForPage(searchQuery,EsDemo.class);
        print(page,"Aggregation 类型-- Metrics");
    }
    @GetMapping("/search/aggregation/bucket")
    public void aggregationBucket(){
        Sort sort = new Sort(Sort.Direction.DESC, "age");
        // 如果不需要数据，可以把分页设置为0 ,这样可以提高性能
        Pageable pageable =  PageRequest.of(0,0,sort);
        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                */
/** 自定义查询（这是不同的地方） **//*
           */
/** 自定义分页 *//*

                .withQuery(QueryBuilders.matchAllQuery()).withPageable(pageable)
                // 1. 根据班级分组统计 KEY=TERM_GRADE key=0班 docCount=6 || KEY=TERM_GRADE key=1班 docCount=5 || KEY=TERM_GRADE key=2班 docCount=1
//                .addAggregation(AggregationBuilders.terms("term_grade").field("grade"))
                // 2. 先根据班级分组再根据性别分组 group by grade,sex
//                .addAggregation(AggregationBuilders.terms("term_grade").field("grade").subAggregation(AggregationBuilders.terms("term_sub_sex").field("sex")))
                // 3. 按班级分组，求出每个班的平均年龄，看平均年龄升序
                .addAggregation( AggregationBuilders.terms("term_grade").field("grade")
                                    .size(20)  // 分组显示条数，默认10条
                                    .order(Terms.Order.aggregation("grade_age_avg",true))   //true 是升级，false 倒序
                                    .subAggregation(  AggregationBuilders.avg("grade_age_avg").field("age")  )   )

//        TermsBuilder teamAgg= AggregationBuilders.terms("team").order(Order.aggregation("total_salary ", false);
//        SumBuilder salaryAgg= AggregationBuilders.avg("total_salary ").field("salary");
//        sbuilder.addAggregation(teamAgg.subAggregation(salaryAgg));
                .build();
        AggregatedPage<EsDemo> page = elasticsearchTemplate.queryForPage(searchQuery,EsDemo.class);
        print(page,"Aggregation 类型-- Bucket");
    }
    */
/** ----------------------------------------------------------------------------------------- 删除代码 ----------------------------------------------------------------------------------------- **//*

    @GetMapping("/deleteById/{id}")
    public Boolean deleteById(@PathVariable("id") String id){
        esDemoRepository.deleteById(id);
        return true;
    }
    */
/**
     * 这个实际上还是以ID删除，如果EsDemo对象的ID如果没有值则会报错
     * @return
     *//*

    @PostMapping("/deleteByEntity")
    public Boolean deleteByEntity( @RequestBody EsDemo esDemo){
        esDemoRepository.delete(esDemo);
        return true;
    }
    @GetMapping("/deleteAll")
    public Boolean deleteAll(){
        esDemoRepository.deleteAll();
        return true;
    }
    */
/**
     * 这个实际上还是以ID删除，如果EsDemo对象的ID如果没有值则会报错
     * @return
     *//*

    @PostMapping("/deleteByEntityList")
    public Boolean deleteByEntityList(@RequestBody List<EsDemo> esDemoList){
        esDemoRepository.deleteAll(esDemoList);
        return true;
    }

    */
/** ----------------------------------------------------------------------------------------- 更新代码 ----------------------------------------------------------------------------------------- **//*

    @PostMapping("/update")
    public EsDemo update(@RequestBody EsDemo esDemo){
        esDemo = esDemoRepository.save(esDemo);
        return esDemo;
    }
    @PostMapping("/updateAll")
    public void updateAll(){
        List<EsDemo> list =  findAll();

        StringBuilder bean = new StringBuilder(" ArrayList<EsDemo> list = new ArrayList<>();\n") ;
        for (int i = 0; i < list.size(); i++) {
            EsDemo esDemo = list.get(i);
            if(esDemo.getGrade()==null){
                esDemo.setGrade(  Math.abs( new Random().nextInt(3) ) +"班");
            }

            bean.append(" EsDemo bean"+(i+1))
                    .append(" = new EsDemo( ")
                    .append("\"").append(esDemo.getId()).append("\"")
                    .append(",\"").append(esDemo.getName()).append("\"")
                    .append(",\"").append(esDemo.getAliasName()).append("\"")
                    .append(",").append(esDemo.getAge())
                    .append(",").append(esDemo.getSex())
                    .append(",").append(" new Random().nextInt(8)%4==0?null: DateUtil.addDay(new Date(),new Random().nextInt(10))")
                    .append(",new BigDecimal(").append(esDemo.getMoney())
                    .append("),\"").append(esDemo.getGrade()).append("\"")
                    .append(");\n" )
                    .append("list.add(bean"+(i+1)+");\n");

        }
        System.out.println(bean);
        esDemoRepository.saveAll(list);
    }
    private EsDemo getEsDemo(){
        EsDemo esDemo = new EsDemo();
        esDemo.setId(IdWorker.getId()+"");
        esDemo.setSex(new Random().nextInt()%2==0?true:false);
        esDemo.setAge(new Random().nextInt(100));
        esDemo.setName(getRandomString(6));
        esDemo.setAliasName(new Random().nextInt(8)%5==0?null: getRandomString(10));
        esDemo.setBirthday(  new Random().nextInt(8)%4==0?null: DateUtil.addDay(new Date(),new Random().nextInt(10)));
        esDemo.setMoney( Math.abs( new Random().nextDouble() ));
        esDemo.setGrade(  Math.abs( new Random().nextInt(3) ) +"班");
        return esDemo;
    }
    private List<EsDemo>  print(Iterable<EsDemo> iterable,String title){
        List<EsDemo> list = new ArrayList<>();
        iterable.forEach(iter->list.add(iter));
        print(list,title);
        return list;
    }
    private void print(AggregatedPage<EsDemo> page,String title){
        System.out.println("#################################### "+title+" ####################################");
        System.out.println("页数--此值不对 " + page.getTotalPages()+"行数－总记录数 " + page.getTotalElements()+" 大小－此值不对 " +
                page.getSize()+" 当前第几页－此值不对 " + page.getNumber()+" 当前页的数量 "+page.getNumberOfElements());
       print(page.getContent(),"分页打印");
        if(page.hasAggregations()){
            System.out.println("#################################### "+ "分组信息" +" ####################################");
            page.getAggregations().asMap().forEach((key,value)->{
                parseAggreation(key,value);
            });
        }
    }
    private void parseAggreation(String key, Aggregation value){
        // Metrics
        if(value instanceof InternalSum){
            System.out.println("分组 key="+key+" value="+ ((InternalSum) value).value());
        }else if(value instanceof InternalValueCount){
            System.out.println("分组 key="+key+" value="+ ((InternalValueCount) value).value());
        }else if( value instanceof InternalMax){
            System.out.println("分组 key="+key+" value="+ ((InternalMax) value).value());
        }else if( value instanceof InternalMin){
            System.out.println("分组 key="+key+" value="+ ((InternalMin) value).value());
        }else if( value instanceof InternalAvg){
            System.out.println("分组 key="+key+" value="+ ((InternalAvg) value).value());
        }else if( value instanceof InternalCardinality){
            System.out.println("分组 key="+key+" value="+ ((InternalCardinality) value).value());
        }
        // Bucket
        else if( value instanceof LongTerms){
            LongTerms  longTerms =  (LongTerms) value ;
            List<LongTerms.Bucket> list  = longTerms.getBuckets() ;
            list.forEach(bucket -> {
                System.out.printf("%s%S%s%s%s%s%s","KEY=",key, " key=",bucket.getKey()+""," docCount=",""+bucket.getDocCount(),"\n");
            });
        }else if( value instanceof StringTerms){
            StringTerms  stringTerms =  (StringTerms) value ;
            List<StringTerms.Bucket> list  = stringTerms.getBuckets() ;
            list.forEach(bucket -> {
                System.out.printf("%s%S%s%s%s%s%s","KEY=",key, " key=",bucket.getKey()+""," docCount=",""+bucket.getDocCount(),"\n");
                bucket.getAggregations().asMap().forEach((subKey,subValue)->{
                    parseAggreation(subKey,subValue);
                });
            });
        }else if( value instanceof DoubleTerms) {
            DoubleTerms doubleTerms = (DoubleTerms) value;
            List list = doubleTerms.getBuckets();
//                    list.forEach(bucket -> {
//                        System.out.printf("%s%S%s%s%s%s%s","KEY=",key, " key=",bucket.getKey()+""," docCount=",""+bucket.getDocCount(),"\n");
//                    });
        }
        else if( value instanceof InternalRange){
//                    System.out.println("分组 key="+key+" value="+ ((InternalRange) value).getBuckets());
            ((InternalRange) value).getBuckets().forEach(b->{
                        InternalRange.Bucket  bucket =  (InternalRange.Bucket) b ;
                        System.out.println("分组 key="+key+" docCount=" +bucket.getDocCount());
                        ((InternalRange.Bucket) b).getAggregations().forEach(sub->{
                            sub.getMetaData().forEach((key1,value1)->{
                                System.out.println("key1 "+ key1 +" value1 = " +value1);
                            });
                        });
                    }
            ) ;
        }
    }
    private void print(List<EsDemo> list,String title){
        System.out.println("#################################### "+title+" ####################################");
        if (!list.isEmpty()) {
            for (int i = 0; i < list.size(); i++) {
                System.out.printf("%s%s\n",i,list.get(i).toString());
            }
        }
    }
    //length用户要求产生字符串的长度
    public static String getRandomString(int length){
        String str="abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random=new Random();
        StringBuffer sb=new StringBuffer();
        for(int i=0;i<length;i++){
            int number=random.nextInt(62);
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }
    @GetMapping("/insertInit")
    public void insertInit(){

        ArrayList<EsDemo> list = new ArrayList<>();
        EsDemo bean1 = new EsDemo( "1177130625766412289","彭麻麻","peng ma ma",65,false, new Random().nextInt(8)%4==0?null: DateUtil.addDay(new Date(),new Random().nextInt(10)),Math.abs( new Random().nextDouble() ),"0班"
                ,"中华人民共和国（People's Republic of China），简称“中国”，成立于1949年10月1日，位于亚洲东部，太平洋西岸"
            ,new String[]{"11","22"},"11 112 12 20 2");
        list.add(bean1);
        EsDemo bean2 = new EsDemo( "1177130625762217986","张四","weican111",26,true, new Random().nextInt(8)%4==0?null: DateUtil.addDay(new Date(),new Random().nextInt(10)),Math.abs( new Random().nextDouble() ),"0班"
        ,"中国政府网_中央人民政府门户网站官网",new String[]{"11","22"},"11 12 33 45");
        list.add(bean2);
        EsDemo bean3 = new EsDemo( "1177130629776166914","习大大","xi da da",66,true, new Random().nextInt(8)%4==0?null: DateUtil.addDay(new Date(),new Random().nextInt(10)),Math.abs( new Random().nextDouble() ),"1班"
        ,"中国(世界四大文明古国之一)_百度百科",new String[]{"44","88"},"112 20 2 8");
        list.add(bean3);
        EsDemo bean4 = new EsDemo( "1177130618636095491","王五","wang wu ",26,false, new Random().nextInt(8)%4==0?null: DateUtil.addDay(new Date(),new Random().nextInt(10)),Math.abs( new Random().nextDouble() ),"1班"
        ,"“金蓝领”为“中国创造”添彩--社会--人民网",new String[]{"11","22"},"6 60 62 12");
        list.add(bean4);
        EsDemo bean5 = new EsDemo( "1177130625766412290","张三","张一山",26,true, new Random().nextInt(8)%4==0?null: DateUtil.addDay(new Date(),new Random().nextInt(10)),Math.abs( new Random().nextDouble() ),"1班"
        ,"在4月29日播出的《这就是中国》节目中，复旦大学中国研究院院长张维为教授就“西方中心论”进行了解构。"
                ,new String[]{"77","88"},"7 9 13");
        list.add(bean5);
//        EsDemo bean6 = new EsDemo( "1177130618631901186","刘阿姨","liu a yi",61,true, new Random().nextInt(8)%4==0?null: DateUtil.addDay(new Date(),new Random().nextInt(10)),new BigDecimal(7000),"1班"
//        ,"周星驰最新电影");
//        list.add(bean6);
//        EsDemo bean7 = new EsDemo( "1177130618636095490","李好","li hao",21,true, new Random().nextInt(8)%4==0?null: DateUtil.addDay(new Date(),new Random().nextInt(10)),new BigDecimal(800),"0班"
//        ,"周星驰最好看的新电影");
//        list.add(bean7);
//        EsDemo bean8 = new EsDemo( "1177130629776166913","姚明","yao ming",44,true, new Random().nextInt(8)%4==0?null: DateUtil.addDay(new Date(),new Random().nextInt(10)),new BigDecimal(6000),"0班"
//        ,"周星驰最新电影，最好，新电影");
//        list.add(bean8);
//        EsDemo bean9 = new EsDemo( "1177130629776166915","张五","zhang wu ",26,false, new Random().nextInt(8)%4==0?null: DateUtil.addDay(new Date(),new Random().nextInt(10)),new BigDecimal(2000),"0班"
//        ,"最最最最好的新新新新电影");
//        list.add(bean9);
//        EsDemo bean10 = new EsDemo( "1178252924850679810","ADrXK4","Us4abUtxeS",39,false, new Random().nextInt(8)%4==0?null: DateUtil.addDay(new Date(),new Random().nextInt(10)),new BigDecimal(1785580597),"1班"
//        ,"最最最最好的新新新新电影,需求为喹");
//        list.add(bean10);
//        EsDemo bean11 = new EsDemo( "1178252924859068418","79MZWy","BwDRHDiSTx",4,false, new Random().nextInt(8)%4==0?null: DateUtil.addDay(new Date(),new Random().nextInt(10)),new BigDecimal(422074804),"2班"
//        ,"最最最最好的新新新新电影");
//        list.add(bean11);
//        EsDemo bean12 = new EsDemo( "1178252924859068419","y7CqzO","xKnoZ2wDpR",25,false, new Random().nextInt(8)%4==0?null: DateUtil.addDay(new Date(),new Random().nextInt(10)),new BigDecimal(53656238),"0班"
//        ,"最最最最好的新新新新电影");
//        list.add(bean12);

        esDemoRepository.saveAll(list);

    }


    public static void main(String[] args) {
        System.out.println(1176695456404226050L);
    }
//    @InitBinder
//    public void initData(WebDataBinder wdb){
//        wdb.registerCustomEditor(Date.class, new CustomDateEditor(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss"), true));
//    }
}
*/
