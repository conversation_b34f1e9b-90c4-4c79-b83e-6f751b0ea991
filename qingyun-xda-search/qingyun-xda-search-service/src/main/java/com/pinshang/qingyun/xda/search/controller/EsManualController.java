package com.pinshang.qingyun.xda.search.controller;

import com.pinshang.qingyun.base.api.ApiResult;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDeliveryTimeIDTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDeliveryTimeODTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityFrontClient;
import com.pinshang.qingyun.xda.search.dto.*;
import com.pinshang.qingyun.xda.search.enums.XdaSearchEsIndexEnums;
import com.pinshang.qingyun.xda.search.service.EsCategoryService;
import com.pinshang.qingyun.xda.search.service.EsCommodityService;
import com.pinshang.qingyun.xda.search.service.es.CategoryElasticSearchService;
import com.pinshang.qingyun.xda.search.service.es.CommodityElasticSearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/4/7
 */
@RestController
@RequestMapping("/manual")
@Slf4j
@Api(value = "手动构建索引", tags = "manualIndex", description = "手动构建索引")
public class EsManualController {

    @Autowired
    private EsCommodityService esCommodityService;

    @Autowired
    private EsCategoryService esCategoryService;

    @Autowired
    private CategoryElasticSearchService categoryEsService;

    @Autowired
    private CommodityElasticSearchService commodityElasticSearchService;
    /**
     * 查询手动刷新服务列表
     * @return
     */

    @GetMapping("/getRefreshIndexList")
    @ApiOperation(value = "查询手动刷新服务列表", notes = "查询手动刷新服务列表")
    public List<XdSearchEsIndexDTO> getRefreshIndexList() {
        EnumSet<XdaSearchEsIndexEnums> esIndexEnums = XdaSearchEsIndexEnums.allList();
        List<XdSearchEsIndexDTO> result = new ArrayList<>();
        if (null != esIndexEnums) {
            esIndexEnums.forEach(item -> {
                result.add(new XdSearchEsIndexDTO(item.getName(),item.getUrl(),item.getShopFlag(),item.getCommodityFlag(),item.getUpdateTimeFlag()));
            });
        }
        return result;
    }


    /**
     * 重建全部客户商品索引
     * @return
     */
//    @GetMapping("/createAllStoreCommodityIndex")
//    @ApiOperation(value = "重建全部客户商品索引", notes = "重建全部客户商品索引")
//    public ApiResult createAllStoreCommodityIndex() {
//        try {
//           return xdaStoreCommodityService.createAllStoreCommodityIndex();
//        } catch (Exception e) {
//            log.error("重建全部客户商品索引-- 出错",e);
//            return new ApiResult().initFailure(e.toString());
//        }
//    }


    /**
     * 更新指定客户的指定商品
     * @param vo
     * @return
     */
//    @PostMapping("/updateStoreCommodityIndex")
//    @ApiOperation(value = "更新指定客户的指定商品", notes = "更新指定客户的指定商品")
//    public ApiResult updateStoreCommodityIndex(@RequestBody EsManualIDTO vo) {
//        try {
//            List<Long> storeIdList = null;
//            if (SpringUtil.hasText(vo.getStoreIds())) {
//                List<String> storeIdStrList = Arrays.asList(vo.getStoreIds().split((",")));
//                storeIdList = storeIdStrList.stream().map(Long::valueOf).distinct().collect(Collectors.toList());
//            }
//            List<Long> commodityIdList = null;
//            if(SpringUtil.hasText(vo.getCommodityIds())){
//                List<String> commodityIdStrList = Arrays.asList(vo.getCommodityIds().split((",")));
//                commodityIdList = commodityIdStrList.stream().map(Long::valueOf).distinct().collect(Collectors.toList());
//            }
//            return xdaStoreCommodityService.rebuildXdaStoreCommodity(storeIdList, commodityIdList);
//        } catch (Exception e) {
//            log.error("更新指定客户的指定商品-- 出错",e);
//            return new ApiResult().initFailure(e.toString());
//        }
//    }

    @PostMapping("/allCommoditySyncEs")
    @ApiOperation(value = "商品同步ES", notes = "商品同步ES")
    public ApiResult allCommoditySyncEs(@RequestBody EsManualIDTO req) {
        try {
            return commodityElasticSearchService.allCommoditySyncEs(req);
        } catch (Exception e) {
            log.error("重建商品索引-- 出错",e);
            return new ApiResult().initFailure(e.toString());
        }
    }

    @PostMapping("/allCategorySyncEs")
    @ApiOperation(value = "品类同步ES", notes = "品类同步ES")
    public ApiResult allCategorySyncEs(@RequestBody EsManualIDTO req) {
        try {
            return categoryEsService.allCategorySyncEs(req);
        } catch (Exception e) {
            log.error("重建品类索引-- 出错",e);
            return new ApiResult().initFailure(e.toString());
        }
    }

//    @PostMapping("/allStoreCommoditySyncEs")
//    @ApiOperation(value = "全量客户商品同步ES", notes = "全量客户商品同步ES")
//    public ApiResult allStoreCommoditySyncEs(@RequestBody EsManualIDTO req) {
//        try {
//            return storeCommodityElasticSearchService.allStoreCommoditySyncEs(req);
//        } catch (Exception e) {
//            log.error("同步客户商品至es出错",e);
//            return new ApiResult().initFailure(e.toString());
//        }
//    }

    @GetMapping("/allCategoryToSql")
    public ApiResult allCategoryToSql(){
        try {
            esCategoryService.allCategoryToSql();
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("商品分类同步到mysql-- 出错",e);
            return new ApiResult().initFailure(e.toString());
        }
    }

    @PostMapping("/commodityToMysql")
    public ApiResult commodityToMysql(@RequestBody EsManualIDTO req){
        try {
            List<Long> commodityIds = new ArrayList<>();
            if (null != req.getCommodityIds() && req.getCommodityIds().length() > 0) {
                commodityIds = Arrays.asList(req.getCommodityIds().split(",")).stream().map(e -> Long.valueOf(e)).collect(Collectors.toList());
            }
            esCommodityService.commodityToMysql(commodityIds);
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("商品商品同步到mysql-- 出错",e);
            return new ApiResult().initFailure(e.toString());
        }
    }

    @GetMapping("/allCommodityToMysql")
    public ApiResult allCommodityToMysql(){
        try {
            esCommodityService.commodityToMysql(null);
            return ApiResult.ok();
        } catch (Exception e) {
            log.error("商品商品同步到mysql-- 出错",e);
            return new ApiResult().initFailure(e.toString());
        }
    }

//    @PostMapping("/storeCommodityDifferenceEs")
//    @ApiOperation(value = "客户商品和ES差异对比", notes = "客户商品和ES差异对比")
//    public ApiResult storeCommodityDifferenceEs(@RequestBody EsManualIDTO req) {
//        try {
//            return storeCommodityElasticSearchService.storeCommodityDifferenceEs(req);
//        } catch (Exception e) {
//            log.error("同步客户商品至es出错",e);
//            return new ApiResult().initFailure(e.toString());
//        }
//    }
    @Autowired
    private XdaCommodityFrontClient xdaCommodityFrontClient;
    @GetMapping("/test")
    public Boolean test(){
        XdaCommodityDeliveryTimeIDTO idto = new XdaCommodityDeliveryTimeIDTO();
        idto.setOrderTime(new Date());
        idto.setStoreId(11L);
        idto.setCommodityIdList(Arrays.asList(111L));
        Map<Long, XdaCommodityDeliveryTimeODTO> deliveryTimeMap =
                xdaCommodityFrontClient.queryXdaCommodityDeliveryTime(idto);
        return Boolean.TRUE;
    }
}
