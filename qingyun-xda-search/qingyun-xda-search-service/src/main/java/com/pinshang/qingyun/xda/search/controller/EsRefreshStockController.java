package com.pinshang.qingyun.xda.search.controller;

import com.pinshang.qingyun.xda.search.service.EsCommodityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 通过job 凌晨刷新限量商品库存
 * </p>
 *
 * <AUTHOR>
 * @since 2024/04/07
 */
@RestController
@RequestMapping("/xda")
public class EsRefreshStockController {
    @Autowired
    private EsCommodityService esCommodityService;

    /**
     * 刷新限量商品库存
     * 2024/06/25 搜索优化，库存不在保存到es，直接从mysql中查询，所以该方法不需要。
     */
    @PostMapping("/refresh/stock")
    public Boolean refreshStock() {
        return esCommodityService.refreshStock();
    }
}
