package com.pinshang.qingyun.xda.search.controller;

import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.ApiErrorCodeEnum;
import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.exception.BusinessException;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.StringUtil;
import com.pinshang.qingyun.marketing.dto.mtCoupon.MtCouponToUseODTO;
import com.pinshang.qingyun.xda.search.aspect.RequestBodyAndHeader;
import com.pinshang.qingyun.xda.search.constants.XdaConstant;
import com.pinshang.qingyun.xda.search.dto.CategoryGoodsReq;
import com.pinshang.qingyun.xda.search.dto.KeyWordGoodsReq;
import com.pinshang.qingyun.xda.search.dto.commodity.XdaCommodityODTO;
import com.pinshang.qingyun.xda.search.dto.front.XdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.search.dto.front.XdaSearchAppIDTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryCommodityODTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryCommodityResODTO;
import com.pinshang.qingyun.xda.search.service.EsXdaCategoryCommodityFrontService;
import com.pinshang.qingyun.xda.search.service.EsXdaCategoryCommodityFrontV2Service;
import com.pinshang.qingyun.xda.search.util.ThreadLocalUtils;
import com.pinshang.qingyun.xda.search.vo.EsXdaGoodsNewVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

import static com.pinshang.qingyun.base.api.ApiResponse.convert;

/**
 * @ClassName EsXdaCategoryCommodityController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/2 17:47
 * @Version 1.0
 */
@RequestMapping("/xda/search")
@RestController
@Api(value = "鲜达商品检索", tags = "/xda/search", description = "鲜达商品搜索类")
@Slf4j
public class EsXdaCategoryCommodityFrontController {
    private final String NULL_STR = "null";
    /**
     * 关键字搜索最大长度
     * 参照百度最大36字节
     */
    private final int KEY_WORD_MAX_LENGTH = 36;
    @Autowired
    private EsXdaCategoryCommodityFrontService esXdaCategoryCommodityFrontService;
    @Autowired
    private EsXdaCategoryCommodityFrontV2Service esXdaCategoryCommodityFrontV2Service;

    /**
     * 鲜达商品检索
     */
    @PostMapping(value = "/commodity")
    @ApiOperation(value = "鲜达商品关键字搜索-移动端 -商品检索", response = EsXdaGoodsNewVO.class, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ApiResponse<XdaCommodityODTO> queryGoodsList(@RequestBodyAndHeader XdaSearchAppIDTO appIDTO, HttpServletResponse response) {
        XdaTokenInfo xdaTokenInfo = checkParam();
        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG, QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        if (StringUtil.isBlank(appIDTO.getKeyword()) || NULL_STR.equals(appIDTO.getKeyword())) {
            QYAssert.notNull(appIDTO.getKeyword(), "搜索关键字不能为空！");
        } else if (appIDTO.getKeyword().length() > KEY_WORD_MAX_LENGTH) {
            appIDTO.setKeyword(appIDTO.getKeyword().substring(0, KEY_WORD_MAX_LENGTH));
        }

        KeyWordGoodsReq keyWordGoodsReq = KeyWordGoodsReq.builder()
                .userId(xdaTokenInfo.getUserId())
                .storeId(xdaTokenInfo.getStoreId())
                .orderTime(appIDTO.getOrderTime())
                .keyWord(appIDTO.getKeyword())
                .xdaFirstCategoryId(appIDTO.getXdaFirstCategoryId())
                .logisticsCenterId(appIDTO.getLogisticscenterid())
                .build();
        // 前端是从1开始分页，此处需要减1；反正前端从0开始分页会造成0页与1页也重复
        keyWordGoodsReq.setPageNo(appIDTO.getPageNo() == 0 ? 0 : appIDTO.getPageNo() - 1);
        keyWordGoodsReq.setPageSize(appIDTO.getPageSize());

        return esXdaCategoryCommodityFrontService.search(keyWordGoodsReq);
    }


    /**
     * 优惠券去使用
     */
    @PostMapping(value = "/couponToUse")
    @ApiOperation(value = "优惠券去使用", response = EsXdaGoodsNewVO.class, produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public ApiResponse<XdaCommodityODTO> couponToUse(@RequestBodyAndHeader XdaSearchAppIDTO appIDTO, HttpServletResponse response) {
        XdaTokenInfo xdaTokenInfo = checkParam();

        QYAssert.isTrue(appIDTO.getCouponUserId() != null, "优惠券ID不能为空！");
        QYAssert.isTrue(appIDTO.getOrderTime() != null, "送货日期不能为空！");
        // 优惠券去使用，获取优惠券下的可使用商品信息
        MtCouponToUseODTO mtCouponToUseODTO = esXdaCategoryCommodityFrontService.queryCouponToUse(appIDTO.getCouponUserId(), appIDTO.getOrderTime(), xdaTokenInfo.getStoreId());

        response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG, QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);
        if (!StringUtil.isBlank(appIDTO.getKeyword()) && appIDTO.getKeyword().length() > KEY_WORD_MAX_LENGTH) {
            appIDTO.setKeyword(appIDTO.getKeyword().substring(0, KEY_WORD_MAX_LENGTH));
        }

        KeyWordGoodsReq keyWordGoodsReq = KeyWordGoodsReq.builder()
                .userId(xdaTokenInfo.getUserId())
                .storeId(xdaTokenInfo.getStoreId())
                .orderTime(appIDTO.getOrderTime())
                .keyWord(appIDTO.getKeyword())
                .couponUserId(appIDTO.getCouponUserId())
                .logisticsCenterId(appIDTO.getLogisticscenterid())
                .build();
        // 前端是从1开始分页，此处需要减1；反正前端从0开始分页会造成0页与1页也重复
        keyWordGoodsReq.setPageNo(appIDTO.getPageNo() == 0 ? 0 : appIDTO.getPageNo() - 1);
        keyWordGoodsReq.setPageSize(appIDTO.getPageSize());
        return esXdaCategoryCommodityFrontService.couponToUseSearch(keyWordGoodsReq, mtCouponToUseODTO);
    }

    @ApiOperation(value = "鲜达商品分类", notes = "鲜达商品分类搜索")
    @PostMapping("/category")
    public List<XdaCategoryCommodityResODTO> queryXdaCategoryCommodityList(@RequestBodyAndHeader XdaCategoryAppIDTO appIDTO) {
        try {
            XdaTokenInfo xdaTokenInfo = checkParam();

            Long storeId = xdaTokenInfo.getStoreId();
            appIDTO.setStoreId(storeId);
            appIDTO.setXdaSecondCategoryId(null);
            appIDTO.setAppVersion(xdaTokenInfo.getAppVersion());
            ThreadLocalUtils.setLogisticsCenterId(appIDTO.getLogisticscenterid());

            CategoryGoodsReq categoryGoodsReq = CategoryGoodsReq.builder()
                    .shopId(storeId)
                    .xdaFirstCategoryId(appIDTO.getXdaFirstCategoryId())
                    .userId(xdaTokenInfo.getUserId())
                    .sourceTypeEnum(xdaTokenInfo.getBizSourceType())
                    .xdaCategoryAppIDTO(appIDTO)
                    .build();

            return esXdaCategoryCommodityFrontService.leftCategoryList(categoryGoodsReq);
        } finally {
            ThreadLocalUtils.remove();
        }
    }

    @ApiOperation(value = "鲜达商品分类V2", notes = "鲜达商品分类搜索V2")
    @PostMapping("/categoryV2")
    public ApiResponse<XdaCategoryCommodityResODTO> queryXdaCategoryCommodityListV2(@RequestBodyAndHeader XdaCategoryAppIDTO appIDTO, HttpServletResponse response) {
        try {
            response.addHeader(QingyunConstant.WRAP_RESPONSE_FLAG, QingyunConstant.WRAP_RESPONSE_FLAG_VALUE);

            XdaTokenInfo xdaTokenInfo = checkParam();

            Long storeId = xdaTokenInfo.getStoreId();
            appIDTO.setStoreId(storeId);
            appIDTO.setAppVersion(xdaTokenInfo.getAppVersion());
            appIDTO.setUserId(xdaTokenInfo.getUserId());
            ThreadLocalUtils.setLogisticsCenterId(appIDTO.getLogisticscenterid());
            CategoryGoodsReq categoryGoodsReq = CategoryGoodsReq.builder()
                    .shopId(storeId)
                    .xdaFirstCategoryId(appIDTO.getXdaFirstCategoryId())
                    .xdaSecondCategoryId(appIDTO.getXdaSecondCategoryId())
                    .userId(xdaTokenInfo.getUserId())
                    .sourceTypeEnum(xdaTokenInfo.getBizSourceType())
                    .xdaCategoryAppIDTO(appIDTO)
                    .build();

            List<XdaCategoryCommodityResODTO> list = esXdaCategoryCommodityFrontV2Service.leftCategoryList(categoryGoodsReq);
            return convert2ApiResponse(list, appIDTO);
        } finally {
            ThreadLocalUtils.remove();
        }
    }

    private XdaTokenInfo checkParam() {
        XdaTokenInfo xdaTokenInfo = FastThreadLocalUtil.getXDA();
        QYAssert.notNull(xdaTokenInfo, "请先进行认证！");
        //QYAssert.notNull(xdaTokenInfo.getStoreId(), ApiErrorCodeEnum.LOGIN_EXPIRE.getRemark());
        //QYAssert.isTrue(xdaTokenInfo.getStoreId() != null, "未能获取到用户信息,请重新登录!");
        if(xdaTokenInfo.getStoreId() == null){
            throw new BusinessException(ApiErrorCodeEnum.LOGIN_EXPIRE);
        }
        return xdaTokenInfo;
    }

    private ApiResponse<XdaCategoryCommodityResODTO> convert2ApiResponse(List<XdaCategoryCommodityResODTO> list, XdaCategoryAppIDTO appIDTO) {
        Integer pageSize = appIDTO.getPageSize();
        Integer pageNo = appIDTO.getPageNo();
        Long xdaFirstCategoryId = appIDTO.getXdaFirstCategoryId();
        // 获取总数
        int total = 0;
        // 只处理特价分类
        if (Objects.equals(xdaFirstCategoryId, XdaConstant.FIRST_CATEGORY_SPECIAL) && CollectionUtils.isNotEmpty(list)) {
            XdaCategoryCommodityResODTO firstItem = list.get(0);
            if (firstItem != null && CollectionUtils.isNotEmpty(firstItem.getCommodityList())) {
                XdaCategoryCommodityODTO firstCommodity = firstItem.getCommodityList().get(0);
                if (firstCommodity != null && firstCommodity.getTotal() != null) {
                    total = firstCommodity.getTotal();
                }
            }
        }

        // 构造 PageInfo 对象
        PageInfo<XdaCategoryCommodityResODTO> pageInfo = new PageInfo<>(list);
        pageInfo.setTotal(total);
        pageInfo.setPageNum(pageNo);
        pageInfo.setPageSize(pageSize);

        // 计算总页数
        int totalPages = (total + pageSize - 1) / pageSize;
        pageInfo.setPages(totalPages);

        // 判断是否有下一页
        pageInfo.setHasNextPage(pageNo < totalPages);
        return convert(pageInfo);
    }
}
