package com.pinshang.qingyun.xda.search.controller;

import com.pinshang.qingyun.base.api.TokenInfo;
import com.pinshang.qingyun.base.api.XdaTokenInfo;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.xda.XdaAppCodeEnum;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.xda.search.dto.CategoryGoodsReq;
import com.pinshang.qingyun.xda.search.dto.front.XdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.search.dto.web.XdaWebCategoryCommodityResODTO;
import com.pinshang.qingyun.xda.search.service.EsXdaCategoryCommodityWebService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * @ClassName EsXdaCategoryCommodityWebController
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/2 17:47
 * @Version 1.0
 */
@RequestMapping("/xda/web/search")
@RestController
@Api(value = "鲜达商品检索-web", tags = "/xda/web/search", description = "鲜达商品搜索类-web")
@Slf4j
public class EsXdaCategoryCommodityWebController {
    @Autowired
    private EsXdaCategoryCommodityWebService esXdaCategoryCommodityWebService;

    @ApiOperation(value = "鲜达商品分类-web", notes = "鲜达商品分类搜索-web")
    @PostMapping("/category")
    public XdaWebCategoryCommodityResODTO queryXdaCategoryCommodityList(@RequestBody XdaCategoryAppIDTO appIDTO) {
        checkParam(appIDTO);

        Long storeId = appIDTO.getStoreId();
        appIDTO.setStoreId(storeId);
        CategoryGoodsReq categoryGoodsReq = CategoryGoodsReq.builder()
                .shopId(storeId)
                .xdaFirstCategoryId(appIDTO.getXdaFirstCategoryId())
                .xdaSecondCategoryId(appIDTO.getXdaSecondCategoryId())
                .xdaCategoryAppIDTO(appIDTO)
                .build();
        buildDefaultXdaToken(storeId);
        return esXdaCategoryCommodityWebService.leftCategoryList(categoryGoodsReq);
    }

    private static void buildDefaultXdaToken(Long storeId) {
        XdaTokenInfo xdaTokenInfo = new XdaTokenInfo();
        xdaTokenInfo.setStoreId(storeId);
        xdaTokenInfo.setIsTouristStore(Boolean.FALSE);
        xdaTokenInfo.setAppCode(XdaAppCodeEnum.ANDROID.getAppCode());
        FastThreadLocalUtil.setXDA(xdaTokenInfo);
    }

    private TokenInfo checkParam(XdaCategoryAppIDTO appIDTO) {
        TokenInfo tokenInfo = FastThreadLocalUtil.getQY();
        QYAssert.notNull(tokenInfo, "请先进行认证！");
        QYAssert.notNull(appIDTO.getStoreId(), "请选择商户!");
        return tokenInfo;
    }
}
