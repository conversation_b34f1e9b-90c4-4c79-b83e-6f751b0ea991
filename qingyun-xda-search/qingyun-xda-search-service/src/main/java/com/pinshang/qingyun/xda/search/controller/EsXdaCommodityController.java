package com.pinshang.qingyun.xda.search.controller;

import com.pinshang.qingyun.xda.search.dto.EsXdaUpdateDiffRecordIDTO;
import com.pinshang.qingyun.xda.search.service.es.CommodityElasticSearchService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/04/17
 * @Version 1.0
 */
@RequestMapping("/xda/storeCommodity")
@RestController
@Api(value = "鲜达客户商品", tags = "/xda/search", description = "鲜达客户商品")
@Slf4j
public class EsXdaCommodityController {
    @Autowired
    private CommodityElasticSearchService commodityElasticSearchService;

    @RequestMapping(value = "/updateDiffRecord" , method = RequestMethod.POST)
    @ApiOperation(value = "更新异常数据")
     public void updateDiffRecord(@RequestBody EsXdaUpdateDiffRecordIDTO idto){
         commodityElasticSearchService.updateEsXdCommodityStatus(idto);
    }
}
