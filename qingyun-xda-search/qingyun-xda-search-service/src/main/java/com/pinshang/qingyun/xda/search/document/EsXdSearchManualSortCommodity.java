package com.pinshang.qingyun.xda.search.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 0.1.0
 * @Date 2022/3/4 9:44
 * @Copyright ©qm
 * @Description -- 类说明
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "xd-search-manual-sort-commodity", replicas = 2)
public class EsXdSearchManualSortCommodity {

    @Id
    private String id;

    /**
     * 关键字
     */
    @Field(type = FieldType.Keyword)
    private String keyword;

    /**
     * 商品id
     */
    @Field(type = FieldType.Long)
    private Long commodityId;

    /**
     * 排序号
     */
    @Field(type = FieldType.Integer)
    private Integer sort;

    @Field(type = FieldType.Long)
    private Long createId;

    /**
     * 创建时间
     */
    @Field(type=FieldType.Date, format= DateFormat.date_optional_time)
    @JsonFormat(pattern ="yyyy-MM-dd'T'HH:mm:ss" ,timezone = "GMT+8")
    private Date createTime;


}
