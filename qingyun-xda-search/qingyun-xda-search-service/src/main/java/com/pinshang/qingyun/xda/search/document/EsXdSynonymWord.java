package com.pinshang.qingyun.xda.search.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;

/**
 * 前置仓近义词
 */
@Data
@Document(indexName = "xd-search-synonym-word",replicas = 2)
@NoArgsConstructor
public class EsXdSynonymWord {
    /** 1. 主键（近义词分组编码）　**/
    @Id
    private String id;
    /** 2. 近义词分组名称 **/
    @Field(type = FieldType.Text,index = false)
    private String name;
    /** 3. 近义词**/
    @Field(type = FieldType.Text,analyzer = "whitespace",searchAnalyzer = "ik_smart")
    private String synonymWord;
    /** 4. 状态: 0= 停用,1=启用 **/
    @Field(type = FieldType.Integer,index = false)
    private Integer status;
    /** 5. 修改的时间 **/
    @Field(type=FieldType.Date, format=DateFormat.date_optional_time)
    @JsonFormat(pattern ="yyyy-MM-dd'T'HH:mm:ss" ,timezone = "GMT+8")
    private Date time;

    public EsXdSynonymWord(String code, String name, String synonymWord, Integer status, Date date){
        this.id = code ;
        this.name = name ;
        this.synonymWord = synonymWord.replaceAll("、"," ") ;
        this.status = status ;
        this.time = date ;
    }
}
