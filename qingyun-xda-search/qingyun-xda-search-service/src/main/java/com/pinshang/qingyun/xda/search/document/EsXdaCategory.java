package com.pinshang.qingyun.xda.search.document;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;


@Data
@Document(indexName = "xda-category", replicas = 2)
@NoArgsConstructor
@AllArgsConstructor
public class EsXdaCategory {

    @Id
    private Long id;

    @Field(type = FieldType.Long)
    private Long cateId;

    @Field(type = FieldType.Long)
    private Long parentId;

    @Field(type = FieldType.Text)
    private String cateName;

    @Field(type = FieldType.Integer)
    private Integer cateLevel;

    @Field(type = FieldType.Integer)
    private Integer sortNum;

//    @Field(type = FieldType.Date, format = {}, pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date createTime;
//
//    @Field(type = FieldType.Date, format = {}, pattern = "yyyy-MM-dd HH:mm:ss")
//    private Date updateTime;

}
