package com.pinshang.qingyun.xda.search.document;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * @ClassName EsXdaCommodity
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/3 14:34
 * @Version 1.0
 */
@Data
@Document(indexName = "xda-commodity", replicas = 2)
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class EsXdaCommodity {
    @Id
    private Long id;
    @Field(type = FieldType.Long)
    private Long commodityId;

    /** 前台品名和分类，中间加空格 */
    @Field(type = FieldType.Text,analyzer = "ik_max_word",searchAnalyzer = "ik_smart")
    private String commoditySearchName;

    @Field(type = FieldType.Keyword)
    private String commodityCode;

    @Field(type = FieldType.Text)
    private String commodityAppName;

    @Field(type = FieldType.Text)
    private String commoditySubName;

    @Field(type = FieldType.Integer)
    private Integer sortNum;

    @Field(type = FieldType.Text)
    private String commoditySpec;

    @Field(type = FieldType.Text)
    private String defaultImageUrl;

    @Field(type = FieldType.Keyword)
    private String commodityUnitName;

    @Field(type = FieldType.Long)
    private Long xdaFirstCategoryId;

    /** 前台一级分类名称 */
    @Field(type = FieldType.Keyword)
    private String xdaFirstCategoryName;

    @Field(type = FieldType.Long)
    private Long xdaSecondCategoryId;

    /** 前台二级分类名称 */
    @Field(type = FieldType.Keyword)
    private String xdaSecondCategoryName;

    @Field(type = FieldType.Text)
    private String deliveryDateRangeCode;

    @Field(type = FieldType.Text)
    private String deliveryDateRangeValue;


    /** 批发 2-8 */
    @Field(type = FieldType.Text)
    private String pfDeliveryDateRangeCode;
    /** 批发 T+2~T+8 */
    @Field(type = FieldType.Text)
    private String pfDeliveryDateRangeValue;

    /** 订货范围前区间 */
    @Field(type = FieldType.Integer)
    private Integer deliveryDateAnteriorInterval;

    /** 订货范围前区间 */
    @Field(type = FieldType.Integer)
    private Integer deliveryDateAfterInterval;

    /** 批发 订货范围前区间 */
    @Field(type = FieldType.Integer)
    private Integer pfDeliveryDateAnteriorInterval;

    /** 批发 订货范围后区间 */
    @Field(type = FieldType.Integer)
    private Integer pfDeliveryDateAfterInterval;

    @Field(type = FieldType.Boolean)
    private Boolean soldOut;

    @Field(type = FieldType.Boolean)
    private Boolean soldOut1;

    @Field(type = FieldType.Boolean)
    private Boolean soldOut2;

    @Field(type = FieldType.Boolean)
    private Boolean soldOut3;

    @Field(type = FieldType.Boolean)
    private Boolean soldOut4;

    @Field(type = FieldType.Boolean)
    private Boolean soldOut5;

    @Field(type = FieldType.Boolean)
    private Boolean soldOut6;

    @Field(type = FieldType.Boolean)
    private Boolean soldOut7;

    @Field(type = FieldType.Integer)
    private Integer appStatus;

    @Field(type = FieldType.Integer)
    private Integer pfAppStatus;
}
