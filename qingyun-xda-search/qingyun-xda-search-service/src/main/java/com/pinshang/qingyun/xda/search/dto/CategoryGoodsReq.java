package com.pinshang.qingyun.xda.search.dto;

import com.pinshang.qingyun.base.enums.OrderSourceTypeEnum;
import com.pinshang.qingyun.xda.search.dto.front.XdaCategoryAppIDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/2/11 3:18
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明 一级分类下的商品
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CategoryGoodsReq implements Serializable {
    @ApiModelProperty(value = "门店Id", required = true)
    private Long shopId;

    @ApiModelProperty(value = "一级分类id")
    private Long xdaFirstCategoryId;

    @ApiModelProperty(value = "一级分类id")
    private Long xdaSecondCategoryId;

    @ApiModelProperty(value = "用户手机号，app、微信不需要输入，后台", required = false, hidden = true)
    private String userName;

    @ApiModelProperty(value = "用户Id，【不填】便于后台使用，前端不用传", required = false, hidden = true)
    private Long userId;

    @ApiModelProperty(value = "web端需要填 渠道 1-App，3-小程序，8-云超小程序，9-云超APP，app与小程序这个字段不用填，后台自动判断", required = false, hidden = true)
    @Deprecated
    private Integer sourceType;

    @ApiModelProperty(value = "web端需要填 渠道 1-App，3-小程序，8-云超小程序，9-云超APP，app与小程序这个字段不用填，后台自动判断", required = false, hidden = true)
    private OrderSourceTypeEnum sourceTypeEnum;

    @ApiModelProperty(value = "定制展示的标签，1展示一级推荐二级分类的新品，2关闭一级推荐二级分类的新品，默认2关闭", required = false)
    private Integer labelType;

    private XdaCategoryAppIDTO xdaCategoryAppIDTO;

    private Integer pageNo = 1;

    private Integer pageSize = 100;

    private static final long serialVersionUID = 6775790255401411646L;
    public OrderSourceTypeEnum getSourceTypeEnum() {
        return sourceTypeEnum == null ? OrderSourceTypeEnum.getEnumByCode( sourceType) : sourceTypeEnum;
    }
}
