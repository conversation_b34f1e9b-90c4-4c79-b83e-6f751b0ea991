package com.pinshang.qingyun.xda.search.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商品文描信息
 *
 * <AUTHOR>
 *
 * @date 2019年11月19日
 */
@Data
@ApiModel
@NoArgsConstructor
public class CommodityTextODTO extends Update7DaysSoldOutDTO{

	@ApiModelProperty("商品ID")
	private Long commodityId;

	@ApiModelProperty("前台品名")
	private String commodityAppName;

	@ApiModelProperty("前台一级品类ID")
	private Long xdaFirstCategoryId;
	@ApiModelProperty("前台二级品类ID")
	private Long xdaSecondCategoryId;
	@ApiModelProperty("前台一级品类")
	private String xdaFirstCategoryName;
	@ApiModelProperty("前台二级品类")
	private String xdaSecondCategoryName;

	/** 上下架状态：0-上架，1-下架 */
	private Integer appStatus;

	/**
	 * 订货范围前区间
	 */
	private Integer deliveryDateAnteriorInterval;

	/**
	 * 订货范围后区间
	 */
	private Integer deliveryDateAfterInterval;


	
}
