package com.pinshang.qingyun.xda.search.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 手动更新索引参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EsManualIDTO {
    private String commodityIds ;
    private String storeIds ;

    /** yyyy-MM-dd HH:mm:ss */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
