package com.pinshang.qingyun.xda.search.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KeyWordGoodsReq implements Serializable {

    /**
     * 销量排序
     */
    public static final String SORT_SALES_VOLUME = "salesVolume";
    /**
     * 价格排序
     */
    public static final String SORT_PRICE = "price";
    /**
     * 当前页号, 默认第 1 页
     */
    private Integer pageNo = 1;
    /**
     * 每页显示记录数, 默认 10 条
     */
    private Integer pageSize = 10;
    @ApiModelProperty(value = "客户ID", hidden = true)
    private Long storeId;
    @ApiModelProperty(value = "门店Id,兼容大黄的字段值,等于shopId", required = true)
    private Long lid;

    @ApiModelProperty(value = "搜索关键字", required = true)
    private String keyWord;

    @ApiModelProperty(value = "用户手机号，app、微信不需要输入，后台会处理", required = false, hidden = true)
    private String userName;

    @ApiModelProperty(value = "用户Id，【不填】便于后台使用，前端不用传", required = false, hidden = true)
    private Long userId;

    @ApiModelProperty(value = "web端需要填 渠道 1-App，3-小程序，，app与小程序这个字段不用填，后台自动判断", required = false, hidden = true)
    private Integer sourceType;

    //    @ApiModelProperty(value = "特价搜索引擎类型 1-es搜索，2-Redis搜索，默认es搜索", required = false)
    @ApiModelProperty(value = "搜索类型，1-用户输入搜索，2-历史记录搜索，3-热词搜索，0或空未获取到搜索类型", required = false)
    private Integer searchType;

    @ApiModelProperty(value = "设备id", required = false)
    private String identity;

    @ApiModelProperty(value = "排序字段 1-销量排序，2-价格排序，0-默认不排序", required = false)
    private Integer sortKey;

    @ApiModelProperty(value = "排序顺序 1-升序，-2-降序，0-默认不排序", required = false)
    private Integer upOrDown;
    @ApiModelProperty(value = "一级分类ID", hidden = true)
    private Long xdaFirstCategoryId;
    @ApiModelProperty(value = "二级分类ID", hidden = true)
    private Long xdaSecondCategoryId;
    @ApiModelProperty(required = true,value = "送货日期",position = 1)
    private Date orderTime;


    @ApiModelProperty("优惠券id")
    private Long couponUserId;

    @ApiModelProperty("商品ID集合")
    private List<Long> filterCommodityIdList;

    @ApiModelProperty("物流中心ID")
    private Long logisticsCenterId;
}
