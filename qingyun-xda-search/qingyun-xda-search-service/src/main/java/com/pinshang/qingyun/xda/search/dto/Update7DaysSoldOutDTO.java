package com.pinshang.qingyun.xda.search.dto;

import lombok.Data;

/**
 * @Author: sk
 * @Date: 2024/4/9
 */
@Data
public class Update7DaysSoldOutDTO {

    private Long commodityId;

    /** 1有库存 0售罄 */
    private Integer soldOut;
    private Integer soldOut1;
    private Integer soldOut2;
    private Integer soldOut3;
    private Integer soldOut4;
    private Integer soldOut5;
    private Integer soldOut6;
    private Integer soldOut7;

    public void setSoldOut7Day(Update7DaysSoldOutDTO soldOutDTO){
        this.soldOut = soldOutDTO.getSoldOut();
        this.soldOut1 = soldOutDTO.getSoldOut1();
        this.soldOut2 = soldOutDTO.getSoldOut2();
        this.soldOut3 = soldOutDTO.getSoldOut3();
        this.soldOut4 = soldOutDTO.getSoldOut4();
        this.soldOut5 = soldOutDTO.getSoldOut5();
        this.soldOut6 = soldOutDTO.getSoldOut6();
        this.soldOut7 = soldOutDTO.getSoldOut7();
    }
//    public void setUnLimitAndStockSoldOut(Integer soldOut){
//        this.soldOut = soldOut;
//        this.soldOut1 = soldOut;
//        this.soldOut2 = soldOut;
//        this.soldOut3 = soldOut;
//        this.soldOut4 = soldOut;
//        this.soldOut5 = soldOut;
//        this.soldOut6 = soldOut;
//        this.soldOut7 = soldOut;
//
//    }
}
