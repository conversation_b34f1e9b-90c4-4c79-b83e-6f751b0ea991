package com.pinshang.qingyun.xda.search.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 手动刷新服务列表返回
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdSearchEsIndexDTO implements Serializable {
    private static final long serialVersionUID = 3485957614174787974L;
    @ApiModelProperty("索引名称")
    private String indexName;
    @ApiModelProperty("请求URL")
    private String url;
    @ApiModelProperty("是否支持指定门店：1=是，0=否")
    private Integer shopFlag;
    @ApiModelProperty("是否支持指定商品：1=是，0=否")
    private Integer commodityFlag;
    @ApiModelProperty("是否支持指定时间：1=是，0=否")
    private Integer updateTimeFlag;

}
