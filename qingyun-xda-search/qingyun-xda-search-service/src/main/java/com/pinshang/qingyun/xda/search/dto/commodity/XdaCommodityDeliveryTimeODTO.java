package com.pinshang.qingyun.xda.search.dto.commodity;

import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.DateUtil;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;


/**
 * 鲜达APP商品送货日期范围
 */
@Data
public class XdaCommodityDeliveryTimeODTO {

    private Long commodityId;
    /**
     * 例:2-8
     */
    private String deliveryDateRangeCode;
    /**
     * 例:T+2~T+8
     */
    private String deliveryDateRangeValue;

    private List<Date> deliveryDateList;

    private Boolean isCanOrder;

    private List<String> distributionTipList;



    public Boolean getIsCanOrder() {
        return isCanOrder==null?Boolean.FALSE:isCanOrder;
    }

    public List<Date> getDeliveryDateList() {
        if(StringUtils.isEmpty(deliveryDateRangeCode)){
            return Collections.EMPTY_LIST;
        }
        List<Date> dateList = new ArrayList<>();
        String[] deliveryDateRangeArr = deliveryDateRangeCode.split("-",-1);
        for(int i = Integer.parseInt(deliveryDateRangeArr[0]); i<=Integer.parseInt(deliveryDateRangeArr[deliveryDateRangeArr.length-1]); i++){
            try {
                Date date = DateUtil.getDatePart(DateTimeUtil.buildNewDate(DateUtil.getNowDate(),i));
                if(!dateList.contains(date)){
                    dateList.add(date);
                }
            } catch (ParseException e) {
            }
        }
        return dateList;
    }

    public List<String> processDistributionTips(List<Date> dateList,String storeBeginTime,String storeEndTime) {
        List<String> distributionTips = new ArrayList<>();
        if(!storeBeginTime.equals("0:00") && !storeBeginTime.equals("00:00")){
            distributionTips.add("0:00~"+storeBeginTime+"时间段，不可订货;");
        }
        distributionTips.add("今天"+storeBeginTime+"~"+storeEndTime+"下单，最快"+DateTimeUtil.formatDate(dateList.get(0),"yyyy-MM-dd")+"到货。");
        if(dateList.size()>1){
            distributionTips.add("今天"+storeEndTime+"~23:59下单，最快"+DateTimeUtil.formatDate(dateList.get(1),"yyyy-MM-dd")+"到货。");
        }
        return distributionTips;
    }
}
