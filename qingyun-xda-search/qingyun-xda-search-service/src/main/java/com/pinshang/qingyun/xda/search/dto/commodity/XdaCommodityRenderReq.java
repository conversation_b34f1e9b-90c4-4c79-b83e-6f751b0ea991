package com.pinshang.qingyun.xda.search.dto.commodity;

import com.pinshang.qingyun.base.enums.SourceTypeEnums;
import com.pinshang.qingyun.base.enums.pic.PicSizeEnums;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class XdaCommodityRenderReq {
    /**
     * 页面入口来源
     * 1, "鲜达APP-分类商品页"
     * 2, "鲜达APP-商品详情页"
     */
    private Integer fromPage;
    /**
     * 送货日期
     */
    private Date orderTime;

    /**
     * 客户ID
     * 示例：752908786325622016
     */
    private Long storeId;

    /**
     * 商品ID集合
     * 示例：[2580787591198700,468788174651305600,6772315779888100]
     */
    private List<Long> commodityIdList;

    /**
     * 商品默认图片尺寸。不传时返回原图
     * 示例：PIC_120x120
     */
    private PicSizeEnums defaultImageSize;

    /**
     * 请求来源(预留)：1=App，3=小程序，不传默认为1=APP
     */
    private Integer sourceType;

    /**
     * 是否需要自定义标签，不传默认为true
     */
    private Boolean needTag;

    /**
     * 是否需要特价不传默认为true，不传默认为true
     */
    private Boolean needSpecialPrice;

    /**
     * 是否需要促销，不传默认为true
     */
    private Boolean needPromotion;

    /**
     * 是否需要验证可订货，不传默认为true
     */
    private Boolean needCanOrder;

    /**
     * 是否需要限量标签，不传默认为true
     */
    private Boolean needLimit;

    /**
     * 是否需要返回下架商品，不传默认false；购物车使用，默认不返回
     */
    private Boolean needAppDown;

    /**
     * 是否需要主图，不传默认true；分类页系列品不需要主图
     */
    private Boolean needDefaultImage;

    /**
     * 是否需要展示系列品，默认不展示；详情和分类页展示
     */
    private Boolean needSerial;

    /**
     * 是否需要已加入购物车数量，默认不展示；详情和分类页展示
     */
    private Boolean needCartQuantity;

    public Integer getSourceType() {
        return sourceType == null ? SourceTypeEnums.APP.convert() : sourceType;
    }

    public Boolean getNeedTag() {
        return needTag == null ? Boolean.TRUE : needTag;
    }

    public Boolean getNeedSpecialPrice() {
        return needSpecialPrice == null ? Boolean.TRUE : needSpecialPrice;
    }

    public Boolean getNeedPromotion() {
        return needPromotion == null ? Boolean.TRUE : needPromotion;
    }

    public Boolean getNeedCanOrder() {
        return needCanOrder == null ? Boolean.TRUE : needCanOrder;
    }

    public Boolean getNeedLimit() {
        return needLimit == null ? Boolean.TRUE : needLimit;
    }

    public Boolean getNeedDefaultImage() {
        return needDefaultImage == null ? Boolean.TRUE : needDefaultImage;
    }

    public Boolean getNeedAppDown() {
        return needAppDown == null ? Boolean.FALSE : needAppDown;
    }

    public Boolean getNeedSerial() {
        return needSerial == null ? Boolean.FALSE : needSerial;
    }

//    public Boolean getNeedSale() {
//        return needSale==null?Boolean.FALSE:needSale;
//    }

    public Boolean getNeedCartQuantity() {
        return needCartQuantity == null ? false : needCartQuantity;
    }
}
