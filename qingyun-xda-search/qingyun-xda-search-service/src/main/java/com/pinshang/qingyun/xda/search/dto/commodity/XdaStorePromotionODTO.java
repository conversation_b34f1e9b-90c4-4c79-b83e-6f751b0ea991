package com.pinshang.qingyun.xda.search.dto.commodity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 鲜达APP当前正在生效的赠品信息
 */
@Data
public class XdaStorePromotionODTO {
    @ApiModelProperty(value = "赠品方案ID",hidden = true)
    private Long giftModelId;
    @ApiModelProperty("赠品方案名称，APP促销标签取此值")
    private String giftModelName;
    @ApiModelProperty("赠品备注，APP商品详情促销内容取此值")
    private String giftModelDesc;
    @ApiModelProperty(value = "赠品类型：1-订单金额,2-商品数量,3-商品金额",hidden = true)
    private Integer giftModelType;
    @ApiModelProperty(value = "赠品方式：1-赠送一次,2-数倍累加",hidden = true)
    private Integer conditionType;
    @ApiModelProperty(value = "赠品满足条件值：如订单金额达到多少",hidden = true)
    private BigDecimal conditionValue;
    @ApiModelProperty(value = "赠送商品ID",hidden = true)
    private Long commodityId;
    @ApiModelProperty(value = "赠品数量",hidden = true)
    private BigDecimal giftNumber;
    @ApiModelProperty(value = "赠品限量",hidden = true)
    private BigDecimal giftMaxNumber;

}
