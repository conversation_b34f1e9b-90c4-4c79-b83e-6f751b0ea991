package com.pinshang.qingyun.xda.search.dto.front;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdaCategoryAppIDTO extends XdaCategoryBaseIDTO {

    @ApiModelProperty(value = "一级分类ID", position = 1)
    private Long xdaFirstCategoryId;

    @ApiModelProperty(value = "二级分类ID", position = 2)
    private Long xdaSecondCategoryId;

    private BigDecimal xdaPrice;

    private Integer pageNo = 1;

    private Integer pageSize = 100;

    @ApiModelProperty(value = "用户Id，【不填】便于后台使用，前端不用传", required = false, hidden = true)
    private Long userId;

}
