package com.pinshang.qingyun.xda.search.dto.front.categoryfront;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdaCategoryCommodityResODTO {

    /**
     * 一级分类
     */
    @ApiModelProperty(value = "一级分类解析使用-分类ID",position = 1)
    private Long xdaCategoryId;
    @ApiModelProperty(value = "一级分类解析使用-分类ID字符串",position = 1)
    private String xdaCategoryIdStr;
    @ApiModelProperty(value = "一级分类解析使用-分类名称",position = 2)
    private String xdaCategoryName;
    @ApiModelProperty(value = "分类排序",hidden = true)
    private Integer xdaCategorySort;
    @ApiModelProperty(value = "父节点ID",hidden = true)
    private Long parentId;
    /**特惠分类提示信息**/
    @ApiModelProperty(value = "一级分类解析使用-特惠分类提示信息")
    private String thCategoryTips;
    @ApiModelProperty(value = "一级分类解析使用-特惠分类规则详细信息")
    private String thCategoryTipsDetails;
    @ApiModelProperty("一级分类解析使用-是否满足特惠")
    private Boolean isThInvalidate = Boolean.FALSE;

    @ApiModelProperty(value = "二级分类列表",position = 3)
    private List<XdaCategorODTO> xdaSecondCategoryList;


    /**
     * 二级分类返回字段
     */
    @ApiModelProperty(value = "一级分类ID",position = 4)
    private Long xdaFirstCategoryId;

    @ApiModelProperty(value = "二级分类ID",position = 5)
    private Long xdaSecondCategoryId;

    @ApiModelProperty(value = "二级分类名称",position = 6)
    private String xdaSecondCategoryName;

    @ApiModelProperty(hidden = true)
    private Integer sortNum;

    @ApiModelProperty(value = "二级分类下的商品列表",position = 7)
    private List<XdaCategoryCommodityODTO> commodityList;

    @Data
    @ToString
    public static class XdaCategorODTO {
        @ApiModelProperty(value = "一级分类解析使用-分类ID",position = 1)
        private Long xdaCategoryId;
        @ApiModelProperty(value = "一级分类解析使用-分类ID字符串",position = 1)
        private String xdaCategoryIdStr;
        @ApiModelProperty(value = "一级分类解析使用-分类名称",position = 2)
        private String xdaCategoryName;
        @ApiModelProperty(value = "分类排序",hidden = true)
        private Integer xdaCategorySort;
        @ApiModelProperty(value = "父节点ID",hidden = true)
        private Long parentId;
        /**特惠分类提示信息**/
        @ApiModelProperty(value = "一级分类解析使用-特惠分类提示信息")
        private String thCategoryTips;
        @ApiModelProperty(value = "一级分类解析使用-特惠分类规则详细信息")
        private String thCategoryTipsDetails;
        @ApiModelProperty("一级分类解析使用-是否满足特惠")
        private Boolean isThInvalidate = Boolean.FALSE;
    }

}
