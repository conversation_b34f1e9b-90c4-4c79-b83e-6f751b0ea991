package com.pinshang.qingyun.xda.search.dto.front.categoryfront;

import com.pinshang.qingyun.xda.search.constants.XdaConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * 鲜达APP 分类
 */
@Data
@NoArgsConstructor
public class XdaCategoryODTO extends XdaCategoryBaseODTO {

    @ApiModelProperty(value = "二级分类列表",position = 3)
    private List<XdaCategoryODTO> xdaSecondCategoryList;

    public static XdaCategoryODTO buildRecommendLabel(Long firstCategoryId) {
        XdaCategoryODTO baseODTO = new XdaCategoryODTO();
        baseODTO.setXdaCategoryId(XdaConstant.RECOMMOND_CATEGORT_ID);
        baseODTO.setXdaCategoryName("精选");
        baseODTO.setXdaCategorySort(0);
        baseODTO.setParentId(firstCategoryId);
        return baseODTO;
    }
}
