package com.pinshang.qingyun.xda.search.dto.front.categoryfront;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdaSecondCategoryCommodityResODTO {


    @ApiModelProperty(value = "特惠商品二级分类商品id集合", position = 1)
    private List<Long> thCommodityIds;

    @ApiModelProperty(value = "促销商品二级分类商品id集合", position = 2)
    private List<Long> promotionCommodityIds;

    @ApiModelProperty(value = "常购商品二级分类商品id集合", position = 3)
    private List<Long> oftenBuyCommodityIds;

    @ApiModelProperty(value = "收藏商品二级分类商品id集合", position = 4)
    private List<Long> collectCommodityIds;

    private Boolean specialCategoryFlag;

}
