package com.pinshang.qingyun.xda.search.dto.front.commodityfront;

import com.pinshang.qingyun.base.enums.SourceTypeEnums;
import com.pinshang.qingyun.base.enums.pic.PicSizeEnums;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class XdaCommodityAppIDTO {
    @ApiModelProperty(value = "送货日期",required = true,position = 1)
    private Date orderTime;
    @ApiModelProperty(value = "客户ID",example = "752908786325622016",required = true,position = 2)
    private Long storeId;
    @ApiModelProperty(value = "商品ID集合",position = 3,example = "[2580787591198700,468788174651305600,6772315779888100]")
    private List<Long> commodityIdList;
    @ApiModelProperty(value = "商品默认图片尺寸。不传时返回原图",example = "PIC_120x120",position = 4)
    private PicSizeEnums defaultImageSize;
    @ApiModelProperty(value = "请求来源(预留)：1=App，3=小程序，不传默认为1=APP",hidden = true)
    private Integer sourceType;

    @ApiModelProperty(value = "是否需要自定义标签，不传默认为true",hidden = true)
    private Boolean needTag;
    @ApiModelProperty(value = "是否需要特价不传默认为true，不传默认为true",hidden = true)
    private Boolean needSpecialPrice;
    @ApiModelProperty(value = "是否需要促销，不传默认为true",hidden = true)
    private Boolean needPromotion;
    @ApiModelProperty(value = "是否需要验证可订货，不传默认为true",hidden = true)
    private Boolean needCanOrder;
    @ApiModelProperty(value = "是否需要限量标签，不传默认为true",hidden = true)
    private Boolean needLimit;

    @ApiModelProperty(value = "是否需要返回下架商品，不传默认false；购物车使用，默认不返回",hidden = true)
    private Boolean needAppDown;
    @ApiModelProperty(value = "是否需要主图，不传默认true；分类页系列品不需要主图",hidden = true)
    private Boolean needDefaultImage;
    @ApiModelProperty(value ="是否需要展示系列品，默认不展示；详情和分类页展示",hidden = true)
    private Boolean needSerial;
    @ApiModelProperty(value ="是否需要已加入购物车数量，默认不展示；详情和分类页展示",hidden = true)
    private Boolean needCartQuantity;

    public Integer getSourceType() {
        return sourceType==null? SourceTypeEnums.APP.convert():sourceType;
    }

    public Boolean getNeedTag() {
        return needTag==null?Boolean.TRUE:needTag;
    }
    public Boolean getNeedSpecialPrice() {
        return needSpecialPrice==null?Boolean.TRUE:needSpecialPrice;
    }

    public Boolean getNeedPromotion() {
        return needPromotion==null?Boolean.TRUE:needPromotion;
    }

    public Boolean getNeedCanOrder() {
        return needCanOrder==null?Boolean.TRUE:needCanOrder;
    }

    public Boolean getNeedLimit() {
        return needLimit==null?Boolean.TRUE:needLimit;
    }

    public Boolean getNeedDefaultImage() {
        return needDefaultImage==null?Boolean.TRUE:needDefaultImage;
    }

    public Boolean getNeedAppDown() {
        return needAppDown==null?Boolean.FALSE:needAppDown;
    }

    public Boolean getNeedSerial() {
        return needSerial==null?Boolean.FALSE:needSerial;
    }

//    public Boolean getNeedSale() {
//        return needSale==null?Boolean.FALSE:needSale;
//    }

    public Boolean getNeedCartQuantity() {
        return needCartQuantity==null?false:needCartQuantity;
    }
}
