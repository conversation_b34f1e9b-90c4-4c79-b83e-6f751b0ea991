package com.pinshang.qingyun.xda.search.dto.web;

import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryCommodityResODTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdaWebCategoryCommodityResODTO {

    @ApiModelProperty("品类")
    private List<XdaCategoryCommodityResODTO> categorys;

    @ApiModelProperty("商品")
    private List<XdaCategoryCommodityResODTO> commoditys;

}
