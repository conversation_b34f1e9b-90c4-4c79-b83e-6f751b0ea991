package com.pinshang.qingyun.xda.search.enums;

public enum XdaCategoryEnums {
    OFTEN_BUY(0L, 1L, "我的常购"),
    MY_COLLECT(0L, 2L, "我的收藏"),
    SPECIAL_PROMOTION(-1L, 1L, "促销商品"),
    TH(-2L, 1L, "特惠商品"),
    RECOMMEND(0L, 0L, "精选");

    private final Long firstCategoryId;
    private final Long secondCategoryId;
    private final String secondCategoryName;

    // 构造函数
    XdaCategoryEnums(Long firstCategoryId, Long secondCategoryId, String secondCategoryName) {
        this.firstCategoryId = firstCategoryId;
        this.secondCategoryId = secondCategoryId;
        this.secondCategoryName = secondCategoryName;
    }

    // 获取一级分类 ID
    public Long getFirstCategoryId() {
        return firstCategoryId;
    }

    // 获取二级分类 ID
    public Long getSecondCategoryId() {
        return secondCategoryId;
    }

    // 获取二级分类名称
    public String getSecondCategoryName() {
        return secondCategoryName;
    }
}