package com.pinshang.qingyun.xda.search.enums;

import com.pinshang.qingyun.box.utils.EnumUtils;

import java.util.EnumSet;

/**
 * 手动刷新ES索引
 */
public enum XdaSearchEsIndexEnums {
//	CREATE_CATEGORY_INDEX("重建全部前台分类索引", "/manual/createAllCategoryIndex",0,0,0),
//    CREATE_COMMODITY_INDEX("重建全部商品索引", "/manual/createAllCommodityIndex",0,0,0),

    ALL_COMMODITY_TO_MYSQL("全部商品同步mysql", "/manual/allCommodityToMysql",0,0,0),
    COMMODITY_TO_MYSQL("指定商品同步mysql", "/manual/commodityToMysql",0,1,0),

    ALL_CATEGORY_TO_MYSQL("全量分类同步mysql", "/manual/allCategoryToSql",0,0,0),

//    CREATE_STORE_COMMODITY_INDEX("重建全部客户商品mysql(慎用)", "/manual/createAllStoreCommodityIndex",0,0,0),
//    UPDATE_STORE_COMMODITY_INDEX("更新指定客户的指定商品mysql", "/manual/updateStoreCommodityIndex",1,1,0),

    ALL_COMMODITY_SYNC_ES("全量商品同步ES", "/manual/allCommoditySyncEs",0,0,1),
    ALL_CATEGORY_SYNC_ES("全量品类同步ES", "/manual/allCategorySyncEs",0,0,1),

//    ALL_STORE_COMMODITY_SYNC_ES("全量客户商品同步ES", "/manual/allStoreCommoditySyncEs",1,1,1),


    ;
	//索引操作名称
	private String name;
	//请求路径
    private String url;
    //支持门店参数：
    private Integer shopFlag;
    //支持商品参数：
    private Integer commodityFlag;

    /** yyyy-MM-dd HH:mm:ss */
    private Integer updateTimeFlag;

    XdaSearchEsIndexEnums(String name, String url, Integer shopFlag, Integer commodityFlag, Integer updateTimeFlag) {
        this.name = name;
        this.url = url;
        this.shopFlag = shopFlag;
        this.commodityFlag = commodityFlag;
        this.updateTimeFlag = updateTimeFlag;
    }

    public String getName() {
        return name;
    }

    public String getUrl() {
        return url;
    }

    public Integer getShopFlag() {
        return shopFlag;
    }

    public Integer getCommodityFlag() {
        return commodityFlag;
    }

    public Integer getUpdateTimeFlag() {
        return updateTimeFlag;
    }

    public static XdaSearchEsIndexEnums get(String url) {
        return EnumUtils.fromEnumProperty(XdaSearchEsIndexEnums.class, "url", url);
    }
    
    public static String getName(String url) {
        XdaSearchEsIndexEnums o = get(url);
    	return null == o? "": o.name;
    }
    
    public static EnumSet<XdaSearchEsIndexEnums> allList() {
        return EnumSet.allOf(XdaSearchEsIndexEnums.class);
    }

}
