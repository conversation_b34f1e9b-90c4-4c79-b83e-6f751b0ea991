package com.pinshang.qingyun.xda.search.listener;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.kafka.base.BaseKafkaOnlineSwitchProcessor;
import com.pinshang.qingyun.kafka.base.KafkaMessageWrapper;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.xda.search.dto.CommodityAppStatusDTO;
import com.pinshang.qingyun.xda.search.enums.AppTypeEnums;
import com.pinshang.qingyun.xda.search.service.EsCategoryService;
import com.pinshang.qingyun.xda.search.service.EsCommodityService;
import com.pinshang.qingyun.xda.search.service.EsCommodityTextService;
import com.pinshang.qingyun.xda.search.service.EsXdSynonymWordService;
import com.pinshang.qingyun.xda.search.vo.ESXdaOrderCommodityVO;
import com.pinshang.qingyun.xda.search.vo.EsXdSearchSynonymWordKafkaVo;
import com.pinshang.qingyun.xda.search.vo.EsXdaCategoryChangeKafkaVO;
import com.pinshang.qingyun.xda.search.vo.EsXdaCommodityTextChangeKafkaVO;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/04/03
 * @Version 1.0
 */
@Component
@Slf4j
public class XdaEsListener extends BaseKafkaOnlineSwitchProcessor {
    @Autowired
    private EsCommodityTextService esCommodityTextService;

    @Autowired
    private EsCategoryService esCategoryService;

    @Autowired
    private EsCommodityService esCommodityService;

    @Autowired
    private EsXdSynonymWordService esXdSynonymWordService;
    @Autowired
    private RedissonClient redissonClient;

    @KafkaListener(
            topics = "${application.name.switch}"+KafkaTopicConstant.XDA_COMMODITY_TEXT_CHANGE_TOPIC
            ,errorHandler = "kafkaConsumerErrorHandler"
            ,containerFactory = "kafkaListenerContainerFactory")
    public void updateXdaCommodityText(String message) {
        log.info("topic:{}, message:{}", KafkaTopicConstant.XDA_COMMODITY_TEXT_CHANGE_TOPIC, message);
        try {
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            List<EsXdaCommodityTextChangeKafkaVO> kafkaVoList = JSON.parseArray(messageWrapper.getData().toString(), EsXdaCommodityTextChangeKafkaVO.class);
            if(SpringUtil.isNotEmpty(kafkaVoList)){
                esCommodityTextService.updateXdaCommodityText(kafkaVoList);
            }
        } catch (Exception e) {
            log.error("鲜达商品文描变更，kafkaTopic={}，消息消费异常：{}",KafkaTopicConstant.XDA_COMMODITY_TEXT_CHANGE_TOPIC, e);
        }
    }

    @KafkaListener(
            topics = "${application.name.switch}"+KafkaTopicConstant.XDA_CATEGORY_CHANGE_TOPIC
            ,errorHandler = "kafkaConsumerErrorHandler"
            ,containerFactory = "kafkaListenerContainerFactory")
    public void updateXdaCategory(String message) {
        log.info("topic:{}, message:{}", KafkaTopicConstant.XDA_CATEGORY_CHANGE_TOPIC, message);
        try {
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            List<EsXdaCategoryChangeKafkaVO> kafkaVoList = JSON.parseArray(messageWrapper.getData().toString(), EsXdaCategoryChangeKafkaVO.class);
            if(SpringUtil.isNotEmpty(kafkaVoList)){
                esCategoryService.updateXdaCategory(kafkaVoList);
            }
        } catch (Exception e) {
            log.error("鲜达品类变更，kafkaTopic={}，消息消费异常：{}",KafkaTopicConstant.XDA_CATEGORY_CHANGE_TOPIC, e);
        }
    }


    @KafkaListener(
            topics = "${application.name.switch}"+KafkaTopicConstant.XDA_ORDER_COMMODITY_CHANGE_TOPIC
            ,errorHandler = "kafkaConsumerErrorHandler"
            ,containerFactory = "kafkaListenerContainerFactory")
    public void updateXdaOrderCommodity(String message) {
        log.info("topic:{}, message:{}", KafkaTopicConstant.XDA_ORDER_COMMODITY_CHANGE_TOPIC, message);

        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        List<ESXdaOrderCommodityVO> kafkaVoList = JSON.parseArray(messageWrapper.getData().toString(), ESXdaOrderCommodityVO.class);
        if(SpringUtil.isNotEmpty(kafkaVoList)){

            RLock lock = redissonClient.getLock("XDASEARCH:updateXdaOrderCommodity");
            lock.lock(10L, TimeUnit.SECONDS);
            try {
                kafkaVoList.forEach(kafkaVo -> {
                    // 兼容历史消息,不传appType就默认是鲜达
                    if(kafkaVo.getAppType() == null) {
                        kafkaVo.setAppType(AppTypeEnums.XDA.getCode());
                    }
                });
                esCommodityTextService.updateXdaOrderCommodity(kafkaVoList);
            } finally {
                if (lock.isLocked()) {
                    lock.unlock();
                }
            }
        }
    }

//    @KafkaListener(
//            topics = "${application.name.switch}"+KafkaTopicConstant.TOB_COMMODITY_STOCK_CHANGE_TOPIC
//            ,errorHandler = "kafkaConsumerErrorHandler"
//            ,containerFactory = "kafkaListenerContainerFactory")
//    public void updateXdaStoreCommodity(String message) {
//        log.info("topic:{}, message:{}", KafkaTopicConstant.TOB_COMMODITY_STOCK_CHANGE_TOPIC, message);
//        try {
//            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
//            TobCommodityStockKafkaVo kafkaVo = JSON.parseObject(messageWrapper.getData().toString(), TobCommodityStockKafkaVo.class);
//            if(null != kafkaVo){
//               esXdaStoreCommodityService.updateXdaCommodityStock(kafkaVo);
//            }
//        } catch (Exception e) {
//            log.error("鲜达库存变更，kafkaTopic={}，消息消费异常：{}",KafkaTopicConstant.TOB_COMMODITY_STOCK_CHANGE_TOPIC, e);
//        }
//    }

    @KafkaListener( topics = "${application.name.switch}"+KafkaTopicConstant.ES_XDA_COMMODITY_APP_STATUS
            ,errorHandler = "kafkaConsumerErrorHandler"
            ,containerFactory = "kafkaListenerContainerFactory")
    public void commodityAppStatus(String message) {
        log.warn("commodityAppStatus上下架message = {}",  message);
        try {
            Thread.sleep(3000L);
        } catch (Exception e) {
            log.warn("commodityAppStatus上下架message = {}",  message, e);
        }
        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
        CommodityAppStatusDTO dto = JsonUtil.json2java(messageWrapper.getData().toString(), CommodityAppStatusDTO.class);

        RLock lock = redissonClient.getLock("XDASEARCH:commodityAppStatus");
        lock.lock(10L, TimeUnit.SECONDS);
        try {
            esCommodityService.commodityAppStatus(dto);
        } finally {
            if (lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    //客户商品表t_xda_store_commodity_es 不在维护

//    /**
//     * 产品价格调整  根据产品价格方案
//     * @param message
//     */
//    @KafkaListener(
//            topics = "${application.name.switch}"+KafkaTopicConstant.ES_XDA_COMMODITY_PRICE_ADJUST
//            ,errorHandler = "kafkaConsumerErrorHandler"
//            ,containerFactory = "kafkaListenerContainerFactory")
//    public void commodityPriceAdjust(String message) {
//        log.warn("commodityPrice商品价格message = {}",  message);
//        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
//
//        StoreCommodityAdjustPriceKafkaDTO dto = JsonUtil.json2java(messageWrapper.getData().toString(), StoreCommodityAdjustPriceKafkaDTO.class);
//
//        xdaStoreCommodityService.commodityPriceAdjust(dto);
//    }
//
//    @KafkaListener( topics = "${application.name.switch}"+KafkaTopicConstant.ES_XDA_STORE_STOP_OR_START
//            ,errorHandler = "kafkaConsumerErrorHandler"
//            ,containerFactory = "kafkaListenerContainerFactory")
//    public void storeStopOrStart(String message) {
//        log.warn("storeStopOrStart鲜达商户开启和停用message = {}",  message);
//        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
//
//        StoreStopOrStartDTO dto = JsonUtil.json2java(messageWrapper.getData().toString(), StoreStopOrStartDTO.class);
//
//        xdaStoreCommodityService.storeStopOrStart(dto);
//    }
//
//    @KafkaListener( topics = "${application.name.switch}"+KafkaTopicConstant.XDA_SEARCH_PRODUCT_PRICE_SWITCH_TOPIC
//            ,errorHandler = "kafkaConsumerErrorHandler"
//            ,containerFactory = "kafkaListenerContainerFactory")
//    public void productPriceSwitch(String message) {
//        log.warn("productPriceSwitch鲜达商户切换产品价格方案message = {}",  message);
//        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
//
//        ProductPriceSwitchDTO dto = JsonUtil.json2java(messageWrapper.getData().toString(), ProductPriceSwitchDTO.class);
//
//        xdaStoreCommodityService.productPriceSwitch(dto);
//    }

    @KafkaListener(topics = "${application.name.switch}"+KafkaTopicConstant.XD_SEARCH_SYNONYM_WORD_CHANGE_TOPIC
            ,errorHandler = "kafkaConsumerErrorHandler"
            ,containerFactory = "kafkaListenerContainerFactory")
    public void xdSearchSynonymWordChange(String message) {
        try {
            KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
            EsXdSearchSynonymWordKafkaVo kafkaVo = JsonUtil.json2java(messageWrapper.getData().toString(), EsXdSearchSynonymWordKafkaVo.class);
            if(kafkaVo == null){
                log.error("近义词变更索引增量更新消息空异常,topic={}", KafkaTopicConstant.XD_SEARCH_SYNONYM_WORD_CHANGE_TOPIC);
                return;
            }
            esXdSynonymWordService.synonymWordUpdateIndex(kafkaVo);
        } catch (Exception e) {
            log.error("近义词同步ES，kafkaTopic={}，消息消费异常：{}", KafkaTopicConstant.XD_SEARCH_SYNONYM_WORD_CHANGE_TOPIC, e.getMessage(), e);
        }
    }
    @Override
    public List<String> getKafkaIds() {
        return Arrays.asList(
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.XDA_COMMODITY_TEXT_CHANGE_TOPIC,
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.XDA_CATEGORY_CHANGE_TOPIC,
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.XDA_ORDER_COMMODITY_CHANGE_TOPIC,
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.TOB_COMMODITY_STOCK_CHANGE_TOPIC,
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.ES_XDA_COMMODITY_APP_STATUS,
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.ES_XDA_COMMODITY_PRICE_ADJUST,
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.ES_XDA_STORE_STOP_OR_START,
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.XDA_SEARCH_PRODUCT_PRICE_SWITCH_TOPIC,
                QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.XD_SEARCH_SYNONYM_WORD_CHANGE_TOPIC
                );
    }

}
