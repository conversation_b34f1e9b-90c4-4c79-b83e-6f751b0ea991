package com.pinshang.qingyun.xda.search.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.search.model.XdaCategoryEs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 鲜达前台品类
 *
 * <AUTHOR>
 *
 * @date 2019年11月21日
 */
@Mapper
@Repository
public interface XdaCategoryEsMapper extends MyMapper<XdaCategoryEs> {
    int batchUpdateByCateIdList(@Param("list") List<XdaCategoryEs> list);

    int batchInsert(@Param("list") List<XdaCategoryEs> list);
}
