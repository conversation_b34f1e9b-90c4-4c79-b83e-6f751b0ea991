package com.pinshang.qingyun.xda.search.mapper;

import com.pinshang.qingyun.base.mybatis.MyMapper;
import com.pinshang.qingyun.xda.search.model.XdaCommodityEs;
import com.pinshang.qingyun.xda.search.vo.XdaCommoditySoldOutUpdateVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 鲜达前台品类
 *
 * <AUTHOR>
 *
 * @date 2019年11月21日
 */
@Mapper
@Repository
public interface XdaCommodityEsMapper extends MyMapper<XdaCommodityEs> {

    /**
     * 更新商品状态 上下架
     * @param ids
     * @param appStatus
     * @return
     */
    Integer updateCommodityAppStatus(@Param("ids") List<Long> ids, @Param("appStatus") Integer appStatus);

    Integer batchUpdateCommodityText(@Param("list") List<XdaCommodityEs> list);

    Integer batchUpdateCommoditySoldOut(@Param("list") List<XdaCommoditySoldOutUpdateVO> list);

    List<XdaCommodityEs> selectCommodityEsByCommodityIds(@Param("commodityIds") List<Long> commodityIds);

    Integer batchInsert(@Param("list") List<XdaCommodityEs> list);

    void updateBatchById(@Param("list")List<XdaCommodityEs> list);

    Integer batchUpdateDeliveryDate(@Param("list") List<XdaCommodityEs> list);

    List<XdaCommodityEs> selectByPage(@Param("updateTime") Date updateTime, @Param("from") Integer from, @Param("offset") Integer offset);

}
