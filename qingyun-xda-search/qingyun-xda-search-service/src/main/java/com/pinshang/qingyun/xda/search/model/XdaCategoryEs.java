package com.pinshang.qingyun.xda.search.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@NoArgsConstructor
@Table(name="t_xda_category_es")
public class XdaCategoryEs {

    @Id
    private Long id;

    @ApiModelProperty("类id")
    private Long cateId;

    @ApiModelProperty("父id")
    private Long parentId;

    @ApiModelProperty("品类名称")
    private String cateName;

    @ApiModelProperty("品类级别")
    private Integer cateLevel;

    @ApiModelProperty("排序号")
    private Integer sortNum;

    private Date createTime;

    private Date updateTime;
}
