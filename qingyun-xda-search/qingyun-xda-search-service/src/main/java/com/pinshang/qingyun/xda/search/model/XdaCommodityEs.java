package com.pinshang.qingyun.xda.search.model;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.builder.EqualsBuilder;

import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Data
@NoArgsConstructor
@Table(name="t_xda_commodity_es")
public class XdaCommodityEs {

    @Id
    private Long id;

    private Long commodityId;

    private String commodityCode;

    @ApiModelProperty("前台品名")
    private String commodityAppName;

    @ApiModelProperty("副标题")
    private String commoditySubName;

    @ApiModelProperty("排序")
    private Integer sortNum;

    @ApiModelProperty("商品规格")
    private String commoditySpec;

    @ApiModelProperty("默认图片")
    private String defaultImageUrl;

    @ApiModelProperty("商品单位")
    private String commodityUnitName;

    @ApiModelProperty("2-8")
    private String deliveryDateRangeCode;
    @ApiModelProperty("T+2~T+8")
    private String deliveryDateRangeValue;

    @ApiModelProperty("批发 2-8")
    private String pfDeliveryDateRangeCode;
    @ApiModelProperty("批发 T+2~T+8")
    private String pfDeliveryDateRangeValue;


    @ApiModelProperty("前台一级分类")
    private Long xdaFirstCategoryId;

    @ApiModelProperty("前台二级分类")
    private Long xdaSecondCategoryId;

    @ApiModelProperty("0上架 1下架")
    private Integer appStatus;

    @ApiModelProperty("批发 0上架 1下架")
    private Integer pfAppStatus;

    @ApiModelProperty("前台品名和分类 中间加空格")
    private String commoditySearchName;

    @ApiModelProperty("前台一级分类名称")
    private String xdaFirstCategoryName;

    @ApiModelProperty("前台二级分类名称")
    private String xdaSecondCategoryName;

    @ApiModelProperty("订货范围前区间")
    private Integer deliveryDateAnteriorInterval;
    @ApiModelProperty("订货范围后区间")
    private Integer deliveryDateAfterInterval;

    @ApiModelProperty("批发 订货范围前区间")
    private Integer pfDeliveryDateAnteriorInterval;
    @ApiModelProperty("批发 订货范围后区间")
    private Integer pfDeliveryDateAfterInterval;

    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut1;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut2;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut3;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut4;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut5;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut6;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut7;

    private Date createTime;

    private Date updateTime;

    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (!(obj instanceof XdaCommodityEs)) return false;

        XdaCommodityEs xdaCommodityEs = (XdaCommodityEs) obj;
        return new EqualsBuilder()
                .append(this.commodityAppName, xdaCommodityEs.getCommodityAppName())
                .append(this.commoditySubName, xdaCommodityEs.getCommoditySubName())
                .append(this.sortNum, xdaCommodityEs.getSortNum())
                .append(this.commoditySpec, xdaCommodityEs.getCommoditySpec())
                .append(this.defaultImageUrl, xdaCommodityEs.getDefaultImageUrl())
                .append(this.commodityUnitName, xdaCommodityEs.getCommodityUnitName())
                .append(this.deliveryDateRangeCode, xdaCommodityEs.getDeliveryDateRangeCode())
                .append(this.deliveryDateRangeValue, xdaCommodityEs.getDeliveryDateRangeValue())
                .append(this.deliveryDateAnteriorInterval, xdaCommodityEs.getDeliveryDateAnteriorInterval())
                .append(this.deliveryDateAfterInterval, xdaCommodityEs.getDeliveryDateAfterInterval())
                .append(this.xdaFirstCategoryId, xdaCommodityEs.getXdaFirstCategoryId())
                .append(this.xdaSecondCategoryId, xdaCommodityEs.getXdaSecondCategoryId())
                .append(this.appStatus, xdaCommodityEs.getAppStatus())
                .append(this.pfAppStatus, xdaCommodityEs.getPfAppStatus())
                .append(this.pfDeliveryDateRangeCode, xdaCommodityEs.getPfDeliveryDateRangeCode())
                .append(this.pfDeliveryDateRangeValue, xdaCommodityEs.getPfDeliveryDateRangeValue())
                .append(this.pfDeliveryDateAnteriorInterval, xdaCommodityEs.getPfDeliveryDateAnteriorInterval())
                .append(this.pfDeliveryDateAfterInterval, xdaCommodityEs.getPfDeliveryDateAfterInterval())
                .isEquals();
    }
}
