package com.pinshang.qingyun.xda.search.repository;

import com.pinshang.qingyun.xda.search.constants.EsConstants;
import com.pinshang.qingyun.xda.search.document.EsXdaCommodity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4 10:53
 */
@Repository
@Slf4j
public class EsXdaCommodityDao {

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Autowired
    private EsXdaCommodityRepository esXdaCommodityRepository;


    public List<EsXdaCommodity> searchXdaCommodity(List<Long> commodityIdList,
                                                   Integer pageSize,
                                                   Boolean isPfStore,
                                                   Boolean commodityIdNeedSort) {

        if (null == pageSize || pageSize <= 0) {
            pageSize = EsConstants.AGGREGATION_TERMS_SIZE_1000;
        }
        if (CollectionUtils.isEmpty(commodityIdList)) {
            return Collections.emptyList();
        }
        NativeSearchQueryBuilder queryBuilder = new NativeSearchQueryBuilder();
        BoolQueryBuilder boolQueryShortBuilder = QueryBuilders.boolQuery();
        String fieldName = isPfStore ? "pfAppStatus" : "appStatus";
        boolQueryShortBuilder.must(QueryBuilders.termsQuery("commodityId", commodityIdList));
        boolQueryShortBuilder.must(QueryBuilders.termQuery(fieldName, 0));
        queryBuilder.withQuery(boolQueryShortBuilder);
        queryBuilder.withPageable(Pageable.ofSize(pageSize));
        SearchHits<EsXdaCommodity> searchHits = elasticsearchRestTemplate.search(queryBuilder.build(), EsXdaCommodity.class);
        // 解析结果 返回
        if (CollectionUtils.isEmpty(searchHits.getSearchHits())) {
            return Collections.emptyList();
        }
        List<EsXdaCommodity> searchOriginList = searchHits
                .getSearchHits()
                .stream()
                .map(SearchHit::getContent)
                .collect(Collectors.toList());
        // 需要保持 commodityIdList 原有的顺序
        List<EsXdaCommodity> sortList = searchOriginList;
        if (commodityIdNeedSort) {
            sortList = new ArrayList<>();
            // 将原始结果映射为 Map
            Map<Long, EsXdaCommodity> commodityMap = searchOriginList
                    .stream()
                    .collect(Collectors.toMap(EsXdaCommodity::getCommodityId, Function.identity(), (a, b) -> a));
            for (Long commodityId : commodityIdList) {
                EsXdaCommodity esXdaCommodity = commodityMap.get(commodityId);
                if (null != esXdaCommodity) {
                    sortList.add(esXdaCommodity);
                }
            }
        }
        return sortList;
    }


}
