package com.pinshang.qingyun.xda.search.service;

import com.alibaba.fastjson.JSONObject;
import com.pinshang.qingyun.base.constant.XdaAppVersionConstant;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.search.BaseSearchBizTypeEnum;
import com.pinshang.qingyun.base.search.dto.search.SearchCommodityClientDTO;
import com.pinshang.qingyun.base.search.dto.search.SearchCommodityClientReqDTO;
import com.pinshang.qingyun.base.search.dto.search.SearchCommodityClientRespDTO;
import com.pinshang.qingyun.base.search.dto.switcher.SearchSwitcherClientReqDTO;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.NumberUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.marketing.dto.mtCoupon.MtCouponToUseODTO;
import com.pinshang.qingyun.order.dto.tob.EsXdaCommoditySoldOutIDTO;
import com.pinshang.qingyun.order.dto.tob.EsXdaCommoditySoldOutODTO;
import com.pinshang.qingyun.order.service.ToBCommodityStockClient;
import com.pinshang.qingyun.store.dto.customer.StoreSelectODTO;
import com.pinshang.qingyun.xda.product.dto.StoreCommodityPriceODTO;
import com.pinshang.qingyun.xda.product.dto.category.FeignCategoryCommodityFieldIDTO;
import com.pinshang.qingyun.xda.product.dto.category.FeignCategoryCommodityFieldODTO;
import com.pinshang.qingyun.xda.product.dto.front.FeignXdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.product.service.XdaCategoryClient;
import com.pinshang.qingyun.xda.product.service.XdaCommodityFrontClient;
import com.pinshang.qingyun.xda.search.constants.EsConstants;
import com.pinshang.qingyun.xda.search.constants.XdaConstant;
import com.pinshang.qingyun.xda.search.document.EsXdaCategory;
import com.pinshang.qingyun.xda.search.document.EsXdaCommodity;
import com.pinshang.qingyun.xda.search.dto.CategoryGoodsReq;
import com.pinshang.qingyun.xda.search.dto.EsManualIDTO;
import com.pinshang.qingyun.xda.search.dto.KeyWordGoodsReq;
import com.pinshang.qingyun.xda.search.dto.commodity.XdaCommodityAppODTO;
import com.pinshang.qingyun.xda.search.dto.commodity.XdaCommodityODTO;
import com.pinshang.qingyun.xda.search.dto.commodity.XdaCommodityRenderReq;
import com.pinshang.qingyun.xda.search.dto.front.XdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.search.dto.front.XdaSearchAppIDTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryCommodityODTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryCommodityResODTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryODTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaSecondCategoryCommodityResODTO;
import com.pinshang.qingyun.xda.search.dto.front.commodityfront.XdaSerialCommodityDetailODTO;
import com.pinshang.qingyun.xda.search.enums.FromPageEnums;
import com.pinshang.qingyun.xda.search.enums.SalesStatusEnums;
import com.pinshang.qingyun.xda.search.enums.XdaCategoryEnums;
import com.pinshang.qingyun.xda.search.service.es.CategoryElasticSearchService;
import com.pinshang.qingyun.xda.search.service.es.CommodityElasticSearchService;
import com.pinshang.qingyun.xda.search.service.manager.EsXdaCategoryManagerService;
import com.pinshang.qingyun.xda.search.service.third.XdaSearchOperateService;
import com.pinshang.qingyun.xda.search.util.ReflectionUtils;
import com.pinshang.qingyun.xda.search.util.StopWatchUtil;
import com.pinshang.qingyun.xda.search.vo.SearchResultVo;
import com.pinshang.qingyun.xda.search.vo.category.SpecialCategoryInfo;
import com.pinshang.qingyun.xda.search.vo.info.SerialCommodityProcessInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.clients.elasticsearch7.ElasticsearchAggregations;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName AbstractSearchService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/2 20:51
 * @Version 1.0
 */
@Slf4j
public abstract class AbstractSearchService<D, T, E, R> implements ISearchService<D, T, E, R> {
    private final String[] XDA_CATEGORY_ID_ALIAS = {"xda_first_category_id", "xda_second_category_id"};
    private final String[] XDA_CATEGORY_ID = {"xdaFirstCategoryId", "xdaSecondCategoryId"};
    @Autowired
    private EsXdSynonymWordService esXdSynonymWordService;
    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;
    @Autowired
    private XdaCommodityRenderService xdaCommodityRenderService;
    @Autowired
    private XdaCategoryClient xdaCategoryClient;
    @Autowired
    private CategoryElasticSearchService categoryElasticSearchService;
    @Autowired
    private CommodityElasticSearchService commodityElasticSearchService;
    @Autowired
    private XdaCommodityFrontClient xdaCommodityFrontClient;
    @Autowired
    private ToBCommodityStockClient tobCommodityStockClient;
    @Autowired
    private StoreService storeService;
    @Autowired
    private EsCommodityService esCommodityService;
    @Autowired
    private XdaSearchOperateService xdaSearchOperateService;

    @Autowired
    private EsXdaCategoryManagerService esXdaCategoryManagerService;


    private static void buildStoreCategoryList(List<EsXdaCategory> esXdaCategories, List<XdaCategoryODTO> categoryList) {
        if (SpringUtil.isEmpty(esXdaCategories)) {
            return;
        }

        for (EsXdaCategory esXdaCategory : esXdaCategories) {
            XdaCategoryODTO xdaCategoryODTO = new XdaCategoryODTO();
            xdaCategoryODTO.setXdaCategoryId(esXdaCategory.getCateId());
            xdaCategoryODTO.setXdaCategoryName(esXdaCategory.getCateName());
            xdaCategoryODTO.setXdaCategorySort(esXdaCategory.getSortNum());
            categoryList.add(xdaCategoryODTO);
        }
    }


    public static List<XdaCategoryODTO> buildStoreAllCategoryList(List<EsXdaCategory> storeFirstCategorys, List<EsXdaCategory> storeSecondCategorys) {
        if (SpringUtil.isEmpty(storeFirstCategorys)) {
            return Collections.emptyList();
        }

        List<XdaCategoryODTO> categoryList = new ArrayList<>(storeFirstCategorys.size());

        Map<Long, List<EsXdaCategory>> parentIdMap = storeSecondCategorys.stream()
                .collect(Collectors.groupingBy(
                        EsXdaCategory::getParentId,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(EsXdaCategory::getSortNum))
                                        .collect(Collectors.toList())
                        )
                ));

        for (EsXdaCategory esXdaCategory : storeFirstCategorys) {
            XdaCategoryODTO xdaCategoryODTO = new XdaCategoryODTO();
            xdaCategoryODTO.setXdaCategoryId(esXdaCategory.getCateId());
            xdaCategoryODTO.setXdaCategoryName(esXdaCategory.getCateName());
            xdaCategoryODTO.setXdaCategorySort(esXdaCategory.getSortNum());
            categoryList.add(xdaCategoryODTO);

            List<EsXdaCategory> secondCategoryList = parentIdMap.get(esXdaCategory.getCateId());
            if (CollectionUtils.isEmpty(secondCategoryList)) {
                continue;
            }

            List<XdaCategoryODTO> xdaSecondCategoryList = buildXdaSecondCategoryList(esXdaCategory, secondCategoryList);
            xdaCategoryODTO.setXdaSecondCategoryList(xdaSecondCategoryList);
        }
        return categoryList;
    }

    private static List<XdaCategoryODTO> buildXdaSecondCategoryList(EsXdaCategory esXdaCategory, List<EsXdaCategory> secondCategoryList) {
        List<XdaCategoryODTO> xdaSecondCategoryList = new ArrayList<>(secondCategoryList.size());
        for (EsXdaCategory xdaCategory : secondCategoryList) {
            XdaCategoryODTO secondCategoryODTO = new XdaCategoryODTO();
            secondCategoryODTO.setXdaCategoryId(xdaCategory.getCateId());
            secondCategoryODTO.setXdaCategoryName(xdaCategory.getCateName());
            secondCategoryODTO.setXdaCategorySort(xdaCategory.getSortNum());
            secondCategoryODTO.setParentId(esXdaCategory.getCateId());
            xdaSecondCategoryList.add(secondCategoryODTO);
        }
        return xdaSecondCategoryList;
    }

    static List<XdaCategoryCommodityResODTO> buildDefaultOftenBuy(Long secondCategoryMyOftenBuy, String categoryName) {
        XdaCategoryCommodityResODTO oftenBuy = new XdaCategoryCommodityResODTO();
        oftenBuy.setXdaSecondCategoryId(secondCategoryMyOftenBuy);
        oftenBuy.setXdaSecondCategoryName(categoryName);
        oftenBuy.setXdaFirstCategoryId(XdaConstant.FIRST_CATEGORY_OFTEN_BUY);
        oftenBuy.setCommodityList(Collections.emptyList());
        return Arrays.asList(oftenBuy);
    }

    private static void buildAssignedCategoryCommodity(XdaCategoryEnums typeEnum, SerialCommodityProcessInfo serialCommodityProcessInfo, List<Long> commodityIds, List<XdaCategoryCommodityResODTO> xdaCategoryCommodityResODTOS) {

        StopWatch stopWatch = StopWatchUtil.begin();

        List<XdaCategoryCommodityODTO> categoryCommoditys = serialCommodityProcessInfo.getXdaCategoryCommodityODTOS();

        if (CollectionUtils.isNotEmpty(commodityIds) && CollectionUtils.isNotEmpty(categoryCommoditys)) {

            Map<Long, XdaCategoryCommodityODTO> groupByCommodityId = categoryCommoditys.stream()
                    .collect(Collectors.toMap(XdaCategoryCommodityODTO::getCommodityId, Function.identity()));
            Map<Long, XdaCategoryCommodityODTO> serialCommodityMap = serialCommodityProcessInfo.getSerialCommodityMap();
            if (SpringUtil.isNotEmpty(serialCommodityMap)) {
                groupByCommodityId.putAll(serialCommodityMap);
            }

            XdaCategoryCommodityResODTO commodityRes = new XdaCategoryCommodityResODTO();
            commodityRes.setXdaFirstCategoryId(typeEnum.getFirstCategoryId());
            commodityRes.setXdaSecondCategoryId(typeEnum.getSecondCategoryId());
            commodityRes.setXdaSecondCategoryName(typeEnum.getSecondCategoryName());

            // 排序
            List<XdaCategoryCommodityODTO> commoditys = commodityIds.stream()
                    .map(groupByCommodityId::get)
                    .filter(Objects::nonNull)
                    .sorted(commoditySort())
                    .collect(Collectors.toList());
            commodityRes.setCommodityList(commoditys);

            xdaCategoryCommodityResODTOS.add(0, commodityRes);
        }

        StopWatchUtil.end("构建" + typeEnum.getSecondCategoryName() + "分类商品信息", stopWatch);
    }

    static Comparator<XdaCategoryCommodityODTO> commoditySort() {
        return Comparator.comparing(XdaCategoryCommodityODTO::getSalesStatus)
                .thenComparing(XdaCategoryCommodityODTO::getSortNum)
                .thenComparing((x, y) -> {
                    if (x.getUpdateTime() == null && y.getUpdateTime() == null) {
                        return 0;
                    } else if (x.getUpdateTime() == null) {
                        return 1;
                    } else if (y.getUpdateTime() == null) {
                        return -1;
                    } else {
                        return y.getUpdateTime().compareTo(x.getUpdateTime());
                    }
                });
    }

    static Comparator<XdaCommodityODTO> xdaCommoditySort() {
        return Comparator.comparing(XdaCommodityODTO::getSalesStatus)
                .thenComparing(XdaCommodityODTO::getSortNum)
                .thenComparing((x, y) -> {
                    if (x.getUpdateTime() == null && y.getUpdateTime() == null) {
                        return 0;
                    } else if (x.getUpdateTime() == null) {
                        return 1;
                    } else if (y.getUpdateTime() == null) {
                        return -1;
                    } else {
                        return y.getUpdateTime().compareTo(x.getUpdateTime());
                    }
                });
    }

    private static SearchCommodityClientReqDTO getClientReqDTO(KeyWordGoodsReq param) {
        SearchCommodityClientReqDTO clientReqDTO = BeanCloneUtils.copyTo(param, SearchCommodityClientReqDTO.class);
        clientReqDTO.setBusinessType(BaseSearchBizTypeEnum.XIAN_DA.getCode());
        clientReqDTO.setKeyword(param.getKeyWord());
        // param pageNo 从0 开始
        clientReqDTO.setPageNum(1);
        // 默认一个关键词能匹配最多 500 条数据，因为没存当前门店关联的商品，这里先默认500条商品都在当前门店上线
        clientReqDTO.setPageSize(500);
        return clientReqDTO;
    }

    @Override
    public D search(T t) {
        return null;
    }

    @Override
    public <E1> E1 leftCategoryList(R r) {
        return null;
    }

    /**
     * 关键字搜索
     * 近义词与普通搜索
     *
     * @param param
     * @return
     */
    protected SearchResultVo keyWordSearch(KeyWordGoodsReq param, List<Long> storeCommodityIdList) {
        String keyWord = param.getKeyWord();
        keyWord = querySynonymWord(keyWord);
        Boolean isPfStore = storeService.isPfsStore(param.getStoreId());
        boolean switchBaseSearch = isSwitchBaseSearch(param);
        boolean needQueryFromXdaEs = !switchBaseSearch;
        List<EsXdaCommodity> pageInfo = new ArrayList<>();
        if (switchBaseSearch) {
            // 切 base-search 查询 List 集合,200 条数据
            try {
                pageInfo = searchFromBaseSearch(param, storeCommodityIdList, isPfStore);
            } catch (Exception e) {
                // 记录日志，且 异常后 查 xda-es 兜底
                log.error("base-search查询异常：message={}，error={}", keyWord, e.getMessage(), e);
                needQueryFromXdaEs = true;
            }
        }
        if (needQueryFromXdaEs) {
            pageInfo = searchFromXdaEs(storeCommodityIdList, isPfStore, keyWord);
        }
        return new SearchResultVo(pageInfo, Long.valueOf(pageInfo.size()), pageInfo.size() > 0);
    }

    public boolean isSwitchBaseSearch(Long userId) {
        return xdaSearchOperateService.checkGrayExperiment(SearchSwitcherClientReqDTO
                .builder()
                .businessType(BaseSearchBizTypeEnum.XIAN_DA.getCode())
                .userId(userId)
                .build());
    }

    public boolean isSwitchBaseSearch(KeyWordGoodsReq param) {
        return xdaSearchOperateService.checkGrayExperiment(SearchSwitcherClientReqDTO
                .builder()
                .businessType(BaseSearchBizTypeEnum.XIAN_DA.getCode())
                .userId(param.getUserId())
                .build());
    }

    private List<EsXdaCommodity> searchFromXdaEs(List<Long> storeCommodityIdList, Boolean isPfStore, String keyWord) {
        // 根据keyword一次最多搜索出来200条，再排序
        Pageable pageable = PageRequest.of(0, 200);
        List<EsXdaCommodity> pageInfo = new ArrayList<>();
        if (isPfStore) {
            QueryBuilder query = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("pfAppStatus", 0))
                    .must(QueryBuilders.termsQuery("commodityId", storeCommodityIdList))
                    .must(QueryBuilders.matchQuery("commoditySearchName", keyWord));
            pageInfo = pageSearch(pageable, query);
        }else {
            QueryBuilder query = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("appStatus", 0))
                    .must(QueryBuilders.termsQuery("commodityId", storeCommodityIdList))
                    .must(QueryBuilders.matchQuery("commoditySearchName", keyWord));
            pageInfo = pageSearch(pageable, query);
        }
        return pageInfo;
    }

    private List<EsXdaCommodity> searchFromBaseSearch(KeyWordGoodsReq param,
                                                      List<Long> storeCommodityIdList,
                                                      Boolean isPfStore) {
        SearchCommodityClientReqDTO clientReqDTO = getClientReqDTO(param);
        SearchCommodityClientRespDTO clientRespDTO = xdaSearchOperateService.searchByCondition(clientReqDTO);
        List<SearchCommodityClientDTO> clientDTOList = Optional.ofNullable(clientRespDTO)
                .map(SearchCommodityClientRespDTO::getCommodityList)
                .orElse(Collections.emptyList());
        return getEsXdaCommodityListById(clientDTOList, storeCommodityIdList, isPfStore);
    }

    private List<EsXdaCommodity> getEsXdaCommodityListById(List<SearchCommodityClientDTO> clientDTOList,
                                                           List<Long> storeCommodityIdList,
                                                           Boolean isPfStore) {
        // 保持base-search 原有的顺序
        List<Long> commodityIdList = new ArrayList<>();
        for (SearchCommodityClientDTO clientDTO : clientDTOList) {
            if (storeCommodityIdList.contains(clientDTO.getCommodityId())) {
                // 当前门店的商品
                commodityIdList.add(clientDTO.getCommodityId());
            }
        }
        BoolQueryBuilder boolQueryShortBuilder = QueryBuilders.boolQuery();
        if (Boolean.TRUE.equals(isPfStore)) {
            boolQueryShortBuilder.must(QueryBuilders.termQuery("pfAppStatus", 0));
        } else {
            boolQueryShortBuilder.must(QueryBuilders.termQuery("appStatus", 0));
        }
        boolQueryShortBuilder.filter(QueryBuilders.termsQuery("commodityId", commodityIdList));
        List<EsXdaCommodity> esXdaCommodityList = pageSearch(PageRequest.of(0, EsConstants.AGGREGATION_TERMS_SIZE_1000), boolQueryShortBuilder);
        Map<Long, EsXdaCommodity> resultMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(esXdaCommodityList)) {
            for (EsXdaCommodity es : esXdaCommodityList) {
                resultMap.put(es.getCommodityId(), es);
            }
        }
        // 按 commodityIdList 顺序返回
        List<EsXdaCommodity> sortedList = new ArrayList<>();
        for (Long id : commodityIdList) {
            if (resultMap.containsKey(id)) {
                EsXdaCommodity esXdShopCommodity = resultMap.get(id);
                sortedList.add(esXdShopCommodity);
            }
        }
        return sortedList;
    }

    /**
     * 关键字搜索
     * 近义词与普通搜索
     *
     * @param param
     * @return
     */
    protected SearchResultVo couponUseKeyWordSearch(KeyWordGoodsReq param, List<Long> storeCommodityIdList, MtCouponToUseODTO mtCouponToUseODTO) {
        String keyWord = param.getKeyWord();
        keyWord = querySynonymWord(keyWord);
        Pageable pageable = PageRequest.of(0, EsConstants.ES_MAX_SIZE);

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        if(storeService.isPfsStore(param.getStoreId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("pfAppStatus", 0));
        }else {
            boolQueryBuilder.filter(QueryBuilders.termQuery("appStatus", 0));
        }

        boolQueryBuilder.filter(QueryBuilders.termsQuery("commodityId", storeCommodityIdList));
        if(StringUtils.isNotBlank(keyWord)){
            boolQueryBuilder.filter(QueryBuilders.matchQuery("commoditySearchName", keyWord));
        }

        // 优惠券下面的商品过滤
        if(CollectionUtils.isNotEmpty(mtCouponToUseODTO.getCategoryIdList())){
            boolQueryBuilder.filter(QueryBuilders.termsQuery("xdaSecondCategoryId", mtCouponToUseODTO.getCategoryIdList()));
        }

        if(CollectionUtils.isNotEmpty(mtCouponToUseODTO.getInCommodityIdList())){
            boolQueryBuilder.filter(QueryBuilders.termsQuery("commodityId", mtCouponToUseODTO.getInCommodityIdList()));
        }

        if(CollectionUtils.isNotEmpty(mtCouponToUseODTO.getNotInCommodityIdList())){
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("commodityId", mtCouponToUseODTO.getNotInCommodityIdList()));
        }

        List<EsXdaCommodity> pageInfo = pageSearch(pageable, boolQueryBuilder);
        return new SearchResultVo(pageInfo, Long.valueOf(pageInfo.size()), pageInfo.size() > 0);
    }

    /**
     * 关键字搜索对应的近义词查询
     *
     * @param keyWord
     */
    protected String querySynonymWord(String keyWord) {
        try {
            List<String> wordList = esXdSynonymWordService.querySynonymWord(keyWord);
            if (SpringUtil.isNotEmpty(wordList)) {
                keyWord = StringUtils.join(wordList, " ");
            }

        } catch (Exception e) {
            log.error("关键字搜索查询近义词异常：{}，异常详情", e.getMessage(), e);
        }
        return keyWord;
    }

    /**
     * 基础分页搜索
     *
     * @param pageable
     * @param queryBuilder
     * @return
     */
    protected List<EsXdaCommodity> pageSearch(Pageable pageable, QueryBuilder queryBuilder) {
        /*//商品检索排序处理
        Long interval = ReflectionUtils.dateOfCalculation(req.getOrderTime());
        String fieldName = "soldOut";
        if (interval > 0) {
            fieldName = fieldName + interval;
        }
        String sortScript = "if(doc['deliveryDateAfterInterval'].size() == 0 || doc['deliveryDateAnteriorInterval'].size() == 0 || doc['" + fieldName + "'].size() == 0){return 3;}" +
                "else if(params.factor > doc['deliveryDateAfterInterval'].value){return 3;}" +
                "else if( params.factor < doc['deliveryDateAnteriorInterval'].value){return 3;}" +
                "else if(doc['" + fieldName + "'].value == true){return 1;}else{return 2;}";
        Script script = new Script(Script.DEFAULT_SCRIPT_TYPE, "painless", sortScript, Collections.singletonMap("factor", interval));*/
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        nativeSearchQueryBuilder.withQuery(queryBuilder)
                .withPageable(pageable);
                //.withSorts(new ScriptSortBuilder(script, ScriptSortBuilder.ScriptSortType.NUMBER).order(SortOrder.ASC), new ScoreSortBuilder().order(SortOrder.DESC));

        /*return esXdShopCommodityRepository.search(nativeSearchQueryBuilder.build());*/
        List<EsXdaCommodity> list = new ArrayList<>();
        SearchHits<EsXdaCommodity> search = elasticsearchTemplate.search(nativeSearchQueryBuilder.build(), EsXdaCommodity.class);
        List<SearchHit<EsXdaCommodity>> searchHits = search.getSearchHits();
        if (CollectionUtils.isNotEmpty(searchHits)) {
            for (SearchHit<EsXdaCommodity> searchHit : searchHits) {
                EsXdaCommodity es = searchHit.getContent();
                list.add(es);
            }
        }
        return list;
    }

    /**
     * 优惠券去使用
     * @param keyWordGoodsReq
     * @param esXdaCommodityList
     * @param storeCommodityPriceList
     * @return
     */
    protected List<XdaCommodityODTO> buildCouponUseGoodsList(KeyWordGoodsReq keyWordGoodsReq, List<EsXdaCommodity> esXdaCommodityList, List<StoreCommodityPriceODTO> storeCommodityPriceList) {
        List<XdaCommodityODTO> xdaCommodityInfoODTOS = getXdaCommodityODTOS(keyWordGoodsReq, esXdaCommodityList, storeCommodityPriceList);
        return xdaCommodityInfoODTOS;

    }

    /**
     * 组装商品列表(分类页商品、搜素框)
     * @param keyWordGoodsReq
     * @param esXdaCommodityList
     * @param storeCommodityPriceList
     * @return
     */
    @NotNull
    protected List<XdaCommodityODTO> buildGoodsList(KeyWordGoodsReq keyWordGoodsReq, List<EsXdaCommodity> esXdaCommodityList, List<StoreCommodityPriceODTO> storeCommodityPriceList) {
        List<XdaCommodityODTO> xdaCommodityInfoODTOS = getXdaCommodityODTOS(keyWordGoodsReq, esXdaCommodityList, storeCommodityPriceList);

        // 补充商品信息
        replenishCommodityInfo(xdaCommodityInfoODTOS, keyWordGoodsReq.getStoreId());
        return xdaCommodityInfoODTOS;

    }

    /**
     * 组装商品基本信息
     * @param keyWordGoodsReq
     * @param esXdaCommodityList
     * @param storeCommodityPriceList
     * @return
     */
    private  List<XdaCommodityODTO> getXdaCommodityODTOS(KeyWordGoodsReq keyWordGoodsReq, List<EsXdaCommodity> esXdaCommodityList, List<StoreCommodityPriceODTO> storeCommodityPriceList) {
        Map<Long, BigDecimal> priceMap = storeCommodityPriceList.stream().collect(Collectors.toMap(StoreCommodityPriceODTO::getCommodityId,StoreCommodityPriceODTO::getCommodityPrice,(key1 , key2)-> key2));
        List<Long> commodityIdList = esXdaCommodityList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

        StoreSelectODTO store = storeService.getStoreById(keyWordGoodsReq.getStoreId());
        Integer businessType = getSafeBusinessType(store.getBusinessType());

        // 查询库 存信息，用于渲染
        EsXdaCommoditySoldOutIDTO esXdaCommoditySoldOutIDTO = new EsXdaCommoditySoldOutIDTO();
        esXdaCommoditySoldOutIDTO.setCommodityIdList(commodityIdList);
        esXdaCommoditySoldOutIDTO.setType(YesOrNoEnums.YES.getCode());
        esXdaCommoditySoldOutIDTO.setDate(keyWordGoodsReq.getOrderTime());
        esXdaCommoditySoldOutIDTO.setBusinessType(businessType);
        esXdaCommoditySoldOutIDTO.setLogisticsCenterId(keyWordGoodsReq.getLogisticsCenterId());
        List<EsXdaCommoditySoldOutODTO> list = tobCommodityStockClient.queryCommodityInventory(esXdaCommoditySoldOutIDTO);
        Map<Long, Integer> soldOutMap = list.stream().collect(Collectors.toMap(EsXdaCommoditySoldOutODTO::getCommodityId,EsXdaCommoditySoldOutODTO::getSoldOut,(key1 , key2)-> key2));

        StopWatch stopWatch = StopWatchUtil.begin();

        // 是否批发客户
        Boolean isPfsStore = storeService.isPfsStore(keyWordGoodsReq.getStoreId());
        //根据前端传进来的订单日期获取对应的库存字段
        Long interval = ReflectionUtils.dateOfCalculation(keyWordGoodsReq.getOrderTime());

        List<XdaCommodityODTO> xdaCommodityInfoODTOS = esXdaCommodityList.stream().map(esXdCommodity -> {

            XdaCommodityODTO xdaCommodityInfoODTO = JSONObject.parseObject(JSONObject.toJSONString(esXdCommodity), XdaCommodityODTO.class);
            xdaCommodityInfoODTO.setCommodityPrice(priceMap.get(esXdCommodity.getCommodityId()));
            xdaCommodityInfoODTO.setCommodityIdStr(String.valueOf(esXdCommodity.getCommodityId()));
            xdaCommodityInfoODTO.setCommodityName(esXdCommodity.getCommodityAppName());
            xdaCommodityInfoODTO.setCommoditySubName(esXdCommodity.getCommoditySubName());
            Integer sortNum = esXdCommodity.getSortNum();
            xdaCommodityInfoODTO.setSortNum(Objects.nonNull(sortNum) ? sortNum : 100000);
            xdaCommodityInfoODTO.setImageUrl(esXdCommodity.getDefaultImageUrl());
            xdaCommodityInfoODTO.setXdaFirstCategoryId(esXdCommodity.getXdaFirstCategoryId());
            xdaCommodityInfoODTO.setXdaSecondCategoryId(esXdCommodity.getXdaSecondCategoryId());
            xdaCommodityInfoODTO.setAppStatus(esXdCommodity.getAppStatus());

            Integer deliveryDateAnteriorInterval = null;
            Integer deliveryDateAfterInterval = null;
            String deliveryDateRangeCode = "";
            String deliveryDateRangeValue = "";
            if(isPfsStore) {
                deliveryDateAnteriorInterval = esXdCommodity.getPfDeliveryDateAnteriorInterval();
                deliveryDateAfterInterval = esXdCommodity.getPfDeliveryDateAfterInterval();
                deliveryDateRangeCode = esXdCommodity.getPfDeliveryDateRangeCode();
                deliveryDateRangeValue = esXdCommodity.getPfDeliveryDateRangeValue();
            }else {
                deliveryDateAnteriorInterval = esXdCommodity.getDeliveryDateAnteriorInterval();
                deliveryDateAfterInterval = esXdCommodity.getDeliveryDateAfterInterval();
                deliveryDateRangeCode = esXdCommodity.getDeliveryDateRangeCode();
                deliveryDateRangeValue = esXdCommodity.getDeliveryDateRangeValue();
            }

            // 数据异常处理，异步重新刷新commodityEs表
            if(deliveryDateAnteriorInterval == null || deliveryDateAfterInterval == null) {
                esCommodityService.commodityToMysqlAsync(Collections.singletonList(esXdCommodity.getCommodityId()));
                xdaCommodityInfoODTO.setSalesStatus(SalesStatusEnums.SALES_STATUS_3.getCode());
            }else {

                // 设置送货日期区间,XdaCommodityRenderService.renderXdaCommodityInfo 调用
                xdaCommodityInfoODTO.setDeliveryDateRangeCode(deliveryDateRangeCode);
                xdaCommodityInfoODTO.setDeliveryDateRangeValue(deliveryDateRangeValue);

                //当前日期和订单日期间隔不在这个区间内 设置为3-当前送货日期不支持订货的商品
                if (interval.compareTo(Long.valueOf(deliveryDateAnteriorInterval)) < 0
                        || interval.compareTo(Long.valueOf(deliveryDateAfterInterval)) > 0) {
                    xdaCommodityInfoODTO.setSalesStatus(SalesStatusEnums.SALES_STATUS_3.getCode());
                }
                //当前日期和订单日期间隔在这个区间内 但是没有库存的 设置为2-已抢光的商品
                if (interval.compareTo(Long.valueOf(deliveryDateAnteriorInterval)) >= 0
                        && interval.compareTo(Long.valueOf(deliveryDateAfterInterval)) <= 0) {
                    //Boolean soldout = ReflectionUtils.getSoldOutMethod(esXdCommodity, interval);
                    Boolean soldout = (soldOutMap.get(esXdCommodity.getCommodityId()) != null ?
                            (YesOrNoEnums.YES.getCode().equals(soldOutMap.get(esXdCommodity.getCommodityId())) ? true : false) : false);
                    //有库存 设置为1-正常可订货的商品
                    if (Boolean.TRUE.equals(soldout)) {
                        xdaCommodityInfoODTO.setSalesStatus(SalesStatusEnums.SALES_STATUS_1.getCode());
                    }
                    //无库存 设置为2-已抢光的商品
                    if (Boolean.FALSE.equals(soldout)) {
                        xdaCommodityInfoODTO.setSalesStatus(SalesStatusEnums.SALES_STATUS_2.getCode());
                    }
                }
            }

            return xdaCommodityInfoODTO;
        }).collect(Collectors.toList());

        StopWatchUtil.end("处理商户商品信息", stopWatch);
        return xdaCommodityInfoODTOS;
    }

    /**
     * 渲染商品信息(搜素框、优惠券去使用)
     * @param keyWordGoodsReq
     * @param xdaCommodityInfoODTOS
     */
    protected void renderXdaCommodity(KeyWordGoodsReq keyWordGoodsReq, List<XdaCommodityODTO> xdaCommodityInfoODTOS) {
        //渲染商品信息
        XdaCommodityRenderReq req = XdaCommodityRenderReq.builder()
                .fromPage(FromPageEnums.XDA_APP_CATEGORY_COMMODITY_LIST.getCode())
                .storeId(keyWordGoodsReq.getStoreId())
                .orderTime(keyWordGoodsReq.getOrderTime())
                //http://192.168.0.213/zentao/story-view-11306.html###
                .defaultImageSize(null)
                .build();
        if (null != keyWordGoodsReq.getXdaFirstCategoryId()) {
            req.setNeedCartQuantity(true);
        }
        xdaCommodityRenderService.renderXdaCommodityInfo(req, xdaCommodityInfoODTOS);
    }

    protected void renderXdaCommodityCategory(KeyWordGoodsReq keyWordGoodsReq, List<XdaCommodityODTO> xdaCommodityInfoODTOS) {

        StopWatch stopWatch = StopWatchUtil.begin();

        //渲染商品信息
        XdaCommodityRenderReq req = XdaCommodityRenderReq.builder()
                .fromPage(FromPageEnums.XDA_APP_CATEGORY_COMMODITY_LIST.getCode())
                .storeId(keyWordGoodsReq.getStoreId())
                .orderTime(keyWordGoodsReq.getOrderTime())
                //http://192.168.0.213/zentao/story-view-11306.html###
                .defaultImageSize(null)
                .build();
        if (null != keyWordGoodsReq.getXdaFirstCategoryId()) {
            req.setNeedCartQuantity(true);
        }
        xdaCommodityRenderService.renderXdaCommodityInfo(req, xdaCommodityInfoODTOS);

        StopWatchUtil.end("渲染商品信息", stopWatch);
    }

    protected void renderXdaThCommodityCategory(KeyWordGoodsReq keyWordGoodsReq, List<XdaCommodityODTO> xdaCommodityInfoODTOS) {

        StopWatch stopWatch = StopWatchUtil.begin();

        //渲染商品信息
        XdaCommodityRenderReq req = XdaCommodityRenderReq.builder()
                .fromPage(FromPageEnums.XDA_APP_CATEGORY_COMMODITY_LIST.getCode())
                .storeId(keyWordGoodsReq.getStoreId())
                .orderTime(keyWordGoodsReq.getOrderTime())
                //http://192.168.0.213/zentao/story-view-11306.html###
                .defaultImageSize(null)
                .build();
        if (null != keyWordGoodsReq.getXdaFirstCategoryId()) {
            req.setNeedCartQuantity(true);
        }
        xdaCommodityRenderService.renderXdaThCommodityInfo(req, xdaCommodityInfoODTOS);

        StopWatchUtil.end("渲染特惠商品信息", stopWatch);
    }

    protected List<Long> getXdaStoreCategoryIdList(QueryBuilder storeTermQueryBuilder, Integer level) {
        // xd_first_category_id 为聚合查询取的别名
        int size = level == 0 ? EsConstants.AGGREGATION_TERMS_SIZE : EsConstants.AGGREGATION_TERMS_SIZE_1000;
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder()
                .withQuery(storeTermQueryBuilder)
                .addAggregation(AggregationBuilders.terms(XDA_CATEGORY_ID_ALIAS[level]).field(XDA_CATEGORY_ID[level]).size(size))
                .withPageable(PageRequest.of(0, 1));

        SearchHits<EsXdaCommodity> search = elasticsearchTemplate.search(nativeSearchQueryBuilder.build(), EsXdaCommodity.class);

        Map<String, Aggregation> aggMap = ((ElasticsearchAggregations) search.getAggregations()).aggregations().asMap();
        ParsedLongTerms xdaCategoryTerms = (ParsedLongTerms) aggMap.get(XDA_CATEGORY_ID_ALIAS[level]);
        List<Long> xdaCategoryIdList = xdaCategoryTerms.getBuckets().stream()
                .map(bucket -> (Long) bucket.getKeyAsNumber())
                .distinct()
                .collect(Collectors.toList());

        return xdaCategoryIdList;
    }

    /**
     * 补充商品信息
     *
     * @param xdaCommodityInfoODTOS
     */
    public void replenishCommodityInfo(List<XdaCommodityODTO> xdaCommodityInfoODTOS, Long storeId) {

        StopWatch stopWatch = StopWatchUtil.begin();

        //补足字段
        List<Long> commodityIdList = xdaCommodityInfoODTOS.stream()
                .map(XdaCommodityODTO::getCommodityId).collect(Collectors.toList());

        FeignCategoryCommodityFieldIDTO feignCategoryCommodityFieldIDTO = new FeignCategoryCommodityFieldIDTO();
        feignCategoryCommodityFieldIDTO.setCommodityIdList(commodityIdList);
        feignCategoryCommodityFieldIDTO.setStoreId(storeId);

        //补充缺失的字段
        List<FeignCategoryCommodityFieldODTO> feignCategoryCommodityFieldODTOS = xdaCategoryClient.queryCategoryCommodityField(feignCategoryCommodityFieldIDTO);
        Map<Long, FeignCategoryCommodityFieldODTO> groupByCommodityId = feignCategoryCommodityFieldODTOS.stream()
                .collect(Collectors.toMap(FeignCategoryCommodityFieldODTO::getCommodityId, Function.identity()));

        if (CollectionUtils.isEmpty(feignCategoryCommodityFieldODTOS)) {
            return;
        }

        List<XdaCommodityODTO> remove = new ArrayList<>();
        for (XdaCommodityODTO xdaCommodityODTO : xdaCommodityInfoODTOS) {
            Long commodityId = xdaCommodityODTO.getCommodityId();
            FeignCategoryCommodityFieldODTO feignCategoryCommodityFieldODTO = groupByCommodityId.get(commodityId);

            if (Objects.isNull(feignCategoryCommodityFieldODTO)) {
                remove.add(xdaCommodityODTO);
                log.info("商品数据不全，commodityId:[{}]", commodityId);
                continue;
            }

            xdaCommodityODTO.setCommoditySpec(feignCategoryCommodityFieldODTO.getCommoditySpec());
            xdaCommodityODTO.setCommodityCode(feignCategoryCommodityFieldODTO.getCommodityCode());
            xdaCommodityODTO.setSalesBoxCapacity(feignCategoryCommodityFieldODTO.getSalesBoxCapacity());
            xdaCommodityODTO.setIsQuickFreeze(feignCategoryCommodityFieldODTO.getIsQuickFreeze());
            xdaCommodityODTO.setIsWeight(feignCategoryCommodityFieldODTO.getIsWeight());
            xdaCommodityODTO.setCommodityPackageSpec(feignCategoryCommodityFieldODTO.getCommodityPackageSpec());
            xdaCommodityODTO.setCommodityWeight(feignCategoryCommodityFieldODTO.getCommodityWeight());
            xdaCommodityODTO.setStorageCondition(feignCategoryCommodityFieldODTO.getStorageCondition());
            xdaCommodityODTO.setQualityDays(feignCategoryCommodityFieldODTO.getQualityDays());
            xdaCommodityODTO.setCommodityUnitName(feignCategoryCommodityFieldODTO.getCommodityUnitName());
            xdaCommodityODTO.setImageUrl(feignCategoryCommodityFieldODTO.getImageUrl());
            xdaCommodityODTO.setSaleQuantity(feignCategoryCommodityFieldODTO.getSaleQuantity());
            xdaCommodityODTO.setSerialCommodityId(feignCategoryCommodityFieldODTO.getSerialCommodityId());
            xdaCommodityODTO.setUpdateTime(feignCategoryCommodityFieldODTO.getUpdateTime());
        }

        // xdaCommodityInfoODTOS.removeAll(remove);
        // 改成使用id删除 removeIdList
        List<Long> removeIdList = remove.stream()
                .map(XdaCommodityAppODTO::getCommodityId)
                .collect(Collectors.toList());
        xdaCommodityInfoODTOS.removeIf(item -> removeIdList.contains(item.getCommodityId()));

        StopWatchUtil.end("补充商品信息", stopWatch);
    }

    @Deprecated
    protected List<XdaCategoryODTO> getEsFirstCategoryList(CategoryGoodsReq categoryGoodsReq) {
        Long storeId = categoryGoodsReq.getShopId();
        // 产品价格方案下面的商品id集合
        List<Long> storeCommodityIdList = getXdaStoreCategoryIdList(storeId);

        //获取商户商品一级分类 idList
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termsQuery("commodityId", storeCommodityIdList))
                .must(QueryBuilders.termQuery("appStatus", 0));
        List<Long> xdaStoreFirstCategoryIdList = getXdaStoreCategoryIdList(boolQueryBuilder, 0);

        List<EsXdaCategory> storeFirstCategorys = listFirstCategoryByIdsFromES(xdaStoreFirstCategoryIdList,1);

        List<XdaCategoryODTO> categoryList = new ArrayList<>();

        buildStoreCategoryList(storeFirstCategorys, categoryList);

        return categoryList;
    }

    public List<Long> getXdaStoreCategoryIdList(Long storeId) {
        // 查询产品价格方案信息，渲染价格
        List<StoreCommodityPriceODTO> storeCommodityPriceList = xdaCommodityFrontClient.getStoreCommodityPrice(storeId);
        if (CollectionUtils.isEmpty(storeCommodityPriceList)) {
            // 如果客户产品价格方案下面商品没有，返回空
            return Collections.emptyList();
        }

        // 产品价格方案下面的商品id集合
        return storeCommodityPriceList.stream().map(StoreCommodityPriceODTO::getCommodityId).collect(Collectors.toList());
    }

    public List<XdaCategoryODTO> getEsFirstCategoryListAndSecondCategory(CategoryGoodsReq categoryGoodsReq) {
        StopWatch stopWatch = StopWatchUtil.begin();
        //获取商户商品一级分类 idList
        Long storeId = categoryGoodsReq.getShopId();
        List<Long> storeCommodityIdList = getXdaStoreCategoryIdList(storeId);

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termsQuery("commodityId", storeCommodityIdList))
                .must(QueryBuilders.termQuery("appStatus", 0));
        List<Long> xdaStoreFirstCategoryIdList = getXdaStoreCategoryIdList(boolQueryBuilder, 0);

        List<Long> xdaStoreScondCategoryIdList = getXdaStoreCategoryIdList(boolQueryBuilder, 1);

        List<EsXdaCategory> storeFirstCategorys = listFirstCategoryByIdsFromES(xdaStoreFirstCategoryIdList,1);

        List<EsXdaCategory> storeSecondCategorys = listFirstCategoryByIdsFromES(xdaStoreScondCategoryIdList,2);
        List<XdaCategoryODTO> list = buildStoreAllCategoryList(storeFirstCategorys, storeSecondCategorys);
        StopWatchUtil.end("XdaSearch聚合一级类目和二级类目耗时", stopWatch);
        return list;

    }

    protected void processExtraCategory(Date orderTime, List<XdaCategoryODTO> categoryList, Long storeId) {

    }

    private List<EsXdaCategory> listFirstCategoryByIdsFromES(List<Long> categoryIdList,Integer level) {
        if (SpringUtil.isEmpty(categoryIdList)) {
            return Collections.emptyList();
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termsQuery("cateId", categoryIdList))
                .must(QueryBuilders.termQuery("cateLevel", level));

        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        nativeSearchQueryBuilder.withQuery(boolQueryBuilder).withSorts(SortBuilders.fieldSort("sortNum").order(SortOrder.ASC));
        SearchHits<EsXdaCategory> search = elasticsearchTemplate.search(nativeSearchQueryBuilder.build(), EsXdaCategory.class);

        return search.getSearchHits().stream()
                .map(SearchHit::getContent)
                .collect(Collectors.toList());
    }

    public List<XdaCategoryCommodityResODTO> getSecondCategory2CommodityList(CategoryGoodsReq categoryGoodsReq) {
        long beginTime = System.currentTimeMillis();

        XdaCategoryAppIDTO xdaCategoryAppIDTO = categoryGoodsReq.getXdaCategoryAppIDTO();
        Long xdaFirstCategoryId = xdaCategoryAppIDTO.getXdaFirstCategoryId();

        //特殊一级类目处理
        XdaSecondCategoryCommodityResODTO xdaSecondCategoryCommodityResODTO = processSpecialCategory(categoryGoodsReq);
        if (BooleanUtils.isTrue(xdaSecondCategoryCommodityResODTO.getSpecialCategoryFlag())) {
            List<XdaCategoryCommodityResODTO> xdaCategoryCommodityResODTOS = new ArrayList<>();
            Map<XdaCategoryEnums, List<Long>> categoryMap = new HashMap<>();
            boolean thFlag = xdaFirstCategoryId.equals(XdaConstant.FIRST_CATEGORY_TH);
            if (thFlag && CollectionUtils.isNotEmpty(xdaSecondCategoryCommodityResODTO.getThCommodityIds())) {
                categoryMap.put(XdaCategoryEnums.TH, xdaSecondCategoryCommodityResODTO.getThCommodityIds());
            }

            boolean specialFlag = xdaFirstCategoryId.equals(XdaConstant.FIRST_CATEGORY_SPECIAL);
            if (specialFlag && CollectionUtils.isNotEmpty(xdaSecondCategoryCommodityResODTO.getPromotionCommodityIds())) {
                categoryMap.put(XdaCategoryEnums.SPECIAL_PROMOTION, xdaSecondCategoryCommodityResODTO.getPromotionCommodityIds());
            }

            boolean oftenBuyFlag = xdaFirstCategoryId.equals(XdaConstant.FIRST_CATEGORY_OFTEN_BUY);
            if (oftenBuyFlag) {
                if (CollectionUtils.isNotEmpty(xdaSecondCategoryCommodityResODTO.getOftenBuyCommodityIds())) {
                    categoryMap.put(XdaCategoryEnums.OFTEN_BUY, xdaSecondCategoryCommodityResODTO.getOftenBuyCommodityIds());
                }
                if (CollectionUtils.isNotEmpty(xdaSecondCategoryCommodityResODTO.getCollectCommodityIds())) {
                    categoryMap.put(XdaCategoryEnums.MY_COLLECT, xdaSecondCategoryCommodityResODTO.getCollectCommodityIds());
                }
            }

            // 统一处理不同的类别和商品ID
            categoryMap.forEach((categoryEnum, commodityIds) -> {
                SerialCommodityProcessInfo serialCommodityProcessInfo = this.processXdaCategoryCommodityAndSerialCommodity(XdaSearchAppIDTO.convert(xdaCategoryAppIDTO),
                        commodityIds, categoryEnum);
                buildAssignedCategoryCommodity(categoryEnum, serialCommodityProcessInfo, commodityIds, xdaCategoryCommodityResODTOS);

                if (Objects.equals(categoryEnum, XdaCategoryEnums.SPECIAL_PROMOTION)) {
                    // 特价分类时，移除非特价的商品
                    xdaCategoryCommodityResODTOS.stream()
                            .filter(Objects::nonNull)
                            .map(XdaCategoryCommodityResODTO::getCommodityList)
                            .filter(CollectionUtils::isNotEmpty)
                            .forEach(commodityList -> commodityList.removeIf(x -> Objects.nonNull(x) && !Objects.equals(x.getIsSpecialPrice(), 1)));
                }

                // 特殊处理 特惠 类别的商品排序
                if (Objects.equals(categoryEnum, XdaCategoryEnums.TH)) {
                    //与远程调用返回的商品id排序保持一致
                    restoreCommodityOrder(commodityIds, xdaCategoryCommodityResODTOS);
                }
            });
            return xdaCategoryCommodityResODTOS;
        }

        //查询推荐商品id集合
        List<Long> recommendCommodityIds = listRecommondCommodityIds(categoryGoodsReq);

        //查询商品信息 和 系列品信息
        SerialCommodityProcessInfo serialCommodityProcessInfo = this.processXdaCategoryCommodityAndSerialCommodity(XdaSearchAppIDTO.convert(xdaCategoryAppIDTO),
                recommendCommodityIds,XdaCategoryEnums.RECOMMEND);

        List<XdaCategoryCommodityODTO> categoryCommoditys = serialCommodityProcessInfo.getXdaCategoryCommodityODTOS();
        if (CollectionUtils.isEmpty(categoryCommoditys) && CollectionUtils.isEmpty(recommendCommodityIds)) {
            return Collections.emptyList();
        }

        List<XdaCategoryCommodityResODTO> xdaCategoryCommodityResODTOS = new ArrayList<>();

        buildCategoryCommodity(categoryGoodsReq, categoryCommoditys, xdaCategoryCommodityResODTOS);

        buildAssignedCategoryCommodity(XdaCategoryEnums.RECOMMEND, serialCommodityProcessInfo, recommendCommodityIds, xdaCategoryCommodityResODTOS);

        log.info("\n\n\n==========>>>鲜达APP.分类商品：耗时={}毫秒，idto={}", (System.currentTimeMillis() - beginTime), xdaCategoryAppIDTO);
        return xdaCategoryCommodityResODTOS;
    }

    public void restoreCommodityOrder(List<Long> commodityIds, List<XdaCategoryCommodityResODTO> newList) {
        // 创建商品ID到排序索引的Map
        Map<Long, Integer> commodityOrderMap = new HashMap<>();

        // 遍历原始列表，记录每个商品的ID及其在commodityList中的索引位置
        for (int i = 0; i < commodityIds.size(); i++) {
            commodityOrderMap.put(commodityIds.get(i), i);
        }

        // 2. 对新列表中的商品列表进行排序
        for (XdaCategoryCommodityResODTO resODTO : newList) {
            if (CollectionUtils.isNotEmpty(resODTO.getCommodityList())) {
                resODTO.getCommodityList().sort((o1, o2) -> {
                    Integer order1 = commodityOrderMap.get(o1.getCommodityId());
                    Integer order2 = commodityOrderMap.get(o2.getCommodityId());

                    // 如果某个商品ID不在原始列表中，保持原顺序
                    if (order1 == null) {
                        return 1;
                    }
                    if (order2 == null) {
                        return -1;
                    }
                    return order1 - order2;
                });
            }
        }
    }

    private XdaSecondCategoryCommodityResODTO processSpecialCategory(CategoryGoodsReq categoryGoodsReq) {

        StopWatch stopWatch = StopWatchUtil.begin();

        XdaCategoryAppIDTO xdaCategoryAppIDTO = categoryGoodsReq.getXdaCategoryAppIDTO();
        Long xdaFirstCategoryId = xdaCategoryAppIDTO.getXdaFirstCategoryId();

        XdaSecondCategoryCommodityResODTO xdaSecondCategoryCommodityResODTO = new XdaSecondCategoryCommodityResODTO();

        // 特惠商品一级分类标识
        boolean thFlag = xdaFirstCategoryId.equals(XdaConstant.FIRST_CATEGORY_TH);
        if (thFlag) {
            // 查询特惠商品详情
            xdaCategoryAppIDTO.setXdaSecondCategoryId(XdaConstant.SECOND_CATEGORY_TH);
            FeignXdaCategoryAppIDTO feignXdaCategoryAppIDTO = BeanCloneUtils.copyTo(xdaCategoryAppIDTO, FeignXdaCategoryAppIDTO.class);
            List<Long> commodityIdList = xdaCategoryClient.queryXdaCategoryCommodityIdList(feignXdaCategoryAppIDTO);
            xdaSecondCategoryCommodityResODTO.setThCommodityIds(commodityIdList);
            xdaSecondCategoryCommodityResODTO.setSpecialCategoryFlag(Boolean.TRUE);
        }

        // 促销商品一级分类标识
        boolean specialFlag = xdaFirstCategoryId.equals(XdaConstant.FIRST_CATEGORY_SPECIAL);
        Boolean isVisitor = FastThreadLocalUtil.getXDA().getIsTouristStore();
        if (specialFlag ) {
            xdaSecondCategoryCommodityResODTO.setSpecialCategoryFlag(Boolean.TRUE);
            if (!isVisitor) {
                //走远程获取
                xdaCategoryAppIDTO.setXdaSecondCategoryId(XdaConstant.SECOND_CATEGORY_SPECIAL);
                FeignXdaCategoryAppIDTO feignXdaCategoryAppIDTO = BeanCloneUtils.copyTo(xdaCategoryAppIDTO, FeignXdaCategoryAppIDTO.class);
                List<Long> commodityIdList = xdaCategoryClient.queryXdaCategoryCommodityIdList(feignXdaCategoryAppIDTO);
                xdaSecondCategoryCommodityResODTO.setPromotionCommodityIds(commodityIdList);
            }
        }

        boolean oftenBuyFlag = xdaFirstCategoryId.equals(XdaConstant.FIRST_CATEGORY_OFTEN_BUY);
        if (oftenBuyFlag) {
            xdaSecondCategoryCommodityResODTO.setSpecialCategoryFlag(Boolean.TRUE);
            if (!isVisitor) {
                //走远程获取
                if(Objects.equals(xdaCategoryAppIDTO.getXdaSecondCategoryId(),XdaConstant.SECOND_CATEGORY_MY_OFTEN_BUY)){
                    xdaCategoryAppIDTO.setXdaSecondCategoryId(XdaConstant.SECOND_CATEGORY_MY_OFTEN_BUY);
                    FeignXdaCategoryAppIDTO oftenBuyFeignXdaCategoryAppIDTO = BeanCloneUtils.copyTo(xdaCategoryAppIDTO, FeignXdaCategoryAppIDTO.class);
                    List<Long> oftenBuyCommodityIdList = xdaCategoryClient.queryXdaCategoryCommodityIdList(oftenBuyFeignXdaCategoryAppIDTO);
                    xdaSecondCategoryCommodityResODTO.setOftenBuyCommodityIds(oftenBuyCommodityIdList);
                }
                if(Objects.equals(xdaCategoryAppIDTO.getXdaSecondCategoryId(),XdaConstant.SECOND_CATEGORY_MY_COLLECT)){
                    xdaCategoryAppIDTO.setXdaSecondCategoryId(XdaConstant.SECOND_CATEGORY_MY_COLLECT);
                    FeignXdaCategoryAppIDTO collecFeignXdaCategoryAppIDTO = BeanCloneUtils.copyTo(xdaCategoryAppIDTO, FeignXdaCategoryAppIDTO.class);
                    List<Long> collectCommodityIdList = xdaCategoryClient.queryXdaCategoryCommodityIdList(collecFeignXdaCategoryAppIDTO);
                    xdaSecondCategoryCommodityResODTO.setCollectCommodityIds(collectCommodityIdList);
                }
            }
        }

        StopWatchUtil.end("特殊类目商品查询", stopWatch);
        return xdaSecondCategoryCommodityResODTO;
    }

    void processOftenBuyCategory(Boolean isVisitor, XdaCategoryAppIDTO xdaCategoryAppIDTO,SpecialCategoryInfo specialCategoryInfo) {
        // 常购清单一级分类标识 ,下面两个二级类目
        Long xdaFirstCategoryId = xdaCategoryAppIDTO.getXdaFirstCategoryId();
        boolean oftenBuyFlag = xdaFirstCategoryId.equals(XdaConstant.FIRST_CATEGORY_OFTEN_BUY);
        if (!oftenBuyFlag) {
           return ;
        }

        specialCategoryInfo.setSpecialCategoryFlag(Boolean.TRUE);

        if (isVisitor) {
            return;
        }
        //走远程获取
        xdaCategoryAppIDTO.setXdaSecondCategoryId(XdaConstant.SECOND_CATEGORY_MY_OFTEN_BUY);
        List<XdaCategoryCommodityResODTO> oftenBuy = callRemoteCategoryQuery(xdaCategoryAppIDTO, XdaConstant.OFTEN_BUY_SECOND_CATEGORT_NAME);
        xdaCategoryAppIDTO.setXdaSecondCategoryId(XdaConstant.SECOND_CATEGORY_MY_COLLECT);
        List<XdaCategoryCommodityResODTO> collect = callRemoteCategoryQuery(xdaCategoryAppIDTO, XdaConstant.COLLECT_SECOND_CATEGORT_NAME);
        if (CollectionUtils.isEmpty(oftenBuy)) {
            oftenBuy = buildDefaultOftenBuy(XdaConstant.SECOND_CATEGORY_MY_OFTEN_BUY, XdaConstant.OFTEN_BUY_SECOND_CATEGORT_NAME);
        }
        List<XdaCategoryCommodityResODTO> resultList = new ArrayList<>(oftenBuy);
        if (CollectionUtils.isEmpty(collect)) {
            collect = buildDefaultOftenBuy(XdaConstant.SECOND_CATEGORY_MY_COLLECT, XdaConstant.COLLECT_SECOND_CATEGORT_NAME);
        }
        resultList.addAll(collect);

        specialCategoryInfo.setXdaCategoryCommodityResODTOS(resultList);
    }

    protected List<Long> listRecommondCommodityIds(CategoryGoodsReq categoryGoodsReq) {
        return Collections.emptyList();
    }

    private SerialCommodityProcessInfo processXdaCategoryCommodityAndSerialCommodity(XdaSearchAppIDTO searchIDTO, List<Long> recommendCommodityIds, XdaCategoryEnums xdaCategoryEnums) {
        /**查询分类下的商品信息**/
        List<XdaCommodityODTO> appODTOList = this.queryXdaCommodityList(searchIDTO, recommendCommodityIds, xdaCategoryEnums);
        if (CollectionUtils.isEmpty(appODTOList)) {
            return new SerialCommodityProcessInfo();
        }

        return processSerialCommodity(searchIDTO, appODTOList);
    }

    private List<XdaCommodityODTO> queryXdaCommodityList(XdaSearchAppIDTO searchIDTO, List<Long> recommendCommodityIds, XdaCategoryEnums xdaCategoryEnums) {
        // 查询产品价格方案信息，渲染价格
        List<StoreCommodityPriceODTO> storeCommodityPriceList = xdaCommodityFrontClient.getStoreCommodityPrice(searchIDTO.getStoreId());
        if(CollectionUtils.isEmpty(storeCommodityPriceList)){
            // 如果客户产品价格方案下面商品没有，返回空
            return new ArrayList<>();
        }

        // 产品价格方案下面的商品id集合
        List<Long> storeCommodityIdList = storeCommodityPriceList.stream().map(StoreCommodityPriceODTO::getCommodityId).collect(Collectors.toList());
        List<EsXdaCommodity> esXdaCommodities = esXdaCategoryManagerService.getEsXdaStoreCategoryCommoditiesSwitch(searchIDTO, recommendCommodityIds, storeCommodityIdList, xdaCategoryEnums);
        if (CollectionUtils.isEmpty(esXdaCommodities)) return Collections.emptyList();

        KeyWordGoodsReq keyWordGoodsReq = new KeyWordGoodsReq();
        keyWordGoodsReq.setStoreId(searchIDTO.getStoreId());
        keyWordGoodsReq.setXdaFirstCategoryId(searchIDTO.getXdaFirstCategoryId());
        keyWordGoodsReq.setOrderTime(searchIDTO.getOrderTime());
        keyWordGoodsReq.setLogisticsCenterId(searchIDTO.getLogisticscenterid());
        List<XdaCommodityODTO> xdaCommodityInfoODTOS = buildGoodsList(keyWordGoodsReq, esXdaCommodities, storeCommodityPriceList);

        // 166以上版本，鲜达分类页，促销分类-分页查询
        xdaCommodityInfoODTOS = processCommodityPagination(searchIDTO, xdaCategoryEnums, xdaCommodityInfoODTOS);

        if (Objects.equals(xdaCategoryEnums, XdaCategoryEnums.TH)) {
            renderXdaThCommodityCategory(keyWordGoodsReq, xdaCommodityInfoODTOS);
        } else {
            renderXdaCommodityCategory(keyWordGoodsReq, xdaCommodityInfoODTOS);
        }
        return xdaCommodityInfoODTOS;
    }

    public List<EsXdaCommodity> getEsXdaStoreCategoryCommodities(XdaSearchAppIDTO searchIDTO, List<Long> recommendCommodityIds, List<Long> storeCommodityIdList, XdaCategoryEnums xdaCategoryEnums) {
        Boolean isPfStore = storeService.isPfsStore(searchIDTO.getStoreId());
        StopWatch stopWatch = StopWatchUtil.begin();

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if(isPfStore) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("pfAppStatus", 0));
        }else {
            boolQueryBuilder.filter(QueryBuilders.termQuery("appStatus", 0));
        }
        boolQueryBuilder.filter(QueryBuilders.termsQuery("commodityId", storeCommodityIdList));
        if (CollectionUtils.isNotEmpty(recommendCommodityIds)) {
            BoolQueryBuilder fuzzyQueryBuilder = new BoolQueryBuilder();
            BoolQueryBuilder baseQuery = fuzzyQueryBuilder.should(QueryBuilders.termsQuery("commodityId", recommendCommodityIds));
            // 判断 specialCategoryFlag 是否为 特殊分类，特殊分类查询不带一级分类ID
            if (!Objects.equals(xdaCategoryEnums, XdaCategoryEnums.TH)
                    && !Objects.equals(xdaCategoryEnums, XdaCategoryEnums.SPECIAL_PROMOTION)
                    && !Objects.equals(xdaCategoryEnums, XdaCategoryEnums.OFTEN_BUY)
                    && !Objects.equals(xdaCategoryEnums, XdaCategoryEnums.MY_COLLECT)
            ) {
                baseQuery.should(QueryBuilders.termQuery("xdaFirstCategoryId", searchIDTO.getXdaFirstCategoryId()));
            }
            boolQueryBuilder.must(baseQuery);
        } else {
            boolQueryBuilder.filter(QueryBuilders.termQuery("xdaFirstCategoryId", searchIDTO.getXdaFirstCategoryId()));

            if (Objects.nonNull(searchIDTO.getXdaSecondCategoryId())
                    && !Objects.equals(searchIDTO.getXdaSecondCategoryId(), XdaConstant.RECOMMOND_CATEGORT_ID)) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("xdaSecondCategoryId", searchIDTO.getXdaSecondCategoryId()));
            }
        }


        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .build();
        SearchHits<EsXdaCommodity> esXdaCommodityHits = elasticsearchTemplate.search(nativeSearchQuery, EsXdaCommodity.class);

        if (CollectionUtils.isEmpty(esXdaCommodityHits.getSearchHits())) {
            return Collections.emptyList();
        }

        List<EsXdaCommodity> esXdaCommodities = new ArrayList<>(esXdaCommodityHits.getSearchHits().stream()
                .map(SearchHit::getContent)
                .collect(Collectors.toMap(EsXdaCommodity::getCommodityId, Function.identity()))
                .values());

        // 如果是批发客户，则返回批发商品的上下架信息
        esXdaCommodities.forEach(esXdaCommodity -> {
            if(isPfStore) {
                esXdaCommodity.setAppStatus(esXdaCommodity.getPfAppStatus());
            }
        });
        StopWatchUtil.end("es查询商户商品", stopWatch);
        return esXdaCommodities;
    }

    List<XdaCategoryCommodityResODTO> callRemoteCategoryQuery(XdaCategoryAppIDTO xdaCategoryAppIDTO, String xdaSecondCategoryName) {
        return Collections.emptyList();
    }

    /**
     * 处理系列品展示信息
     *
     * @param categoryCommodityODTO
     * @param serialCommodityInfoList
     * @return
     */
    public void processXdaSerialCommodityInfo(XdaCategoryCommodityODTO categoryCommodityODTO, List<XdaCommodityODTO> serialCommodityInfoList) {
        if (CollectionUtils.isEmpty(serialCommodityInfoList)) {
            return;
        }
        List<XdaCommodityODTO> sortList = serialCommodityInfoList.stream().sorted(Comparator.comparing(XdaCommodityODTO::getSortPrice)).collect(Collectors.toList());
        Set<String> specList = new LinkedHashSet<>();
        Set<String> priceList = new LinkedHashSet<>();
        List<XdaSerialCommodityDetailODTO> serialDetailList = new ArrayList<>();
        for (int i = 0; i < sortList.size(); i++) {
            specList.add(sortList.get(i).getCommoditySpec());
            if (i == 0 || i == sortList.size() - 1) {
                priceList.add(NumberUtil.format_2_roundHalfUp(sortList.get(i).getSortPrice()));
            }
            serialDetailList.add(XdaSerialCommodityDetailODTO.convert(sortList.get(i)));
        }
        categoryCommodityODTO.setSerialCommoditySpec(String.join("|", specList));
        categoryCommodityODTO.setSerialCommodityPrice(String.join("-", priceList));
        categoryCommodityODTO.setSerialCommodityDetailList(serialDetailList);
    }

    private SerialCommodityProcessInfo processSerialCommodity(XdaSearchAppIDTO searchIDTO, List<XdaCommodityODTO> appODTOList) {
        SerialCommodityProcessInfo serialCommodityProcessInfo = new SerialCommodityProcessInfo();
        Map<Long, XdaCategoryCommodityODTO> serialCommodityMap = new HashMap<>();
        /**系列品**/
        Map<Long, List<XdaCommodityODTO>> serialMap = appODTOList.stream()
                .collect(Collectors.groupingBy(XdaCommodityODTO::getSerialCommodityId, LinkedHashMap::new, Collectors.toList()));
        List<XdaCategoryCommodityODTO> commodityODTOList = new ArrayList<>();

        Long xdaFirstCategoryId = searchIDTO.getXdaFirstCategoryId();
        appODTOList.forEach(appODTO -> {
            XdaCategoryCommodityODTO categoryCommodityODTO = XdaCategoryCommodityODTO.convert(appODTO);
            Long commodityId = appODTO.getCommodityId();

            Long serialCommodityId = appODTO.getSerialCommodityId();

            /** 一级分类id为空/一级分类id等于常购清单/一级分类id等于特惠商品/系列品主品ID=0 的时候不考虑系列品 **/
            if (null == xdaFirstCategoryId || xdaFirstCategoryId.equals(XdaConstant.FIRST_CATEGORY_OFTEN_BUY)
                    || xdaFirstCategoryId.equals(XdaConstant.FIRST_CATEGORY_TH) || serialCommodityId == 0) {
                categoryCommodityODTO.setIsSerial(0);
                commodityODTOList.add(categoryCommodityODTO);
            } else {
                List<XdaCommodityODTO> serialList = serialMap.get(serialCommodityId);
                boolean isSerial = CollectionUtils.isNotEmpty(serialList) && serialList.size() > 1;
                isSerial = isSerial && serialList.stream().anyMatch(bean -> bean.getCommodityId().equals(serialCommodityId));

                if (!isSerial) {
                    categoryCommodityODTO.setIsSerial(0);
                    categoryCommodityODTO.setSerialCommodityId(0L);
                    commodityODTOList.add(categoryCommodityODTO);
                    return;
                }
                if (serialCommodityId.equals(commodityId)) {
                    categoryCommodityODTO.setIsSerial(1);
                    // 处理系列品展示信息
                    this.processXdaSerialCommodityInfo(categoryCommodityODTO, serialList);
                    serialList.forEach(serialCommodity -> serialCommodityMap.put(serialCommodity.getCommodityId(), XdaCategoryCommodityODTO.convert(serialCommodity)));
                    commodityODTOList.add(categoryCommodityODTO);
                }
            }
        });

        serialCommodityProcessInfo.setXdaCategoryCommodityODTOS(commodityODTOList);
        serialCommodityProcessInfo.setSerialCommodityMap(serialCommodityMap);
        return serialCommodityProcessInfo;
    }

    protected void buildCategoryCommodity(CategoryGoodsReq categoryGoodsReq, List<XdaCategoryCommodityODTO> categoryCommoditys, List<XdaCategoryCommodityResODTO> xdaCategoryCommodityResODTOS) {
        if (CollectionUtils.isEmpty(categoryCommoditys)) {
            return;
        }

        StopWatch stopWatch = StopWatchUtil.begin();

        Long xdaFirstCategoryId = categoryGoodsReq.getXdaFirstCategoryId();

        Map<Long, List<XdaCategoryCommodityODTO>> groupBySecondCategoryId = categoryCommoditys.stream()
                .collect(Collectors.groupingBy(
                        XdaCategoryCommodityODTO::getXdaSecondCategoryId,
                        Collectors.collectingAndThen(Collectors.toList(),
                                list -> {
                                    try {
                                        list.sort(commoditySort());
                                    } catch (Exception e) {
                                        EsManualIDTO req = new EsManualIDTO();
                                        commodityElasticSearchService.allCommoditySyncEs(req);
                                        log.error("=======排序异常,分类请求发生异常 xdaFirstCategoryId:[{}],storeId:[{}]======{}", xdaFirstCategoryId, FastThreadLocalUtil.getXDA().getStoreId(), e);
                                        throw e;
                                    }
                                    return list;
                                }
                        )
                ));


        Set<Long> secondCategoryIdSet = groupBySecondCategoryId.keySet();
        // list转map之后，二级分类的排序就乱了
        List<EsXdaCategory> categories = categoryElasticSearchService.listByParentIdAndCateIdList(xdaFirstCategoryId, secondCategoryIdSet);
        if (SpringUtil.isEmpty(categories)) {
            return;
        }

        // list有序的
        List<Long> sortSecondCategoryIdSet = new ArrayList<>();
        categories.forEach(esXdaCategory -> {
            if(!sortSecondCategoryIdSet.contains(esXdaCategory.getCateId())){
                sortSecondCategoryIdSet.add(esXdaCategory.getCateId());
            }
        });
        Map<Long, EsXdaCategory> secondCateNameMap = categories.stream().collect(Collectors.toMap(EsXdaCategory::getCateId, Function.identity()));

        for (Long secondCateId : sortSecondCategoryIdSet) {
            XdaCategoryCommodityResODTO categoryCommodity = new XdaCategoryCommodityResODTO();
            categoryCommodity.setXdaFirstCategoryId(xdaFirstCategoryId);

            EsXdaCategory secondCate = secondCateNameMap.get(secondCateId);
            categoryCommodity.setXdaSecondCategoryName(secondCate.getCateName());

            categoryCommodity.setXdaSecondCategoryId(secondCateId);
            categoryCommodity.setSortNum(secondCate.getSortNum());

            List<XdaCategoryCommodityODTO> xdaCategoryCommodityODTOS = groupBySecondCategoryId.get(secondCateId);
            List<XdaCategoryCommodityODTO> xdaCategoryCommodityResODTOList = JSONObject.parseArray(JSONObject.toJSONString(xdaCategoryCommodityODTOS)
                    , XdaCategoryCommodityODTO.class);

            categoryCommodity.setCommodityList(xdaCategoryCommodityResODTOList);
            xdaCategoryCommodityResODTOS.add(categoryCommodity);
        }

        StopWatchUtil.end("构建普通二级分类", stopWatch);
    }

    /**
     * 166以上版本，鲜达分类页，促销分类-分页查询
     * <ul>
     *     <li><a href="http://192.168.0.213/zentao/story-view-11706.html">需求地址</a></li>
     * </ul>
     */
    private List<XdaCommodityODTO> processCommodityPagination(XdaSearchAppIDTO searchIDTO, XdaCategoryEnums xdaCategoryEnums, List<XdaCommodityODTO> xdaCommodityInfoODTOS) {
        if (CollectionUtils.isNotEmpty(xdaCommodityInfoODTOS)) {
            // 分页前的商品总条数
            int total = xdaCommodityInfoODTOS.size();

            // 检查是否需要特价商品分页处理，166以上版本，特价商品分页
            boolean isSpecialPromotionPaging =
                    searchIDTO.getAppVersion() != null &&
                            searchIDTO.getAppVersion().compareToIgnoreCase(XdaAppVersionConstant.XDA_VERSION_166) > 0 &&
                            Objects.equals(xdaCategoryEnums, XdaCategoryEnums.SPECIAL_PROMOTION);

            if (isSpecialPromotionPaging) {
                // 特价商品排序并分页，保证不可售的在最后一页
                xdaCommodityInfoODTOS.sort(xdaCommoditySort());
                xdaCommodityInfoODTOS = paginateList(xdaCommodityInfoODTOS, searchIDTO.getPageNo(), searchIDTO.getPageSize());
            }

            // 传递分页前的商品总条数，用于前端分页
            xdaCommodityInfoODTOS.forEach(item -> item.setTotal(total));
        }

        return xdaCommodityInfoODTOS;
    }

    public <U> List<U> paginateList(List<U> list, int page, int pageSize) {
        // 参数校验
        if (CollectionUtils.isEmpty(list) || page <= 0 || pageSize <= 0) {
            return Collections.emptyList();
        }

        // 计算分页索引
        int fromIndex = (page - 1) * pageSize;
        if (fromIndex >= list.size()) {
            return Collections.emptyList();
        }

        int toIndex = Math.min(fromIndex + pageSize, list.size());
        return list.subList(fromIndex, toIndex);
    }

    public Integer getSafeBusinessType(Integer businessType) {
        // 定义允许直接返回的业务类型列表
        Set<Integer> specialBusinessTypes = new HashSet<>(Arrays.asList(
                BusinessTypeEnums.TD_SALE.getCode(),
                BusinessTypeEnums.BIGSHOP_SALE.getCode(),
                BusinessTypeEnums.PLAN_SALE.getCode(),
                BusinessTypeEnums.B_COUNTRY.getCode()
        ));

        if (businessType != null && specialBusinessTypes.contains(businessType)) {
            return businessType;
        }

        return BusinessTypeEnums.SALE.getCode();
    }

}
