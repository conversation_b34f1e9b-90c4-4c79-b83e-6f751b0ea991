package com.pinshang.qingyun.xda.search.service;

import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.product.dto.category.XdaCategoryODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.XdaCommodityTextODTO;
import com.pinshang.qingyun.xda.product.service.XdaCategoryClient;
import com.pinshang.qingyun.xda.product.service.XdaCommodityTextClient;
import com.pinshang.qingyun.xda.search.mapper.XdaCategoryEsMapper;
import com.pinshang.qingyun.xda.search.mapper.XdaCommodityEsMapper;
import com.pinshang.qingyun.xda.search.model.XdaCategoryEs;
import com.pinshang.qingyun.xda.search.model.XdaCommodityEs;
import com.pinshang.qingyun.xda.search.repository.EsXdaCategoryDao;
import com.pinshang.qingyun.xda.search.service.es.CommodityElasticSearchService;
import com.pinshang.qingyun.xda.search.vo.EsXdaCategoryChangeKafkaVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/04/07
 * @Version 1.0
 */
@Service
@Slf4j
public class EsCategoryService {
    @Autowired
    private XdaCategoryEsMapper xdaCategoryEsMapper;

    @Autowired
    private XdaCategoryClient xdaCategoryClient;

    @Autowired
    private XdaCommodityEsMapper xdaCommodityEsMapper;

    @Autowired
    private XdaCommodityTextClient xdaCommodityTextClient;

    @Autowired
    private CommodityElasticSearchService commodityElasticSearchService;

    @Autowired
    private EsXdaCategoryDao esXdaCategoryDao;

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateXdaCategory(List<EsXdaCategoryChangeKafkaVO> voList) {
        // 每次请求只有一种状态
        Integer status = voList.get(0).getStatus();
        Date now = new Date();

        if (0 == status) {
            List<Long> cateIdList = voList.stream().map(EsXdaCategoryChangeKafkaVO::getCateId).collect(Collectors.toList());
            Example deleteShopExample = new Example(XdaCategoryEs.class);
            deleteShopExample.createCriteria().andIn("cateId", cateIdList);
            xdaCategoryEsMapper.deleteByExample(deleteShopExample);
        } else if (1 == status) {
            for (EsXdaCategoryChangeKafkaVO it : voList) {
                XdaCategoryEs es = new XdaCategoryEs();
                es.setId(it.getCateId());
                es.setCateId(it.getCateId());
                es.setParentId(it.getParentId());
                es.setCateName(it.getCateName());
                es.setCateLevel(it.getCateLevel());
                es.setSortNum(it.getSortNum());
                es.setUpdateTime(now);
                es.setCreateTime(now);
                xdaCategoryEsMapper.insert(es);
            }
        } else {
            // 分类名只能逐个修改
            if(voList.size() == 1 && StringUtils.isNotBlank(voList.get(0).getCateName())){
                EsXdaCategoryChangeKafkaVO cate = voList.get(0);
                Example example = new Example(XdaCategoryEs.class);
                example.createCriteria().andEqualTo("cateId", cate.getCateId());
                List<XdaCategoryEs> oldRecorList = xdaCategoryEsMapper.selectByExample(example);
                XdaCategoryEs oldCate = oldRecorList.get(0);
                // 当前分类es表的cateName和传入的修改后值不同, 则刷新
                if(!oldCate.getCateName().equals(cate.getCateName())) {
                    // 走commodityId索引 , 按commodityId更新, 查询类下面的商品id
                    List<XdaCommodityTextODTO> commodityTextList = xdaCommodityTextClient.selectCommodityByCateId(cate.getCateId());
//                    if (SpringUtil.isNotEmpty(commodityTextList)) {
//                        for (XdaCommodityTextODTO item : commodityTextList) {
//                            XdaStoreCommodityEs updateStore = getXdaStoreCommodityEs(item, now, cate);
//                            xdaStoreCommodityEsMapper.batchUpdateSelective(Collections.singletonList(updateStore));
//                        }
//
//                    }

                    if (SpringUtil.isNotEmpty(commodityTextList)) {
                        List<XdaCommodityEs> updateList = new ArrayList<>();
                        for (XdaCommodityTextODTO XdaCommodityTextODTO : commodityTextList) {
                            XdaCommodityEs xdaCommodityEs = new XdaCommodityEs();
                            xdaCommodityEs.setCommodityId(XdaCommodityTextODTO.getCommodityId());
                            if (1 == cate.getCateLevel()) {
                                xdaCommodityEs.setXdaFirstCategoryName(cate.getCateName());
                            } else {
                                xdaCommodityEs.setXdaSecondCategoryName(cate.getCateName());
                                xdaCommodityEs.setCommoditySearchName(XdaCommodityTextODTO.getCommodityAppName() + " " + cate.getCateName());
                            }
                            xdaCommodityEs.setUpdateTime(new Date());
                            updateList.add(xdaCommodityEs);
                        }
                        xdaCommodityEsMapper.batchUpdateCommodityText(updateList);

                        List<Long> commodityIdList = commodityTextList.stream().map(XdaCommodityTextODTO::getCommodityId).collect(Collectors.toList());
                        commodityElasticSearchService.commoditySyncEsByCommodityIdList(commodityIdList);
                    }


                }
            }
            // 更新分类es表数据
            List<XdaCategoryEs> list = new ArrayList<>(voList.size());
            voList.forEach(it -> {
                XdaCategoryEs es = new XdaCategoryEs();
                es.setCateId(it.getCateId());
                es.setParentId(it.getParentId());
                es.setCateName(it.getCateName());
                es.setCateLevel(it.getCateLevel());
                es.setSortNum(it.getSortNum());
                es.setUpdateTime(now);
                list.add(es);
            });
            xdaCategoryEsMapper.batchUpdateByCateIdList(list);
        }
        return true;
    }

//    @NotNull
//    private static XdaStoreCommodityEs getXdaStoreCommodityEs(XdaCommodityTextODTO item, Date now, EsXdaCategoryChangeKafkaVO cate) {
//        XdaStoreCommodityEs updateStore = new XdaStoreCommodityEs();
//        updateStore.setCommodityId(item.getCommodityId());
//        updateStore.setUpdateTime(now);
//        if(Objects.equals(cate.getCateLevel(), CategoryLevelEnums.SECOND.getCode())) {
//            updateStore.setXdaSecondCategoryName(cate.getCateName());
//            updateStore.setCommoditySearchName(item.getCommodityAppName() + " " + item.getSecondCategoryId());
//        }else{
//            updateStore.setXdaFirstCategoryName(cate.getCateName());
//        }
//        return updateStore;
//    }

    @Transactional
    public void allCategoryToSql() {
        List<XdaCategoryODTO> xdaCategoryODTOList = xdaCategoryClient.findAllXdaCategory();

        List<XdaCategoryEs> exitList = xdaCategoryEsMapper.selectAll();
        Map<Long, XdaCategoryEs> exitMap = exitList.stream().collect(Collectors.toMap(XdaCategoryEs::getCateId, it -> it));

//        List<Long> deleteIds = new ArrayList<>();
        List<XdaCategoryEs> insertData = new ArrayList<>();
        List<XdaCategoryEs> updateData = new ArrayList<>();
        Date date = new Date();
        for (XdaCategoryODTO dto : xdaCategoryODTOList) {
            if (exitMap.containsKey(dto.getId())) { //已经存在的
                XdaCategoryEs old = exitMap.get(dto.getId());
                //已经修改的要删除掉
                exitMap.remove(dto.getId());
                if (!old.getCateName().equals(dto.getCateName()) || !old.getCateLevel().equals(dto.getCateLevel()) || !old.getParentId().equals(dto.getParentId()) || !old.getSortNum().equals(dto.getSortNum())) {
                    XdaCategoryEs es = BeanCloneUtils.copyTo(dto, XdaCategoryEs.class);
                    es.setId(dto.getId());
                    es.setCateId(dto.getId());
                    es.setUpdateTime(date);
                    updateData.add(es);
                }
            } else { //不存在的
                XdaCategoryEs es = BeanCloneUtils.copyTo(dto, XdaCategoryEs.class);
                es.setId(dto.getId());
                es.setCateId(dto.getId());
                insertData.add(es);
            }
        }
        if (!exitMap.isEmpty()) {
            List<Long> deleteIds = new ArrayList<>(exitMap.keySet());
            log.warn("需要删除的分类id={}", deleteIds);
            Example deleteExample = new Example(XdaCategoryEs.class);
            deleteExample.createCriteria().andIn("id", deleteIds);
            xdaCategoryEsMapper.deleteByExample(deleteExample);
            //删除es那边的信息
            esXdaCategoryDao.deleteByIds(deleteIds);
        }
        if (SpringUtil.isNotEmpty(insertData)) {
            log.warn("添加的分类数据条数={}", insertData.size());
            xdaCategoryEsMapper.batchInsert(insertData);
        }
        if (SpringUtil.isNotEmpty(updateData)) {
            log.warn("修改的分类数据条数={}", updateData.size());
            xdaCategoryEsMapper.batchUpdateByCateIdList(updateData);
        }
    }

    public List<XdaCategoryEs> listByUpdateTime(Date date) {
        Example example = new Example(XdaCategoryEs.class);
        example.createCriteria().andGreaterThanOrEqualTo("updateTime", date);
        return xdaCategoryEsMapper.selectByExample(example);
    }

}
