package com.pinshang.qingyun.xda.search.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextEsODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectXdaCommodityInfoListIDTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityTextClient;
import com.pinshang.qingyun.xda.search.dto.CommodityAppStatusDTO;
import com.pinshang.qingyun.xda.search.mapper.XdaCommodityEsMapper;
import com.pinshang.qingyun.xda.search.model.XdaCommodityEs;
import com.pinshang.qingyun.xda.search.service.es.CommodityElasticSearchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class EsCommodityService {

    @Autowired
    private XdaCommodityEsMapper xdaCommodityEsMapper;

    @Autowired
    private XdaCommodityTextClient xdaCommodityTextClient;

    @Autowired
    private CommodityElasticSearchService commodityElasticSearchService;

    /**
     * 商品上下架  是根据商品商品来的，需要更新客户商品表信息
     *
     * @param dto
     */
    @Transactional
    public void commodityAppStatus(CommodityAppStatusDTO dto) {
        QYAssert.isTrue(SpringUtil.isNotEmpty(dto.getCommodityIds()) || SpringUtil.isNotEmpty(dto.getNewCommodityIdList()), "商品ID不能为空");
        QYAssert.isTrue(dto.getAppStatus() != null, "商品状态不能为空");

        List<Long> allCommodityIds = new ArrayList<>();

        if (SpringUtil.isNotEmpty(dto.getNewCommodityIdList())) {
            allCommodityIds.addAll(dto.getNewCommodityIdList());
        }
        if (SpringUtil.isNotEmpty(dto.getCommodityIds())) {
            allCommodityIds.addAll(dto.getCommodityIds());
        }

        commodityToMysql(allCommodityIds);
    }

    /**
     * 分类页、搜索页数据异常。进行异步刷新
     * @param commodityIds
     */
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void commodityToMysqlAsync(List<Long> commodityIds) {
        commodityToMysql(commodityIds);
    }

    @Transactional
    public void commodityToMysql(List<Long> commodityIds) {
        // 查询鲜达商品，上架未上架的都查出来
        SelectXdaCommodityInfoListIDTO idto = new SelectXdaCommodityInfoListIDTO();
        idto.setCommodityIdList(commodityIds);
        List<CommodityTextEsODTO> commodityTextEsODTOList = xdaCommodityTextClient.selectCommodityTextEsList(idto);
        log.warn("pinshang库鲜达商品数量={}", commodityTextEsODTOList.size());

        // 过滤鲜达或者批发上架的商品,如果鲜达和批发都下架，则删除
        commodityTextEsODTOList = commodityTextEsODTOList.stream().filter(e -> YesOrNoEnums.NO.getCode().equals(e.getAppStatus())
                                          || YesOrNoEnums.NO.getCode().equals(e.getPfAppStatus())).collect(Collectors.toList());
        if (SpringUtil.isNotEmpty(commodityTextEsODTOList)) {

            //当前表已经存在的
            Example example = new Example(XdaCommodityEs.class);
            if (SpringUtil.isNotEmpty(commodityIds)) {
                example.createCriteria().andIn("commodityId", commodityIds);
            }
            List<XdaCommodityEs> exitCommodityList = xdaCommodityEsMapper.selectByExample(example);
            log.warn("es表存在的商品条数={}",exitCommodityList.size());
            Map<Long, XdaCommodityEs> exitMap = exitCommodityList.stream().collect(Collectors.toMap(XdaCommodityEs::getCommodityId, e -> e));


            Map<Long, CommodityTextEsODTO> textMap = commodityTextEsODTOList.stream().filter(e -> e.getCommodityId() != null).distinct().collect(Collectors.toMap(CommodityTextEsODTO::getCommodityId, e -> e));

            //查询基础信息
            if (SpringUtil.isEmpty(commodityIds)) {
                commodityIds = commodityTextEsODTOList.stream().map(CommodityTextEsODTO::getCommodityId).distinct().collect(Collectors.toList());
            }
            List<XdaCommodityEs> xdaCommodityEsList = xdaCommodityEsMapper.selectCommodityEsByCommodityIds(commodityIds);
            log.warn("report库商品基本信息条数={}",xdaCommodityEsList.size());
            if (SpringUtil.isEmpty(xdaCommodityEsList)) {
                return;
            }

            List<XdaCommodityEs> updateData = new ArrayList<>();
            List<XdaCommodityEs> insertData = new ArrayList<>();
            for (XdaCommodityEs xdaCommodityEs : xdaCommodityEsList) {
                CommodityTextEsODTO commodityTextEsODTO = textMap.get(xdaCommodityEs.getCommodityId());
                if (null != commodityTextEsODTO) {
                    xdaCommodityEs.setId(commodityTextEsODTO.getCommodityId());
                    xdaCommodityEs.setCommodityAppName(commodityTextEsODTO.getCommodityAppName());
                    xdaCommodityEs.setCommoditySubName(commodityTextEsODTO.getCommoditySubName());
                    xdaCommodityEs.setDefaultImageUrl(commodityTextEsODTO.getDefaultImageUrl());
                    xdaCommodityEs.setDeliveryDateRangeCode(commodityTextEsODTO.getDeliveryDateRangeCode());
                    xdaCommodityEs.setDeliveryDateRangeValue(commodityTextEsODTO.getDeliveryDateRangeValue());
                    xdaCommodityEs.setAppStatus(commodityTextEsODTO.getAppStatus());
                    xdaCommodityEs.setXdaFirstCategoryId(commodityTextEsODTO.getXdaFirstCategoryId());
                    xdaCommodityEs.setXdaSecondCategoryId(commodityTextEsODTO.getXdaSecondCategoryId());
                    xdaCommodityEs.setSortNum(commodityTextEsODTO.getSortNum());
                    xdaCommodityEs.setCommoditySearchName(commodityTextEsODTO.getCommodityAppName() + " " + commodityTextEsODTO.getXdaSecondCategoryName());
                    xdaCommodityEs.setXdaFirstCategoryName(commodityTextEsODTO.getXdaFirstCategoryName());
                    xdaCommodityEs.setXdaSecondCategoryName(commodityTextEsODTO.getXdaSecondCategoryName());
                    if (StringUtils.isNotBlank(commodityTextEsODTO.getDeliveryDateRangeCode())) {
                        String[] codes = commodityTextEsODTO.getDeliveryDateRangeCode().split("-");
                        xdaCommodityEs.setDeliveryDateAfterInterval(Integer.parseInt(codes[1]));
                        xdaCommodityEs.setDeliveryDateAnteriorInterval(Integer.parseInt(codes[0]));
                    }

                    xdaCommodityEs.setPfAppStatus(commodityTextEsODTO.getPfAppStatus());
                    xdaCommodityEs.setPfDeliveryDateRangeCode(commodityTextEsODTO.getPfDeliveryDateRangeCode());
                    xdaCommodityEs.setPfDeliveryDateRangeValue(commodityTextEsODTO.getPfDeliveryDateRangeValue());
                    if (StringUtils.isNotBlank(commodityTextEsODTO.getPfDeliveryDateRangeCode())) {
                        String[] codes = commodityTextEsODTO.getPfDeliveryDateRangeCode().split("-");
                        xdaCommodityEs.setPfDeliveryDateAfterInterval(Integer.parseInt(codes[1]));
                        xdaCommodityEs.setPfDeliveryDateAnteriorInterval(Integer.parseInt(codes[0]));
                    }
                }

                if (exitMap.containsKey(xdaCommodityEs.getCommodityId())) {
                    XdaCommodityEs old = exitMap.get(xdaCommodityEs.getCommodityId());
                    if (!old.equals(xdaCommodityEs)) {
                        updateData.add(xdaCommodityEs);
                    }
                    exitMap.remove(xdaCommodityEs.getCommodityId());
                } else {
                    insertData.add(xdaCommodityEs);
                }
            }

            log.warn("商品全量同步需要删除的商品信息数量={}",exitMap.size());
            if (!exitMap.isEmpty()) {
                List<Long> deleteIds = new ArrayList<>(exitMap.keySet());
                Example deleteExample = new Example(XdaCommodityEs.class);
                deleteExample.createCriteria().andIn("commodityId", deleteIds);
                xdaCommodityEsMapper.deleteByExample(deleteExample);

                commodityElasticSearchService.deleteByIds(deleteIds);
            }

            log.warn("商品全量同步需要更新的商品信息数量={}",updateData.size());
            if (SpringUtil.isNotEmpty(updateData)) {
                commodityBatchUpdate(updateData);

            }
            log.warn("商品全量同步需要新增的商品信息数量={}",insertData.size());
            if (SpringUtil.isNotEmpty(insertData)) {
                commodityBatchInsert(insertData);

            }
        }else{

            //如果鲜达和批发都下架了，则直接删除
            Example example = new Example(XdaCommodityEs.class);
            if(CollectionUtils.isNotEmpty(commodityIds)) {
                example.createCriteria().andIn("commodityId", commodityIds);
            }
            List<XdaCommodityEs> commodityEsList = xdaCommodityEsMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(commodityEsList)) {
                xdaCommodityEsMapper.deleteByExample(example);

                List<Long> ids = commodityEsList.stream().map(XdaCommodityEs::getCommodityId).collect(Collectors.toList());
                log.warn("需要删除的下架商品信息={}", ids);
                commodityElasticSearchService.deleteByIds(ids);
            }
        }
    }

    public void commodityBatchInsert(List<XdaCommodityEs> list) {
        int index = 0;
        int count = 500;
        while (true) {
            List<XdaCommodityEs> items = list.stream().skip(index).limit(count).collect(Collectors.toList());
            if (items.size() > 0) {
                xdaCommodityEsMapper.batchInsert(items);
                index += items.size();
            } else {
                break;
            }
        }

        List<Long> commodityIds = list.stream().map(XdaCommodityEs::getCommodityId).collect(Collectors.toList());
        log.warn("商品全量同步需要添加的商品信息数量={}",list.size());
        commodityElasticSearchService.commoditySyncEsByCommodityIdList(commodityIds);
    }

    public void commodityBatchUpdate(List<XdaCommodityEs> list) {
        int index = 0;
        int count = 500;
        while (true) {
            List<XdaCommodityEs> items = list.stream().skip(index).limit(count).collect(Collectors.toList());
            if (items.size() > 0) {
                xdaCommodityEsMapper.updateBatchById(items);
                index += items.size();
            } else {
                break;
            }
        }
        List<Long> commodityIds = list.stream().map(XdaCommodityEs::getCommodityId).collect(Collectors.toList());
        log.warn("商品全量同步需要更新的商品信息数量={}",list.size());
        commodityElasticSearchService.commoditySyncEsByCommodityIdList(commodityIds);
    }

    /**
     * 第二天凌晨 刷新限量商品库存状态
     * 1-有库存，0-售罄
     */
    public Boolean refreshStock() {
        /***************1. 查询所有限量商品********************/
//        List<Long> commodityIds = tobCommodityStockClient.selectCommodityIdByStockType(StockTypeEnum.LIMIT.getCode());
//        log.info("查询大仓所有限量的商品,返回:{}", JSON.toJSONString(commodityIds));
//        if (CollectionUtils.isEmpty(commodityIds)) {
//            return true;
//        }
//
//        Date now = new Date();
//        List<XdaCommodityEs> xdaCommodityEsList = esCommodityTextService.getXdaCommodityListByCommodityIdList(commodityIds);
//        if(SpringUtil.isEmpty(xdaCommodityEsList)){
//            log.info("当前XdaCommodityEs中不存在限量商品:{}", JSON.toJSONString(commodityIds));
//            return true;
//        }
//        // 只需要处理在XdaCommodityEs表中存在的限量商品
//        commodityIds = xdaCommodityEsList.stream().map(XdaCommodityEs::getCommodityId).collect(Collectors.toList());
//        /***************2. 查询t+7天的库存********************/
//        EsXdaCommoditySoldOutIDTO idto = new EsXdaCommoditySoldOutIDTO();
//        idto.setCommodityIdList(commodityIds);
//        idto.setType(1);
//        idto.setDate(DateUtil.addDay(new Date(), 7));
//        //查询限量商品的库存
//        List<EsXdaCommoditySoldOutODTO> commodityInventoryList = tobCommodityStockClient.queryCommodityInventory(idto);
//        QYAssert.isTrue(SpringUtil.isNotEmpty(commodityInventoryList), "未查询到t+7库存");
//        Map<Long, Integer> commodityInventoryMap = commodityInventoryList.stream().collect(Collectors.toMap(EsXdaCommoditySoldOutODTO::getCommodityId, EsXdaCommoditySoldOutODTO::getSoldOut));
//
//        /***************3. 更新t_xda_commodity_es表的sold_out字段********************/
//        esCommodityTextService.updateStock(commodityInventoryMap, now, xdaCommodityEsList);
//
//        /***************4. 多线程更新t_xda_store_commodity_es表的sold_out字段********************/
//
//        for (XdaCommodityEs xdaCommodityEs : xdaCommodityEsList) {
//            Integer commodityInventory = commodityInventoryMap.get(xdaCommodityEs.getCommodityId());
//            xdaStoreCommodityUpdateService.updateByCommodity(commodityInventory, now, xdaCommodityEs );
//        }
        return true;
    }


    public List<XdaCommodityEs> pageByUpdateTime(Date updateTime, Integer from, Integer offset) {
       return  xdaCommodityEsMapper.selectByPage(updateTime, from, offset);
    }

}
