package com.pinshang.qingyun.xda.search.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.BeanUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextEsODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.SelectXdaCommodityInfoListIDTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityTextClient;
import com.pinshang.qingyun.xda.search.enums.AppTypeEnums;
import com.pinshang.qingyun.xda.search.mapper.XdaCommodityEsMapper;
import com.pinshang.qingyun.xda.search.model.XdaCommodityEs;
import com.pinshang.qingyun.xda.search.service.es.CommodityElasticSearchService;
import com.pinshang.qingyun.xda.search.vo.ESXdaOrderCommodityVO;
import com.pinshang.qingyun.xda.search.vo.EsXdaCommodityTextChangeKafkaVO;
import com.pinshang.qingyun.xda.search.vo.XdaCommoditySoldOutUpdateVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/04/07
 * @Version 1.0
 */
@Service
@Slf4j
public class EsCommodityTextService {
    @Autowired
    private XdaCommodityEsMapper xdaCommodityEsMapper;


    @Autowired
    private XdaCommodityTextClient xdaCommodityTextClient;

    @Lazy
    @Autowired
    private CommodityElasticSearchService commodityElasticSearchService;


    @Transactional(rollbackFor = Exception.class)
    public Boolean updateXdaCommodityText(List<EsXdaCommodityTextChangeKafkaVO> voList){
        List<XdaCommodityEs> xdaCommodityEsList = new ArrayList<>(voList.size());
//        List<XdaStoreCommodityEs> xdaStoreCommodityEsList = new ArrayList<>(voList.size());
        // 查询当前商品信息
        List<Long> commodityIdList = voList.stream().map(EsXdaCommodityTextChangeKafkaVO::getCommodityId).collect(Collectors.toList());
        SelectXdaCommodityInfoListIDTO commodityQuery = new SelectXdaCommodityInfoListIDTO();
        commodityQuery.setCommodityIdList(commodityIdList);
        List<CommodityTextEsODTO> commodityInfoList = xdaCommodityTextClient.selectCommodityTextEsList(commodityQuery);
        Map<Long, CommodityTextEsODTO> commodityMap = commodityInfoList.stream().collect(Collectors.toMap(CommodityTextEsODTO::getCommodityId, Function.identity()));

        // 查询历史XdaCommodityEs表信息
        Example oldCommodityExample = new Example(XdaCommodityEs.class);
        oldCommodityExample.createCriteria().andIn("commodityId", commodityIdList);
        List<XdaCommodityEs> oldCommodityList = xdaCommodityEsMapper.selectByExample(oldCommodityExample);
        if(SpringUtil.isEmpty(oldCommodityList)){
            log.info("所有商品不存在于t_xda_commodity_es. commodity_id= {}" , commodityIdList);
            return true;
        }
        Map<Long, XdaCommodityEs> oldCommodityMap = oldCommodityList.stream().collect(Collectors.toMap(XdaCommodityEs::getCommodityId, Function.identity()));

        voList.forEach(it -> {
            QYAssert.isTrue(null != it.getCommodityId(), "商品id不能为空");
            XdaCommodityEs oldCommodity = oldCommodityMap.get(it.getCommodityId());
            if(null == oldCommodity) {
                log.info("商品不存在于t_xda_commodity_es. commodity_id= {}", it.getCommodityId());
            }else {
                CommodityTextEsODTO commodityInfo = commodityMap.get(it.getCommodityId());

                XdaCommodityEs xdaCommodityEs = this.handleXdaCommodityEs(oldCommodity, commodityInfo);
                if (null != xdaCommodityEs) {
                    xdaCommodityEsList.add(xdaCommodityEs);
                }
            }
        });
        if(!xdaCommodityEsList.isEmpty()) {
//            xdaCommodityEsMapper.batchUpdateCommodityText(xdaCommodityEsList);
            xdaCommodityEsMapper.updateBatchById(xdaCommodityEsList);
        }

        commodityElasticSearchService.commoditySyncEsByCommodityIdList(commodityIdList);
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean updateXdaOrderCommodity(List<ESXdaOrderCommodityVO> voList){
        List<XdaCommodityEs> xdaCommodityEsList = new ArrayList<>(voList.size());
        Date now = new Date();
        String[] deliveryDate = voList.get(0).getDeliveryDateRangeCode().split("-");
        int deliveryDateAnteriorInterval = Integer.parseInt(deliveryDate[0]);
        int deliveryDateAfterInterval = Integer.parseInt(deliveryDate[1]);
        List<Long> commodityIdList = voList.stream().map(ESXdaOrderCommodityVO::getCommodityId).collect(Collectors.toList());
        Example example = new Example(XdaCommodityEs.class);
        example.createCriteria().andIn("commodityId", commodityIdList);
        List<XdaCommodityEs> recordList = xdaCommodityEsMapper.selectByExample(example);
        if(SpringUtil.isEmpty(recordList)){
            log.warn("商品不存在于t_xda_commodity_es表, 商品id:{}", commodityIdList);
            return true;
        }
        //Map<Long, String> recordMap = recordList.stream().collect(Collectors.toMap(XdaCommodityEs::getCommodityId, XdaCommodityEs::getDeliveryDateRangeCode));
        Map<Long, XdaCommodityEs> recordMap = recordList.stream().collect(Collectors.toMap(XdaCommodityEs::getCommodityId, Function.identity()));

        voList.forEach(it -> {
            QYAssert.isTrue(null != it.getCommodityId(), "商品id不能为空");
            if(recordMap.containsKey(it.getCommodityId())){
                XdaCommodityEs item = recordMap.get(it.getCommodityId());
                if(AppTypeEnums.XDA.getCode().equals(it.getAppType())) {
                    item.setDeliveryDateRangeCode(it.getDeliveryDateRangeCode());
                    item.setDeliveryDateRangeValue(it.getDeliveryDateRangeValue());
                    item.setDeliveryDateAfterInterval(deliveryDateAfterInterval);
                    item.setDeliveryDateAnteriorInterval(deliveryDateAnteriorInterval);
                }else {
                    item.setPfDeliveryDateRangeCode(it.getDeliveryDateRangeCode());
                    item.setPfDeliveryDateRangeValue(it.getDeliveryDateRangeValue());
                    item.setPfDeliveryDateAfterInterval(deliveryDateAfterInterval);
                    item.setPfDeliveryDateAnteriorInterval(deliveryDateAnteriorInterval);
                }

                item.setUpdateTime(now);
                xdaCommodityEsList.add(item);
            }
        });
        if(!xdaCommodityEsList.isEmpty()) {
            xdaCommodityEsMapper.batchUpdateDeliveryDate(xdaCommodityEsList);
        }

        commodityElasticSearchService.commoditySyncEsByCommodityIdList(commodityIdList);
//        if(SpringUtil.isNotEmpty(updateCommodityIdList)) {
//            xdaStoreCommodityEsMapper.updateDeliveryDate(updateCommodityIdList, deliveryDateAnteriorInterval, deliveryDateAfterInterval, now);
//        }
        return true;
    }

    private XdaCommodityEs handleXdaCommodityEs(XdaCommodityEs oldCommodity, CommodityTextEsODTO commodityInfo){
        Date now = new Date();
        XdaCommodityEs xdaCommodityEs = oldCommodity;
//        xdaCommodityEs.setCommodityId(oldCommodity.getCommodityId());
        boolean flag = false;
        if(!Objects.equals(oldCommodity.getCommodityAppName(), commodityInfo.getCommodityAppName())){
            xdaCommodityEs.setCommodityAppName(commodityInfo.getCommodityAppName());
            xdaCommodityEs.setCommoditySearchName(commodityInfo.getCommodityAppName() + " " + commodityInfo.getXdaSecondCategoryName());
            flag = true;
        }
        if(!Objects.equals(oldCommodity.getCommoditySubName(), commodityInfo.getCommoditySubName())){
            xdaCommodityEs.setCommoditySubName(commodityInfo.getCommoditySubName());
            flag = true;
        }
        if(null == commodityInfo.getSortNum() && oldCommodity.getSortNum() != null){
            xdaCommodityEs.setSortNum(null);
            flag = true;
        }else if(null != commodityInfo.getSortNum() && oldCommodity.getSortNum() == null){
            xdaCommodityEs.setSortNum(commodityInfo.getSortNum());
            flag = true;
        }else if(oldCommodity.getSortNum() != null && oldCommodity.getSortNum().intValue() != commodityInfo.getSortNum()){
            xdaCommodityEs.setSortNum(commodityInfo.getSortNum());
            flag = true;
        }
        if(!Objects.equals(oldCommodity.getDefaultImageUrl(), commodityInfo.getDefaultImageUrl())){
            xdaCommodityEs.setDefaultImageUrl(commodityInfo.getDefaultImageUrl());
            flag = true;
        }
        if(!Objects.equals(oldCommodity.getXdaFirstCategoryId(),commodityInfo.getXdaFirstCategoryId())){
            xdaCommodityEs.setXdaFirstCategoryId(commodityInfo.getXdaFirstCategoryId());
            flag = true;
        }
        if(!Objects.equals(oldCommodity.getXdaFirstCategoryName(),commodityInfo.getXdaFirstCategoryName())){
            xdaCommodityEs.setXdaFirstCategoryName(commodityInfo.getXdaFirstCategoryName());
            flag = true;
        }
        if(!Objects.equals(oldCommodity.getXdaSecondCategoryId(),commodityInfo.getXdaSecondCategoryId())){
            xdaCommodityEs.setXdaSecondCategoryId(commodityInfo.getXdaSecondCategoryId());
            flag = true;
        }
        if(!Objects.equals(oldCommodity.getXdaSecondCategoryName(),commodityInfo.getXdaSecondCategoryName())){
            xdaCommodityEs.setXdaSecondCategoryName(commodityInfo.getXdaSecondCategoryName());
            xdaCommodityEs.setCommoditySearchName(commodityInfo.getCommodityAppName() + " " + commodityInfo.getXdaSecondCategoryName());
            flag = true;
        }
        if(!Objects.equals(oldCommodity.getDeliveryDateRangeCode(),commodityInfo.getDeliveryDateRangeCode())){
            xdaCommodityEs.setDeliveryDateRangeCode(commodityInfo.getDeliveryDateRangeCode());
            flag = true;
        }
        if(!Objects.equals(oldCommodity.getDeliveryDateRangeValue(),commodityInfo.getDeliveryDateRangeValue())){
            xdaCommodityEs.setDeliveryDateRangeValue(commodityInfo.getDeliveryDateRangeValue());
            flag = true;
        }
        if(!Objects.equals(oldCommodity.getPfDeliveryDateRangeCode(),commodityInfo.getPfDeliveryDateRangeCode())){
            xdaCommodityEs.setPfDeliveryDateRangeCode(commodityInfo.getPfDeliveryDateRangeCode());
            flag = true;
        }
        if(!Objects.equals(oldCommodity.getPfDeliveryDateRangeValue(),commodityInfo.getPfDeliveryDateRangeValue())){
            xdaCommodityEs.setPfDeliveryDateRangeValue(commodityInfo.getPfDeliveryDateRangeValue());
            flag = true;
        }
        xdaCommodityEs.setUpdateTime(now);
        return flag ? xdaCommodityEs : null;
    }

//    private XdaStoreCommodityEs handleXdaStoreCommodityEs(XdaCommodityEs oldCommodity,  CommodityTextEsODTO commodityInfo,
//                                                          XdaCategoryODTO cate1Info,  XdaCategoryODTO cate2Info){
//        Date now = new Date();
//        XdaStoreCommodityEs xdaStoreCommodityEs = new XdaStoreCommodityEs();
//        xdaStoreCommodityEs.setCommodityId(commodityInfo.getCommodityId());
//        boolean flag = false;
//        if(!Objects.equals(commodityInfo.getCommodityAppName(), oldCommodity.getCommodityAppName())) {
//            String newSearchName = commodityInfo.getCommodityAppName() + " " + cate2Info.getCateName();
//            xdaStoreCommodityEs.setCommoditySearchName(newSearchName);
//            flag = true;
//        }
//        if(!Objects.equals(commodityInfo.getXdaFirstCategoryId(), oldCommodity.getXdaFirstCategoryId())) {
//            xdaStoreCommodityEs.setXdaFirstCategoryId(commodityInfo.getXdaFirstCategoryId());
//            xdaStoreCommodityEs.setXdaFirstCategoryName(cate1Info.getCateName());
//            flag = true;
//        }
//        if(!Objects.equals(commodityInfo.getXdaSecondCategoryId(), oldCommodity.getXdaSecondCategoryId())) {
//            String newSearchName = commodityInfo.getCommodityAppName() + " " + cate2Info.getCateName();
//            xdaStoreCommodityEs.setCommoditySearchName(newSearchName);
//            xdaStoreCommodityEs.setXdaSecondCategoryId(commodityInfo.getXdaSecondCategoryId());
//            xdaStoreCommodityEs.setXdaSecondCategoryName(cate2Info.getCateName());
//            flag = true;
//        }
//        xdaStoreCommodityEs.setUpdateTime(now);
//        return flag ? xdaStoreCommodityEs : null;
//    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStock( Map<Long, Integer> commodityInventoryMap, Date now, List<XdaCommodityEs> xdaCommodityEsList){
        List<XdaCommoditySoldOutUpdateVO> updateCommodityList = xdaCommodityEsList.stream()
                .filter(it -> commoditySoldOutNeedUpdate(commodityInventoryMap.get(it.getCommodityId()), it))
                .map(it -> {
                    XdaCommoditySoldOutUpdateVO item = new XdaCommoditySoldOutUpdateVO();
                    item.setCommodityId(it.getCommodityId());
                    item.setSoldOut(it.getSoldOut1());
                    item.setSoldOut1(it.getSoldOut2());
                    item.setSoldOut2(it.getSoldOut3());
                    item.setSoldOut3(it.getSoldOut4());
                    item.setSoldOut4(it.getSoldOut5());
                    item.setSoldOut5(it.getSoldOut6());
                    item.setSoldOut6(it.getSoldOut7());
                    item.setSoldOut7(commodityInventoryMap.get(it.getCommodityId()));
                    item.setUpdateTime(now);
                    return item;
                })
                .collect(Collectors.toList());
        if(SpringUtil.isNotEmpty(updateCommodityList)){
            xdaCommodityEsMapper.batchUpdateCommoditySoldOut(updateCommodityList);
        }
    }

    public List<XdaCommodityEs> getXdaCommodityListByCommodityIdList(List<Long> commodityIds){
        Example example = new Example(XdaCommodityEs.class);
        example.selectProperties("id","commodityId", "soldOut", "soldOut1", "soldOut2", "soldOut3", "soldOut4", "soldOut5", "soldOut6", "soldOut7", "appStatus" );
        example.createCriteria().andIn("commodityId", commodityIds);
        List<XdaCommodityEs> xdaCommodityEsList = xdaCommodityEsMapper.selectByExample(example);
        if(SpringUtil.isEmpty(xdaCommodityEsList)) {
            log.info("商品表信息为空, commodityIdList:{}", commodityIds);
        }
        return xdaCommodityEsList;
    }

    public Boolean commoditySoldOutNeedUpdate(int soldOut, XdaCommodityEs oldRecord){
        return !Objects.equals(oldRecord.getSoldOut1(), oldRecord.getSoldOut()) || !Objects.equals(oldRecord.getSoldOut2(), oldRecord.getSoldOut1())
               || !Objects.equals(oldRecord.getSoldOut3(), oldRecord.getSoldOut2())|| !Objects.equals(oldRecord.getSoldOut4(), oldRecord.getSoldOut3())
                || !Objects.equals(oldRecord.getSoldOut5(), oldRecord.getSoldOut4()) || !Objects.equals(oldRecord.getSoldOut6(), oldRecord.getSoldOut5())
                || !Objects.equals(oldRecord.getSoldOut7(), oldRecord.getSoldOut6()) || soldOut !=  oldRecord.getSoldOut7();
    }

}
