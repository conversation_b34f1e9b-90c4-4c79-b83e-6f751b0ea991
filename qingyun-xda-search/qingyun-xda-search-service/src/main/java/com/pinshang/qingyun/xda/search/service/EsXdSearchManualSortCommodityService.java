package com.pinshang.qingyun.xda.search.service;

import com.pinshang.qingyun.xda.search.document.EsXdSearchManualSortCommodity;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @ClassName EsXdSearchManualSortCommodityService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/2 20:00
 * @Version 1.0
 */
@Slf4j
@Service
public class EsXdSearchManualSortCommodityService {

    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;

    /**
     * 手工排序商品分组关键词查询-前端搜索商品排序
     *
     * @param keyWord
     * @return
     */
    public List<EsXdSearchManualSortCommodity> searchKeyWord(String keyWord) {
        if (StringUtils.hasText(keyWord)) {
            Sort sort = Sort.by(new Sort.Order(Sort.Direction.DESC,"sort"));
            Pageable pageable = PageRequest.of(0, 100, sort);
            QueryBuilder queryBuilder = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termQuery("keyword", keyWord));
            NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
            nativeSearchQueryBuilder.withQuery(queryBuilder).withPageable(pageable);

            SearchHits<EsXdSearchManualSortCommodity> search = elasticsearchTemplate.search(nativeSearchQueryBuilder.build(),EsXdSearchManualSortCommodity.class);
            List<SearchHit<EsXdSearchManualSortCommodity>> searchHits = search.getSearchHits();
            List<EsXdSearchManualSortCommodity> list = new ArrayList<>();
            if(CollectionUtils.isNotEmpty(searchHits)){
                for(SearchHit<EsXdSearchManualSortCommodity> searchHit : searchHits){
                    EsXdSearchManualSortCommodity es = searchHit.getContent();
                    list.add(es);
                }
                return list;
            }
        }
        return Collections.EMPTY_LIST;
    }
}
