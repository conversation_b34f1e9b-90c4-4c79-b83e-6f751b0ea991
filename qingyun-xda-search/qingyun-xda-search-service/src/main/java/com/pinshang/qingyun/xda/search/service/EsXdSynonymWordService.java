package com.pinshang.qingyun.xda.search.service;

import com.google.common.collect.Lists;
import com.pinshang.qingyun.xda.search.document.EsXdSynonymWord;
import com.pinshang.qingyun.xda.search.repository.EsXdSynonymWordRepository;
import com.pinshang.qingyun.xda.search.service.es.CommodityElasticSearchService;
import com.pinshang.qingyun.xda.search.vo.EsXdSearchSynonymWordKafkaVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @ClassName EsXdSynonymWordService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/2 20:07
 * @Version 1.0
 */
@Service
@Slf4j
public class EsXdSynonymWordService {
    @Autowired
    private ElasticsearchRestTemplate elasticsearchTemplate;

    @Autowired
    private EsXdSynonymWordRepository esXdSynonymWordRepository;

    @Autowired
    private CommodityElasticSearchService commodityElasticSearchService;

    /**
     * 查询索引中的近义词
     * */
    public List<String> querySynonymWord(String searchWord){
        if(StringUtils.isNotBlank(searchWord)){
            List<String> synonymWordList = new ArrayList<>();
            QueryBuilder queryBuilder = QueryBuilders.boolQuery().must(QueryBuilders.termQuery("synonymWord",searchWord));
            NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
            nativeSearchQueryBuilder.withQuery(queryBuilder);
            SearchHits<EsXdSynonymWord> search = elasticsearchTemplate.search(nativeSearchQueryBuilder.build(),EsXdSynonymWord.class);
            List<SearchHit<EsXdSynonymWord>> searchHits = search.getSearchHits();
            if(CollectionUtils.isNotEmpty(searchHits)){
                for(SearchHit<EsXdSynonymWord> searchHit : searchHits){
                    EsXdSynonymWord model = searchHit.getContent();
                    String synonymWord = model.getSynonymWord();
                    if(StringUtils.isNotBlank(synonymWord)){
                        synonymWordList.addAll(Arrays.asList(synonymWord.split(" ")));
                    }
                }
            }

            return synonymWordList;
        }
        return Collections.emptyList();
    }

    public void synonymWordUpdateIndex(EsXdSearchSynonymWordKafkaVo vo){
        Date currentDate = new Date();
        // 默认类型为运营配置的类型
        int synonymWordType = Optional.of(vo).map(EsXdSearchSynonymWordKafkaVo::getSynonymWordType).orElse(1);
        if (synonymWordType == 1) {
            // 运营配置的近义词变更，更新近义词 ES
            EsXdSynonymWord esXdSynonymWord = new EsXdSynonymWord(vo.getCode(), vo.getName(), vo.getSynonymWord(), vo.getStatus(), currentDate);
            if (vo.getStatus() == 1) {
                esXdSynonymWordRepository.save(esXdSynonymWord);
            } else {
                esXdSynonymWordRepository.delete(esXdSynonymWord);
            }
        }

        try {
            // 同步更新ES 数据
            commodityElasticSearchService.updateEsCommodity4SynonymWord(Lists.newArrayList(StringUtils.split(vo.getSynonymWord(),"、")));
        } catch (Exception e) {
            log.warn("synonymWordUpdateIndex updateEsCommodity4SynonymWord error:[{}]",vo, e);
        }
    }
}
