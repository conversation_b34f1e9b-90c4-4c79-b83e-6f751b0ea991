package com.pinshang.qingyun.xda.search.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.ListToPageInfoUtil;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.marketing.dto.mtCoupon.MtCouponToUseIDTO;
import com.pinshang.qingyun.marketing.dto.mtCoupon.MtCouponToUseODTO;
import com.pinshang.qingyun.marketing.service.mtCoupon.MtCouponTobClient;
import com.pinshang.qingyun.xda.product.dto.StoreCommodityPriceODTO;
import com.pinshang.qingyun.xda.product.dto.category.FeignXdaCategoryResODTO;
import com.pinshang.qingyun.xda.product.dto.category.FeignXdaExtraCategoryIDTO;
import com.pinshang.qingyun.xda.product.dto.front.FeignXdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.product.dto.front.FeignXdaCategoryCommodityResODTO;
import com.pinshang.qingyun.xda.product.dto.front.FeignXdaRecommondCommodityIDTO;
import com.pinshang.qingyun.xda.product.dto.front.FeignXdaRecommondCommodityODTO;
import com.pinshang.qingyun.xda.product.service.XdaCategoryClient;
import com.pinshang.qingyun.xda.product.service.XdaCommodityFrontClient;
import com.pinshang.qingyun.xda.search.document.EsXdaCommodity;
import com.pinshang.qingyun.xda.search.dto.CategoryGoodsReq;
import com.pinshang.qingyun.xda.search.dto.KeyWordGoodsReq;
import com.pinshang.qingyun.xda.search.dto.commodity.XdaCommodityODTO;
import com.pinshang.qingyun.xda.search.dto.front.XdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryBaseODTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryCommodityResODTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryODTO;
import com.pinshang.qingyun.xda.search.service.manager.CouponCommodityManagerService;
import com.pinshang.qingyun.xda.search.util.StopWatchUtil;
import com.pinshang.qingyun.xda.search.util.ThreadLocalUtils;
import com.pinshang.qingyun.xda.search.vo.EsXdaCategoryGoodsNewVO;
import com.pinshang.qingyun.xda.search.vo.SearchResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;


/**
 * @ClassName EsXdaCommodityFrontService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/2 18:09
 * @Version 1.0
 */
@Service
@Slf4j
public class EsXdaCategoryCommodityFrontService extends AbstractSearchService<ApiResponse<XdaCommodityODTO>, KeyWordGoodsReq, List<EsXdaCategoryGoodsNewVO>, CategoryGoodsReq> {

    @Autowired
    private XdaCategoryClient xdaCategoryClient;
    @Autowired
    private XdaCommodityFrontClient xdaCommodityFrontClient;
    @Autowired
    private MtCouponTobClient mtCouponTobClient;
    @Autowired
    private CouponCommodityManagerService couponCommodityManagerService;

    /**
     * 补充特殊的一级类目，如 特惠/常购等
     *
     * @param categoryList
     * @param feignXdaCategoryResODTO
     */
    private static void addSpecialCategory(List<XdaCategoryODTO> categoryList, FeignXdaCategoryResODTO feignXdaCategoryResODTO) {
        if (SpringUtil.isEmpty(feignXdaCategoryResODTO.getCategoryList())) {
            return;
        }

        List<XdaCategoryODTO> xdaCategoryODTOS = feignXdaCategoryResODTO.getCategoryList().stream().map(xdaCategoryODTO -> {
            //复制一级
            XdaCategoryODTO result = BeanCloneUtils.copyTo(xdaCategoryODTO, XdaCategoryODTO.class);
            //复制二级
            List<XdaCategoryODTO> secondCategoryList = BeanCloneUtils.copyTo(xdaCategoryODTO.getSecondCategoryList(), XdaCategoryODTO.class);
            result.setXdaSecondCategoryList(secondCategoryList);
            return result;
        }).collect(Collectors.toList());
        categoryList.addAll(0, xdaCategoryODTOS);
    }

    /**
     * 补充二级推荐类目
     *
     * @param categoryList
     * @param feignXdaCategoryResODTO
     */
    private static void addRecommondCategoryIfNecessary(List<XdaCategoryODTO> categoryList, FeignXdaCategoryResODTO feignXdaCategoryResODTO) {
        if (SpringUtil.isEmpty(feignXdaCategoryResODTO.getRecommondFirstCategoryIdSet())) {
            return;
        }

        Set<Long> recommondFirstCategoryIdSet = feignXdaCategoryResODTO.getRecommondFirstCategoryIdSet();

        categoryList.forEach(category -> {
            Long firstCategoryId = category.getXdaCategoryId();

            if (recommondFirstCategoryIdSet.contains(firstCategoryId)) {
                XdaCategoryODTO recommondCategory = XdaCategoryODTO.buildRecommendLabel(firstCategoryId);
                List<XdaCategoryODTO> xdaSecondCategoryList = category.getXdaSecondCategoryList();

                if (SpringUtil.isEmpty(xdaSecondCategoryList)) {
                    xdaSecondCategoryList = new ArrayList<>(1);
                    category.setXdaSecondCategoryList(xdaSecondCategoryList);
                }
                xdaSecondCategoryList.add(0, recommondCategory);
            }
        });
    }

    /**
     * 关键字搜索
     * 1、近义词搜索，2、普通搜索
     *
     * @param keyWordGoodsReq
     * @return
     */
    @Override
    public ApiResponse<XdaCommodityODTO> search(KeyWordGoodsReq keyWordGoodsReq) {
        PageInfo<XdaCommodityODTO> esXdGoodsNewVOPage = this.getKeyWordSearchList(keyWordGoodsReq);
        return ApiResponse.convert(esXdGoodsNewVOPage);
    }

    /**
     * 关键字搜索 -普通+近义词搜索
     *
     * @param keyWordGoodsReq
     * @return
     */
    public PageInfo<XdaCommodityODTO> getKeyWordSearchList(KeyWordGoodsReq keyWordGoodsReq) {
        try {
            // 查询产品价格方案信息，渲染价格
            List<StoreCommodityPriceODTO> storeCommodityPriceList = xdaCommodityFrontClient.getStoreCommodityPrice(keyWordGoodsReq.getStoreId());
            if (CollectionUtils.isEmpty(storeCommodityPriceList)) {
                // 如果客户产品价格方案下面商品没有，返回空
                return new PageInfo();
            }
            // 产品价格方案下面的商品id集合
            List<Long> storeCommodityIdList = storeCommodityPriceList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

            // 根据keyword一次最多搜索出来200条，再排序
            SearchResultVo goodsPage = keyWordSearch(keyWordGoodsReq, storeCommodityIdList);
            List<EsXdaCommodity> esXdCommodityList = goodsPage.getSortList();
            if(CollectionUtils.isEmpty(esXdCommodityList)){
                return new PageInfo();
            }
            //通过es或redis查询回来数据查询具体商品信息
            List<XdaCommodityODTO> goodsList = buildGoodsList(keyWordGoodsReq, esXdCommodityList, storeCommodityPriceList);
            // 按 salesStatus 排序
            goodsList = goodsList.stream().sorted(Comparator.comparing(XdaCommodityODTO::getSalesStatus)).collect(Collectors.toList());
            PageInfo<XdaCommodityODTO> goodsPageInfo = ListToPageInfoUtil.convert(goodsList, keyWordGoodsReq.getPageSize(), keyWordGoodsReq.getPageNo() + 1);

            // 渲染商品信息
            renderXdaCommodity(keyWordGoodsReq, goodsPageInfo.getList());

            return goodsPageInfo;
        } catch (Exception e) {
            // 这里异常捕获的原因在于，当搜索异常时，推荐依旧可用
            log.error("-->>es普通查询商品异常,异常详情：{},req = {} ", e.getMessage(), keyWordGoodsReq, e);
        }
        return new PageInfo<>();
    }

    /**
     * 优惠券去使用，获取优惠券下的可使用商品信息
     * @param couponUserId
     * @param orderTime
     * @param storeId
     * @return
     */
    public MtCouponToUseODTO queryCouponToUse(Long couponUserId, Date orderTime, Long storeId) {
        MtCouponToUseIDTO mtCouponToUseIDTO = MtCouponToUseIDTO.builder()
                .couponUserId(couponUserId)
                .orderTime(orderTime)
                .storeId(storeId)
                .build();
        MtCouponToUseODTO mtCouponToUseODTO = mtCouponTobClient.queryCouponToUseCommodity(mtCouponToUseIDTO);

        return mtCouponToUseODTO;
    }

    /**
     * 优惠券去使用
     * @param keyWordGoodsReq
     * @return
     */
    public ApiResponse<XdaCommodityODTO> couponToUseSearch(KeyWordGoodsReq keyWordGoodsReq, MtCouponToUseODTO mtCouponToUseODTO) {
        // 查询产品价格方案信息，渲染价格
        List<StoreCommodityPriceODTO> storeCommodityPriceList = xdaCommodityFrontClient.getStoreCommodityPrice(keyWordGoodsReq.getStoreId());
        if (CollectionUtils.isEmpty(storeCommodityPriceList)) {
            // 如果客户产品价格方案下面商品没有，返回空
            return ApiResponse.convert(new PageInfo<>());
        }
        // 产品价格方案下面的商品id集合
        List<Long> storeCommodityIdList = storeCommodityPriceList.stream().map(item -> item.getCommodityId()).collect(Collectors.toList());

        // 查询出最大值商品信息
        SearchResultVo goodsPage = couponCommodityManagerService.couponUseKeyWordSearchSwitch(keyWordGoodsReq, storeCommodityIdList, mtCouponToUseODTO);
        List<EsXdaCommodity> esXdCommodityList = goodsPage.getSortList();
        if(CollectionUtils.isEmpty(esXdCommodityList)){
            return ApiResponse.convert(new PageInfo<>());
        }

        List<XdaCommodityODTO> goodsList = buildCouponUseGoodsList(keyWordGoodsReq, esXdCommodityList, storeCommodityPriceList);
        // 按 salesStatus 排序
        goodsList = goodsList.stream().sorted(Comparator.comparing(XdaCommodityODTO::getSalesStatus)).collect(Collectors.toList());
        PageInfo<XdaCommodityODTO> goodsPageInfo = ListToPageInfoUtil.convert(goodsList, keyWordGoodsReq.getPageSize(), keyWordGoodsReq.getPageNo() + 1);

        // 补充商品信息
        replenishCommodityInfo(goodsPageInfo.getList(), keyWordGoodsReq.getStoreId());

        // 渲染商品信息
        renderXdaCommodity(keyWordGoodsReq, goodsPageInfo.getList());
        List<XdaCommodityODTO> enableCouponList = goodsPageInfo.getList().stream().filter(p -> !YesOrNoEnums.NO.getCode().equals(p.getEnableCoupon())).collect(Collectors.toList());
        goodsPageInfo.setList(enableCouponList);
        return ApiResponse.convert(goodsPageInfo);
    }

    /**
     * 商品分类（一级分类，二级分类）
     *
     * @param categoryGoodsReq
     * @return
     */
    @Override
    public List<XdaCategoryCommodityResODTO> leftCategoryList(CategoryGoodsReq categoryGoodsReq) {
        Date orderTime = categoryGoodsReq.getXdaCategoryAppIDTO().getOrderTime();
        QYAssert.notNull(orderTime, "请选择送货日期");

        if (Objects.isNull(categoryGoodsReq.getXdaFirstCategoryId())) {
            //返回一级分类+二级分类信息
            return this.getFirstCategoryList(categoryGoodsReq);
        } else {
            return this.getSecondCategory2CommodityList(categoryGoodsReq);
        }
    }

    @Override
    public List<Long> listRecommondCommodityIds(CategoryGoodsReq categoryGoodsReq) {

        StopWatch stopWatch = StopWatchUtil.begin();

        Long xdaFirstCategoryId = categoryGoodsReq.getXdaFirstCategoryId();

        FeignXdaRecommondCommodityIDTO feignXdaRecommondCommodityIDTO = new FeignXdaRecommondCommodityIDTO();
        feignXdaRecommondCommodityIDTO.setFirstCategoryId(xdaFirstCategoryId);
        FeignXdaRecommondCommodityODTO recommondCommodityList = xdaCategoryClient.queryRecommondCommodity(feignXdaRecommondCommodityIDTO);

        StopWatchUtil.end("推荐商品查询", stopWatch);
        return Objects.nonNull(recommondCommodityList) && CollectionUtils.isNotEmpty(recommondCommodityList.getCommodityIdList())
                ? recommondCommodityList.getCommodityIdList() : Collections.emptyList();
    }

    @Override
    public List<XdaCategoryCommodityResODTO> callRemoteCategoryQuery(XdaCategoryAppIDTO xdaCategoryAppIDTO, String xdaSecondCategoryName) {
        // 查询商品信息
        FeignXdaCategoryAppIDTO feignXdaCategoryAppIDTO = BeanCloneUtils.copyTo(xdaCategoryAppIDTO, FeignXdaCategoryAppIDTO.class);
        FeignXdaCategoryCommodityResODTO feignXdaCategoryCommodityResODTO = xdaCategoryClient.queryXdaCategoryCommodityList(feignXdaCategoryAppIDTO);

        if (Objects.isNull(feignXdaCategoryCommodityResODTO) || CollectionUtils.isEmpty(feignXdaCategoryCommodityResODTO.getCommodityList())) {
            return Collections.emptyList();
        }

        XdaCategoryCommodityResODTO xdaCategoryCommodityResODTO = JSONObject.parseObject(
                JSONObject.toJSONString(feignXdaCategoryCommodityResODTO), XdaCategoryCommodityResODTO.class);
        xdaCategoryCommodityResODTO.setXdaSecondCategoryName(xdaSecondCategoryName);
        return Arrays.asList(xdaCategoryCommodityResODTO);
    }

    public List<XdaCategoryCommodityResODTO> getFirstCategoryList(CategoryGoodsReq categoryGoodsReq) {

        List<XdaCategoryODTO> categoryList = super.getEsFirstCategoryList(categoryGoodsReq);

        Date orderTime = categoryGoodsReq.getXdaCategoryAppIDTO().getOrderTime();
        Long storeId = categoryGoodsReq.getShopId();
        //补充额外的类目
        processExtraCategory(orderTime, categoryList, storeId);

        return BeanCloneUtils.copyTo(categoryList, XdaCategoryCommodityResODTO.class);
    }

    @Override
    public void processExtraCategory(Date orderTime, List<XdaCategoryODTO> categoryList, Long storeId) {

        if (SpringUtil.isEmpty(categoryList)) {
            return;
        }

        FeignXdaCategoryResODTO feignXdaCategoryResODTO = queryExtraCategory(orderTime, categoryList);
        if (Objects.isNull(feignXdaCategoryResODTO)) return;

        addRecommondCategoryIfNecessary(categoryList, feignXdaCategoryResODTO);

        addSpecialCategory(categoryList, feignXdaCategoryResODTO);
    }

    /**
     * 调xda-product查询是否有额外的一级分类信息，二级推荐分类信息
     *
     * @param orderTime
     * @param categoryList
     * @return
     */
    private FeignXdaCategoryResODTO queryExtraCategory(Date orderTime, List<XdaCategoryODTO> categoryList) {
        String orderTimeStr = DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.format(orderTime);
        FeignXdaExtraCategoryIDTO feignXdaExtraCategoryIDTO = new FeignXdaExtraCategoryIDTO();
        if (SpringUtil.isNotEmpty(categoryList)) {
            List<Long> firstCategoryIdList = categoryList.stream().map(XdaCategoryBaseODTO::getXdaCategoryId).collect(Collectors.toList());
            feignXdaExtraCategoryIDTO.setFirstCategoryIdList(firstCategoryIdList);
        }
        feignXdaExtraCategoryIDTO.setOrderTime(orderTimeStr);
        feignXdaExtraCategoryIDTO.setLogisticsCenterId(ThreadLocalUtils.getLogisticsCenterId());
        return xdaCategoryClient.queryExtraCategory(orderTimeStr, feignXdaExtraCategoryIDTO);
    }

}
