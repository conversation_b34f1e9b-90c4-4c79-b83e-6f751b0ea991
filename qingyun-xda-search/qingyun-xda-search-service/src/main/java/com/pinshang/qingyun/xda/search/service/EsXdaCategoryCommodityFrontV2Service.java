package com.pinshang.qingyun.xda.search.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.util.FastThreadLocalUtil;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.xda.search.constants.XdaConstant;
import com.pinshang.qingyun.xda.search.document.EsXdaCategory;
import com.pinshang.qingyun.xda.search.dto.CategoryGoodsReq;
import com.pinshang.qingyun.xda.search.dto.EsManualIDTO;
import com.pinshang.qingyun.xda.search.dto.front.XdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryCommodityODTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryCommodityResODTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryODTO;
import com.pinshang.qingyun.xda.search.service.es.CategoryElasticSearchService;
import com.pinshang.qingyun.xda.search.service.es.CommodityElasticSearchService;
import com.pinshang.qingyun.xda.search.service.manager.EsXdaCategoryManagerService;
import com.pinshang.qingyun.xda.search.util.StopWatchUtil;
import com.pinshang.qingyun.xda.search.vo.category.SpecialCategoryInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.pinshang.qingyun.xda.search.constants.XdaConstant.*;

@Service
@Slf4j
public class EsXdaCategoryCommodityFrontV2Service extends EsXdaCategoryCommodityFrontService {

    @Autowired
    private CommodityElasticSearchService commodityElasticSearchService;

    @Autowired
    private CategoryElasticSearchService categoryElasticSearchService;

    @Autowired
    private EsXdaCategoryManagerService esXdaCategoryManagerService;

    @Override
    public List<XdaCategoryCommodityResODTO> getFirstCategoryList(CategoryGoodsReq categoryGoodsReq) {

        List<XdaCategoryODTO> categoryList = esXdaCategoryManagerService.getEsFirstCategoryListAndSecondCategorySwitch(categoryGoodsReq);

        Date orderTime = categoryGoodsReq.getXdaCategoryAppIDTO().getOrderTime();
        Long storeId = categoryGoodsReq.getShopId();
        //补充额外的类目
        super.processExtraCategory(orderTime, categoryList, storeId);

        return BeanCloneUtils.copyTo(categoryList, XdaCategoryCommodityResODTO.class);
    }

    @Override
    public List<Long> listRecommondCommodityIds(CategoryGoodsReq categoryGoodsReq) {

        Long xdaSecondCategoryId = categoryGoodsReq.getXdaSecondCategoryId();
        if (!Objects.equals(xdaSecondCategoryId, RECOMMOND_CATEGORT_ID)) {
            return Collections.emptyList();
        }
        return super.listRecommondCommodityIds(categoryGoodsReq);
    }

    @Override
    protected void buildCategoryCommodity(CategoryGoodsReq categoryGoodsReq, List<XdaCategoryCommodityODTO> categoryCommoditys, List<XdaCategoryCommodityResODTO> xdaCategoryCommodityResODTOS) {
        if (CollectionUtils.isEmpty(categoryCommoditys) || Objects.equals(categoryGoodsReq.getXdaSecondCategoryId(), RECOMMOND_CATEGORT_ID)) {
            return;
        }
        StopWatch stopWatch = StopWatchUtil.begin();

        Long xdaFirstCategoryId = categoryGoodsReq.getXdaFirstCategoryId();
        Long xdaSecondCategoryId = categoryGoodsReq.getXdaSecondCategoryId();
        Set<Long> secondCategoryIdSet = Sets.newHashSet(xdaSecondCategoryId);
        List<EsXdaCategory> esXdaCategories = categoryElasticSearchService.listByParentIdAndCateIdList(xdaFirstCategoryId, secondCategoryIdSet);

        try {
            categoryCommoditys.sort(commoditySort());
        } catch (Exception e) {
            EsManualIDTO req = new EsManualIDTO();
            commodityElasticSearchService.allCommoditySyncEs(req);
            log.error("=======排序异常,分类请求发生异常 xdaFirstCategoryId:[{}],storeId:[{}]======{}", xdaFirstCategoryId, FastThreadLocalUtil.getXDA().getStoreId(), e);
            throw e;
        }
        List<XdaCategoryCommodityODTO> xdaCategoryCommodityResODTOList = JSONObject.parseArray(JSONObject.toJSONString(categoryCommoditys)
                , XdaCategoryCommodityODTO.class);
        XdaCategoryCommodityResODTO xdaCategoryCommodityResODTO = new XdaCategoryCommodityResODTO();
        xdaCategoryCommodityResODTO.setXdaFirstCategoryId(xdaFirstCategoryId);

        Map<Long, EsXdaCategory> secondCateNameMap = esXdaCategories.stream().collect(Collectors.toMap(EsXdaCategory::getCateId, Function.identity()));
        EsXdaCategory esXdaCategory = Optional.of(secondCateNameMap.get(xdaSecondCategoryId)).orElseGet(EsXdaCategory::new);

        xdaCategoryCommodityResODTO.setXdaSecondCategoryName(esXdaCategory.getCateName());
        xdaCategoryCommodityResODTO.setXdaSecondCategoryId(categoryCommoditys.get(0).getXdaSecondCategoryId());
        xdaCategoryCommodityResODTO.setCommodityList(xdaCategoryCommodityResODTOList);
        xdaCategoryCommodityResODTOS.add(xdaCategoryCommodityResODTO);
        StopWatchUtil.end("构建普通二级分类", stopWatch);
    }

    @Override
    void processOftenBuyCategory(Boolean isVisitor, XdaCategoryAppIDTO xdaCategoryAppIDTO, SpecialCategoryInfo specialCategoryInfo) {
        Long xdaFirstCategoryId = xdaCategoryAppIDTO.getXdaFirstCategoryId();
        Boolean oftenBuyFlag = xdaFirstCategoryId.equals(XdaConstant.FIRST_CATEGORY_OFTEN_BUY);
        if (!oftenBuyFlag) {
            return;
        }

        specialCategoryInfo.setSpecialCategoryFlag(Boolean.TRUE);

        if (isVisitor) {
            return;
        }
        //走远程获取
        Long xdaSecondCategoryId = xdaCategoryAppIDTO.getXdaSecondCategoryId();
        String categoryName = Objects.equals(xdaSecondCategoryId, SECOND_CATEGORY_MY_OFTEN_BUY) ? OFTEN_BUY_SECOND_CATEGORT_NAME : COLLECT_SECOND_CATEGORT_NAME;

        List<XdaCategoryCommodityResODTO> oftenBuy = callRemoteCategoryQuery(xdaCategoryAppIDTO, categoryName);
        if (CollectionUtils.isEmpty(oftenBuy)) {
            oftenBuy = buildDefaultOftenBuy(xdaSecondCategoryId, categoryName);
        }
        List<XdaCategoryCommodityResODTO> resultList = new ArrayList<>(oftenBuy);

        specialCategoryInfo.setXdaCategoryCommodityResODTOS(resultList);
    }

    @Override
    public List<XdaCategoryCommodityResODTO> leftCategoryList(CategoryGoodsReq categoryGoodsReq) {
        Date orderTime = categoryGoodsReq.getXdaCategoryAppIDTO().getOrderTime();
        QYAssert.notNull(orderTime, "请选择送货日期");

        if (Objects.isNull(categoryGoodsReq.getXdaFirstCategoryId())) {
            //返回一级分类+二级分类信息
            return this.getFirstCategoryList(categoryGoodsReq);
        } else {

            XdaCategoryAppIDTO categoryAppIDTO = categoryGoodsReq.getXdaCategoryAppIDTO();
            QYAssert.isTrue(Objects.nonNull(categoryAppIDTO.getXdaFirstCategoryId())
                    && Objects.nonNull(categoryAppIDTO.getXdaSecondCategoryId()), "一二级分类id不能为空");
            return super.getSecondCategory2CommodityList(categoryGoodsReq);
        }
    }
}
