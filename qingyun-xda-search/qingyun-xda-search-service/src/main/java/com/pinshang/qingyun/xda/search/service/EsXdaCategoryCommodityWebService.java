package com.pinshang.qingyun.xda.search.service;

import com.alibaba.fastjson.JSONObject;
import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.xda.product.dto.category.FeignQueryExtraCategoryIDTO;
import com.pinshang.qingyun.xda.product.dto.category.FeignXdaCategoryResODTO;
import com.pinshang.qingyun.xda.product.dto.front.*;
import com.pinshang.qingyun.xda.product.service.XdaCategoryWebClient;
import com.pinshang.qingyun.xda.search.dto.CategoryGoodsReq;
import com.pinshang.qingyun.xda.search.dto.KeyWordGoodsReq;
import com.pinshang.qingyun.xda.search.dto.commodity.XdaCommodityODTO;
import com.pinshang.qingyun.xda.search.dto.front.XdaCategoryAppIDTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryCommodityResODTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryODTO;
import com.pinshang.qingyun.xda.search.dto.web.XdaWebCategoryCommodityResODTO;
import com.pinshang.qingyun.xda.search.util.StopWatchUtil;
import com.pinshang.qingyun.xda.search.vo.EsXdaCategoryGoodsNewVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.time.DateFormatUtils;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;


/**
 * @ClassName EsXdaCategoryCommodityWebService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/2 18:09
 * @Version 1.0
 */
@Service
@Slf4j
public class EsXdaCategoryCommodityWebService extends AbstractSearchService<ApiResponse<XdaCommodityODTO>, KeyWordGoodsReq, List<EsXdaCategoryGoodsNewVO>, CategoryGoodsReq> {

    @Autowired
    private XdaCategoryWebClient xdaCategoryWebClient;

    /**
     * 商品分类（一级分类，二级分类）
     *
     * @param categoryGoodsReq
     * @return
     */
    @Override
    public XdaWebCategoryCommodityResODTO leftCategoryList(CategoryGoodsReq categoryGoodsReq) {
        Date orderTime = categoryGoodsReq.getXdaCategoryAppIDTO().getOrderTime();
        QYAssert.notNull(orderTime, "请选择送货日期");

        XdaWebCategoryCommodityResODTO xdaWebCategoryCommodityResODTO = new XdaWebCategoryCommodityResODTO();
        if (Objects.isNull(categoryGoodsReq.getXdaFirstCategoryId())) {
            //只返回 一级分类
            List<XdaCategoryCommodityResODTO> firstCategoryList = this.getFirstCategoryList(categoryGoodsReq);
            xdaWebCategoryCommodityResODTO.setCategorys(firstCategoryList);

            XdaCategoryAppIDTO xdaCategoryAppIDTO = categoryGoodsReq.getXdaCategoryAppIDTO();
            xdaCategoryAppIDTO.setXdaFirstCategoryId(firstCategoryList.get(0).getXdaCategoryId());
            List<XdaCategoryCommodityResODTO> secondCategory2CommodityList = this.getSecondCategory2CommodityList(categoryGoodsReq);
            xdaWebCategoryCommodityResODTO.setCommoditys(secondCategory2CommodityList);

            return xdaWebCategoryCommodityResODTO;
        } else {
            List<XdaCategoryCommodityResODTO> secondCategory2CommodityList = this.getSecondCategory2CommodityList(categoryGoodsReq);
            xdaWebCategoryCommodityResODTO.setCommoditys(secondCategory2CommodityList);
            return xdaWebCategoryCommodityResODTO;
        }
    }


    public List<XdaCategoryCommodityResODTO> getFirstCategoryList(CategoryGoodsReq categoryGoodsReq) {

        List<XdaCategoryODTO> categoryList = super.getEsFirstCategoryList(categoryGoodsReq);

        Date orderTime = categoryGoodsReq.getXdaCategoryAppIDTO().getOrderTime();
        Long storeId = categoryGoodsReq.getShopId();
        //补充额外的类目
        processExtraCategory(orderTime, categoryList, storeId);

        return BeanCloneUtils.copyTo(categoryList, XdaCategoryCommodityResODTO.class);
    }

    @Override
    public List<Long> listRecommondCommodityIds(CategoryGoodsReq categoryGoodsReq) {

        StopWatch stopWatch = StopWatchUtil.begin();

        Long xdaFirstCategoryId = categoryGoodsReq.getXdaFirstCategoryId();
        Long storeId = categoryGoodsReq.getShopId();

        FeignXdaRecommondCommodityWebIDTO feignXdaRecommondCommodityIDTO = new FeignXdaRecommondCommodityWebIDTO();
        feignXdaRecommondCommodityIDTO.setFirstCategoryId(xdaFirstCategoryId);
        feignXdaRecommondCommodityIDTO.setStoreId(storeId);
        FeignXdaRecommondCommodityODTO recommondCommodityList = xdaCategoryWebClient.queryRecommondCommodity(feignXdaRecommondCommodityIDTO);

        StopWatchUtil.end("推荐商品查询", stopWatch);
        return Objects.nonNull(recommondCommodityList) && CollectionUtils.isNotEmpty(recommondCommodityList.getCommodityIdList())
                ? recommondCommodityList.getCommodityIdList() : Collections.emptyList();
    }

    @Override
    public List<XdaCategoryCommodityResODTO> callRemoteCategoryQuery(XdaCategoryAppIDTO xdaCategoryAppIDTO, String xdaSecondCategoryName) {
        // 查询商品信息
        FeignXdaCategoryAppIDTO feignXdaCategoryAppIDTO = BeanCloneUtils.copyTo(xdaCategoryAppIDTO, FeignXdaCategoryAppIDTO.class);
        FeignXdaCategoryCommodityResODTO feignXdaCategoryCommodityResODTO = xdaCategoryWebClient.queryXdaCategoryCommodityList(feignXdaCategoryAppIDTO);

        if (Objects.isNull(feignXdaCategoryCommodityResODTO) || CollectionUtils.isEmpty(feignXdaCategoryCommodityResODTO.getCommodityList())) {
            return Collections.emptyList();
        }

        XdaCategoryCommodityResODTO xdaCategoryCommodityResODTO = JSONObject.parseObject(
                JSONObject.toJSONString(feignXdaCategoryCommodityResODTO), XdaCategoryCommodityResODTO.class);
        xdaCategoryCommodityResODTO.setXdaSecondCategoryName(xdaSecondCategoryName);
        return Arrays.asList(xdaCategoryCommodityResODTO);
    }

    @Override
    public void processExtraCategory(Date orderTime, List<XdaCategoryODTO> categoryList, Long storeId) {

        FeignQueryExtraCategoryIDTO req = new FeignQueryExtraCategoryIDTO();
        req.setStoreId(storeId);
        req.setOrderTime(DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.format(orderTime));
        FeignXdaCategoryResODTO feignXdaCategoryResODTO = xdaCategoryWebClient.queryExtraCategoryV2(req);
        if (Objects.isNull(feignXdaCategoryResODTO) && CollectionUtils.isEmpty(feignXdaCategoryResODTO.getCategoryList())) {
            return;
        }

        List<XdaCategoryODTO> xdaCategoryODTOS = BeanCloneUtils.copyTo(feignXdaCategoryResODTO.getCategoryList(), XdaCategoryODTO.class);
        categoryList.addAll(0, xdaCategoryODTOS);
    }

}
