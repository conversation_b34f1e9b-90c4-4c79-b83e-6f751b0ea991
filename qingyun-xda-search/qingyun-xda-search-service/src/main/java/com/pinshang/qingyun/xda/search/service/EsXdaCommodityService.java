package com.pinshang.qingyun.xda.search.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.search.dto.EsXdaUpdateDiffRecordIDTO;
import com.pinshang.qingyun.xda.search.mapper.XdaCommodityEsMapper;
import com.pinshang.qingyun.xda.search.model.XdaCommodityEs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/04/08
 * @Version 1.0
 */
@Service
@Slf4j
public class EsXdaCommodityService {

    @Autowired
    private EsCommodityTextService esCommodityTextService;

    @Autowired
    private XdaCommodityEsMapper xdaCommodityEsMapper;



    /**
     * 更新异常数据
     * @param idto
     * @return
     */
    @Transactional
    public int updateDiffRecord(EsXdaUpdateDiffRecordIDTO idto, Boolean isPfsStore){
        List<XdaCommodityEs> recordList = esCommodityTextService.getXdaCommodityListByCommodityIdList(Collections.singletonList(idto.getCommodityId()));
        QYAssert.isTrue(SpringUtil.isNotEmpty(recordList), "不存在查询数据, 商品id: " + idto.getCommodityId());

        Example commodityExample = new Example(XdaCommodityEs.class);
        commodityExample.createCriteria().andEqualTo("commodityId", idto.getCommodityId());
        XdaCommodityEs commodityUpdate = new XdaCommodityEs();
        commodityUpdate.setUpdateTime(new Date());
        if(isPfsStore) {
            commodityUpdate.setPfAppStatus(idto.getAppStatus());
        }else {
            commodityUpdate.setAppStatus(idto.getAppStatus());
        }

        return xdaCommodityEsMapper.updateByExampleSelective(commodityUpdate, commodityExample);
    }

}
