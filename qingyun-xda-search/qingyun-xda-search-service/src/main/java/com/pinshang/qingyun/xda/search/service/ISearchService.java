package com.pinshang.qingyun.xda.search.service;

/**
 * @ClassName ISearchService
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/2 20:50
 * @Version 1.0
 */
public interface ISearchService<D, T, E, R> {

    /**
     * 搜索
     *
     * @param t
     * @return
     */
    D search(T t);

    /**
     * 左边的分类列表
     * 两种情况 ①单纯分类列表；②分类列表+列表的第一个分类商品列表）
     *
     * @param r
     * @param <E>
     * @return
     */
    <E> E leftCategoryList(R r);
}
