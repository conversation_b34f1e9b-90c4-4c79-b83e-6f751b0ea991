package com.pinshang.qingyun.xda.search.service;

import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.enums.BusinessTypeEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.store.dto.customer.StoreSelectODTO;
import com.pinshang.qingyun.store.dto.xda.StoreManagerIDTO;
import com.pinshang.qingyun.store.dto.xda.StoreManagerODTO;
import com.pinshang.qingyun.store.service.StoreManageClient;
import com.pinshang.qingyun.store.service.XdaStoreUserClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: sk
 * @Date: 2024/5/21
 */
@Slf4j
@Service
public class StoreService {

    @Autowired
    private XdaStoreUserClient xdaStoreUserClient;
    @Autowired
    private StoreManageClient storeManageClient;

    /**
     * 判断是否批发商客户
     * @param storeId
     * @return
     */
    public Boolean isPfsStore(Long storeId){
        QYAssert.isTrue(storeId != null, "storeId不能为空");
        return storeManageClient.isPfsStore(storeId);
    }

    public Boolean isTdaStore(Long storeId){
        StoreSelectODTO storeSelectODTO = storeManageClient.getStoreData(storeId);
        return BusinessTypeEnums.TD_SALE.getCode().equals(storeSelectODTO.getBusinessType());
    }

    public List<Long> getStoreIdsByStoreId(Long storeId){
        StoreSelectODTO storeSelectODTO = storeManageClient.getStoreData(storeId);
        return getStoreIdsByBusinessType(storeSelectODTO.getBusinessType());
    }

    public List<Long> getStoreIdsByBusinessType(Integer businessType) {
        Boolean isTdaStore = BusinessTypeEnums.TD_SALE.getCode().equals(businessType);

        List<Long> storeIdList = new ArrayList<>();
        // 查询所有开启鲜达并且启用状态的storeIdList
        StoreManagerIDTO storeManagerIDTO = new StoreManagerIDTO();
        storeManagerIDTO.setFlag(YesOrNoEnums.YES.getCode());
        List<StoreManagerODTO> storeManagerList = xdaStoreUserClient.queryStoreManagerInfo(storeManagerIDTO);
        if(CollectionUtils.isNotEmpty(storeManagerList)){
            List<StoreManagerODTO> filterList;
            if(isTdaStore){
                filterList = storeManagerList.stream().filter(p -> BusinessTypeEnums.TD_SALE.getCode().equals(p.getBusinessType())).collect(Collectors.toList());
            }else {
                filterList = storeManagerList.stream().filter(p -> !BusinessTypeEnums.TD_SALE.getCode().equals(p.getBusinessType())).collect(Collectors.toList());

            }
            if(CollectionUtils.isNotEmpty(filterList)){
                storeIdList = filterList.stream().map(item -> item.getStoreId()).collect(Collectors.toList());
            }
        }

        return storeIdList;
    }

    public StoreSelectODTO getStoreById(Long storeId) {
        return storeManageClient.getStoreData(storeId);
    }
}
