package com.pinshang.qingyun.xda.search.service;

import com.pinshang.qingyun.base.enums.SmsMessageTypeEnums;
import com.pinshang.qingyun.weixin.dto.WeiXinSendMessageIDTO;
import com.pinshang.qingyun.weixin.service.WeixinTemplateClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * @Author: sk
 * @Date: 2022/3/3
 */
@Service
public class WeChatSendMessageService {

    @Autowired
    private WeixinTemplateClient weixinTemplateClient;

    /**
     * 发送微信消息
     * @param content
     */
    @Async
    public void sendWeChatMessage(String content){
        //发送微信模板信息
        WeiXinSendMessageIDTO idto = new WeiXinSendMessageIDTO();
        idto.setContent(content);
        idto.setMessageType(SmsMessageTypeEnums.REPORT_INFO_WARN);
        weixinTemplateClient.sendMessageWarning(idto);
    }
}
