package com.pinshang.qingyun.xda.search.service;

import com.pinshang.qingyun.base.enums.TagEnums;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.enums.marketing.MarketSourceTypeEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.DateTimeUtil;
import com.pinshang.qingyun.box.utils.ImageUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.marketing.dto.app.CommodityCategoryIDTO;
import com.pinshang.qingyun.marketing.dto.app.CommodityODTO;
import com.pinshang.qingyun.marketing.dto.app.CommodityPromotionIDTO;
import com.pinshang.qingyun.marketing.dto.app.TagODTO;
import com.pinshang.qingyun.marketing.service.MtPromotionClient;
import com.pinshang.qingyun.product.dto.CommodityCategoryODTO;
import com.pinshang.qingyun.product.service.CategoryClient;
import com.pinshang.qingyun.store.dto.store.StoreDurationODTO;
import com.pinshang.qingyun.store.service.StoreManageClient;
import com.pinshang.qingyun.xda.product.dto.XdaShoppingCartIDTO;
import com.pinshang.qingyun.xda.product.dto.XdaShoppingCartODTO;
import com.pinshang.qingyun.xda.product.dto.commodityText.CommodityTextTagInfoODTO;
import com.pinshang.qingyun.xda.product.dto.front.XdaSpecialsCommoditySetODTO;
import com.pinshang.qingyun.xda.product.service.XdaCommodityFrontClient;
import com.pinshang.qingyun.xda.search.dto.commodity.XdaCommodityDeliveryTimeODTO;
import com.pinshang.qingyun.xda.search.dto.commodity.XdaCommodityODTO;
import com.pinshang.qingyun.xda.search.dto.commodity.XdaCommodityRenderReq;
import com.pinshang.qingyun.xda.search.enums.FromPageEnums;
import com.pinshang.qingyun.xda.search.model.CommodityFreezeGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

@Slf4j
@Service
public class XdaCommodityRenderService {

    @Autowired
    private MtPromotionClient mtPromotionClient;

    @Autowired
    private XdaCommodityFrontClient xdaCommodityFrontClient;

    @Value("${pinshang.img-xd-server-url}")
    private String imgServerUrl;
    @Autowired
    private StoreManageClient storeManageClient;
    @Autowired
    private CategoryClient categoryClient;
    /**
     * 渲染商品信息(搜素框、优惠券去使用、分类页商品)
     *
     * @param req               渲染请求入参
     * @param commodityInfoList 渲染之前的商品集合
     */
    public void renderXdaCommodityInfo(XdaCommodityRenderReq req, List<XdaCommodityODTO> commodityInfoList) {
        if (CollectionUtils.isEmpty(commodityInfoList)) {
            return;
        }
        // 商品id集合
        List<Long> commodityIdList = commodityInfoList.stream().map(XdaCommodityODTO::getCommodityId).collect(Collectors.toList());
        Map<Long, CommodityODTO> commodityPromotionMap = Collections.emptyMap();
        if (FromPageEnums.XDA_APP_CATEGORY_COMMODITY_LIST.getCode().equals(req.getFromPage())) {
            // 获取促销信息
            commodityPromotionMap = this.getCommodityPromotionMap(req, commodityInfoList);
        }

        // 查询自定义标签
        Map<Long, List<CommodityTextTagInfoODTO>> tagMap = Collections.emptyMap();
        if (req.getNeedTag()) {
            List<CommodityTextTagInfoODTO> tagList = xdaCommodityFrontClient.selectCommodityTextTagInfoList(commodityIdList);
            tagMap = tagList.stream().collect(Collectors.groupingBy(CommodityTextTagInfoODTO::getCommodityId));
        }

        // 查询是否可订货
        /*Map<Long, com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDeliveryTimeODTO> deliveryTimeMap = Collections.emptyMap();
        if (req.getNeedCanOrder()) {
            XdaCommodityDeliveryTimeIDTO idto = new XdaCommodityDeliveryTimeIDTO();
            idto.setOrderTime(req.getOrderTime());
            idto.setStoreId(req.getStoreId());
            idto.setCommodityIdList(commodityIdList);
            deliveryTimeMap = xdaCommodityFrontClient.queryXdaCommodityDeliveryTime(idto);
        }*/

        // 查询客户截单时间
        StoreDurationODTO sd = storeManageClient.queryStoreDuration(req.getStoreId());

        // 查询购物车数量
        Map<Long, BigDecimal> shopCartQuantityMap = null;
        if (req.getNeedCartQuantity()) {
            shopCartQuantityMap = this.queryShopCartQuantityMapV2(req.getStoreId(), commodityIdList, 1);
        }

        // 查询凑整商品集合
        List<Long> freezeGroupList = this.queryFreezeGroupMap(commodityIdList);

        //渲染商品
        for (XdaCommodityODTO commodityInfo : commodityInfoList) {
            Long commodityId = commodityInfo.getCommodityId();

            // 标签合集-列表页：1-自定义标签、2-特价、3-促销（、4-凑整、5-速冻）、6-限量
            List<CommodityTextTagInfoODTO> listTagList = new ArrayList<>();
            // 标签合集-详情页：1-自定义标签
            List<CommodityTextTagInfoODTO> tagList = tagMap.get(commodityId);
            // 标签合集-详情页：2-特价、4-凑整、5-速冻、6-限量
            List<CommodityTextTagInfoODTO> tagV2List = new ArrayList<>();

            // 自定义标签
            if (SpringUtil.isNotEmpty(tagList)) {
                listTagList.addAll(tagList);
            }

            // 图片
            if (req.getNeedDefaultImage() && StringUtils.isNotEmpty(commodityInfo.getImageUrl())) {
                commodityInfo.setImageUrl(imgServerUrl + ImageUtils.getXdImgUrlV2(commodityInfo.getImageUrl(), req.getDefaultImageSize() == null ? null : req.getDefaultImageSize().getSize()));
            }

            // 特价、促销（买赠、梯度满折，以及之后满减）
            CommodityODTO promotionCommodity = commodityPromotionMap.get(commodityId);
            if (null != promotionCommodity) {
                setPromotionInfo(commodityInfo, promotionCommodity, commodityId, listTagList, tagV2List);
            }

            // 送货日期范围,用ES里面存的商品送货日期范围进行判断。不在走client获取
            if(StringUtils.isNotBlank(commodityInfo.getDeliveryDateRangeCode())
                    && StringUtils.isNotBlank(commodityInfo.getDeliveryDateRangeValue())
                    && sd != null) {
                setCommodityDeliveryTime(req, commodityInfo, sd);
            }
            /*if (deliveryTimeMap != null && deliveryTimeMap.get(commodityId) != null) {
                XdaCommodityDeliveryTimeODTO deliveryTimeDto = BeanCloneUtils.copyTo(deliveryTimeMap.get(commodityId), XdaCommodityDeliveryTimeODTO.class);
                commodityInfo.setIsCanOrder(deliveryTimeDto.getIsCanOrder());
                commodityInfo.setDeliveryTimeODTO(deliveryTimeDto);
                List<Date> xdaDeliveryTimeList = deliveryTimeDto.getDeliveryDateList();
                if (CollectionUtils.isNotEmpty(xdaDeliveryTimeList)) {
                    commodityInfo.setBeginDeliveryTime(xdaDeliveryTimeList.get(0));
                    commodityInfo.setEndDeliveryTime(xdaDeliveryTimeList.get(xdaDeliveryTimeList.size() - 1));
                }
            }*/

            // 凑整
            if (freezeGroupList.contains(commodityId)) {
                commodityInfo.setIsFreezeRounding(1);
                commodityInfo.setIsFreezeRoundingMultiple(30);

                CommodityTextTagInfoODTO czTag = new CommodityTextTagInfoODTO("凑整", "#FF5733", commodityId);
                tagV2List.add(czTag);
            }

            // 购物车数量
            if (shopCartQuantityMap != null && shopCartQuantityMap.get(commodityId) != null) {
                commodityInfo.setShoppingCartQuantity(shopCartQuantityMap.get(commodityId));
            }

            // 设置标签
            commodityInfo.setListTagList(listTagList);
            commodityInfo.setTagList(tagList);
            commodityInfo.setTagV2List(tagV2List);
        }
    }

    /**
     * 设置促销信息
     * @param commodityInfo
     * @param promotionCommodity
     * @param commodityId
     * @param listTagList
     * @param tagV2List
     */
    private void setPromotionInfo(XdaCommodityODTO commodityInfo, CommodityODTO promotionCommodity, Long commodityId, List<CommodityTextTagInfoODTO> listTagList, List<CommodityTextTagInfoODTO> tagV2List) {
        List<TagODTO> promotionTagList = promotionCommodity.getTagODTOList();
        if (SpringUtil.isNotEmpty(promotionTagList)) {
            for (TagODTO thisTag : promotionTagList) {
                if (TagEnums.SPECIAL.getType().equals(thisTag.getType())) {
                    if (commodityInfo.getCommodityPrice() != null && null != promotionCommodity.getSpecialPrice() && commodityInfo.getCommodityPrice().compareTo(promotionCommodity.getSpecialPrice()) > 0) {
                        commodityInfo.setIsSpecialPrice(1);
                        commodityInfo.setSpecialPrice(promotionCommodity.getSpecialPrice());
                        Integer specialLimit = promotionCommodity.getSpecialLimit();
                        String tagName;
                        if (99999 == specialLimit || 0 == specialLimit) {
                            //不限购
                            tagName = "特价";
                        } else {
                            tagName = "特价|每天限" + specialLimit + "份";
                        }
                        CommodityTextTagInfoODTO specialTag = new CommodityTextTagInfoODTO(tagName, "#FF3D00", commodityId);
                        listTagList.add(specialTag);
                        tagV2List.add(specialTag);
                    }
                } else if (TagEnums.PROMOTION.getType().equals(thisTag.getType())) {
                    if (null != thisTag.getName()) {
                        CommodityTextTagInfoODTO promotionTag = new CommodityTextTagInfoODTO(thisTag.getName(), "#FF5733", commodityId);
                        listTagList.add(promotionTag);
                    }
                }
            }
        }
        // 设置是否可用券
        commodityInfo.setEnableCoupon(promotionCommodity.getEnableCoupon());
    }

    /**
     * 设置商品送货日期范围
     * @param req
     * @param commodityInfo
     * @param sd
     */
    private void setCommodityDeliveryTime(XdaCommodityRenderReq req, XdaCommodityODTO commodityInfo, StoreDurationODTO sd) {
        Date orderTime = req.getOrderTime();
        XdaCommodityDeliveryTimeODTO deliveryTimeODTO = new XdaCommodityDeliveryTimeODTO();
        deliveryTimeODTO.setDeliveryDateRangeCode(commodityInfo.getDeliveryDateRangeCode());
        deliveryTimeODTO.setDeliveryDateRangeValue(commodityInfo.getDeliveryDateRangeValue());

        List<Date> dateList = deliveryTimeODTO.getDeliveryDateList();
        deliveryTimeODTO.setIsCanOrder(false);
        if(dateList.contains(orderTime)){
            Boolean flag1 = orderTime.equals(dateList.get(0)) && DateTimeUtil.compareNewDate(sd.getBeginTime(), sd.getEndTime());
            Boolean flag2 = orderTime.compareTo(dateList.get(0))>0 && DateTimeUtil.compareTime(sd.getBeginTime(),DateTimeUtil.getHourAndMinute(0));
            if( flag1 || flag2){
                deliveryTimeODTO.setIsCanOrder(Boolean.TRUE);
            }
        }
        deliveryTimeODTO.setDistributionTipList(deliveryTimeODTO.processDistributionTips(dateList, sd.getBeginTime(), sd.getEndTime()));

        commodityInfo.setIsCanOrder(deliveryTimeODTO.getIsCanOrder());
        commodityInfo.setDeliveryTimeODTO(deliveryTimeODTO);
        List<Date> xdaDeliveryTimeList = deliveryTimeODTO.getDeliveryDateList();
        if (CollectionUtils.isNotEmpty(xdaDeliveryTimeList)) {
            commodityInfo.setBeginDeliveryTime(xdaDeliveryTimeList.get(0));
            commodityInfo.setEndDeliveryTime(xdaDeliveryTimeList.get(xdaDeliveryTimeList.size() - 1));
        }
    }


    /**
     * 渲染特惠商品信息
     *
     * @param req               渲染请求入参
     * @param commodityInfoList 渲染之前的商品集合
     */
    public void renderXdaThCommodityInfo(XdaCommodityRenderReq req, List<XdaCommodityODTO> commodityInfoList) {
        if (CollectionUtils.isEmpty(commodityInfoList)) {
            return;
        }
        List<Long> commodityIdList = commodityInfoList.stream().map(XdaCommodityODTO::getCommodityId).collect(Collectors.toList());

        // 自定义标签
        Map<Long, List<CommodityTextTagInfoODTO>> tagMap = Collections.emptyMap();
        if (req.getNeedTag()) {
            List<CommodityTextTagInfoODTO> tagList = xdaCommodityFrontClient.selectCommodityTextTagInfoList(commodityIdList);
            tagMap = tagList.stream().collect(Collectors.groupingBy(CommodityTextTagInfoODTO::getCommodityId));
        }

        // 查询是否可订货
       /* Map<Long, com.pinshang.qingyun.xda.product.dto.front.XdaCommodityDeliveryTimeODTO> deliveryTimeMap = Collections.emptyMap();
        if (req.getNeedCanOrder()) {
            XdaCommodityDeliveryTimeIDTO idto = new XdaCommodityDeliveryTimeIDTO();
            idto.setOrderTime(req.getOrderTime());
            idto.setStoreId(req.getStoreId());
            idto.setCommodityIdList(commodityIdList);
            deliveryTimeMap = xdaCommodityFrontClient.queryXdaCommodityDeliveryTime(idto);
        }*/

        // 查询客户截单时间
        StoreDurationODTO sd = storeManageClient.queryStoreDuration(req.getStoreId());

        // 查询购物车数量
        Map<Long, BigDecimal> shopCartQuantityMap = null;
        if (req.getNeedCartQuantity()) {
            shopCartQuantityMap = this.queryShopCartQuantityMapV2(req.getStoreId(), commodityIdList, 2);
        }

        // 凑整商品集合
        List<Long> freezeGroupList = this.queryFreezeGroupMap(commodityIdList);

        //查询特惠价格
        List<XdaSpecialsCommoditySetODTO> thCommodityList = xdaCommodityFrontClient.selectXdaSpecialsCommoditySetListByCommodityIdList(commodityIdList);
        Map<Long, XdaSpecialsCommoditySetODTO> commoditySetMap = thCommodityList.stream().collect(Collectors.toMap(XdaSpecialsCommoditySetODTO::getCommodityId, Function.identity()));

        for (XdaCommodityODTO commodityInfo : commodityInfoList) {
            Long commodityId = commodityInfo.getCommodityId();
            XdaSpecialsCommoditySetODTO xdaSpecialsCommoditySet = commoditySetMap.get(commodityId);
            if (Objects.nonNull(xdaSpecialsCommoditySet)) {
                //渲染特惠价格
                commodityInfo.setThPrice(xdaSpecialsCommoditySet.getCommoditySpecialsPrice());
                commodityInfo.setIsThPrice(1);
                commodityInfo.setIsSerial(0);
                if (Objects.nonNull(xdaSpecialsCommoditySet.getCommodityLimit())) {
                    commodityInfo.setThLimitNumber(BigDecimal.valueOf(xdaSpecialsCommoditySet.getCommodityLimit()));
                }
            }

            // 标签合集-列表页：1-自定义标签、2-特价、3-促销（、4-凑整、5-速冻）、6-限量
            List<CommodityTextTagInfoODTO> listTagList = new ArrayList<>();
            // 标签合集-详情页：1-自定义标签
            List<CommodityTextTagInfoODTO> tagList = tagMap.get(commodityId);
            // 标签合集-详情页：2-特价、4-凑整、5-速冻、6-限量
            List<CommodityTextTagInfoODTO> tagV2List = new ArrayList<>();

            // 自定义标签，合并到其他标签中
            if (SpringUtil.isNotEmpty(tagList)) {
                listTagList.addAll(tagList);
            }

            CommodityTextTagInfoODTO thTag = new CommodityTextTagInfoODTO("特惠", "#FF5733", commodityId);
            listTagList.add(thTag);
            tagV2List.add(thTag);

            // 图片
            if (req.getNeedDefaultImage() && StringUtils.isNotEmpty(commodityInfo.getImageUrl())) {
                commodityInfo.setImageUrl(imgServerUrl + ImageUtils.getXdImgUrlV2(commodityInfo.getImageUrl(), req.getDefaultImageSize() == null ? null : req.getDefaultImageSize().getSize()));
            }

            // 送货日期范围,用ES里面存的商品送货日期范围进行判断。不在走client获取
            if(StringUtils.isNotBlank(commodityInfo.getDeliveryDateRangeCode())
                    && StringUtils.isNotBlank(commodityInfo.getDeliveryDateRangeValue())
                    && sd != null) {
                setCommodityDeliveryTime(req, commodityInfo, sd);
            }
            /*if (deliveryTimeMap != null && deliveryTimeMap.get(commodityId) != null) {
                XdaCommodityDeliveryTimeODTO deliveryTimeDto = BeanCloneUtils.copyTo(deliveryTimeMap.get(commodityId), XdaCommodityDeliveryTimeODTO.class);
                commodityInfo.setIsCanOrder(deliveryTimeDto.getIsCanOrder());
                commodityInfo.setDeliveryTimeODTO(deliveryTimeDto);
                List<Date> xdaDeliveryTimeList = deliveryTimeDto.getDeliveryDateList();
                if (CollectionUtils.isNotEmpty(xdaDeliveryTimeList)) {
                    commodityInfo.setBeginDeliveryTime(xdaDeliveryTimeList.get(0));
                    commodityInfo.setEndDeliveryTime(xdaDeliveryTimeList.get(xdaDeliveryTimeList.size() - 1));
                }
            }*/

            // 凑整
            if (freezeGroupList.contains(commodityId)) {
                commodityInfo.setIsFreezeRounding(1);
                commodityInfo.setIsFreezeRoundingMultiple(30);

                CommodityTextTagInfoODTO czTag = new CommodityTextTagInfoODTO("凑整", "#FF5733", commodityId);
                tagV2List.add(czTag);
            }

            // 购物车数量
            if (shopCartQuantityMap != null && shopCartQuantityMap.get(commodityId) != null) {
                commodityInfo.setShoppingCartQuantity(shopCartQuantityMap.get(commodityId));
            }

            // 设置标签
            commodityInfo.setListTagList(listTagList);
            commodityInfo.setTagList(tagList);
            commodityInfo.setTagV2List(tagV2List);

            // 当且仅当
            //商品保质期字段为X天，且文描管理中该商品“是否显示保质期=显示”，才在APP前台商详页显示保质期为X天
            if (!YesOrNoEnums.YES.getCode().equals(commodityInfo.getQualityStatus())) {
                commodityInfo.setQualityDays(null);
            }
        }
    }

    /**
     * 查询商品促销信息
     */
    public Map<Long, CommodityODTO> getCommodityPromotionMap(XdaCommodityRenderReq appIDTO, List<XdaCommodityODTO> appODTOList) {
        List<CommodityCategoryIDTO> commodityCategoryIDTOList = appODTOList.stream().map(o -> {
            CommodityCategoryIDTO cci = new CommodityCategoryIDTO();
            cci.setCommodityId(o.getCommodityId());
            cci.setCategoryId(o.getXdaSecondCategoryId());
            cci.setIsWeight(o.getIsWeight());
            return cci;
        }).collect(Collectors.toList());

        // 覆盖后台3级分类
        setThirdCategoryIds(appODTOList, commodityCategoryIDTOList);

        CommodityPromotionIDTO cpIDTO = new CommodityPromotionIDTO();
        cpIDTO.setChannelType(MarketSourceTypeEnum.XDA.getCode());
        cpIDTO.setShopId(appIDTO.getStoreId());
        cpIDTO.setOrderTime(appIDTO.getOrderTime());
        cpIDTO.setCommodityCategoryIDTOList(commodityCategoryIDTOList);

        long beginTime = System.currentTimeMillis();
        Map<Long, CommodityODTO> commodityPromotionMap = mtPromotionClient.queryCommodityPromotion(cpIDTO);
        log.info("\n\n\n==========>>>鲜达APP.调用mtPromotionClient.queryCommodityPromotion：耗时={}毫秒，入参={}", (System.currentTimeMillis() - beginTime), cpIDTO);
        if (null == commodityPromotionMap) {
            return Collections.emptyMap();
        }
        return commodityPromotionMap;
    }

    /**
     * 设置后台三级分类id
     * @param appODTOList
     * @param commodityCategoryIDTOList
     */
    private void setThirdCategoryIds(List<XdaCommodityODTO> appODTOList, List<CommodityCategoryIDTO> commodityCategoryIDTOList) {
        List<String> commodityIdList = appODTOList.stream().map(item -> item.getCommodityId() + "").collect(Collectors.toList());
        com.pinshang.qingyun.product.dto.CommodityCategoryIDTO categoryIdto = new com.pinshang.qingyun.product.dto.CommodityCategoryIDTO();
        categoryIdto.setCommodityIds(commodityIdList);
        categoryIdto.setLevel(3);
        List<CommodityCategoryODTO> commodityCategoryODTOList = categoryClient.getCategoryIds(categoryIdto);
        if(CollectionUtils.isNotEmpty(commodityCategoryODTOList)) {
            Map<String, String> categoryMap = commodityCategoryODTOList.stream().collect(Collectors.toMap(CommodityCategoryODTO::getCommodityId,CommodityCategoryODTO::getCategoryId,(key1 , key2)-> key2));
            commodityCategoryIDTOList.forEach(item -> {
                String commodityId = item.getCommodityId() + "";
                if(categoryMap.containsKey(commodityId)) {
                    item.setCategoryId(Long.valueOf(categoryMap.get(commodityId)));
                }
            });
        }
    }

    /**
     * 查询商品是否凑整商品
     */
    public List<Long> queryFreezeGroupMap(List<Long> commodityIdList) {
        if (SpringUtil.isEmpty(commodityIdList)) {
            return Collections.emptyList();
        }
        List<CommodityFreezeGroup> commodityFreezeGroups = BeanCloneUtils.copyTo(xdaCommodityFrontClient.getCommodityFreezeGroups(commodityIdList), CommodityFreezeGroup.class);
        return commodityFreezeGroups.stream().map(CommodityFreezeGroup::getCommodityId).collect(Collectors.toList());
    }


    /**
     * 根据类型区分: 特惠商品和普通商品购物车数量
     */
    public Map<Long, BigDecimal> queryShopCartQuantityMapV2(Long storeId, List<Long> commodityIdList, Integer commodityType) {
        XdaShoppingCartIDTO idto = new XdaShoppingCartIDTO();
        idto.setStoreId(storeId);
        idto.setCommodityIdList(commodityIdList);
        idto.setCommodityType(commodityType);
        List<XdaShoppingCartODTO> shoppingCartODTOList = xdaCommodityFrontClient.queryXdaShoppingCartQuantityV2(idto);
        if (CollectionUtils.isEmpty(shoppingCartODTOList)) {
            return Collections.emptyMap();
        }
        Map<Long, BigDecimal> quantityMap = new HashMap<>();
        shoppingCartODTOList.stream().collect(groupingBy(XdaShoppingCartODTO::getCommodityId)).forEach((k, v) -> {
            if (CollectionUtils.isEmpty(v)) {
                return;
            }
            quantityMap.put(k, v.get(0).getQuantity());
        });
        return quantityMap;
    }

}
