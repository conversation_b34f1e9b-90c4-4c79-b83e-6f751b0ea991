package com.pinshang.qingyun.xda.search.service.base;

import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.xda.search.dto.CategoryGoodsReq;
import com.pinshang.qingyun.xda.search.dto.KeyWordGoodsReq;
import com.pinshang.qingyun.xda.search.dto.commodity.XdaCommodityODTO;
import com.pinshang.qingyun.xda.search.service.AbstractSearchService;
import com.pinshang.qingyun.xda.search.vo.EsXdaCategoryGoodsNewVO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 类描述：品类搜索的基类。
 * 防止manger service 和 front service 循环依赖
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29 17:05
 */
@Slf4j
public abstract class AbstractCategoryService extends AbstractSearchService<ApiResponse<XdaCommodityODTO>, KeyWordGoodsReq, List<EsXdaCategoryGoodsNewVO>, CategoryGoodsReq> {


}
