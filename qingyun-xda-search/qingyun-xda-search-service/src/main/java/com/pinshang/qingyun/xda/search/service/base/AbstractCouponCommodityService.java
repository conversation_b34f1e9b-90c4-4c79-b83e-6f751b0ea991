package com.pinshang.qingyun.xda.search.service.base;

import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.xda.search.dto.CategoryGoodsReq;
import com.pinshang.qingyun.xda.search.dto.KeyWordGoodsReq;
import com.pinshang.qingyun.xda.search.dto.commodity.XdaCommodityODTO;
import com.pinshang.qingyun.xda.search.service.AbstractSearchService;
import com.pinshang.qingyun.xda.search.vo.EsXdaCategoryGoodsNewVO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 类描述：优惠券相关服务的基类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10 13:29
 */
@Slf4j
public abstract class AbstractCouponCommodityService extends AbstractSearchService<ApiResponse<XdaCommodityODTO>, KeyWordGoodsReq, List<EsXdaCategoryGoodsNewVO>, CategoryGoodsReq> {


}
