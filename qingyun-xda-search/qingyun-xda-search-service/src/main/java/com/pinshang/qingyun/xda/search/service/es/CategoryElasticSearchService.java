package com.pinshang.qingyun.xda.search.service.es;

import com.alibaba.fastjson.JSONObject;
import com.pinshang.qingyun.base.api.ApiResult;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.SpringUtil;
import com.pinshang.qingyun.xda.search.constants.EsConstants;
import com.pinshang.qingyun.xda.search.document.EsXdaCategory;
import com.pinshang.qingyun.xda.search.dto.EsManualIDTO;
import com.pinshang.qingyun.xda.search.model.XdaCategoryEs;
import com.pinshang.qingyun.xda.search.repository.EsXdaCategoryRepository;
import com.pinshang.qingyun.xda.search.service.EsCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.FetchSourceFilterBuilder;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CategoryElasticSearchService {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Autowired
    private EsXdaCategoryRepository esXdaCategoryRepository;

    @Autowired
    private EsCategoryService esCategoryService;

    private static Date validParam(EsManualIDTO req) {
        Date updateTime = req.getUpdateTime();
        QYAssert.notNull(updateTime, "日期不能为空");
        return updateTime;
    }

    public void deleteByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        esXdaCategoryRepository.deleteAllById(idList);
    }

    public List<EsXdaCategory>  listByParentIdAndCateIdList(Long paretId, Set<Long> cateIdSet) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termsQuery("cateId", cateIdSet))
                .must(QueryBuilders.termQuery("parentId", paretId));

        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withSourceFilter(new FetchSourceFilterBuilder().withIncludes("cateId", "cateName", "sortNum").build())
                .withSort(SortBuilders.fieldSort("sortNum").order(SortOrder.ASC))
                .addAggregation(AggregationBuilders.terms("group_by_cateId").field("cateId").size(EsConstants.AGGREGATION_TERMS_SIZE)
                        .subAggregation(AggregationBuilders.topHits("sorted_results").sort("sortNum", SortOrder.ASC).size(EsConstants.AGGREGATION_TERMS_SIZE)))
                .build();
        SearchHits<EsXdaCategory> searchHits = elasticsearchRestTemplate.search(nativeSearchQuery, EsXdaCategory.class);

        List<EsXdaCategory> categories = searchHits.getSearchHits()
                .stream()
                .map(SearchHit::getContent).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(categories)) {
            return Collections.emptyList();
        }

        return categories;
    }

    public ApiResult allCategorySyncEs(EsManualIDTO req) {
        Date updateTime = validParam(req);

        RLock lock = redissonClient.getLock(QYApplicationContext.redisKeyProfile + "allCategorySyncEs");
        boolean tryLock;
        try {
            tryLock = lock.tryLock(0, 60, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new BizLogicException("品类同步es获取锁失败");
        }
        QYAssert.isTrue(tryLock, "有品类同步任务正在执行中，请稍后重试");

        try {

            List<XdaCategoryEs> xdaCategoryEsList = esCategoryService.listByUpdateTime(updateTime);

            if (CollectionUtils.isEmpty(xdaCategoryEsList)) {
                return ApiResult.ok("没有需要同步的品类数据");
            }
            List<EsXdaCategory> esXdaCategories = BeanCloneUtils.copyTo(xdaCategoryEsList, EsXdaCategory.class);
            esXdaCategoryRepository.saveAll(esXdaCategories);

            log.info("品类同步es成功，req:[{}]", JSONObject.toJSONString(req));
        } finally {
            lock.unlock();
        }

        return ApiResult.ok();
    }

    public List<EsXdaCategory> listFirstCategoryByIdsFromES(List<Long> categoryIdList, Integer level) {
        if (SpringUtil.isEmpty(categoryIdList)) {
            return Collections.emptyList();
        }

        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termsQuery("cateId", categoryIdList))
                .must(QueryBuilders.termQuery("cateLevel", level));

        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        nativeSearchQueryBuilder.withQuery(boolQueryBuilder).withSorts(SortBuilders.fieldSort("sortNum").order(SortOrder.ASC));
        SearchHits<EsXdaCategory> search = elasticsearchRestTemplate.search(nativeSearchQueryBuilder.build(), EsXdaCategory.class);

        return search.getSearchHits()
                .stream()
                .map(SearchHit::getContent)
                .collect(Collectors.toList());
    }


}
