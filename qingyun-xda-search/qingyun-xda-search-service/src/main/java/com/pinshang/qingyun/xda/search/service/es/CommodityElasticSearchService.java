package com.pinshang.qingyun.xda.search.service.es;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.pinshang.qingyun.base.api.ApiResult;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.configure.expand.QYAssert;
import com.pinshang.qingyun.base.constant.QingyunConstant;
import com.pinshang.qingyun.base.enums.search.BaseSearchOptTypeEnum;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.xda.search.constants.EsConstants;
import com.pinshang.qingyun.xda.search.document.EsXdaCommodity;
import com.pinshang.qingyun.xda.search.dto.EsManualIDTO;
import com.pinshang.qingyun.xda.search.dto.EsXdaUpdateDiffRecordIDTO;
import com.pinshang.qingyun.xda.search.mapper.XdaCommodityEsMapper;
import com.pinshang.qingyun.xda.search.model.XdaCommodityEs;
import com.pinshang.qingyun.xda.search.repository.EsXdaCommodityRepository;
import com.pinshang.qingyun.xda.search.service.EsCommodityService;
import com.pinshang.qingyun.xda.search.service.EsXdaCommodityService;
import com.pinshang.qingyun.xda.search.service.StoreService;
import com.pinshang.qingyun.xda.search.service.third.XdaSearchCacheService;
import com.pinshang.qingyun.xda.search.service.third.XdaSearchMsgService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.pinshang.qingyun.xda.search.conf.XdaSearchConfiguration.POOL_EXECUTOR_DELAY;

@Service
@Slf4j
public class CommodityElasticSearchService {

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private ElasticsearchRestTemplate elasticsearchRestTemplate;

    @Autowired
    private EsXdaCommodityRepository esXdaCommodityRepository;

    @Autowired
    @Lazy
    private EsCommodityService esCommodityService;

    @Autowired
    private EsXdaCommodityService esXdaCommodityService;

    @Autowired
    private XdaCommodityEsMapper xdaCommodityEsMapper;

    @Autowired
    private StoreService storeService;

    @Autowired
    private XdaSearchMsgService xdaSearchMsgService;

    @Autowired
    private XdaSearchCacheService xdaSearchCacheService;

    /**
     * 执行ES商品信息的保存操作
     * <p>
     * 此方法接收一个EsXdaCommodity对象列表作为参数，主要用于批量保存商品信息到Elasticsearch中
     * 如果传入的商品列表为空，则直接返回，不执行任何操作
     *
     * @param esXdaCommodities EsXdaCommodity对象列表，代表待保存的商品信息
     */
    private void esSaveOperation(List<EsXdaCommodity> esXdaCommodities) {
        if (CollectionUtils.isEmpty(esXdaCommodities)) {
            return;
        }
        esXdaCommodityRepository.saveAll(esXdaCommodities);
        // 发消息同步 base-search
        xdaSearchMsgService.sendBaseSearchCommodityMsg(esXdaCommodities, BaseSearchOptTypeEnum.UPDATE);
        // 清除缓存
        xdaSearchCacheService.cleanCache(EsConstants.XDA_CACHE_GROUP_DEFAULT);
    }

    /**
     * 根据ID列表执行ES（Elasticsearch）数据删除操作
     * 此方法用于批量删除Elasticsearch中的文档，通过提供的一系列ID来定位并删除文档
     *
     * @param idList 包含待删除文档ID的列表如果列表为空或null，方法将直接返回，不执行任何删除操作
     */
    private void esDeleteAllByIdOperation(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        // 1. 先查出 xda-commodity ES 内的商品
        List<EsXdaCommodity> esXdaCommodityList = getListByCommodityIdList(idList);

        // 2. 删除 xda-commodity ES 内的商品
        esXdaCommodityRepository.deleteAllById(idList);
        // 3.发消息同步 base-search
        xdaSearchMsgService.sendBaseSearchCommodityXdaMsg(esXdaCommodityList, BaseSearchOptTypeEnum.DELETE);
        // 4.清除缓存
        xdaSearchCacheService.cleanCache(EsConstants.XDA_CACHE_GROUP_DEFAULT);
    }

    public void deleteByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return;
        }
        //esXdaCommodityRepository.deleteAllById(idList);
        esDeleteAllByIdOperation(idList);
    }

    public void commoditySyncEsByCommodityIdList(List<Long> commodityIds) {
        Example example = new Example(XdaCommodityEs.class);
        example.createCriteria().andIn("commodityId", commodityIds);
        List<XdaCommodityEs> commodityEsList = xdaCommodityEsMapper.selectByExample(example);
        List<EsXdaCommodity> esXdaCommodities = BeanCloneUtils.copyTo(commodityEsList, EsXdaCommodity.class);
        //esXdaCommodityRepository.saveAll(esXdaCommodities);
        esSaveOperation(esXdaCommodities);
    }

    public ApiResult allCommoditySyncEs(EsManualIDTO req) {
        Date updateTime = req.getUpdateTime();

        RLock lock = redissonClient.getLock(QYApplicationContext.redisKeyProfile + "allCommoditySyncEs");
        boolean tryLock;
        try {
            tryLock = lock.tryLock(0, 60, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            throw new BizLogicException("商品同步es获取锁失败");
        }
        QYAssert.isTrue(tryLock, "有商品同步任务正在执行中，请稍后重试");

        try {

            Integer from = 0;
            Integer offset = 500;
            while (true) {
                List<XdaCommodityEs> xdaCommodityEs = esCommodityService.pageByUpdateTime(updateTime, from, offset);

                if (CollectionUtils.isNotEmpty(xdaCommodityEs)) {
                    List<EsXdaCommodity> esXdaCommodities = BeanCloneUtils.copyTo(xdaCommodityEs, EsXdaCommodity.class);
                    //esXdaCommodityRepository.saveAll(esXdaCommodities);
                    esSaveOperation(esXdaCommodities);
                    from += offset;
                }

                if (CollectionUtils.isEmpty(xdaCommodityEs) || xdaCommodityEs.size() < offset) {
                    break;
                }

                try {
                    TimeUnit.MILLISECONDS.sleep(50);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }

            log.info("商品同步es成功，req:[{}]", JSONObject.toJSONString(req));

        } finally {
            lock.unlock();
        }

        return ApiResult.ok();
    }

    /**
     * 因为库存查询pinshang库的 t_dc_tob_commodity_stock 表，这里只维护是否上下架
     * @param idto
     * @return
     */
    @Async
    public void updateEsXdCommodityStatus(EsXdaUpdateDiffRecordIDTO idto) {
        QYAssert.isTrue(idto != null && idto.getCommodityId() != null, "商品id不能为空");
        QueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.termQuery("commodityId", idto.getCommodityId()));

        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        nativeSearchQueryBuilder.withQuery(queryBuilder);
        SearchHits<EsXdaCommodity> search = elasticsearchRestTemplate.search(nativeSearchQueryBuilder.build(), EsXdaCommodity.class);
        List<SearchHit<EsXdaCommodity>> searchHits = search.getSearchHits();

        //刷新商品es的状态和库存
        if (CollectionUtils.isNotEmpty(searchHits)) {
            EsXdaCommodity esXdaCommodity = searchHits.get(0).getContent();

            Integer appStatus = null;
            Boolean isPfsStore = storeService.isPfsStore(idto.getStoreId());
            if(isPfsStore) {
                appStatus = esXdaCommodity.getPfAppStatus();
            }else {
                appStatus = esXdaCommodity.getAppStatus();
            }

            if (!appStatus.equals(idto.getAppStatus())) {
                int res = esXdaCommodityService.updateDiffRecord(idto, isPfsStore);
                //如果更新数量为0 重新强制刷一下es
                //if (res == 0) {
                    //刷新商品表
                    Example example = new Example(XdaCommodityEs.class);
                    example.createCriteria().andEqualTo("commodityId", idto.getCommodityId());
                    List<XdaCommodityEs> xdaCommodityEsList = xdaCommodityEsMapper.selectByExample(example);
                    if(CollectionUtils.isNotEmpty(xdaCommodityEsList)){
                        XdaCommodityEs xdaCommodityEs = xdaCommodityEsList.get(0);
                        EsXdaCommodity xdaCommodity = BeanCloneUtils.copyTo(xdaCommodityEs, EsXdaCommodity.class);
                        if(isPfsStore) {
                            xdaCommodity.setPfAppStatus(idto.getAppStatus());
                        }else {
                            xdaCommodity.setAppStatus(idto.getAppStatus());
                        }

                        //esXdaCommodityRepository.save(xdaCommodity);
                        esSaveOperation(Collections.singletonList(xdaCommodity));
                    }
                //}
            }
        }
    }

    /**
     * 根据同义词列表更新ES商品索引
     * 此方法用于当同义词列表发生更改时，更新 Elasticsearch 中的商品索引
     * 它首先根据同义词列表获取需要更新的商品ID列表，然后调用另一个方法同步这些商品到ES索引
     *
     * @param synonymWordList 同义词列表
     */
    public void updateEsCommodity4SynonymWord(List<String> synonymWordList) {
        if (CollectionUtils.isEmpty(synonymWordList)) {
            return;
        }
        List<Long> commodityIdList = new ArrayList<>();
        try {
            List<Long> standard = getEsCommodity4SynonymWord(synonymWordList, "standard");
            List<Long> ikSmart = getEsCommodity4SynonymWord(synonymWordList, "ik_smart");
            if(CollectionUtils.isNotEmpty(standard)){
                commodityIdList.addAll(standard);
            }
            if(CollectionUtils.isNotEmpty(ikSmart)){
                commodityIdList.addAll(ikSmart);
            }
        } catch (Exception e) {
            log.warn("updateEsCommodity4SynonymWord error:[{}]",synonymWordList, e);
        }
        if(CollectionUtils.isEmpty(commodityIdList)){
            log.info("updateEsCommodity4SynonymWord skipped, get commodity is empty :[{}]",synonymWordList);
            return;
        }
        // 延迟执行，因为近义词更新后，同步到IK需要时间。
        POOL_EXECUTOR_DELAY.schedule(()->{
            updateEsData(synonymWordList, commodityIdList);
        }, 100, TimeUnit.SECONDS);
    }

    private void updateEsData(List<String> synonymWordList, List<Long> commodityIdList) {
        try {
            // commodityIdList 分隔下，防止ids 过长导致sql报错
            Lists.partition(commodityIdList, QingyunConstant.SEARCH_DEFAULT_PAGG_SIZE)
                    .forEach(this::commoditySyncEsByCommodityIdList);
        } catch (Exception e) {
            log.warn("updateEsCommodity4SynonymWord error for call commoditySyncEsByCommodityIdList method :[{}]", synonymWordList, e);
        }
    }

    private List<Long> getEsCommodity4SynonymWord(String synonymWord) {
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(QueryBuilders.matchQuery("commoditySearchName", synonymWord));
        return searchFromEs(queryBuilder);
    }

    private List<Long> getEsCommodity4SynonymWord(List<String> synonymWordList,String analyzer) {
        MatchQueryBuilder matchQuery = QueryBuilders.matchQuery("commoditySearchName", StringUtils.join(synonymWordList, " "));
        matchQuery.analyzer(analyzer);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery()
                .must(matchQuery);
        return searchFromEs(queryBuilder);
    }

    private List<Long> searchFromEs(BoolQueryBuilder queryBuilder) {
        int pageNo = 0;
        Sort sort = Sort.by(new Sort.Order(Sort.Direction.ASC,"id"));
        List<Long> commodityIdList = new ArrayList<>();
        List<EsXdaCommodity> list = new ArrayList<>();
        while (pageNo == 0 || CollectionUtils.isNotEmpty(list)) {
            list = queryEsXdaCommodityList(sort, pageNo, queryBuilder);
            if(CollectionUtils.isNotEmpty(list)){
                List<Long> subCommodityIds = list.stream().map(EsXdaCommodity::getId).collect(Collectors.toList());
                commodityIdList.addAll(subCommodityIds);
            }
            ++pageNo;
        }
        return commodityIdList;
    }

    private List<EsXdaCommodity> queryEsXdaCommodityList(Sort sort, int pageNo, BoolQueryBuilder queryBuilder){
        List<EsXdaCommodity> list = new ArrayList<>();
        Pageable pageable = PageRequest.of(pageNo, QingyunConstant.SEARCH_DEFAULT_PAGG_SIZE,sort);
        NativeSearchQueryBuilder nativeSearchQueryBuilder = new NativeSearchQueryBuilder();
        nativeSearchQueryBuilder.withQuery(queryBuilder);
        nativeSearchQueryBuilder.withPageable(pageable);
        SearchHits<EsXdaCommodity> search = elasticsearchRestTemplate.search(nativeSearchQueryBuilder.build(),EsXdaCommodity.class);
        List<SearchHit<EsXdaCommodity>> searchHits = search.getSearchHits();
        if(CollectionUtils.isNotEmpty(searchHits)){
            for(SearchHit<EsXdaCommodity> searchHit : searchHits){
                EsXdaCommodity es = searchHit.getContent();
                list.add(es);
            }
        }
        return list;
    }

    public List<EsXdaCommodity> getListByCommodityIdList(List<Long> commodityIdList) {
        if (CollectionUtils.isEmpty(commodityIdList)) {
            return Collections.emptyList();
        }
        BoolQueryBuilder queryBuilder = QueryBuilders
                .boolQuery()
                .must(QueryBuilders.termsQuery("commodityId", commodityIdList));
        return searchEsItemListFromEs(queryBuilder);
    }


    private List<EsXdaCommodity> searchEsItemListFromEs(BoolQueryBuilder queryBuilder) {
        int pageNo = 0;
        Sort sort = Sort.by(new Sort.Order(Sort.Direction.ASC, "id"));
        List<EsXdaCommodity> commodityList = new ArrayList<>();
        List<EsXdaCommodity> list = new ArrayList<>();
        while (pageNo == 0 || CollectionUtils.isNotEmpty(list)) {
            list = queryEsXdaCommodityList(sort, pageNo, queryBuilder);
            if (CollectionUtils.isNotEmpty(list)) {
                commodityList.addAll(list);
            }
            ++pageNo;
        }
        return commodityList;
    }


}
