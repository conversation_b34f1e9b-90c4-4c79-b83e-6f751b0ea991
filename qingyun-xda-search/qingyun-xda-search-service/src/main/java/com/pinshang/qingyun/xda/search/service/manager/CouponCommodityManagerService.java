package com.pinshang.qingyun.xda.search.service.manager;

import com.pinshang.qingyun.base.search.dto.search.*;
import com.pinshang.qingyun.marketing.dto.mtCoupon.MtCouponToUseODTO;
import com.pinshang.qingyun.xda.search.document.EsXdaCommodity;
import com.pinshang.qingyun.xda.search.dto.KeyWordGoodsReq;
import com.pinshang.qingyun.xda.search.service.StoreService;
import com.pinshang.qingyun.xda.search.service.base.AbstractCouponCommodityService;
import com.pinshang.qingyun.xda.search.service.third.XdaSearchOperateService;
import com.pinshang.qingyun.xda.search.util.QueryParserUtil;
import com.pinshang.qingyun.xda.search.vo.SearchResultVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 类描述：优惠券商品搜索，继承优惠券商品搜索基类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29 17:05
 */
@Service
@Slf4j
public class CouponCommodityManagerService extends AbstractCouponCommodityService {

    @Autowired
    private StoreService storeService;
    @Autowired
    private EsBaseSwitcherManagerService esBaseSwitcherManagerService;
    @Autowired
    private XdaSearchOperateService xdaSearchOperateService;
    @Autowired
    private EsBaseSearchManagerService esBaseSearchManagerService;

    /**
     * 优惠券去使用页，切换新搜索
     *
     * @param param                搜索请求参数，包含用户ID等信息
     * @param storeCommodityIdList 店铺商品ID列表
     * @param mtCouponToUseDTO     待使用的优惠券信息
     * @return 搜索结果对象
     */
    public SearchResultVo couponUseKeyWordSearchSwitch(KeyWordGoodsReq param, List<Long> storeCommodityIdList, MtCouponToUseODTO mtCouponToUseDTO) {
        boolean switchBaseSearch = esBaseSwitcherManagerService.isSwitchBaseSearch(param.getUserId());
        boolean needXdaSearch = !switchBaseSearch;
        SearchResultVo resultVo = null;
        if (switchBaseSearch) {
            try {
                resultVo = this.couponUseKeyWordSearchFromBaseSearch(param, storeCommodityIdList, mtCouponToUseDTO);
            } catch (Exception e) {
                // 记录异常日志
                log.error("优惠券搜索异常：message={}，error={}", param, e.getMessage(), e);
                needXdaSearch = true;
            }
        }
        if (needXdaSearch) {
            resultVo = super.couponUseKeyWordSearch(param, storeCommodityIdList, mtCouponToUseDTO);
        }
        return resultVo;
    }

    public SearchResultVo couponUseKeyWordSearchFromBaseSearch(KeyWordGoodsReq param,
                                                               List<Long> storeCommodityIdList,
                                                               MtCouponToUseODTO mtCouponToUseDTO) {
        SearchCommodityClientReqDTO clientReqDTO = QueryParserUtil.getClientReqDTO(param);
        Boolean isPfStore = storeService.isPfsStore(param.getStoreId());
        SearchQueryFilterClientReqDTO queryFilterReq = SearchQueryFilterClientReqDTO.builder().build();
        if (CollectionUtils.isNotEmpty(mtCouponToUseDTO.getInCommodityIdList())) {
            queryFilterReq.setCommodityIdList(mtCouponToUseDTO.getInCommodityIdList());
        }
        if (CollectionUtils.isNotEmpty(mtCouponToUseDTO.getCategoryIdList())) {
            queryFilterReq.setSecondCategoryIdList(mtCouponToUseDTO.getCategoryIdList());
        }
        if (CollectionUtils.isNotEmpty(mtCouponToUseDTO.getNotInCommodityIdList())) {
            queryFilterReq.setCommodityIdNotList(mtCouponToUseDTO.getNotInCommodityIdList());
        }
        clientReqDTO.setQueryFilterReq(queryFilterReq);
        SearchCommodityClientRespDTO clientRespDTO = xdaSearchOperateService.searchByCondition(clientReqDTO);
        if (null == clientRespDTO) {
            return null;
        }
        List<SearchCommodityClientDTO> clientDTOList = Optional.of(clientRespDTO)
                .map(SearchCommodityClientRespDTO::getCommodityList)
                .orElse(Collections.emptyList());
        List<EsXdaCommodity> esXdaCommodityList = esBaseSearchManagerService.getEsXdaCommodityListById(clientDTOList, storeCommodityIdList, isPfStore);
        SearchCommodityPageClientRespDTO pageResp = clientRespDTO.getPageResp();
        boolean hasNext = QueryParserUtil.hasNextPage(clientRespDTO.getPageResp());
        return new SearchResultVo(esXdaCommodityList, pageResp.getTotal(), hasNext);
    }


}
