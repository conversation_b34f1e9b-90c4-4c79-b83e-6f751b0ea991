package com.pinshang.qingyun.xda.search.service.manager;

import com.pinshang.qingyun.base.search.dto.search.SearchCommodityClientDTO;
import com.pinshang.qingyun.base.search.dto.search.SearchCommodityClientReqDTO;
import com.pinshang.qingyun.base.search.dto.search.SearchCommodityClientRespDTO;
import com.pinshang.qingyun.base.search.dto.search.SearchQueryFilterClientReqDTO;
import com.pinshang.qingyun.xda.search.constants.EsConstants;
import com.pinshang.qingyun.xda.search.document.EsXdaCommodity;
import com.pinshang.qingyun.xda.search.dto.CategoryGoodsReq;
import com.pinshang.qingyun.xda.search.dto.KeyWordGoodsReq;
import com.pinshang.qingyun.xda.search.repository.EsXdaCommodityDao;
import com.pinshang.qingyun.xda.search.service.third.XdaSearchCacheService;
import com.pinshang.qingyun.xda.search.service.third.XdaSearchOperateService;
import com.pinshang.qingyun.xda.search.util.QueryParserUtil;
import com.pinshang.qingyun.xda.search.util.XdaSearchCacheUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/29 17:05
 */
@Service
@Slf4j
public class EsBaseSearchManagerService {

    @Autowired
    private XdaSearchOperateService xdaSearchOperateService;

    @Autowired
    private EsXdaCommodityDao esXdaCommodityDao;
    @Autowired
    private XdaSearchCacheService xdaSearchCacheService;

    /**
     * 聚合查询类目 ID 列表
     *
     * @param param                包含请求参数的对象，如商店 ID 等
     * @param storeCommodityIdList 商店商品 ID 列表
     * @param isPfStore            是否未批发门店
     * @param level                搜索的类目级别，1-一级，2-二级
     * @return 返回一个 Long 类型的列表，包含 类目 ID
     */
    public List<Long> aggeGetXdaCategoryIdList(CategoryGoodsReq param,
                                               List<Long> storeCommodityIdList,
                                               Boolean isPfStore,
                                               Integer level) {
        int finalLevel = level == null ? EsConstants.CATEGORY_AGGE_ONE : level;
        String cacheKey = XdaSearchCacheUtil.getXdaStoreCategoryCacheKey(param.getShopId(), finalLevel);
        return xdaSearchCacheService.invokeCache(cacheKey, () -> doSearchXdaCategoryId(param, storeCommodityIdList, isPfStore, finalLevel));
    }

    /**
     * 根据给定的参数执行商品搜索，并根据指定的级别聚合到相应的类目ID
     *
     * @param param                商品分类请求对象，包含搜索所需的参数
     * @param storeCommodityIdList 店铺商品ID列表，用于过滤搜索结果
     * @param isPfStore            是否为特定店铺的标志，用于决定是否过滤搜索结果
     * @param level                聚合级别，决定是聚合到一级类目还是二级类目
     * @return 返回根据指定级别聚合的类目ID列表
     */
    @NotNull
    private List<Long> doSearchXdaCategoryId(CategoryGoodsReq param,
                                             List<Long> storeCommodityIdList,
                                             Boolean isPfStore,
                                             Integer level) {

        int finalLevel = level == null ? EsConstants.CATEGORY_AGGE_ONE : level;
        SearchCommodityClientReqDTO clientReqDTO = QueryParserUtil.getClientReqDTO(param);
        clientReqDTO.setPageSize(1);
        clientReqDTO.setFacet(1);
        clientReqDTO.setFacetType(1);
        clientReqDTO.setQueryFilterReq(SearchQueryFilterClientReqDTO
                .builder()
                .commodityIdList(storeCommodityIdList)
                .build());

        if (finalLevel == EsConstants.CATEGORY_AGGE_TWO) {
            // 是 二级类目id
            clientReqDTO.setFacetType(2);
        }
        SearchCommodityClientRespDTO clientRespDTO = xdaSearchOperateService.searchByCondition(clientReqDTO);
        List<SearchCommodityClientDTO> searchCommodityClientDTOList = Optional
                .ofNullable(clientRespDTO)
                .map(SearchCommodityClientRespDTO::getCommodityList)
                .orElse(Collections.emptyList());

        // 取 类目id,取一级 或 二级 类目id
//        List<EsXdaCommodity> list = getEsXdaCommodityListById(searchCommodityClientDTOList, storeCommodityIdList, isPfStore);
//        List<Long> xdaCategoryIdList = list
//                .stream()
//                .map(esXdaCommodity -> finalLevel == EsConstants.CATEGORY_AGGE_ONE
//                        ? esXdaCommodity.getXdaFirstCategoryId()
//                        : esXdaCommodity.getXdaSecondCategoryId())
//                .filter(Objects::nonNull)
//                .collect(Collectors.toList());

        // 直接 从 base-search 中获取 一级 或 二级 类目ID
        List<Long> xdaCategoryIdList = searchCommodityClientDTOList
                .stream()
                .map(o -> finalLevel == EsConstants.CATEGORY_AGGE_ONE
                        ? o.getFirstCategoryId()
                        : o.getSecondCategoryId()
                )
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        // 记录日志
        log.info("门店搜索商品个数:{} ,聚合到类目个数为：{},聚合级别:{}", storeCommodityIdList.size(), xdaCategoryIdList.size(), finalLevel);
        return xdaCategoryIdList;
    }

    public List<EsXdaCommodity> searchFromBaseSearch(KeyWordGoodsReq param,
                                                     List<Long> storeCommodityIdList,
                                                     Boolean isPfStore) {

        SearchCommodityClientReqDTO clientReqDTO = QueryParserUtil.getClientReqDTO(param);
        SearchCommodityClientRespDTO clientRespDTO = xdaSearchOperateService.searchByCondition(clientReqDTO);
        List<SearchCommodityClientDTO> clientDTOList = Optional.ofNullable(clientRespDTO)
                .map(SearchCommodityClientRespDTO::getCommodityList)
                .orElse(Collections.emptyList());
        // 获取es中的商品信息
        List<EsXdaCommodity> resultList = getEsXdaCommodityListById(clientDTOList, storeCommodityIdList, isPfStore);
        log.info("searchFromBaseSearch 门店价格方案商品个数:{} ,获取商品信息大小：{}", storeCommodityIdList.size(), resultList.size());
        return resultList;
    }


    /**
     * 根据从新搜索查询到的商品ID和门店商品ID列表获取商品信息列表
     * 查询 xda-commodity 索引
     *
     * @param clientDTOList        客户端请求的商品信息DTO列表，包含了需要搜索的商品ID
     * @param storeCommodityIdList 商店商品ID列表，用于筛选属于当前商店的商品
     * @param isPfStore            是否是 批发门店
     * @return 返回一个EsXdaCommodity对象列表，包含了根据条件筛选出的商品信息
     */
    public List<EsXdaCommodity> getEsXdaCommodityListById(List<SearchCommodityClientDTO> clientDTOList,
                                                           List<Long> storeCommodityIdList,
                                                           Boolean isPfStore) {
        // 保持base-search 原有的顺序
        List<Long> commodityIdList = new ArrayList<>();
        for (SearchCommodityClientDTO clientDTO : clientDTOList) {
            if (storeCommodityIdList.contains(clientDTO.getCommodityId())) {
                // 筛选当前商店的商品
                commodityIdList.add(clientDTO.getCommodityId());
            }
        }
        List<EsXdaCommodity> searchXdaCommodity = esXdaCommodityDao.searchXdaCommodity(commodityIdList,
                EsConstants.AGGREGATION_TERMS_SIZE_1000,
                isPfStore,
                true);
        // 记录日志，获取商品信息的大小
        log.info("xda-commodity search param 商品个数:{} ,获取商品信息大小：{}", commodityIdList.size(), searchXdaCommodity.size());
        return searchXdaCommodity;
    }


}
