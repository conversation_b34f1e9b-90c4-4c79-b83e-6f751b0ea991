package com.pinshang.qingyun.xda.search.service.manager;

import com.pinshang.qingyun.base.enums.search.BaseSearchBizTypeEnum;
import com.pinshang.qingyun.base.search.dto.switcher.SearchSwitcherClientReqDTO;
import com.pinshang.qingyun.xda.search.dto.KeyWordGoodsReq;
import com.pinshang.qingyun.xda.search.service.third.XdaSearchOperateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4 16:45
 */
@Service
@Slf4j
public class EsBaseSwitcherManagerService {

    @Autowired
    private XdaSearchOperateService xdaSearchOperateService;

    /**
     * 判断是否使用基础搜索
     *
     * @param userId 店铺关联的用户id，非店铺id和code ，后续可以根据需求切换
     * @return true: 切到新搜索。 false:不切，保持当前搜索
     */
    public boolean isSwitchBaseSearch(Long userId) {
        return xdaSearchOperateService.checkGrayExperiment(SearchSwitcherClientReqDTO
                .builder()
                .businessType(BaseSearchBizTypeEnum.XIAN_DA.getCode())
                .userId(userId)
                .build());
    }

    public boolean isSwitchBaseSearch(KeyWordGoodsReq param) {
        return xdaSearchOperateService.checkGrayExperiment(SearchSwitcherClientReqDTO
                .builder()
                .businessType(BaseSearchBizTypeEnum.XIAN_DA.getCode())
                .userId(param.getUserId())
                .build());
    }


}
