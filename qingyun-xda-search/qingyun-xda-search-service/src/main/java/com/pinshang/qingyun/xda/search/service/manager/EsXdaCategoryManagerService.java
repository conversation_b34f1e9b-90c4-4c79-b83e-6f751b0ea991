package com.pinshang.qingyun.xda.search.service.manager;

import com.pinshang.qingyun.xda.search.constants.EsConstants;
import com.pinshang.qingyun.xda.search.constants.XdaConstant;
import com.pinshang.qingyun.xda.search.document.EsXdaCategory;
import com.pinshang.qingyun.xda.search.document.EsXdaCommodity;
import com.pinshang.qingyun.xda.search.dto.CategoryGoodsReq;
import com.pinshang.qingyun.xda.search.dto.KeyWordGoodsReq;
import com.pinshang.qingyun.xda.search.dto.front.XdaSearchAppIDTO;
import com.pinshang.qingyun.xda.search.dto.front.categoryfront.XdaCategoryODTO;
import com.pinshang.qingyun.xda.search.enums.XdaCategoryEnums;
import com.pinshang.qingyun.xda.search.service.AbstractSearchService;
import com.pinshang.qingyun.xda.search.service.StoreService;
import com.pinshang.qingyun.xda.search.service.base.AbstractCategoryService;
import com.pinshang.qingyun.xda.search.service.es.CategoryElasticSearchService;
import com.pinshang.qingyun.xda.search.util.StopWatchUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4 16:41
 */
@Service
@Slf4j
public class EsXdaCategoryManagerService extends AbstractCategoryService {

    @Autowired
    @Qualifier("dynamicTaskExecutor")
    private Executor dynamicTaskExecutor;

    @Autowired
    private CategoryElasticSearchService categoryElasticSearchService;
    @Autowired
    private EsBaseSwitcherManagerService esBaseSwitcherManagerService;
    @Autowired
    private EsBaseSearchManagerService esBaseSearchManagerService;
    @Autowired
    private StoreService storeService;

    /**
     * 获取 APP 的一级类目列表。
     * 每个一级类目必须包含二级类目列表，不能只返回 一级类目列表
     *
     * @param categoryGoodsReq 包含用户ID的类别商品请求对象
     * @return 一级类目列表
     */
    public List<XdaCategoryODTO> getEsFirstCategoryListAndSecondCategorySwitch(CategoryGoodsReq categoryGoodsReq) {
        List<XdaCategoryODTO> resultList = new ArrayList<>();
        boolean switchBaseSearch = esBaseSwitcherManagerService.isSwitchBaseSearch(categoryGoodsReq.getUserId());
        boolean needXdaSearch = !switchBaseSearch;
        if (switchBaseSearch) {
            try {
                resultList = this.getEsFirstCategoryListAndSecondCategoryBase(categoryGoodsReq);
            } catch (Exception e) {
                // 记录异常日志
                log.error("从base-service 获取一级类目列表异常：message={}，error={}", categoryGoodsReq, e.getMessage(), e);
                needXdaSearch = true;
            }
        }
        if (needXdaSearch) {
            resultList = super.getEsFirstCategoryListAndSecondCategory(categoryGoodsReq);
        }
        return resultList;
    }


    public List<XdaCategoryODTO> getEsFirstCategoryListAndSecondCategoryBase(CategoryGoodsReq categoryGoodsReq) {

        StopWatch stopWatch = StopWatchUtil.begin();
        Long storeId = categoryGoodsReq.getShopId();
        List<Long> storeCommodityIdList = super.getXdaStoreCategoryIdList(storeId);
        // 获取一级类目ID
        CompletableFuture<List<Long>> firstCategoryIdFuture = CompletableFuture.supplyAsync(() ->
                        esBaseSearchManagerService.aggeGetXdaCategoryIdList(
                                categoryGoodsReq,
                                storeCommodityIdList,
                                false,
                                EsConstants.CATEGORY_AGGE_ONE),
                dynamicTaskExecutor);
        // 获取二级类目ID
        CompletableFuture<List<Long>> secondCategoryIdFuture = CompletableFuture.supplyAsync(() ->
                        esBaseSearchManagerService.aggeGetXdaCategoryIdList(
                                categoryGoodsReq,
                                storeCommodityIdList,
                                false,
                                EsConstants.CATEGORY_AGGE_TWO),
                dynamicTaskExecutor);
        // 等待两个类目ID获取完成后，继续并发查类目信息
        CompletableFuture<List<EsXdaCategory>> firstCategoryFuture = firstCategoryIdFuture.thenApplyAsync(
                ids -> categoryElasticSearchService.listFirstCategoryByIdsFromES(ids, EsConstants.XDA_CATEGORY_LEVEL_ONE),
                dynamicTaskExecutor);

        CompletableFuture<List<EsXdaCategory>> secondCategoryFuture = secondCategoryIdFuture.thenApplyAsync(
                ids -> categoryElasticSearchService.listFirstCategoryByIdsFromES(ids, EsConstants.XDA_CATEGORY_LEVEL_TWO),
                dynamicTaskExecutor);
        // 合并结果
        CompletableFuture<List<XdaCategoryODTO>> finalResult = firstCategoryFuture.thenCombine(
                secondCategoryFuture,
                AbstractSearchService::buildStoreAllCategoryList
        );
        List<XdaCategoryODTO> result = finalResult.join();
        // 记录聚合的耗时
        StopWatchUtil.end("BaseSearch聚合一级类目和二级类目耗时", stopWatch);
        log.info("一级类目和二级类目聚合结果：{}", result);
        return result;
    }


    /**
     * 查询 品类下的商品列表
     *
     * @param searchDTO             搜索DTO，包含用户ID等搜索参数
     * @param recommendCommodityIds 推荐商品ID列表
     * @param storeCommodityIdList  商店商品ID列表
     * @param xdaCategoryEnums      商品类别枚举
     * @return 返回符合搜索条件的商品列表
     */
    public List<EsXdaCommodity> getEsXdaStoreCategoryCommoditiesSwitch(XdaSearchAppIDTO searchDTO,
                                                                       List<Long> recommendCommodityIds,
                                                                       List<Long> storeCommodityIdList,
                                                                       XdaCategoryEnums xdaCategoryEnums
    ) {

        boolean switchBaseSearch = esBaseSwitcherManagerService.isSwitchBaseSearch(searchDTO.getUserId());
        boolean needSearchXdaEs = !switchBaseSearch;
        List<EsXdaCommodity> resultList = new ArrayList<>();
        if (switchBaseSearch) {
            try {
                resultList = this.getEsXdaStoreCategoryCommoditiesBase(searchDTO, recommendCommodityIds, storeCommodityIdList, xdaCategoryEnums);
            } catch (Exception e) {
                // 记录异常日志
                log.error("调用es查询商品信息异常：message={}，error={}", searchDTO, e.getMessage(), e);
                needSearchXdaEs = true;
            }
        }
        if (needSearchXdaEs) {
            resultList = super.getEsXdaStoreCategoryCommodities(searchDTO, recommendCommodityIds, storeCommodityIdList, xdaCategoryEnums);
        }
        return resultList;
    }


    public List<EsXdaCommodity> getEsXdaStoreCategoryCommoditiesBase(XdaSearchAppIDTO searchDTO,
                                                                     List<Long> recommendCommodityIds,
                                                                     List<Long> storeCommodityIdList,
                                                                     XdaCategoryEnums xdaCategoryEnums
    ) {
        StopWatch stopWatch = StopWatchUtil.begin();
        Boolean isPfStore = storeService.isPfsStore(searchDTO.getStoreId());
        KeyWordGoodsReq param = KeyWordGoodsReq.builder()
                // 品类查询，限定 当前门店价格方案的所有商品。
                .filterCommodityIdList(storeCommodityIdList)
                .pageSize(EsConstants.ES_CATEGORY_SIZE)
                .build();
        if (!Objects.equals(xdaCategoryEnums, XdaCategoryEnums.TH)
                && !Objects.equals(xdaCategoryEnums, XdaCategoryEnums.SPECIAL_PROMOTION)
                && !Objects.equals(xdaCategoryEnums, XdaCategoryEnums.OFTEN_BUY)
                && !Objects.equals(xdaCategoryEnums, XdaCategoryEnums.MY_COLLECT)
        ) {
            param.setXdaFirstCategoryId(searchDTO.getXdaFirstCategoryId());
        }
        if (Objects.nonNull(searchDTO.getXdaSecondCategoryId())
                && !Objects.equals(searchDTO.getXdaSecondCategoryId(), XdaConstant.RECOMMOND_CATEGORT_ID)) {
            param.setXdaSecondCategoryId(searchDTO.getXdaSecondCategoryId());
        }
        if (CollectionUtils.isNotEmpty(recommendCommodityIds)) {
            param.setFilterCommodityIdList(recommendCommodityIds);
            // 把二级类目ID置空,以 filter commodityIdList 筛选出商品
            param.setXdaSecondCategoryId(null);
        }
        List<EsXdaCommodity> esXdaCommodityList = esBaseSearchManagerService.searchFromBaseSearch(param, storeCommodityIdList, isPfStore);
        // 针对 批发店，把 appStatus 置为 pfAppStatus
        esXdaCommodityList.forEach(esXdaCommodity -> {
            if (isPfStore) {
                esXdaCommodity.setAppStatus(esXdaCommodity.getPfAppStatus());
            }
        });
        StopWatchUtil.end("BaseSearch ---- > es查询商户商品", stopWatch);
        return esXdaCommodityList;
    }


}
