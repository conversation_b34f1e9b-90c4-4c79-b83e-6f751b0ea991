package com.pinshang.qingyun.xda.search.service.third;

import com.pinshang.qingyun.infrastructure.components.ICacheComponent;
import com.pinshang.qingyun.xda.search.constants.EsConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.function.Supplier;

/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/12/13 14:09
 */
@Service
@Slf4j
public class XdaSearchCacheService {

    @Autowired
    private ICacheComponent iCacheComponent;

    /**
     * 使用给定的键和供应商函数从缓存中获取数据
     * 如果缓存中没有数据，则执行加载，并将结果存入缓存
     * 此方法使用了带有泛型的函数，允许返回不同类型的结果
     * 重设缓存时间默认为5分钟，300秒
     *
     * @param key      缓存项的唯一标识符
     * @param supplier 一个供应商函数，用于在缓存未命中时计算结果
     * @param <T>      泛型参数，表示返回值的类型
     * @return 返回缓存中的数据，如果缓存中没有，则返回通过供应商函数计算的结果
     */
    public <T> T invokeCache(String key, Supplier<T> supplier) {
        return invokeCache(key, supplier, EsConstants.XDA_CACHE_EXPIRE_TIME);
    }

    public <T> T invokeCache(String key, Supplier<T> supplier, long cacheExpiresInSeconds) {
        return invokeCache(EsConstants.XDA_CACHE_GROUP_DEFAULT, key, supplier, cacheExpiresInSeconds);
    }

    public <T> T invokeCache(String groupName, String key, Supplier<T> supplier, long cacheExpiresInSeconds) {
        Object value = null;
        try {
            value = iCacheComponent.getData(groupName, key);
            if (Objects.isNull(value)) {
                // 查询 接口 或 DB
                log.info("调用缓存接口，groupName:{},key:{}", groupName, key);
                value = supplier.get();
                if (Objects.nonNull(value) && cacheExpiresInSeconds > 0) {
                    iCacheComponent.save(groupName, cacheExpiresInSeconds, key, value);
                }
            }
        } catch (Exception e) {
            // 记录异常日志
            log.warn("调用缓存异常，groupName:{},key:{}", groupName, key, e);
        }
        return (T) value;
    }

    public void cleanByKey(String key) {
        cleanCache(EsConstants.XDA_CACHE_GROUP_DEFAULT, key);
    }

    public void cleanCache(String groupName) {
        iCacheComponent.cleanByGroup(groupName);
    }

    public void cleanCache(String groupName, String key) {
        iCacheComponent.cleanByGroupKey(groupName, key);
    }

}

