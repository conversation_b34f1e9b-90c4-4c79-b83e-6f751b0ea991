package com.pinshang.qingyun.xda.search.service.third;

import com.google.common.collect.Lists;
import com.pinshang.qingyun.base.api.QYApplicationContext;
import com.pinshang.qingyun.base.enums.search.BaseSearchBizTypeEnum;
import com.pinshang.qingyun.base.enums.search.BaseSearchOptTypeEnum;
import com.pinshang.qingyun.base.search.dto.kafka.BaseSearchCommodityBatchMsgClientDTO;
import com.pinshang.qingyun.base.search.dto.kafka.BaseSearchCommodityMsgClientDTO;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.box.utils.JsonUtil;
import com.pinshang.qingyun.infrastructure.components.IMqSenderComponent;
import com.pinshang.qingyun.infrastructure.components.params.mq.MqMessage;
import com.pinshang.qingyun.kafka.base.KafkaTopicConstant;
import com.pinshang.qingyun.kafka.enums.KafkaMessageOperationTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaMessageTypeEnum;
import com.pinshang.qingyun.kafka.enums.KafkaTopicEnum;
import com.pinshang.qingyun.xda.search.document.EsXdaCommodity;
import com.pinshang.qingyun.xda.search.mapper.XdaCommodityEsMapper;
import com.pinshang.qingyun.xda.search.model.XdaCommodityEs;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/17 18:31
 */
@Service
@Slf4j
public class XdaSearchMsgService {

    @Autowired
    private IMqSenderComponent mqSenderComponent;

    @Autowired
    private XdaCommodityEsMapper xdaCommodityEsMapper;


    @Async
    public void sendBaseSearchCommodityXdaMsg(List<EsXdaCommodity> deletedXdaEsItemList, BaseSearchOptTypeEnum optTypeEnum) {
        try {
            if (CollectionUtils.isEmpty(deletedXdaEsItemList)) {
                return;
            }
            sendBaseSearchCommodityMsg(deletedXdaEsItemList, optTypeEnum);
        } catch (Exception e) {
            // 记录异常日志
            log.error("发送商品信息到kafka异常：message={}，error={}", deletedXdaEsItemList, e.getMessage(), e);
        }
    }

    @Async
    public void sendBaseSearchCommodityIdMsg(List<Long> shopCommodityIdList, BaseSearchOptTypeEnum optTypeEnum) {
        try {
            if (CollectionUtils.isEmpty(shopCommodityIdList)) {
                return;
            }
            Example example = new Example(XdaCommodityEs.class);
            example.createCriteria().andIn("commodityId", shopCommodityIdList);
            List<XdaCommodityEs> xdaCommodityEsList = xdaCommodityEsMapper.selectByExample(example);
            if (CollectionUtils.isEmpty(xdaCommodityEsList)) {
                return;
            }
            List<EsXdaCommodity> esXdaCommodities = BeanCloneUtils.copyTo(xdaCommodityEsList, EsXdaCommodity.class);
            sendBaseSearchCommodityMsg(esXdaCommodities, optTypeEnum);
        } catch (Exception e) {
            // 记录异常日志
            log.error("发送商品信息到kafka异常：message={}，error={}", shopCommodityIdList, e.getMessage(), e);
        }
    }


    @Async
    public void sendBaseSearchCommodityMsg(List<EsXdaCommodity> shopCommodityList, BaseSearchOptTypeEnum optTypeEnum) {
        log.info("topic = {}, message = {}", QYApplicationContext.applicationNameSwitch + KafkaTopicConstant.BASIC_SEARCH_COMMODITY_CHANGE_TOPIC, JsonUtil.java2json(shopCommodityList));

        try {
            // 过滤 不需要同步的商品
            shopCommodityList = shopCommodityList
                    .stream()
                    .filter(item -> needSync(item, optTypeEnum))
                    .collect(Collectors.toList());
            for (List<EsXdaCommodity> commodityList : Lists.partition(shopCommodityList, 50)) {
                List<BaseSearchCommodityMsgClientDTO> msgDTOList = new ArrayList<>();
                for (EsXdaCommodity commodity : commodityList) {
                    BaseSearchCommodityMsgClientDTO msgClientDTO = BeanCloneUtils.copyTo(commodity, BaseSearchCommodityMsgClientDTO.class);
                    msgClientDTO.setShopId(0L);
                    msgClientDTO.setOptType(optTypeEnum.getCode());
                    msgClientDTO.setFirstCategoryId(commodity.getXdaFirstCategoryId());
                    msgClientDTO.setFirstCategoryName(commodity.getXdaFirstCategoryName());
                    msgClientDTO.setSecondCategoryId(commodity.getXdaSecondCategoryId());
                    msgClientDTO.setSecondCategoryName(commodity.getXdaSecondCategoryName());
                    msgClientDTO.setBusinessType(BaseSearchBizTypeEnum.XIAN_DA.getCode());
                    // 兼容 base-search 排序 ，设置soldOut 默认为1-有货
                    msgClientDTO.setSoldOut(1);
                    msgDTOList.add(msgClientDTO);
                }
                BaseSearchCommodityBatchMsgClientDTO batchMsgDTO = BaseSearchCommodityBatchMsgClientDTO.builder()
                        .msgDTOList(msgDTOList)
                        .build();
                try {
                    mqSenderComponent.send(
                            QYApplicationContext.applicationNameSwitch + KafkaTopicEnum.BASIC_SEARCH_COMMODITY_CHANGE_TOPIC.getTopic(),
                            batchMsgDTO,
                            MqMessage.MQ_KAFKA,
                            KafkaMessageTypeEnum.BASIC_SEARCH_COMMODITY_CHANGE_TOPIC.name(),
                            KafkaMessageOperationTypeEnum.INSERT.name()
                    );
                } catch (Exception e) {
                    // 记录异常 日志
                    log.error("发送商品信息到kafka异常：message={}，error={}", batchMsgDTO, e.getMessage(), e);
                    if (optTypeEnum.equals(BaseSearchOptTypeEnum.DELETE)) {
                        // 删除--最终一致性实现...
                    }
                }
            }
        } catch (Exception e) {
            // 记录异常日志
            log.error("发送商品信息到kafka异常：message={}，error={}", shopCommodityList, e.getMessage(), e);
        }
    }

    /**
     * 判断商品是否需要同步
     * <p>
     * 商品需要同步的条件是：appStatus 或 pfAppStatus 至少有一个为上架状态。
     * 上架状态由常量 ON_SHELF 定义，下架状态由 OFF_SHELF 定义。
     *
     * @param esXdaCommodity 商品对象，包含商品的上架状态信息
     * @return 如果商品需要同步，则返回 true；否则返回 false
     */
    private boolean needSync(EsXdaCommodity esXdaCommodity, BaseSearchOptTypeEnum optTypeEnum) {
        if (esXdaCommodity == null) {
            // 对象为 null，则不需要同步
            return false;
        }
        if (optTypeEnum.equals(BaseSearchOptTypeEnum.DELETE)) {
            // 删除操作，则需要同步
            return true;
        }
        // appStatus 和 pfAppStatus 都为 null，则不需要同步
        if (esXdaCommodity.getAppStatus() == null && esXdaCommodity.getPfAppStatus() == null) {
            return false;
        }
        int appStatus = esXdaCommodity.getAppStatus() != null ? esXdaCommodity.getAppStatus() : 1;
        int pfAppStatus = esXdaCommodity.getPfAppStatus() != null ? esXdaCommodity.getPfAppStatus() : 1;
        // 过滤 未上架的 appStatus 和 pfAppStatus 都是下架状态的。0-上架，1-下架
        return appStatus == 0 || pfAppStatus == 0;
    }

}
