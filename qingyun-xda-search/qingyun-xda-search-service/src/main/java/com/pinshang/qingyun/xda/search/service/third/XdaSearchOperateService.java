package com.pinshang.qingyun.xda.search.service.third;

import com.pinshang.qingyun.base.api.ApiResponse;
import com.pinshang.qingyun.base.configure.expand.BizLogicException;
import com.pinshang.qingyun.base.enums.YesOrNoEnums;
import com.pinshang.qingyun.base.search.dto.search.SearchCommodityClientReqDTO;
import com.pinshang.qingyun.base.search.dto.search.SearchCommodityClientRespDTO;
import com.pinshang.qingyun.base.search.dto.switcher.SearchSwitcherClientReqDTO;
import com.pinshang.qingyun.base.search.dto.switcher.SearchSwitcherClientRespDTO;
import com.pinshang.qingyun.base.search.service.SearchCommodityClient;
import com.pinshang.qingyun.base.search.service.SearchSwitcherClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/21 上午11:05
 */
@Service
@Slf4j
public class XdaSearchOperateService {

    @Autowired
    private SearchCommodityClient searchCommodityClient;

    @Autowired
    private SearchSwitcherClient searchSwitcherClient;


    /**
     * 检查灰度实验状态
     * 该方法用于确定当前请求是否应该参与灰度实验
     * 如果响应数据为空、出现异常或状态为否，则返回false，表示不参与灰度实验
     *
     * @param reqDTO 请求参数对象，包含灰度实验检查所需的信息
     * @return boolean 表示灰度实验是否开启 true为开启，false为关闭
     */
    public boolean checkGrayExperiment(SearchSwitcherClientReqDTO reqDTO) {
        try {
            ApiResponse<SearchSwitcherClientRespDTO> apiResponse = searchSwitcherClient.checkGrayExperiment(reqDTO);
            return Optional.ofNullable(apiResponse)
                    .map(ApiResponse::getData)
                    .map(SearchSwitcherClientRespDTO::getOpenStatus)
                    .orElse(YesOrNoEnums.NO.getCode())
                    .equals(YesOrNoEnums.YES.getCode());
        } catch (Exception e) {
            // 记录异常日志
            log.warn("调用灰度实验检查接口异常，reqDTO:{}", reqDTO, e);
            return false;
        }
    }

    /**
     * 根据条件搜索商品，若新搜索服务不可用，则抛异常，调用方处理异常后可以进行兜底处理
     *
     * @param reqDTO 搜索商品的请求参数封装对象，包含搜索条件
     * @return 返回搜索结果封装对象，可能为null
     * @throws BizLogicException 如果搜索过程中发生异常，则抛出业务逻辑异常
     */
    public SearchCommodityClientRespDTO searchByCondition(SearchCommodityClientReqDTO reqDTO) {
        try {
            ApiResponse<SearchCommodityClientRespDTO> apiResponse = searchCommodityClient.searchByCondition(reqDTO);
            return Optional.ofNullable(apiResponse)
                    .map(ApiResponse::getData)
                    .orElse(null);
        } catch (Exception e) {
            log.warn("调用商品搜索接口异常，reqDTO:{}", reqDTO, e);
            throw new BizLogicException(e.getMessage());
        }
    }


}
