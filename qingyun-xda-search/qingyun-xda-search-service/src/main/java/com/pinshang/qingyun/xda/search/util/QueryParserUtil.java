package com.pinshang.qingyun.xda.search.util;

import com.google.common.collect.Lists;
import com.pinshang.qingyun.base.enums.search.BaseSearchBizTypeEnum;
import com.pinshang.qingyun.base.search.dto.search.SearchCommodityClientReqDTO;
import com.pinshang.qingyun.base.search.dto.search.SearchCommodityPageClientRespDTO;
import com.pinshang.qingyun.base.search.dto.search.SearchQueryFilterClientReqDTO;
import com.pinshang.qingyun.box.utils.BeanCloneUtils;
import com.pinshang.qingyun.xda.search.dto.CategoryGoodsReq;
import com.pinshang.qingyun.xda.search.dto.KeyWordGoodsReq;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.*;
import org.jetbrains.annotations.NotNull;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/8 13:51
 */
@Slf4j
public class QueryParserUtil {


    public static final Map<String, String> XD_TO_BASE_SEARCH_MAP = new HashMap<String, String>() {{
        put("shopId", "shopId");
        // 商品ID
        put("commodityId", "commodityId");
        // xdFirstCategoryId --firstCategoryId
        put("xdFirstCategoryId", "firstCategoryId");
        // xdSecondCategoryId--secondCategoryId
        put("xdSecondCategoryId", "secondCategoryId");
        // appSpecialPrice	double	否		APP特价
        put("appSpecialPrice", "appSpecialPrice");
        //miniSpecialPrice	double	否		mini特价
        put("miniSpecialPrice", "miniSpecialPrice");
        //cloudAppSpecialPrice	double	否		云超APP特价
        put("cloudAppSpecialPrice", "cloudAppSpecialPrice");
        //cloudMiniSpecialPrice	double	否		云超mini特价
        put("cloudMiniSpecialPrice", "cloudMiniSpecialPrice");
    }};


    public static SearchQueryFilterClientReqDTO parseQuery(QueryBuilder queryBuilder) {
        SearchQueryFilterClientReqDTO reqDTO = SearchQueryFilterClientReqDTO.builder().build();
        if (queryBuilder instanceof BoolQueryBuilder) {
            BoolQueryBuilder boolQueryBuilder = (BoolQueryBuilder) queryBuilder;
            // 解析 must 子句
            parseMust(boolQueryBuilder, reqDTO);
            // 解析 mustNot 子句
            parseMustNot(boolQueryBuilder, reqDTO);
        }
        return reqDTO;
    }

    private static void parseMust(BoolQueryBuilder boolQueryBuilder, SearchQueryFilterClientReqDTO baseReqDTO) {
        try {
            // 获取 must() 查询条件
            Method getMustMethod = boolQueryBuilder.getClass().getMethod("must");
            List<QueryBuilder> mustQueries = (List<QueryBuilder>) getMustMethod.invoke(boolQueryBuilder);

            if (mustQueries != null) {
                for (QueryBuilder mustQuery : mustQueries) {
                    if (mustQuery instanceof TermQueryBuilder) {
                        TermQueryBuilder termQueryBuilder = (TermQueryBuilder) mustQuery;
                        String fieldName = termQueryBuilder.fieldName();
                        Object value = termQueryBuilder.value();
                        // 先取值，再加新值
                        if (XD_TO_BASE_SEARCH_MAP.containsKey(fieldName)) {
                            String baseKey = XD_TO_BASE_SEARCH_MAP.get(fieldName);
                            if (baseKey.equals("commodityId")) {
                                List<Long> commodityIdList = baseReqDTO.getCommodityIdList();
                                if (commodityIdList == null) {
                                    commodityIdList = new java.util.ArrayList<>();
                                }
                                commodityIdList.add((Long) value);
                                baseReqDTO.setCommodityIdList(commodityIdList);
                            } else if (baseKey.equals("firstCategoryId")) {
                                List<Long> firstCategoryIdList = baseReqDTO.getFirstCategoryIdList();
                                if (firstCategoryIdList == null) {
                                    firstCategoryIdList = new java.util.ArrayList<>();
                                }
                                firstCategoryIdList.add((Long) value);
                            } else if (baseKey.equals("secondCategoryId")) {
                                List<Long> secondCategoryIdList = baseReqDTO.getSecondCategoryIdList();
                                if (secondCategoryIdList == null) {
                                    secondCategoryIdList = new java.util.ArrayList<>();
                                }
                                secondCategoryIdList.add((Long) value);
                            }
                        }
                    } else if (mustQuery instanceof TermsQueryBuilder) {
                        TermsQueryBuilder termsQueryBuilder = (TermsQueryBuilder) mustQuery;
                        String fieldName = termsQueryBuilder.fieldName();
                        List<Object> values = termsQueryBuilder.values();
                        System.out.println("must TermsQuery field: " + fieldName + " values: " + values);
                        if (XD_TO_BASE_SEARCH_MAP.containsKey(fieldName)) {
                            String baseKey = XD_TO_BASE_SEARCH_MAP.get(fieldName);
                            if (baseKey.equals("commodityId")) {
                                List<Long> commodityIdList = baseReqDTO.getCommodityIdList();
                                if (commodityIdList == null) {
                                    commodityIdList = new java.util.ArrayList<>();
                                }
                                for (Object value : values) {
                                    commodityIdList.add((Long) value);
                                }
                                baseReqDTO.setCommodityIdList(commodityIdList);
                            } else if (baseKey.equals("firstCategoryId")) {
                                List<Long> firstCategoryIdList = baseReqDTO.getFirstCategoryIdList();
                                if (firstCategoryIdList == null) {
                                    firstCategoryIdList = new java.util.ArrayList<>();
                                }
                                for (Object value : values) {
                                    firstCategoryIdList.add((Long) value);
                                }
                                baseReqDTO.setFirstCategoryIdList(firstCategoryIdList);
                            } else if (baseKey.equals("secondCategoryId")) {
                                List<Long> secondCategoryIdList = baseReqDTO.getSecondCategoryIdList();
                                if (secondCategoryIdList == null) {
                                    secondCategoryIdList = new java.util.ArrayList<>();
                                }
                                for (Object value : values) {
                                    secondCategoryIdList.add((Long) value);
                                }
                                baseReqDTO.setSecondCategoryIdList(secondCategoryIdList);
                            }
                        }
                    } else if (mustQuery instanceof ExistsQueryBuilder) {
                        ExistsQueryBuilder existsQueryBuilder = (ExistsQueryBuilder) mustQuery;
                        System.out.println("must ExistsQuery field: " + existsQueryBuilder.fieldName());
                        if (XD_TO_BASE_SEARCH_MAP.containsKey(existsQueryBuilder.fieldName())) {
                            String baseKey = XD_TO_BASE_SEARCH_MAP.get(existsQueryBuilder.fieldName());
                            List<String> mustFieldList = baseReqDTO.getMustFieldList();
                            if (mustFieldList == null) {
                                mustFieldList = new java.util.ArrayList<>();
                            }
                            mustFieldList.add(baseKey);
                            baseReqDTO.setMustFieldList(mustFieldList);
                        }
                    } else {
                        System.out.println("Unknown must query type: " + mustQuery.getClass().getSimpleName());
                    }
                }
            }
        } catch (Exception e) {
            // 记录日志
            log.warn("解析mustQuery异常，query:{}", boolQueryBuilder, e);
        }
    }

    private static void parseMustNot(BoolQueryBuilder boolQueryBuilder, SearchQueryFilterClientReqDTO reqDTO) {
        try {
            // 获取 mustNot() 查询条件
            Method getMustNotMethod = boolQueryBuilder.getClass().getMethod("mustNot");
            List<QueryBuilder> mustNotQueries = (List<QueryBuilder>) getMustNotMethod.invoke(boolQueryBuilder);

            if (mustNotQueries != null) {
                for (QueryBuilder mustNotQuery : mustNotQueries) {
                    if (mustNotQuery instanceof TermQueryBuilder) {
                        TermQueryBuilder termQueryBuilder = (TermQueryBuilder) mustNotQuery;
                        String fieldName = termQueryBuilder.fieldName();
                        System.out.println("mustNot TermQuery field: " + fieldName + " value: " + termQueryBuilder.value());
                        if (XD_TO_BASE_SEARCH_MAP.containsKey(fieldName)) {
                            String baseKey = XD_TO_BASE_SEARCH_MAP.get(fieldName);
                            if (baseKey.equals("commodityId")) {
                                List<Long> commodityIdList = reqDTO.getCommodityIdNotList();
                                if (commodityIdList == null) {
                                    commodityIdList = new java.util.ArrayList<>();
                                }
                                commodityIdList.add((Long) termQueryBuilder.value());
                            } else if (baseKey.equals("firstCategoryId")) {
                                List<Long> firstCategoryIdList = reqDTO.getFirstCategoryIdNotList();
                                if (firstCategoryIdList == null) {
                                    firstCategoryIdList = new java.util.ArrayList<>();
                                }
                                firstCategoryIdList.add((Long) termQueryBuilder.value());
                                reqDTO.setFirstCategoryIdNotList(firstCategoryIdList);
                            } else if (baseKey.equals("secondCategoryId")) {
                                List<Long> secondCategoryIdList = reqDTO.getSecondCategoryIdNotList();
                                if (secondCategoryIdList == null) {
                                    secondCategoryIdList = new java.util.ArrayList<>();
                                }
                                secondCategoryIdList.add((Long) termQueryBuilder.value());
                                reqDTO.setSecondCategoryIdNotList(secondCategoryIdList);
                            }
                        }
                    } else if (mustNotQuery instanceof TermsQueryBuilder) {
                        TermsQueryBuilder termsQueryBuilder = (TermsQueryBuilder) mustNotQuery;
                        String fieldName = termsQueryBuilder.fieldName();
                        List<Object> values = termsQueryBuilder.values();
                        System.out.println("mustNot TermsQuery field: " + fieldName + " values: " + values);
                        if (XD_TO_BASE_SEARCH_MAP.containsKey(fieldName)) {
                            String baseKey = XD_TO_BASE_SEARCH_MAP.get(fieldName);
                            if (baseKey.equals("commodityId")) {
                                List<Long> commodityIdList = reqDTO.getCommodityIdNotList();
                                if (commodityIdList == null) {
                                    commodityIdList = new java.util.ArrayList<>();
                                }
                                for (Object value : values) {
                                    commodityIdList.add((Long) value);
                                }
                                reqDTO.setCommodityIdNotList(commodityIdList);
                            } else if (baseKey.equals("firstCategoryId")) {
                                List<Long> firstCategoryIdList = reqDTO.getFirstCategoryIdNotList();
                                if (firstCategoryIdList == null) {
                                    firstCategoryIdList = new java.util.ArrayList<>();
                                }
                                for (Object value : values) {
                                    firstCategoryIdList.add((Long) value);
                                }
                                reqDTO.setFirstCategoryIdNotList(firstCategoryIdList);
                            } else if (baseKey.equals("secondCategoryId")) {
                                List<Long> secondCategoryIdList = reqDTO.getSecondCategoryIdNotList();
                                if (secondCategoryIdList == null) {
                                    secondCategoryIdList = new java.util.ArrayList<>();
                                }
                                for (Object value : values) {
                                    secondCategoryIdList.add((Long) value);
                                }
                                reqDTO.setSecondCategoryIdNotList(secondCategoryIdList);
                            }
                        }
                    } else if (mustNotQuery instanceof ExistsQueryBuilder) {
                        ExistsQueryBuilder existsQueryBuilder = (ExistsQueryBuilder) mustNotQuery;
                        String fieldName = existsQueryBuilder.fieldName();
                        System.out.println("mustNot ExistsQuery field: " + existsQueryBuilder.fieldName());
                        if (XD_TO_BASE_SEARCH_MAP.containsKey(fieldName)) {
                            String baseKey = XD_TO_BASE_SEARCH_MAP.get(fieldName);
                            List<String> mustNotFieldList = reqDTO.getMustNotFieldList();
                            if (mustNotFieldList == null) {
                                mustNotFieldList = new java.util.ArrayList<>();
                            }
                            mustNotFieldList.add(baseKey);
                            reqDTO.setMustNotFieldList(mustNotFieldList);
                        }
                    } else {
                        System.out.println("Unknown mustNot query type: " + mustNotQuery.getClass().getSimpleName());
                    }
                }
            }
        } catch (Exception e) {
            // 记录日志
            log.warn("解析查询异常，query:{}", boolQueryBuilder, e);
        }
    }

    @NotNull
    public static SearchCommodityClientReqDTO getClientReqDTO(KeyWordGoodsReq param) {
        SearchCommodityClientReqDTO clientReqDTO = BeanCloneUtils.copyTo(param, SearchCommodityClientReqDTO.class);
        clientReqDTO.setBusinessType(BaseSearchBizTypeEnum.XIAN_DA.getCode());
        clientReqDTO.setKeyword(param.getKeyWord());
        // param pageNo 从0 开始
        clientReqDTO.setPageNum(param.getPageNo() == null || param.getPageNo() == 0 ? 1 : param.getPageNo());
        clientReqDTO.setShopId(0L);
        SearchQueryFilterClientReqDTO queryFilterReq = SearchQueryFilterClientReqDTO.builder()
                .firstCategoryIdList(param.getXdaFirstCategoryId() == null ? Collections.emptyList() : Lists.newArrayList(param.getXdaFirstCategoryId()))
                .secondCategoryIdList(param.getXdaSecondCategoryId() == null ? Collections.emptyList() : Lists.newArrayList(param.getXdaSecondCategoryId()))
                .commodityIdList(param.getFilterCommodityIdList() == null ? Collections.emptyList() : param.getFilterCommodityIdList())
                .build();
        clientReqDTO.setQueryFilterReq(queryFilterReq);
        return clientReqDTO;
    }

    @NotNull
    public static SearchCommodityClientReqDTO getClientReqDTO(CategoryGoodsReq param) {
        SearchCommodityClientReqDTO clientReqDTO = BeanCloneUtils.copyTo(param, SearchCommodityClientReqDTO.class);
        clientReqDTO.setBusinessType(BaseSearchBizTypeEnum.XIAN_DA.getCode());
        // param 的pageNo 如果从0 开始，则置为 1
        clientReqDTO.setPageNum(param.getPageNo() == null || param.getPageNo() == 0 ? 1 : param.getPageNo());
        clientReqDTO.setShopId(0L);
        return clientReqDTO;
    }

    /**
     * 判断是否有下一页商品信息
     * <p>
     * 该方法用于根据搜索商品页面响应数据判断是否还有下一页数据
     * 它通过检查当前页码、页面大小和总记录数来确定是否存在下一页
     *
     * @param pageResp 页面响应对象，包含分页信息
     * @return 如果有下一页则返回true，否则返回false
     */
    public static boolean hasNextPage(SearchCommodityPageClientRespDTO pageResp) {
        if (pageResp == null) {
            return false;
        }
        if (pageResp.getPageNum() == null || pageResp.getPageNum() == 0) {
            return false;
        }
        if (pageResp.getPageSize() == null || pageResp.getPageSize() == 0) {
            return false;
        }
        if (pageResp.getTotal() == null || pageResp.getTotal() == 0) {
            return false;
        }
        return (long) pageResp.getPageNum() * pageResp.getPageSize() < pageResp.getTotal();
    }

    public static void main(String[] args) {
        // 示例：构建一个 BoolQueryBuilder（全场券的示例）
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder()
                .must(QueryBuilders.termQuery("shopId", 1L))
                .mustNot(QueryBuilders.existsQuery("cloudAppSpecialPrice"));

        // 解析这个查询
        SearchQueryFilterClientReqDTO reqDTO = parseQuery(boolQueryBuilder);
        System.out.println(reqDTO);
        System.out.println("SearchCommodityPageClientRespDTO is null , hasNextPage is >>> " + hasNextPage(null));
    }
}
