package com.pinshang.qingyun.xda.search.util;

import com.pinshang.qingyun.box.utils.TimeUtil;
import org.apache.commons.lang3.StringUtils;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @ClassName ReflectionUtils
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/8 17:34
 * @Version 1.0
 */
public class ReflectionUtils {

    private static ConcurrentHashMap<String, Method> readMethodMap = new ConcurrentHashMap<>();

    public static Boolean getSoldOutMethod(Object object, Long interval) {
        Class clazz = object.getClass();
        try {
            String fieldName = "getSoldOut";
            if (interval > 0) {
                fieldName = (fieldName + interval).intern();
            }

            Method method = readMethodMap.get(fieldName);
            if (Objects.nonNull(method)) {
                return (Boolean) method.invoke(object);
            }

            BeanInfo beanInfo = Introspector.getBeanInfo(clazz);
            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
                method = propertyDescriptor.getReadMethod();

                String name = method.getName();
                if (StringUtils.equals(name, fieldName)) {
                    method.setAccessible(Boolean.TRUE);
                    readMethodMap.put(fieldName, method);
                    break;
                }
            }
            return (Boolean) method.invoke(object);
        } catch (Exception e) {
            throw new RuntimeException("获取get方法时发生异常:", e);
        }
    }

//    public static void main(String[] args) throws ParseException {
//        EsXdaStoreCommodity esXdaStoreCommodity = new EsXdaStoreCommodity();
//
//        Date parse = DateFormatUtils.ISO_8601_EXTENDED_DATE_FORMAT.parse("2024-04-25");
//        for (int i = 0; i < 10000; i++) {
////            Boolean soldOutMethod = ReflectionUtils.getSoldOutMethod(esXdaStoreCommodity, parse);
//            Boolean soldOutMethod = ReflectionUtils.getSoldOutMethod(esXdaStoreCommodity, 2L);
//        }
//
//        StopWatch stopWatch = StopWatchUtil.begin();
//
//        for (int i = 0; i < 10000; i++) {
////            Boolean soldOutMethod = ReflectionUtils.getSoldOutMethod(esXdaStoreCommodity, parse);
//            Boolean soldOutMethod = ReflectionUtils.getSoldOutMethod(esXdaStoreCommodity, 2L);
//        }
//
//        for (int i = 0; i < 10000; i++) {
////            Boolean soldOutMethod = ReflectionUtils.getSoldOutMethod(esXdaStoreCommodity, parse);
//            Boolean soldOutMethod = ReflectionUtils.getSoldOutMethod(esXdaStoreCommodity, 2L);
//        }
//        StopWatchUtil.end("xxxx",stopWatch);
//    }

    public static Boolean getSoldOutMethod(Object object, Date orderTime) {
        Class clazz = object.getClass();
        try {
            Long interval = dateOfCalculation(orderTime);
            String fieldName = "getSoldOut";
            if (interval > 0) {
                fieldName = fieldName + interval;
            }
            Method method = org.dozer.util.ReflectionUtils.getMethod(clazz, fieldName);

            Boolean soldOut = (Boolean) method.invoke(object);
            return soldOut;
        } catch (Exception e) {
            throw new RuntimeException("获取get方法时发生异常:", e);
        }
    }

    public static void setSoldOutMethod(Object object, Date orderTime, Boolean soldOut) {
        Class clazz = object.getClass();
        try {
            Long interval = dateOfCalculation(orderTime);
            String fieldName = "setSoldOut";
            if (interval > 0) {
                fieldName = fieldName + interval;
            }
            Method method = org.dozer.util.ReflectionUtils.getMethod(clazz, fieldName);

            method.invoke(object, soldOut);
        } catch (Exception e) {
            throw new RuntimeException("获取get方法时发生异常:", e);
        }
    }

    public static Long dateOfCalculation(Date orderTime) {
        Long res = 0L;
        //计算送货日期和当前日期相差几天
        if (orderTime != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            try {
                res = TimeUtil.countDays(sdf.parse(sdf.format(new Date())), orderTime);
            } catch (ParseException e) {
                throw new RuntimeException(e);
            }
        }
        return res;
    }
}
