package com.pinshang.qingyun.xda.search.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.StopWatch;

@Slf4j
public class StopWatchUtil {

    public static StopWatch begin() {
        StopWatch watch = new StopWatch();
        watch.start();
        return watch;
    }

    public static void end(String msg, StopWatch watch) {

        watch.stop();

        log.info(StringUtils.join(msg, " 执行消耗时间:" + watch.getTotalTimeMillis()), " 毫秒");
    }

}
