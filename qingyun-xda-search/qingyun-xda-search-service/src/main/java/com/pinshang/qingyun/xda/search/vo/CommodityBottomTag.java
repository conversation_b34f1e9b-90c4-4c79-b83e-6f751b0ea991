package com.pinshang.qingyun.xda.search.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;

/**
 * @Author: sk
 * @Date: 2023/3/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommodityBottomTag implements Serializable,Comparable<CommodityBottomTag> {
    private static final long serialVersionUID = -4715674721056759196L;

    private String picUrl;
    private Integer sort;

    @Override
    public int compareTo(@NotNull CommodityBottomTag o) {
        return this.getSort().compareTo(o.getSort());
    }
}
