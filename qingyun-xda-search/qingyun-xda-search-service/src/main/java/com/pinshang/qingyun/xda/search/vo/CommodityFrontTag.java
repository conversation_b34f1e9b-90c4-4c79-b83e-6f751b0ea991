package com.pinshang.qingyun.xda.search.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.io.Serializable;

/**
 * @Author: sk
 * @Date: 2023/3/21
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CommodityFrontTag implements Serializable,Comparable<CommodityFrontTag>  {
    private static final long serialVersionUID = -4715674232970147196L;

    private String picUrl;
    private Integer sort;
    private Integer realWidth;
    private Integer realHeight;

    @Override
    public int compareTo(@NotNull CommodityFrontTag o) {
        return this.getSort().compareTo(o.getSort());
    }
}
