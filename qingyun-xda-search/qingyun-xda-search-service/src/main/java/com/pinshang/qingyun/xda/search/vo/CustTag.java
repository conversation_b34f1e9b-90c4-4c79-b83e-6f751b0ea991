package com.pinshang.qingyun.xda.search.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/2/14 15:29
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明-标签组
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CustTag implements Serializable {
    @ApiModelProperty(value = "标签名称", required = true)
    private String tagName;

    @ApiModelProperty(value = "标签颜色", required = true)
    private String tagBgColor;

    private static final long serialVersionUID = -4715674729701059196L;
}
