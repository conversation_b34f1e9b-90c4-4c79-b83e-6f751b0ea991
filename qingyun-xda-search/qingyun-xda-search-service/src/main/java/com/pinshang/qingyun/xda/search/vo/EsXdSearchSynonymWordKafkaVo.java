package com.pinshang.qingyun.xda.search.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 近义词新增/修改同步ES
 * */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EsXdSearchSynonymWordKafkaVo {
    /**组名*/
    private String name;

    /**编码*/
    private String code;

    /**近义词:逗号分隔多个近义词*/
    private String synonymWord;

    /**状态: 0= 停用,1=启用*/
    private Integer status;

    @ApiModelProperty("业务类型：1-通用，2-云超搜索，3-鲜达搜索")
    private Integer businessType;

    @ApiModelProperty("词典类型：1=同义词，2=近义词，3=上位词，4=下位词，5=实体识别")
    private Integer dictType;

    /**
     * 字典类型1-原有，2-新词典
     */
    @ApiModelProperty("近义词消息变更类型：1-原有，2-新词典")
    private Integer synonymWordType;

}
