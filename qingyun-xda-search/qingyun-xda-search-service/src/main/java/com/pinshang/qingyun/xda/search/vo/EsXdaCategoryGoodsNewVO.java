package com.pinshang.qingyun.xda.search.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Version 0.1.0
 * @Date 2020/4/9 17:15
 * @Copyright © 2019-2020 qm
 * @Description -- 类说明 商品检索的数据
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EsXdaCategoryGoodsNewVO implements Serializable {

    @ApiModelProperty(value = "记录id", required = true, position = 1)
    private String id;

    @ApiModelProperty(value = "第一级类别id", required = true)
    private Long firstDisplayedLevelId;

    @ApiModelProperty(value = "firstDisplayedLevelId字段的字符串形式", required = true)
    private String firstDisplayedLevelIdSeq;

    @ApiModelProperty(value = "第一级类别名称", required = true)

    private String firstDisplayedLevelName;

    @ApiModelProperty(value = "第一级类别排序，整数", required = true)
    private Integer firstDisplayedLevelSort;

    @ApiModelProperty(value = "第一级类别顶部图片地址", required = true)
    private String firstDisplayedLevelPic;



    @ApiModelProperty(value = "第二级类别id", required = true)
    private Long secondDisplayedLevelId;

    @ApiModelProperty(value = "secondDisplayedLevelId字段的字符串形式", required = true)
    private String secondDisplayedLevelIdSeq;

    @ApiModelProperty(value = "第二级类别名称", required = true)
    private String secondDisplayedLevelName;

    @ApiModelProperty(value = "第二级类别排序", required = true)
    private Integer secondDisplayedLevelSort;

    @ApiModelProperty(value = "未见原始解释", required = true)
    private String businessType;

    @ApiModelProperty(value = "未见原始解释", required = true, dataType = "comm_cate_display")
    private String dataType;

    @ApiModelProperty(value = "创建时间", required = true)
    private String createTime;

    @ApiModelProperty(value = "更新时间", required = true)
    private String updateTime;

    @ApiModelProperty(value = "商品信息列表", required = true)
    private List<EsXdaGoodsNewVO> commodityList;


    public String getFirstDisplayedLevelIdSeq() {
        return this.firstDisplayedLevelId == null ? "" : this.firstDisplayedLevelId.toString();
    }

    public String getSecondDisplayedLevelIdSeq() {
        return this.secondDisplayedLevelId == null ? "" : this.secondDisplayedLevelId.toString();
    }

    private static final long serialVersionUID = -1096975189765207189L;
}
