package com.pinshang.qingyun.xda.search.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.pinshang.qingyun.base.configure.codec.DecimalSerializerKeep2;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName EsXdGoodsNewVO
 * @Description TODO
 * <AUTHOR>
 * @Date 2024/4/2 18:36
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EsXdaGoodsNewVO implements Serializable {

    private static final long serialVersionUID = 5826158204299521325L;

    @ApiModelProperty(value = "商品ID",position = 1)
    private Long commodityId;
    @ApiModelProperty(value ="商品ID字符串",position = 1)
    private String commodityIdStr;
    @ApiModelProperty(value ="商品前台名称",position = 3)
    private String commodityName;
    @ApiModelProperty(value ="副标题",position = 4)
    private String commoditySubName;
    @ApiModelProperty(value ="规格",position = 5)
    private String commoditySpec;
    @ApiModelProperty(value ="计量单位",position = 6)
    private String commodityUnitName;
    @ApiModelProperty(value ="APP上架状态：0-上架，1-下架；购物车使用",hidden = true)
    private Integer appStatus;
    @ApiModelProperty(value ="默认图片URL,拼接后的url",position = 7)
    private String imageUrl;
    @ApiModelProperty(value ="商品单价，取客户价格方案",position = 8)
    @JsonSerialize(using = DecimalSerializerKeep2.class)
    private BigDecimal commodityPrice;
    @ApiModelProperty(value ="前台一级品类ID",position = 9)
    private Long xdaFirstCategoryId;
    @ApiModelProperty(value ="前台二级品类ID",position = 10)
    private Long xdaSecondCategoryId;
    @ApiModelProperty(value ="商品分类页排序号",hidden = true)
    private Integer sortNum;
    @ApiModelProperty(value ="最早可订货时间,订单使用",hidden = true)
    private Date beginDeliveryTime;
    @ApiModelProperty(value ="最晚可订货时间,订单使用",hidden = true)
    private Date endDeliveryTime;
    @ApiModelProperty(value = "客户ID",position = 11)
    private Long storeId;

    public String getCommodityIdStr() {
        return this.commodityId==null?"":String.valueOf(this.commodityId);
    }
}

