package com.pinshang.qingyun.xda.search.vo;

import com.pinshang.qingyun.xda.search.document.EsXdaCommodity;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class SearchResultVo {
    private List<EsXdaCommodity> sortList;
    //总记录数
    private Long total;
    //是否有下一页
    private Boolean hasNextPage = false;

    public SearchResultVo(List<EsXdaCommodity> sortList, Long total, Boolean hasNextPage){
        this.sortList = sortList;
        this.total = total;
        this.hasNextPage = hasNextPage;
    }
}
