package com.pinshang.qingyun.xda.search.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 消息场景：B端商品大仓库存变动同步给pinshang库
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TobCommodityStockKafkaVo {

    /**
     * 业务类型 0=B端销售  10=通达销售
     */
    private Integer type;
    /**
     * 商品id
     */
    private Long commodityId;
    /**
     * 首选B端仓库ID
     */
    private Long warehouseId;
    /**
     * 库存依据类型
     */
    private Integer stockType;
    /**
     * 触发方式 ToBSyncTypeEnum 1.订单 2.修改限量方式
     */
    private Integer syncType;
    /**
     * 生效方式: 1=限总量，2=每天循环限量
     */
    private Integer effectType;
    /**
     * 限量份数
     */
    private Integer limitNumber;
    /**
     * 是否有库存；0：无；1：有
     */
    private Integer stockStatus;

    /**
     * 当前时间
     */
    private Date currentTime;

    /**
     * 历史库存状态
     */
    private List<TobCommodityStockStatusKafkaVo> stockStatusList;

}
