package com.pinshang.qingyun.xda.search.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XdShopCommodityLimitPromotionInfo {

    @ApiModelProperty("限时抢购价格")
    private BigDecimal limitPromotionPrice;

    @ApiModelProperty("限时抢购开始时间")
    private Date beginTime;

    @ApiModelProperty("限时抢购结束时间")
    private Date endTime;

    @ApiModelProperty("当前时间")
    private Date currentTime;

}
