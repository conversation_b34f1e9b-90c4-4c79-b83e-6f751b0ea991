package com.pinshang.qingyun.xda.search.vo;

import com.pinshang.qingyun.xda.search.model.XdaCommodityEs;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/04/15
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class XdaCommoditySoldOutUpdateVO {
    /**
     * 商品id
     */
    private Long commodityId;

    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut1;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut2;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut3;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut4;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut5;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut6;
    @ApiModelProperty("1有库存 0售罄")
    private Integer soldOut7;

    private Integer appStatus;

    private Date updateTime;


    private List<Long> storeIdList;
    public XdaCommodityEs copy2XdaCommodityEs(){
        XdaCommodityEs es = new XdaCommodityEs();
        es.setCommodityId(this.commodityId);
        es.setSoldOut(this.soldOut);
        es.setSoldOut1(this.soldOut1);
        es.setSoldOut2(this.soldOut2);
        es.setSoldOut3(this.soldOut3);
        es.setSoldOut4(this.soldOut4);
        es.setSoldOut5(this.soldOut5);
        es.setSoldOut6(this.soldOut6);
        es.setSoldOut7(this.soldOut7);
        es.setUpdateTime(this.updateTime);
        es.setAppStatus(this.appStatus);
        return es;
    }

}
