server:
  tomcat:
    uri-encoding: UTF-8
  servlet:
    encoding:
      charset: UTF-8
      enabled: true
      force: true
mybatis:
  type-aliases-package: com.pinshang.qingyun.xda.search.model
  mapper-locations: classpath*:mappers/*.xml
  configuration:
    map-underscore-to-camel-case: true
    use-generated-keys: true
mapper:
  mappers:
    - com.pinshang.qingyun.base.mybatis.MyMapper
  not-empty: false
  identity: MYSQL
  enum-as-simple-type: true
spring:
  session:
    store-type=none
  jackson:
    time-zone: GMT+8
feign:
  httpclient:
    enabled: true
  hystrix:
    enabled: true
eureka:
  client:
    healthcheck:
      enabled: true
  instance:
    metadata-map:
      instanceVersion: V3.1.4
      appProfile: ${spring.profiles.active}
      appSwitch: ${application.name.switch}
      appCode: ${pinshang.application-name}

management:
  endpoints:
    web:
      exposure:
        include: '*'