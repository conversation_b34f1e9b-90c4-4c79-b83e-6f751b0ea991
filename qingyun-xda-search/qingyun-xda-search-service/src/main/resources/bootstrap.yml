pinshang:
  application-prefix:
  application-name: qingyun-xda-search-service
spring:
  application:
    name: ${application.name.switch:${pinshang.application-prefix}}${pinshang.application-name}
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ANT_PATH_MATCHER
    view:
      prefix: /templates/
      suffix: .ftl
  freemarker:
    cache: false
    request-context-attribute: request
server:
  tomcat:
    uri-encoding: UTF-8
    basedir: ./logs/${spring.application.name}
  port: 9066
logging:
  file:
    name: ${pinshang.application-name}
    path: ./logs/${spring.application.name}
app:
  id: ${pinshang.application-name}
apollo:
  #  meta: http://*************:8080
  cluster: ${application.name.switch:default}
  bootstrap:
    enabled: true

#-----------------------------------  me  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  config:
    activate:
      on-profile: me
#  cloud:
#    config:
#      uri: http://localhost:9001/
#      username: admin
#      password: 111111
apollo:
  meta: http://**************:8080
#-----------------------------------  dev 各环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  config:
    activate:
      on-profile: dev
#  cloud:
#    config:
#      uri: http://*************:9001/
#      username: admin
#      password: 111111
apollo:
  meta: http://**************:8080
#-----------------------------------  test  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  config:
    activate:
      on-profile: test
#  cloud:
#    config:
#      uri: http://*************:9001/
#      username: admin
#      password: 111111
apollo:
  meta: http://*************:8080
#-----------------------------------  wg-prod  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  config:
    activate:
      on-profile: wg-prod
#  cloud:
#    config:
#      uri: http://192.168.100.35:9001/
#      username: admin
#      password: qingyun123
apollo:
  meta: http://192.168.103.103:8080
#-----------------------------------  wg-hd-prod  环境配置开始－－－－－－－－－－－－－－－－－－－
---
# 配置中心的地址
spring:
  config:
    activate:
      on-profile: wg-hd-prod
#  cloud:
#    config:
#      uri: http://192.168.100.65:9001/
#      username: admin
#      password: qingyun123
apollo:
  meta: http://192.168.100.13:8080