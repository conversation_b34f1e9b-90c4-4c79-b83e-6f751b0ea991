<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.search.mapper.XdaCategoryEsMapper">
    <update id="batchUpdateByCateIdList">
        <foreach collection="list" item="item" index="index" separator=";" open="" close="">
        UPDATE t_xda_category_es
        SET
        <if test="null != item.parentId">
            parent_id = #{item.parentId},
        </if>
        <if test="null != item.cateName and '' != item.cateName">
            cate_name = #{item.cateName},
        </if>
        <if test="null != item.cateLevel">
            cate_level = #{item.cateLevel},
        </if>
        <if test="null != item.sortNum">
            sort_num = #{item.sortNum},
        </if>
        update_time = #{item.updateTime}
        WHERE cate_id = #{item.cateId}
        </foreach>
    </update>

    <insert id="batchInsert">
        insert into t_xda_category_es (id, cate_id, parent_id, cate_name, cate_level, sort_num, create_time, update_time)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id},#{item.cateId}, #{item.parentId}, #{item.cateName}, #{item.cateLevel}, #{item.sortNum}, now(), now())
        </foreach>
    </insert>

</mapper>