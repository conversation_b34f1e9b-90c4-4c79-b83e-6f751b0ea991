<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pinshang.qingyun.xda.search.mapper.XdaCommodityEsMapper">

    <update id="updateCommodityAppStatus">
        UPDATE t_xda_commodity_es
        SET app_status = #{appStatus},
            update_time = now()
        WHERE id IN
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

    <update id="batchUpdateCommodityText">
        <foreach collection="list" item="item" index="index" separator=";" open="" close="">
            UPDATE t_xda_commodity_es
            SET
            <if test="null != item.commodityAppName and '' != item.commodityAppName">
                commodity_app_name = #{item.commodityAppName},
            </if>
            <if test="null != item.commoditySubName and '' != item.commoditySubName">
                commodity_sub_name = #{item.commoditySubName},
            </if>
            <if test="null != item.sortNum">
                sort_num = #{item.sortNum},
            </if>
            <if test="null != item.commoditySpec and '' != item.commoditySpec">
                commodity_spec = #{item.commoditySpec},
            </if>
            <if test="null != item.defaultImageUrl and '' != item.defaultImageUrl">
                default_image_url = #{item.defaultImageUrl},
            </if>
            <if test="null != item.commodityUnitName and '' != item.commodityUnitName">
                commodity_unit_name = #{item.commodityUnitName},
            </if>
            <if test="null != item.deliveryDateRangeCode and '' != item.deliveryDateRangeCode">
                delivery_date_range_code = #{item.deliveryDateRangeCode},
            </if>
            <if test="null != item.deliveryDateRangeValue and '' != item.deliveryDateRangeValue">
                delivery_date_range_value = #{item.deliveryDateRangeValue},
            </if>
            <if test="null != item.xdaFirstCategoryId">
                xda_first_category_id = #{item.xdaFirstCategoryId},
            </if>
            <if test="null != item.xdaSecondCategoryId ">
                xda_second_category_id = #{item.xdaSecondCategoryId},
            </if>
            <if test = "null != item.commoditySearchName">
                commodity_search_name = #{item.commoditySearchName},
            </if>
            <if test = "null != item.xdaFirstCategoryName">
                xda_first_category_name = #{item.xdaFirstCategoryName},
            </if>
            <if test = "null != item.xdaSecondCategoryName">
                xda_second_category_name = #{item.xdaSecondCategoryName},
            </if>
            <if test = "null != item.deliveryDateAnteriorInterval">
                delivery_date_anterior_interval = #{item.deliveryDateAnteriorInterval},
            </if>
            <if test = "null != item.deliveryDateAfterInterval">
                delivery_date_after_interval = #{item.deliveryDateAfterInterval},
            </if>
            update_time = #{item.updateTime}
            WHERE commodity_id = #{item.commodityId}
        </foreach>
    </update>


    <update id="batchUpdateCommoditySoldOut">
        <foreach collection="list" item="item" index="index" separator=";" open="" close="">
            UPDATE t_xda_commodity_es set
            sold_out = #{item.soldOut},
            sold_out1 = #{item.soldOut1},
            sold_out2 = #{item.soldOut2},
            sold_out3 = #{item.soldOut3},
            sold_out4 = #{item.soldOut4},
            sold_out5 = #{item.soldOut5},
            sold_out6 = #{item.soldOut6},
            sold_out7 = #{item.soldOut7},
            update_time = #{item.updateTime}
            WHERE commodity_id = #{item.commodityId}
        </foreach>
    </update>

    <select id="selectCommodityEsByCommodityIds" resultType="com.pinshang.qingyun.xda.search.model.XdaCommodityEs">
        select id AS commodityId,
               commodity_code AS commodityCode,
               commodity_spec AS commoditySpec,
               commodity_unit_name AS commodityUnitName,
               is_weight AS isWeight
        from t_commodity
        <where>
            <if test="null != commodityIds and commodityIds.size() > 0">
                and id IN
                <foreach collection="commodityIds" item="ids" index="index" open="(" close=")" separator=",">
                    #{ids}
                </foreach>
            </if>
        </where>
    </select>
    <select id="selectByPage" resultType="com.pinshang.qingyun.xda.search.model.XdaCommodityEs">
        SELECT a.* FROM (
            select id from t_xda_commodity_es where 1=1
            <if test="null != updateTime">
                and update_time >= #{updateTime}
            </if>
            order by id asc limit #{from},#{offset}
        ) b,t_xda_commodity_es a where a.id = b.id;
    </select>

    <insert id = "batchInsert">
        insert into t_xda_commodity_es
        (
        id,
        commodity_id,
        commodity_code,
        commodity_app_name,
        commodity_sub_name,
        commodity_spec,
        default_image_url,
        commodity_unit_name,
        sort_num,
        xda_first_category_id,
        xda_second_category_id,
        delivery_date_range_code,
        delivery_date_range_value,
        app_status,
        commodity_search_name,
        xda_first_category_name,
        xda_second_category_name,
        delivery_date_anterior_interval,
        delivery_date_after_interval,
        create_time,
        update_time,
        pf_app_status,
        pf_delivery_date_range_code,
        pf_delivery_date_range_value,
        pf_delivery_date_anterior_interval,
        pf_delivery_date_after_interval
        )
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (
         #{item.id},
         #{item.commodityId},
         #{item.commodityCode},
         #{item.commodityAppName},
         #{item.commoditySubName},
         #{item.commoditySpec},
         #{item.defaultImageUrl},
         #{item.commodityUnitName},
         #{item.sortNum},
         #{item.xdaFirstCategoryId},
         #{item.xdaSecondCategoryId},
         #{item.deliveryDateRangeCode},
         #{item.deliveryDateRangeValue},
         #{item.appStatus},
         #{item.commoditySearchName},
         #{item.xdaFirstCategoryName},
         #{item.xdaSecondCategoryName},
         #{item.deliveryDateAnteriorInterval},
         #{item.deliveryDateAfterInterval},
         now(),
         now(),
         #{item.pfAppStatus},
         #{item.pfDeliveryDateRangeCode},
         #{item.pfDeliveryDateRangeValue},
         #{item.pfDeliveryDateAnteriorInterval},
         #{item.pfDeliveryDateAfterInterval}
        )
        </foreach>
    </insert>

    <update id="updateBatchById">
        <foreach collection="list" item="item" separator=";">
            update t_xda_commodity_es
            set
            commodity_code = #{item.commodityCode},
            commodity_app_name = #{item.commodityAppName},
            commodity_sub_name = #{item.commoditySubName},
            sort_num = #{item.sortNum},
            commodity_spec = #{item.commoditySpec},
            default_image_url = #{item.defaultImageUrl},
            commodity_unit_name = #{item.commodityUnitName},
            delivery_date_range_code = #{item.deliveryDateRangeCode},
            delivery_date_range_value = #{item.deliveryDateRangeValue},
            xda_first_category_id = #{item.xdaFirstCategoryId},
            xda_second_category_id = #{item.xdaSecondCategoryId},
            commodity_search_name = #{item.commoditySearchName},
            xda_first_category_name = #{item.xdaFirstCategoryName},
            xda_second_category_name = #{item.xdaSecondCategoryName},
            delivery_date_anterior_interval = #{item.deliveryDateAnteriorInterval},
            delivery_date_after_interval = #{item.deliveryDateAfterInterval},
            app_status = #{item.appStatus},
            pf_app_status = #{item.pfAppStatus},
            pf_delivery_date_range_code = #{item.pfDeliveryDateRangeCode},
            pf_delivery_date_range_value = #{item.pfDeliveryDateRangeValue},
            pf_delivery_date_anterior_interval = #{item.pfDeliveryDateAnteriorInterval},
            pf_delivery_date_after_interval = #{item.pfDeliveryDateAfterInterval},
            update_time = now()
            where id = #{item.id}
        </foreach>
    </update>

    <update id="batchUpdateDeliveryDate">
        <foreach collection="list" item="item" separator=";">
            update t_xda_commodity_es
            set
            delivery_date_range_code = #{item.deliveryDateRangeCode},
            delivery_date_range_value = #{item.deliveryDateRangeValue},
            delivery_date_anterior_interval = #{item.deliveryDateAnteriorInterval},
            delivery_date_after_interval = #{item.deliveryDateAfterInterval},

            pf_delivery_date_range_code = #{item.pfDeliveryDateRangeCode},
            pf_delivery_date_range_value = #{item.pfDeliveryDateRangeValue},
            pf_delivery_date_anterior_interval = #{item.pfDeliveryDateAnteriorInterval},
            pf_delivery_date_after_interval = #{item.pfDeliveryDateAfterInterval},

            update_time = now()
            where id = #{item.id}
        </foreach>
    </update>

</mapper>