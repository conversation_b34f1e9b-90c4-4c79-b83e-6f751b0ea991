{"es-demo-json": {"properties": {"birthday": {"format": "date_optional_time", "type": "date"}, "aliasName": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}, "money": {"type": "double"}, "grade": {"type": "keyword"}, "sex": {"type": "boolean"}, "name": {"type": "keyword"}, "id": {"type": "text", "fields": {"keyword": {"ignore_above": 256, "type": "keyword"}}}, "title": {"search_analyzer": "ik_smart", "analyzer": "ik_max_word", "type": "text"}, "age": {"type": "long"}}}}