package com.pinshang.qingyun.xda.search;

import com.alibaba.fastjson.JSON;
import com.pinshang.qingyun.box.utils.DateUtil;
import com.pinshang.qingyun.order.dto.tob.EsXdaCommoditySoldOutIDTO;
import com.pinshang.qingyun.order.dto.tob.EsXdaCommoditySoldOutODTO;
import com.pinshang.qingyun.order.service.ToBCommodityStockClient;
import com.pinshang.qingyun.xda.search.dto.CommodityAppStatusDTO;
import com.pinshang.qingyun.xda.search.dto.EsXdaUpdateDiffRecordIDTO;
import com.pinshang.qingyun.xda.search.service.*;
import com.pinshang.qingyun.xda.search.vo.EsXdaCategoryChangeKafkaVO;
import com.pinshang.qingyun.xda.search.vo.EsXdaCommodityTextChangeKafkaVO;
import com.pinshang.qingyun.xda.search.vo.TobCommodityStockKafkaVo;
import lombok.Getter;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2024/04/09
 * @Version 1.0
 */
public class XdaEsTest extends AbstractJunitBase {
    @Getter
    @Autowired
    private EsCommodityTextService esCommodityTextService;
    @Autowired
    private EsCategoryService esCategoryService;

    @Autowired
    private EsCommodityService esCommodityService;

    @Autowired
    private EsXdaCommodityService esXdaStoreCommodityService;

    @Autowired
    private ToBCommodityStockClient tobCommodityStockClient;

    @Rollback(false)
    @Test
    public void appStatus() {

        CommodityAppStatusDTO dto = new CommodityAppStatusDTO();
//        List<Long> ids = new ArrayList<>();
//        ids.add(438143086454694400L);
//        dto.setCommodityIds(ids);
//        List<Long> newIds = new ArrayList<>();
//        newIds.add(999965182752826608L);
//        dto.setNewCommodityIdList(newIds);
//        dto.setAppStatus(1);
//        esCommodityService.commodityAppStatus(dto);

//        StoreCommodityAdjustPriceKafkaDTO dto = new StoreCommodityAdjustPriceKafkaDTO();
//        dto.setModelIdList(Arrays.asList(9217427011619033759L));
//        dto.setOperateType(1);
//        dto.setCommodityList(Arrays.asList(new StoreCommodityAdjustPriceKafkaDTO.CommodityPriceVo(2580787591198700L,new BigDecimal("6.50")) ));
//        esCommodityService.commodityPriceAdjust(dto);

//        List<Long> commodityIds = new ArrayList<>();
//        commodityIds.add(31420677825407000L);
//        esCommodityService.commodityToMysql(commodityIds);

//        esCommodityService.commodityToMysql(null);

//        StoreStopOrStartDTO dto = new StoreStopOrStartDTO();
//        dto.setStoreId(999872035591639009L);
//        dto.setOperateType(1);
//        xdaStoreCommodityService.storeStopOrStart(dto);

//        esCommodityService.commodityToMysql(null);
 //       esCategoryService.allCategoryToSql();

//        String message = "{\"wrapper\":\"kafka\",\"data\":{\"modelIdList\":[2013101615150000010],\"commodityList\":[{\"commodityId\":438143086454694400,\"commodityPrice\":1.48}],\"operateType\":3}}";
//        KafkaMessageWrapper messageWrapper = JSON.parseObject(message, KafkaMessageWrapper.class);
//
//        StoreCommodityAdjustPriceKafkaDTO dto = JsonUtil.json2java(messageWrapper.getData().toString(), StoreCommodityAdjustPriceKafkaDTO.class);
//        esCommodityService.commodityPriceAdjust(dto);
    }

    @Rollback(false)
    @Test
    public void storeCommodity() {
 //       xdaStoreCommodityService.createAllStoreCommodityIndex();
 //       xdaStoreCommodityService.rebuildXdaStoreCommodity(Arrays.asList(999872035591639040L), Arrays.asList(8398958710773801L));
 //       xdaStoreCommodityService.rebuildXdaStoreCommodity(null, Arrays.asList(8398958710773801L, 6772315779888100L, 8263766990835201L));
 //       xdaStoreCommodityService.rebuildXdaStoreCommodity(Arrays.asList(999872035591638998L,999872035591639008L, 999872035591639009L), null);
//        xdaStoreCommodityService.rebuildXdaStoreCommodity(Arrays.asList(999872035591639009L), null);
//        xdaStoreCommodityService.rebuildXdaStoreCommodity(null, Arrays.asList(999965182752825712L));
//        xdaStoreCommodityService.rebuildXdaStoreCommodity(null, null);
    }

    @Rollback(false)
    @Test
    public void text(){
        EsXdaCommodityTextChangeKafkaVO vo = new EsXdaCommodityTextChangeKafkaVO();
        vo.setCommodityId(2580787591198700L);
        List<EsXdaCommodityTextChangeKafkaVO> voList = Collections.singletonList(vo);
        esCommodityTextService.updateXdaCommodityText(voList);

    }

    @Rollback(false)
    @Test
    public void cate(){
        EsXdaCategoryChangeKafkaVO vo = new EsXdaCategoryChangeKafkaVO();
        vo.setCateId(139L);
        vo.setStatus(2);
        vo.setCateLevel(2);
        vo.setCateName("吐司面包吐司面包");
        vo.setParentId(138L);
        vo.setSortNum(1);
        List<EsXdaCategoryChangeKafkaVO> voList = Collections.singletonList(vo);
        esCategoryService.updateXdaCategory(voList);

    }


    @Test
    @Rollback(value = false)
    public void refreshStock(){
        esCommodityService.refreshStock();
    }


    @Test
    @Rollback(value = false)
    public void updateDiffRecord(){
       /* EsXdaUpdateDiffRecordIDTO idto = new EsXdaUpdateDiffRecordIDTO();
        idto.setCommodityId(582829522022324224L);
        idto.setAppStatus(1);
        idto.setDate(DateUtil.addDay(new Date(), 3));
        idto.setSoldOut(0);
        int i = esXdaStoreCommodityService.updateDiffRecord(idto);
        System.out.println(i);*/
    }


    @Rollback(false)
    @Test
    public void queryCommodityInventory() {
        EsXdaCommoditySoldOutIDTO idto = new EsXdaCommoditySoldOutIDTO();
        idto.setDate(new Date());
        idto.setType(2);
        idto.setCommodityIdList(Arrays.asList(56095697583367000L, 4046139883264700L, 717276816248969600L));
        List<EsXdaCommoditySoldOutODTO> result = tobCommodityStockClient.queryCommodityInventory(idto);
        System.out.println(result);
    }

}
