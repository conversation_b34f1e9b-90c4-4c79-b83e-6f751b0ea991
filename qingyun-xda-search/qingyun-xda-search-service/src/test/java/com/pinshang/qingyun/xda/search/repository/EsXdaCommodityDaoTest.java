package com.pinshang.qingyun.xda.search.repository;

import com.google.common.collect.Lists;
import com.pinshang.qingyun.xda.search.AbstractJunitBase;
import com.pinshang.qingyun.xda.search.document.EsXdaCommodity;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4 13:09
 */
@Slf4j
public class EsXdaCommodityDaoTest extends AbstractJunitBase {

    @Autowired
    private EsXdaCommodityDao esXdaCommodityDao;

    @Test
    public void test01() {
        List<Long> commodityIdList = Lists.newArrayList(
                2580787591198700L, 4511066835394600L, 5356816720938800L, 6772315779888100L, 7951724559737401L
        );
        Integer pageSize = 10;
        Boolean isPfStore = false;
        Boolean commodityIdNeedSort = true;
        List<EsXdaCommodity> esXdaCommodityList = esXdaCommodityDao.searchXdaCommodity(commodityIdList, pageSize, isPfStore, commodityIdNeedSort);
        log.info("esXdaCommodityList:{}", esXdaCommodityList);
    }
}
