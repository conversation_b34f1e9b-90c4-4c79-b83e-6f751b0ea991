package com.pinshang.qingyun.xda.search.service;

import com.pinshang.qingyun.xda.search.AbstractJunitBase;
import com.pinshang.qingyun.xda.search.service.manager.EsBaseSwitcherManagerService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/4 16:51
 */
@Slf4j
public class EsBaseSwitcherManagerServiceTest extends AbstractJunitBase {

    @Autowired
    private EsBaseSwitcherManagerService esBaseSwitcherManagerService;

    @Test
    public void test01() {
        Long userId = 1L;
        boolean switchBaseSearch = esBaseSwitcherManagerService.isSwitchBaseSearch(userId);

        log.info("--->> userId = {} .... switchBaseSearch:{}", userId, switchBaseSearch);
    }
}
