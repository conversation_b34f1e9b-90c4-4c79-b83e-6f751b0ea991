package com.pinshang.qingyun.xda.search.service;

import com.pinshang.qingyun.xda.search.AbstractJunitBase;
import com.pinshang.qingyun.xda.search.service.third.XdaSearchCacheService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5 10:14
 */
@Slf4j
public class XdaSearchCacheServiceTest extends AbstractJunitBase {

    @Autowired
    private XdaSearchCacheService xdaSearchCacheService;

    @Autowired
    private StoreService storeService;

    @Test
    public void test01() throws Exception {
        Long shopId = 999872035591660701L;
        long start1 = System.currentTimeMillis();
        Boolean store_ps = xdaSearchCacheService.invokeCache("xda_store_ps", () -> storeService.isPfsStore(shopId));
        log.info("shopId ={} store_ps:{},---> 耗时：{}", shopId, store_ps, System.currentTimeMillis() - start1);

        Thread.sleep(200L);
        long start2 = System.currentTimeMillis();
        Boolean store_ps2 = xdaSearchCacheService.invokeCache("xda_store_ps", () -> storeService.isPfsStore(shopId));
        log.info("shopId ={} store_ps:{},---> 耗时：{}", shopId, store_ps2, System.currentTimeMillis() - start2);
        Thread.sleep(1000L);

        long start3 = System.currentTimeMillis();
        Boolean store_ps3 = xdaSearchCacheService.invokeCache("xda_store_ps", () -> storeService.isPfsStore(shopId));
        log.info("shopId ={} store_ps:{},---> 耗时：{}", shopId, store_ps3, System.currentTimeMillis() - start3);
    }


}
