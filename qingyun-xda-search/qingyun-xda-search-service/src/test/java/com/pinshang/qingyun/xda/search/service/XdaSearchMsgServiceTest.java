package com.pinshang.qingyun.xda.search.service;

import com.pinshang.qingyun.base.enums.search.BaseSearchOptTypeEnum;
import com.pinshang.qingyun.xda.search.AbstractJunitBase;
import com.pinshang.qingyun.xda.search.service.third.XdaSearchMsgService;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 类描述：
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/4/17 18:31
 */
@Slf4j
public class XdaSearchMsgServiceTest extends AbstractJunitBase {

    @Autowired
    private XdaSearchMsgService xdaSearchMsgService;

    @Test
    public void test01() {
        List<Long> shopCommodityIdList = Lists.newArrayList(153573040845360512L);
        BaseSearchOptTypeEnum optTypeEnum = BaseSearchOptTypeEnum.ADD;
        xdaSearchMsgService.sendBaseSearchCommodityIdMsg(shopCommodityIdList, optTypeEnum);
        try {
            Thread.sleep(10000000L);
        } catch (Exception e) {
            log.info("test01 error ", e);
        }
    }
}
